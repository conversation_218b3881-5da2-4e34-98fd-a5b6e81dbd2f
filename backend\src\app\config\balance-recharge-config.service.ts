import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BalanceRechargeConfig } from './entities/balance-recharge-config.entity';
import { BalanceRechargeLimit } from './entities/balance-recharge-limit.entity';
import { 
  CreateBalanceRechargeConfigDto, 
  UpdateBalanceRechargeConfigDto, 
  BalanceRechargeConfigQueryDto,
  UpdateBalanceRechargeLimitDto
} from './dto/balance-recharge-config.dto';

@Injectable()
export class BalanceRechargeConfigService {
  constructor(
    @InjectRepository(BalanceRechargeConfig)
    private balanceRechargeConfigRepository: Repository<BalanceRechargeConfig>,
    @InjectRepository(BalanceRechargeLimit)
    private balanceRechargeLimitRepository: Repository<BalanceRechargeLimit>,
  ) {}

  async create(createDto: CreateBalanceRechargeConfigDto, userId: number) {
    // 验证活动时间逻辑
    if (createDto.activityStartTime && createDto.activityEndTime) {
      const startTime = new Date(createDto.activityStartTime);
      const endTime = new Date(createDto.activityEndTime);
      
      if (startTime >= endTime) {
        throw new BadRequestException('活动开始时间必须早于结束时间');
      }
    }

    const config = this.balanceRechargeConfigRepository.create();
    Object.assign(config, createDto, {
      activityStartTime: createDto.activityStartTime ? new Date(createDto.activityStartTime) : null,
      activityEndTime: createDto.activityEndTime ? new Date(createDto.activityEndTime) : null,
      createdBy: userId,
      updatedBy: userId,
    });

    return await this.balanceRechargeConfigRepository.save(config);
  }

  async findAll(query: BalanceRechargeConfigQueryDto) {
    const queryBuilder = this.balanceRechargeConfigRepository
      .createQueryBuilder('config')
      .leftJoinAndSelect('config.creator', 'creator')
      .leftJoinAndSelect('config.updater', 'updater');

    // 添加筛选条件
    if (query.status !== undefined) {
      queryBuilder.andWhere('config.status = :status', { status: query.status });
    }

    // 按排序顺序排列
    queryBuilder.orderBy('config.sortOrder', 'ASC').addOrderBy('config.id', 'ASC');

    const configs = await queryBuilder.getMany();

    return configs.map(config => ({
      ...config,
      creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
      updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
    }));
  }

  async findOne(id: number) {
    const config = await this.balanceRechargeConfigRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });

    if (!config) {
      throw new NotFoundException('余额充值配置不存在');
    }

    return {
      ...config,
      creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
      updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
    };
  }

  async update(id: number, updateDto: UpdateBalanceRechargeConfigDto, userId: number) {
    const config = await this.balanceRechargeConfigRepository.findOne({ where: { id } });
    if (!config) {
      throw new NotFoundException('余额充值配置不存在');
    }

    // 验证活动时间逻辑
    if (updateDto.activityStartTime && updateDto.activityEndTime) {
      const startTime = new Date(updateDto.activityStartTime);
      const endTime = new Date(updateDto.activityEndTime);
      
      if (startTime >= endTime) {
        throw new BadRequestException('活动开始时间必须早于结束时间');
      }
    }

    // 如果只更新了开始时间或结束时间中的一个，需要验证与现有时间的关系
    if (updateDto.activityStartTime && !updateDto.activityEndTime && config.activityEndTime) {
      const startTime = new Date(updateDto.activityStartTime);
      if (startTime >= config.activityEndTime) {
        throw new BadRequestException('活动开始时间必须早于结束时间');
      }
    }

    if (updateDto.activityEndTime && !updateDto.activityStartTime && config.activityStartTime) {
      const endTime = new Date(updateDto.activityEndTime);
      if (config.activityStartTime >= endTime) {
        throw new BadRequestException('活动开始时间必须早于结束时间');
      }
    }

    // 更新配置
    Object.assign(config, updateDto, { 
      updatedBy: userId,
      activityStartTime: updateDto.activityStartTime ? new Date(updateDto.activityStartTime) : config.activityStartTime,
      activityEndTime: updateDto.activityEndTime ? new Date(updateDto.activityEndTime) : config.activityEndTime,
    });

    const savedConfig = await this.balanceRechargeConfigRepository.save(config);
    return savedConfig;
  }

  async remove(id: number) {
    const config = await this.balanceRechargeConfigRepository.findOne({ where: { id } });
    if (!config) {
      throw new NotFoundException('余额充值配置不存在');
    }

    await this.balanceRechargeConfigRepository.remove(config);
    return { message: '删除成功' };
  }

  // 获取所有生效的配置（考虑活动时间）
  async findAllEffective() {
    const configs = await this.balanceRechargeConfigRepository.find({
      where: { status: 1 },
      order: { sortOrder: 'ASC', id: 'ASC' },
    });

    return configs.map(config => {
      const activityStatus = config.getActivityStatus();
      
      return {
        id: config.id,
        tierName: config.tierName,
        rechargeAmount: config.rechargeAmount,
        effectiveRechargeAmount: config.getEffectiveRechargeAmount(),
        activityBonusAmount: config.getActivityBonusAmount(),
        isActivityActive: config.isActivityActive(),
        activityStatusDescription: activityStatus.description,
        sortOrder: config.sortOrder,
      };
    });
  }

  // 获取当前活动中的配置
  async getActiveActivityConfigs() {
    const allConfigs = await this.balanceRechargeConfigRepository.find({
      where: { status: 1 },
      order: { sortOrder: 'ASC' },
    });

    return allConfigs.filter(config => config.isActivityActive());
  }

  // 批量更新活动时间
  async updateActivityTime(startTime: Date, endTime: Date, userId: number) {
    if (startTime >= endTime) {
      throw new BadRequestException('活动开始时间必须早于结束时间');
    }

    await this.balanceRechargeConfigRepository.update(
      {},
      {
        activityStartTime: startTime,
        activityEndTime: endTime,
        updatedBy: userId,
      }
    );

    return { message: '批量更新活动时间成功' };
  }

  // 获取充值限制
  async getRechargeLimit() {
    console.log('🔍 Service: 开始查询余额充值限制...');

    const limit = await this.balanceRechargeLimitRepository.findOne({
      where: { status: 1 },
      relations: ['creator', 'updater'],
    });

    console.log('📊 Service: 查询结果:', limit);

    if (!limit) {
      console.log('❌ Service: 没有找到充值限制配置');
      throw new NotFoundException('充值限制配置不存在');
    }

    console.log('🔍 Service: 字段类型检查:');
    console.log('- minAmount:', typeof limit.minAmount, '值:', limit.minAmount);
    console.log('- maxAmount:', typeof limit.maxAmount, '值:', limit.maxAmount);

    try {
      // 手动创建一个简单的对象，避免实体的Transform装饰器
      const result = {
        id: limit.id,
        limitName: limit.limitName,
        minAmount: parseFloat(String(limit.minAmount)),
        maxAmount: parseFloat(String(limit.maxAmount)),
        status: limit.status,
        remark: limit.remark,
        createdBy: limit.createdBy,
        updatedBy: limit.updatedBy,
        createTime: limit.createTime,
        updateTime: limit.updateTime,
        limitDescription: `充值金额范围：¥${parseFloat(String(limit.minAmount))} - ¥${parseFloat(String(limit.maxAmount))}`,
        creator: limit.creator ? { id: limit.creator.id, username: limit.creator.username } : null,
        updater: limit.updater ? { id: limit.updater.id, username: limit.updater.username } : null,
      };

      console.log('✅ Service: 最终返回结果:', result);
      return result;

    } catch (error) {
      console.error('❌ Service: 处理过程中出错:', error);
      throw error;
    }
  }

  // 更新充值限制
  async updateRechargeLimit(updateDto: UpdateBalanceRechargeLimitDto, userId: number) {
    // 验证金额逻辑
    if (updateDto.maxAmount > 0 && updateDto.maxAmount <= updateDto.minAmount) {
      throw new BadRequestException('最高充值金额必须大于最低充值金额');
    }

    const limit = await this.balanceRechargeLimitRepository.findOne({
      where: { status: 1 },
    });

    if (!limit) {
      // 如果不存在，创建新的
      const newLimit = this.balanceRechargeLimitRepository.create({
        ...updateDto,
        createdBy: userId,
        updatedBy: userId,
      });
      return await this.balanceRechargeLimitRepository.save(newLimit);
    } else {
      // 更新现有的
      Object.assign(limit, updateDto, { updatedBy: userId });
      return await this.balanceRechargeLimitRepository.save(limit);
    }
  }

  // 验证充值金额是否在允许范围内
  async validateRechargeAmount(amount: number): Promise<{ isValid: boolean; error?: string }> {
    const limit = await this.balanceRechargeLimitRepository.findOne({
      where: { status: 1 },
    });

    if (!limit) {
      return { isValid: true };
    }

    if (!limit.isAmountValid(amount)) {
      return { 
        isValid: false, 
        error: limit.getAmountValidationError(amount) || undefined
      };
    }

    return { isValid: true };
  }
}
