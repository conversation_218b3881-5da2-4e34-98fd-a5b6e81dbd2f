"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoldRechargeConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const gold_recharge_config_entity_1 = require("./entities/gold-recharge-config.entity");
let GoldRechargeConfigService = class GoldRechargeConfigService {
    goldRechargeConfigRepository;
    constructor(goldRechargeConfigRepository) {
        this.goldRechargeConfigRepository = goldRechargeConfigRepository;
    }
    async create(createDto, userId) {
        if (createDto.activityStartTime && createDto.activityEndTime) {
            const startTime = new Date(createDto.activityStartTime);
            const endTime = new Date(createDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        const config = this.goldRechargeConfigRepository.create();
        Object.assign(config, createDto, {
            activityStartTime: createDto.activityStartTime ? new Date(createDto.activityStartTime) : null,
            activityEndTime: createDto.activityEndTime ? new Date(createDto.activityEndTime) : null,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.goldRechargeConfigRepository.save(config);
    }
    async findAll(query) {
        const queryBuilder = this.goldRechargeConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater');
        if (query.status !== undefined) {
            queryBuilder.andWhere('config.status = :status', { status: query.status });
        }
        queryBuilder.orderBy('config.sortOrder', 'ASC').addOrderBy('config.id', 'ASC');
        const configs = await queryBuilder.getMany();
        return configs.map(config => ({
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        }));
    }
    async findOne(id) {
        const config = await this.goldRechargeConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!config) {
            throw new common_1.NotFoundException('金币充值配置不存在');
        }
        return {
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        };
    }
    async update(id, updateDto, userId) {
        const config = await this.goldRechargeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('金币充值配置不存在');
        }
        if (updateDto.activityStartTime && updateDto.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            const endTime = new Date(updateDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityStartTime && !updateDto.activityEndTime && config.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            if (startTime >= config.activityEndTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityEndTime && !updateDto.activityStartTime && config.activityStartTime) {
            const endTime = new Date(updateDto.activityEndTime);
            if (config.activityStartTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        Object.assign(config, updateDto, {
            updatedBy: userId,
            activityStartTime: updateDto.activityStartTime ? new Date(updateDto.activityStartTime) : config.activityStartTime,
            activityEndTime: updateDto.activityEndTime ? new Date(updateDto.activityEndTime) : config.activityEndTime,
        });
        const savedConfig = await this.goldRechargeConfigRepository.save(config);
        return savedConfig;
    }
    async remove(id) {
        const config = await this.goldRechargeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('金币充值配置不存在');
        }
        await this.goldRechargeConfigRepository.remove(config);
        return { message: '删除成功' };
    }
    async findAllEffective() {
        const configs = await this.goldRechargeConfigRepository.find({
            where: { status: 1 },
            order: { sortOrder: 'ASC', id: 'ASC' },
        });
        return configs.map(config => {
            const activityStatus = config.getActivityStatus();
            return {
                id: config.id,
                tierName: config.tierName,
                goldAmount: config.goldAmount,
                price: config.price,
                effectiveGoldAmount: config.getEffectiveGoldAmount(),
                activityBonusGold: config.getActivityBonusGold(),
                isActivityActive: config.isActivityActive(),
                activityStatusDescription: activityStatus.description,
                sortOrder: config.sortOrder,
            };
        });
    }
    async getActiveActivityConfigs() {
        const allConfigs = await this.goldRechargeConfigRepository.find({
            where: { status: 1 },
            order: { sortOrder: 'ASC' },
        });
        return allConfigs.filter(config => config.isActivityActive());
    }
    async updateActivityTime(startTime, endTime, userId) {
        if (startTime >= endTime) {
            throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
        }
        await this.goldRechargeConfigRepository.update({}, {
            activityStartTime: startTime,
            activityEndTime: endTime,
            updatedBy: userId,
        });
        return { message: '批量更新活动时间成功' };
    }
};
exports.GoldRechargeConfigService = GoldRechargeConfigService;
exports.GoldRechargeConfigService = GoldRechargeConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(gold_recharge_config_entity_1.GoldRechargeConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], GoldRechargeConfigService);
//# sourceMappingURL=gold-recharge-config.service.js.map