import{j as e}from"./index-CHjq8S-S.js";import{a}from"./react-BUTTOX-3.js";import{B as x}from"./index-DDI4OfxQ.js";import{c as y}from"./index-DMJlwb4Y.js";import{aq as f,d as g,az as h,s as r}from"./antd-CXPM1OiB.js";const w=()=>{const[o,n]=a.useState(!1),[i,d]=a.useState([]),[l,m]=a.useState(0),c=async()=>{var s;try{n(!0),console.log("开始调用权限列表API...");const t=await y({current:1,pageSize:10});console.log("API响应:",t),t&&t.result?(d(t.result.list||[]),m(t.result.total||0),r.success(`成功加载 ${((s=t.result.list)==null?void 0:s.length)||0} 条权限数据`)):r.error("API响应格式错误")}catch(t){console.error("加载权限数据失败:",t);const p=t instanceof Error?t.message:"未知错误";r.error("加载权限数据失败: "+p)}finally{n(!1)}};a.useEffect(()=>{c()},[]);const u=[{title:"ID",dataIndex:"id",key:"id"},{title:"权限名称",dataIndex:"name",key:"name"},{title:"权限代码",dataIndex:"code",key:"code"},{title:"权限类型",dataIndex:"type",key:"type"},{title:"状态",dataIndex:"status",key:"status",render:s=>s===1?"启用":"禁用"},{title:"描述",dataIndex:"description",key:"description"}];return e.jsx(x,{children:e.jsxs(f,{title:"权限管理测试页面",children:[e.jsxs("div",{style:{marginBottom:16},children:[e.jsx(g,{type:"primary",onClick:c,loading:o,children:"重新加载数据"}),e.jsxs("span",{style:{marginLeft:16},children:["总计: ",l," 条数据，当前显示: ",i.length," 条"]})]}),e.jsx(h,{columns:u,dataSource:i,rowKey:"id",loading:o,pagination:{total:l,pageSize:10,current:1,showSizeChanger:!0}})]})})};export{w as default};
