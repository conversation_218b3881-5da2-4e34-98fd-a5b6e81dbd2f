"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppAuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
const entities_1 = require("../../app/entities");
let AppAuthService = class AppAuthService {
    userRepository;
    channelRepository;
    adRepository;
    pageRepository;
    deviceLogRepository;
    jwtService;
    configService;
    constructor(userRepository, channelRepository, adRepository, pageRepository, deviceLogRepository, jwtService, configService) {
        this.userRepository = userRepository;
        this.channelRepository = channelRepository;
        this.adRepository = adRepository;
        this.pageRepository = pageRepository;
        this.deviceLogRepository = deviceLogRepository;
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async login(loginDto, ip, userAgent) {
        const { loginType, identifier, password, deviceId, deviceInfo } = loginDto;
        let whereCondition = {};
        switch (loginType) {
            case 'username':
                whereCondition = { username: identifier };
                break;
            case 'email':
                whereCondition = { email: identifier };
                break;
            case 'phone':
                whereCondition = { phone: identifier };
                break;
            default:
                throw new common_1.BadRequestException('不支持的登录方式');
        }
        const user = await this.userRepository.findOne({
            where: whereCondition,
            select: ['id', 'uid', 'username', 'email', 'phone', 'password', 'nickname', 'avatar', 'status', 'vipLevel', 'rechargeBalance', 'goldBalance'],
        });
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        if (user.status === 1) {
            throw new common_1.UnauthorizedException('账户已被封禁');
        }
        if (user.status === 2) {
            throw new common_1.UnauthorizedException('账户已注销');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        if (deviceId) {
            await this.recordDeviceLog(user.id, deviceId, ip, userAgent, 2);
        }
        await this.userRepository.update(user.id, {
            lastLoginTime: new Date(),
            lastLoginIp: ip,
        });
        const tokens = await this.generateTokens(user);
        return {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            tokenType: 'Bearer',
            expiresIn: 7 * 24 * 60 * 60,
            user: {
                id: user.id,
                uid: user.uid,
                username: user.username,
                email: user.email,
                nickname: user.nickname,
                avatar: user.avatar,
                vipLevel: user.vipLevel,
                rechargeBalance: user.rechargeBalance,
                goldBalance: user.goldBalance,
            },
        };
    }
    async register(registerDto, ip, userAgent) {
        const { username, email, phone, password, nickname, inviteCode, channelIdentifier, adIdentifier, pageIdentifier, deviceId, deviceInfo, } = registerDto;
        if (username) {
            const existingUser = await this.userRepository.findOne({ where: { username } });
            if (existingUser) {
                throw new common_1.ConflictException('用户名已存在');
            }
        }
        if (email) {
            const existingUser = await this.userRepository.findOne({ where: { email } });
            if (existingUser) {
                throw new common_1.ConflictException('邮箱已存在');
            }
        }
        if (phone) {
            const existingUser = await this.userRepository.findOne({ where: { phone } });
            if (existingUser) {
                throw new common_1.ConflictException('手机号已存在');
            }
        }
        let inviter = null;
        let invitationPath = '';
        if (inviteCode) {
            const inviterId = parseInt(inviteCode);
            if (!isNaN(inviterId)) {
                inviter = await this.userRepository.findOne({ where: { id: inviterId } });
                if (inviter) {
                    invitationPath = inviter.invitationPath ? `${inviter.invitationPath}${inviter.id}>` : `${inviter.id}>`;
                }
            }
        }
        let channelId;
        let adId;
        let promotionalPageId;
        let acquisitionTag = '';
        if (channelIdentifier) {
            const channel = await this.channelRepository.findOne({ where: { identifier: channelIdentifier } });
            if (channel) {
                channelId = channel.id;
                acquisitionTag = channel.identifier;
            }
        }
        if (adIdentifier && channelId) {
            const ad = await this.adRepository.findOne({ where: { identifier: adIdentifier, channelId } });
            if (ad) {
                adId = ad.id;
                acquisitionTag += `-${ad.identifier}`;
            }
        }
        if (pageIdentifier && adId) {
            const page = await this.pageRepository.findOne({ where: { identifier: pageIdentifier, adId } });
            if (page) {
                promotionalPageId = page.id;
                acquisitionTag += `-${page.identifier}`;
            }
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const lastUser = await this.userRepository.findOne({
            order: { id: 'DESC' },
            select: ['id'],
        });
        const uid = (lastUser?.id || 0) + 100000;
        const user = await this.userRepository.save({
            uid,
            username,
            email,
            phone,
            password: hashedPassword,
            nickname: nickname || username,
            inviterId: inviter?.id,
            invitationPath,
            channelId,
            adId,
            promotionalPageId,
            acquisitionTag,
            registerIp: ip,
            lastLoginIp: ip,
            lastLoginTime: new Date(),
        });
        if (deviceId) {
            await this.recordDeviceLog(user.id, deviceId, ip, userAgent, 1);
        }
        const tokens = await this.generateTokens(user);
        return {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            tokenType: 'Bearer',
            expiresIn: 7 * 24 * 60 * 60,
            user: {
                id: user.id,
                uid: user.uid,
                username: user.username,
                email: user.email,
                nickname: user.nickname,
                avatar: user.avatar || '',
                vipLevel: user.vipLevel,
                rechargeBalance: user.rechargeBalance,
                goldBalance: user.goldBalance,
            },
        };
    }
    async generateTokens(user) {
        const payload = {
            sub: user.id,
            uid: user.uid,
            username: user.username,
            type: 'app',
        };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = this.jwtService.sign(payload, {
            secret: this.configService.get('JWT_REFRESH_SECRET'),
            expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN'),
        });
        return { accessToken, refreshToken };
    }
    async recordDeviceLog(userId, deviceId, ipAddress, userAgent, logType) {
        await this.deviceLogRepository.save({
            userId,
            deviceId,
            ipAddress,
            userAgent,
            logType,
        });
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
            });
            const user = await this.userRepository.findOne({
                where: { id: payload.sub },
                select: ['id', 'uid', 'username', 'email', 'status'],
            });
            if (!user || user.status !== 0) {
                throw new common_1.UnauthorizedException('用户不存在或已被禁用');
            }
            const tokens = await this.generateTokens(user);
            return tokens;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('刷新令牌无效');
        }
    }
};
exports.AppAuthService = AppAuthService;
exports.AppAuthService = AppAuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.AppUser)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.MarketingChannel)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.MarketingAd)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.PromotionalPage)),
    __param(4, (0, typeorm_1.InjectRepository)(entities_1.DeviceLogRealtime)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        config_1.ConfigService])
], AppAuthService);
//# sourceMappingURL=app-auth.service.js.map