-- =============================================
-- 广告配置表图片跳转结构重构脚本
-- =============================================

-- 1. 备份现有数据到临时表
CREATE TABLE ad_configs_backup AS 
SELECT * FROM ad_configs;

-- 2. 添加新的图片跳转数组字段
ALTER TABLE ad_configs 
ADD COLUMN image_items JSONB DEFAULT '[]'::jsonb;

-- 3. 将现有数据迁移到新结构
-- 对于轮播类型(ad_type=1)，每张图片使用相同的跳转配置
UPDATE ad_configs 
SET image_items = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'imageUrl', image_url,
            'jumpType', jump_type,
            'jumpTarget', jump_target,
            'title', COALESCE(title, ''),
            'description', ''
        )
    )
    FROM (
        SELECT 
            jsonb_array_elements_text(images) as image_url,
            jump_type,
            jump_target,
            title
        FROM ad_configs ac2 
        WHERE ac2.id = ad_configs.id
    ) as image_data
)
WHERE images IS NOT NULL AND jsonb_array_length(images) > 0;

-- 4. 删除旧字段
ALTER TABLE ad_configs 
DROP COLUMN images,
DROP COLUMN jump_type,
DROP COLUMN jump_target;

-- 5. 添加约束和注释
ALTER TABLE ad_configs 
ADD CONSTRAINT check_image_items_not_empty 
CHECK (jsonb_array_length(image_items) > 0);

COMMENT ON COLUMN ad_configs.image_items IS '图片跳转项数组，每个元素包含imageUrl、jumpType、jumpTarget、title、description';

-- 6. 创建索引优化查询
CREATE INDEX IF NOT EXISTS idx_ad_configs_image_items_gin ON ad_configs USING GIN (image_items);

-- 7. 更新表注释
COMMENT ON TABLE ad_configs IS '广告配置表 - 支持多图片独立跳转配置';

-- 8. 插入示例数据（轮播广告，每张图片不同跳转）
INSERT INTO ad_configs (ad_identifier, ad_type, title, image_items, sort_order, status, remark, created_by, create_time, update_time) VALUES
('home_carousel_multi', 1, '首页多图轮播', '[
    {
        "imageUrl": "/uploads/ads/carousel1.jpg",
        "jumpType": 1,
        "jumpTarget": "/games/popular",
        "title": "热门游戏",
        "description": "查看最受欢迎的游戏"
    },
    {
        "imageUrl": "/uploads/ads/carousel2.jpg", 
        "jumpType": 1,
        "jumpTarget": "/vip/upgrade",
        "title": "VIP升级",
        "description": "升级VIP享受更多特权"
    },
    {
        "imageUrl": "/uploads/ads/carousel3.jpg",
        "jumpType": 2, 
        "jumpTarget": "https://activity.example.com/event1",
        "title": "限时活动",
        "description": "参与活动赢取丰厚奖励"
    }
]'::jsonb, 1, 1, '多图轮播示例，每张图片独立跳转', 2, NOW(), NOW()),

('home_grid_4', 7, '首页4宫格', '[
    {
        "imageUrl": "/uploads/ads/grid1.jpg",
        "jumpType": 1,
        "jumpTarget": "/games/slots",
        "title": "老虎机",
        "description": "经典老虎机游戏"
    },
    {
        "imageUrl": "/uploads/ads/grid2.jpg",
        "jumpType": 1, 
        "jumpTarget": "/games/poker",
        "title": "扑克",
        "description": "德州扑克等纸牌游戏"
    },
    {
        "imageUrl": "/uploads/ads/grid3.jpg",
        "jumpType": 1,
        "jumpTarget": "/games/sports",
        "title": "体育竞猜", 
        "description": "体育赛事竞猜"
    },
    {
        "imageUrl": "/uploads/ads/grid4.jpg",
        "jumpType": 2,
        "jumpTarget": "https://live.example.com",
        "title": "真人娱乐",
        "description": "真人荷官在线娱乐"
    }
]'::jsonb, 1, 1, '首页4宫格示例，每个图标独立跳转', 2, NOW(), NOW());

-- 9. 验证数据迁移
DO $$
DECLARE
    total_count INTEGER;
    migrated_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM ad_configs_backup;
    SELECT COUNT(*) INTO migrated_count FROM ad_configs WHERE image_items IS NOT NULL AND jsonb_array_length(image_items) > 0;
    
    RAISE NOTICE '数据迁移完成: 原始记录数 %, 迁移记录数 %', total_count, migrated_count;
    
    IF migrated_count < total_count THEN
        RAISE WARNING '部分数据可能未正确迁移，请检查备份表 ad_configs_backup';
    END IF;
END $$;
