"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const entities_1 = require("./entities");
const auth_module_1 = require("./auth/auth.module");
const user_module_1 = require("./user/user.module");
const role_module_1 = require("./role/role.module");
const permission_module_1 = require("./permission/permission.module");
const menu_module_1 = require("./menu/menu.module");
const workbook_module_1 = require("./workbook/workbook.module");
const routes_module_1 = require("./routes/routes.module");
let SystemModule = class SystemModule {
};
exports.SystemModule = SystemModule;
exports.SystemModule = SystemModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.SysUser,
                entities_1.SysRole,
                entities_1.SysPermission,
                entities_1.SysMenu,
                entities_1.SysWorkbook,
            ]),
            auth_module_1.SystemAuthModule,
            user_module_1.SystemUserModule,
            role_module_1.SystemRoleModule,
            permission_module_1.SystemPermissionModule,
            menu_module_1.SystemMenuModule,
            workbook_module_1.SystemWorkbookModule,
            routes_module_1.SystemRoutesModule,
        ],
        controllers: [],
        providers: [],
        exports: [
            typeorm_1.TypeOrmModule,
            auth_module_1.SystemAuthModule,
            user_module_1.SystemUserModule,
            role_module_1.SystemRoleModule,
            permission_module_1.SystemPermissionModule,
            menu_module_1.SystemMenuModule,
            workbook_module_1.SystemWorkbookModule,
            routes_module_1.SystemRoutesModule,
        ],
    })
], SystemModule);
//# sourceMappingURL=system.module.js.map