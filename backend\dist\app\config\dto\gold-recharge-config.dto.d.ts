export declare class CreateGoldRechargeConfigDto {
    tierName: string;
    goldAmount: number;
    price: number;
    activityBonusGold?: number;
    activityStartTime?: string;
    activityEndTime?: string;
    sortOrder?: number;
    status?: number;
}
export declare class UpdateGoldRechargeConfigDto {
    tierName?: string;
    goldAmount?: number;
    price?: number;
    activityBonusGold?: number;
    activityStartTime?: string;
    activityEndTime?: string;
    sortOrder?: number;
    status?: number;
}
export declare class GoldRechargeConfigQueryDto {
    status?: number;
}
export declare class EffectiveGoldRechargeConfigDto {
    id: number;
    tierName: string;
    goldAmount: number;
    price: number;
    effectiveGoldAmount: number;
    activityBonusGold: number;
    isActivityActive: boolean;
    activityStatusDescription: string;
    sortOrder: number;
}
