"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionListResponseDto = exports.TransactionItemDto = exports.UserAssetStatisticsResponseDto = exports.TransactionStatisticsResponseDto = exports.TransactionListQueryDto = exports.CreateTransactionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateTransactionDto {
    userId;
    amount;
    status;
    transactionType;
    orderId;
    description;
    remark;
    operatorId;
}
exports.CreateTransactionDto = CreateTransactionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTransactionDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易金额' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTransactionDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-收入，2-支出' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(2),
    __metadata("design:type", Number)
], CreateTransactionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易类型' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTransactionDto.prototype, "transactionType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '订单号' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTransactionDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '交易描述' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTransactionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '备注信息' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateTransactionDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '操作员ID' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateTransactionDto.prototype, "operatorId", void 0);
class TransactionListQueryDto {
    userId;
    status;
    transactionType;
    startDate;
    endDate;
    page;
    pageSize;
}
exports.TransactionListQueryDto = TransactionListQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], TransactionListQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '状态：1-收入，2-支出' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], TransactionListQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '交易类型' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : undefined),
    __metadata("design:type", Number)
], TransactionListQueryDto.prototype, "transactionType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '开始日期' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], TransactionListQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '结束日期' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], TransactionListQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '页码', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : 1),
    __metadata("design:type", Number)
], TransactionListQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: '每页数量', default: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_transformer_1.Transform)(({ value }) => value ? parseInt(value) : 20),
    __metadata("design:type", Number)
], TransactionListQueryDto.prototype, "pageSize", void 0);
class TransactionStatisticsResponseDto {
    totalIncome;
    totalExpense;
    balance;
    transactionCount;
}
exports.TransactionStatisticsResponseDto = TransactionStatisticsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总收入' }),
    __metadata("design:type", Number)
], TransactionStatisticsResponseDto.prototype, "totalIncome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总支出' }),
    __metadata("design:type", Number)
], TransactionStatisticsResponseDto.prototype, "totalExpense", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前余额' }),
    __metadata("design:type", Number)
], TransactionStatisticsResponseDto.prototype, "balance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易笔数' }),
    __metadata("design:type", Number)
], TransactionStatisticsResponseDto.prototype, "transactionCount", void 0);
class UserAssetStatisticsResponseDto {
    cash;
    gold;
    recharge;
}
exports.UserAssetStatisticsResponseDto = UserAssetStatisticsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '现金统计', type: TransactionStatisticsResponseDto }),
    __metadata("design:type", TransactionStatisticsResponseDto)
], UserAssetStatisticsResponseDto.prototype, "cash", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币统计', type: TransactionStatisticsResponseDto }),
    __metadata("design:type", TransactionStatisticsResponseDto)
], UserAssetStatisticsResponseDto.prototype, "gold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值统计', type: TransactionStatisticsResponseDto }),
    __metadata("design:type", TransactionStatisticsResponseDto)
], UserAssetStatisticsResponseDto.prototype, "recharge", void 0);
class TransactionItemDto {
    id;
    userId;
    uid;
    amount;
    balanceBefore;
    balanceAfter;
    status;
    transactionType;
    orderId;
    description;
    remark;
    operatorId;
    createTime;
    updateTime;
}
exports.TransactionItemDto = TransactionItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易ID' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户UID' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "uid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易金额' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易前余额' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "balanceBefore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易后余额' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "balanceAfter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-收入，2-支出' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易类型' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "transactionType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '订单号' }),
    __metadata("design:type", String)
], TransactionItemDto.prototype, "orderId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易描述' }),
    __metadata("design:type", String)
], TransactionItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息' }),
    __metadata("design:type", String)
], TransactionItemDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '操作员ID' }),
    __metadata("design:type", Number)
], TransactionItemDto.prototype, "operatorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], TransactionItemDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], TransactionItemDto.prototype, "updateTime", void 0);
class TransactionListResponseDto {
    list;
    total;
    page;
    pageSize;
}
exports.TransactionListResponseDto = TransactionListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '交易列表', type: [TransactionItemDto] }),
    __metadata("design:type", Array)
], TransactionListResponseDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数' }),
    __metadata("design:type", Number)
], TransactionListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码' }),
    __metadata("design:type", Number)
], TransactionListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量' }),
    __metadata("design:type", Number)
], TransactionListResponseDto.prototype, "pageSize", void 0);
//# sourceMappingURL=transaction.dto.js.map