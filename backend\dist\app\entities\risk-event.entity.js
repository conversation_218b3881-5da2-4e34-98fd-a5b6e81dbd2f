"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskEvent = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
let RiskEvent = class RiskEvent {
    id;
    userId;
    eventType;
    riskIncrement;
    details;
    status;
    operatorId;
    user;
    createdAt;
    updatedAt;
};
exports.RiskEvent = RiskEvent;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], RiskEvent.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], RiskEvent.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'event_type', length: 50 }),
    __metadata("design:type", String)
], RiskEvent.prototype, "eventType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_increment' }),
    __metadata("design:type", Number)
], RiskEvent.prototype, "riskIncrement", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], RiskEvent.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '事件处理状态：0-待处理，1-处理中，2-已关闭-安全，3-已关闭-已处置' }),
    __metadata("design:type", Number)
], RiskEvent.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operator_id', nullable: true }),
    __metadata("design:type", Number)
], RiskEvent.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], RiskEvent.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], RiskEvent.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], RiskEvent.prototype, "updatedAt", void 0);
exports.RiskEvent = RiskEvent = __decorate([
    (0, typeorm_1.Entity)('risk_events')
], RiskEvent);
//# sourceMappingURL=risk-event.entity.js.map