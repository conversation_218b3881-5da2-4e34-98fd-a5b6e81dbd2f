import { SystemUserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
export declare class SystemUserController {
    private readonly userService;
    constructor(userService: SystemUserService);
    create(createUserDto: CreateUserDto, req: any): Promise<{
        code: number;
        message: string;
        result: {
            user: import("../entities").SysUser;
            generatedPassword: string | undefined;
        };
    }>;
    findAll(queryUserDto: QueryUserDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").SysUser[];
            total: number;
            current: number;
            pageSize: number;
        };
    }>;
    getUserInfo(req: any): Promise<{
        code: number;
        message: string;
        result: {
            roles: string[];
            id: number;
            username: string;
            email: string;
            password: string;
            phoneNumber: string;
            avatar: string;
            description: string;
            status: number;
            lastLoginTime: Date;
            isSuperAdmin: boolean;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysUser;
    }>;
    update(id: string, updateUserDto: UpdateUserDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysUser;
    }>;
    remove(id: string, req: any): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto, req: any): Promise<{
        code: number;
        message: string;
        result: {
            newPassword: string;
        };
    }>;
    checkSuperAdmin(req: any): Promise<{
        code: number;
        message: string;
        result: {
            isSuperAdmin: boolean;
        };
    }>;
    setSuperAdmin(req: any): Promise<{
        code: number;
        message: string;
        result: null;
    } | {
        code: number;
        message: string;
        result: {
            existingSuperAdmin: string;
        };
    }>;
}
