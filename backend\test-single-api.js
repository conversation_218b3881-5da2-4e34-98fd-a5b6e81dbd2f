const http = require('http');

// 登录获取token
async function login() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      username: 'admin',
      password: 'admin123'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.code === 200) {
            resolve(response.result.token);
          } else {
            reject(new Error(`登录失败: ${response.message}`));
          }
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

// 测试余额充值限制API
async function testBalanceLimitsAPI(token) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/config/recharge/balance/limits',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    console.log('🔒 测试余额充值限制API:');
    console.log('请求URL:', `http://${options.hostname}:${options.port}${options.path}`);
    console.log('请求头:', options.headers);

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200) {
            console.log('✅ 余额充值限制API成功，返回数据:', response);
            resolve(response);
          } else {
            console.log('❌ 余额充值限制API错误:', response);
            console.log('错误状态码:', res.statusCode);
            console.log('错误详情:', JSON.stringify(response, null, 2));
            reject(new Error(`API错误: ${response.message}`));
          }
        } catch (error) {
          console.log('❌ 解析响应失败:', error.message);
          console.log('原始响应:', data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ 请求失败:', error.message);
      reject(error);
    });

    req.end();
  });
}

async function main() {
  try {
    console.log('🔐 正在登录获取token...');
    const token = await login();
    console.log('✅ 登录成功，获取到token:', token.substring(0, 50) + '...\n');

    await testBalanceLimitsAPI(token);

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

main();
