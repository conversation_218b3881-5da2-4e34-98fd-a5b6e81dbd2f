"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppAuthModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const app_auth_controller_1 = require("./app-auth.controller");
const app_auth_service_1 = require("./app-auth.service");
const entities_1 = require("../../app/entities");
let AppAuthModule = class AppAuthModule {
};
exports.AppAuthModule = AppAuthModule;
exports.AppAuthModule = AppAuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.AppUser,
                entities_1.MarketingChannel,
                entities_1.MarketingAd,
                entities_1.PromotionalPage,
                entities_1.DeviceLogRealtime,
                entities_1.DeviceLogHistory,
            ]),
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
        ],
        controllers: [app_auth_controller_1.AppAuthController],
        providers: [app_auth_service_1.AppAuthService],
        exports: [app_auth_service_1.AppAuthService],
    })
], AppAuthModule);
//# sourceMappingURL=app-auth.module.js.map