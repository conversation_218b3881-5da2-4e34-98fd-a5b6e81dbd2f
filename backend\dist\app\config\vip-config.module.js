"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const vip_config_service_1 = require("./vip-config.service");
const vip_config_controller_1 = require("./vip-config.controller");
const test_vip_config_controller_1 = require("./test-vip-config.controller");
const daily_reward_service_1 = require("./daily-reward.service");
const vip_config_entity_1 = require("./entities/vip-config.entity");
const app_user_entity_1 = require("../entities/app-user.entity");
const cash_transaction_entity_1 = require("../entities/cash-transaction.entity");
const gold_transaction_entity_1 = require("../entities/gold-transaction.entity");
let VipConfigModule = class VipConfigModule {
};
exports.VipConfigModule = VipConfigModule;
exports.VipConfigModule = VipConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                vip_config_entity_1.VipConfig,
                app_user_entity_1.AppUser,
                cash_transaction_entity_1.CashTransaction,
                gold_transaction_entity_1.GoldTransaction,
            ]),
        ],
        controllers: [vip_config_controller_1.VipConfigController, test_vip_config_controller_1.TestVipConfigController],
        providers: [vip_config_service_1.VipConfigService, daily_reward_service_1.DailyRewardService],
        exports: [vip_config_service_1.VipConfigService, daily_reward_service_1.DailyRewardService],
    })
], VipConfigModule);
//# sourceMappingURL=vip-config.module.js.map