var le=t=>{throw TypeError(t)};var J=(t,e,r)=>e.has(t)||le("Cannot "+r);var s=(t,e,r)=>(J(t,e,"read from private field"),r?r.call(t):e.get(t)),f=(t,e,r)=>e.has(t)?le("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),u=(t,e,r,i)=>(J(t,e,"write to private field"),i?i.call(t,r):e.set(t,r),r),l=(t,e,r)=>(J(t,e,"access private method"),r);import{r as F,S as Oe,ab as de,ac as O,Q as X,ad as q,ae as Ee,af as Y,ag as fe,ah as Ie,ai as Qe,aj as xe,ak as pe,V as ye,W as we}from"./index-CHjq8S-S.js";import{a as Q}from"./react-BUTTOX-3.js";import{s as Te,n as ge}from"./useMutation-BkoFKhTK.js";function Ge(){return F.get("roles/simple",{ignoreLoading:!0}).json()}function Je(t){return F.get("roles/list",{searchParams:t,ignoreLoading:!0}).json()}function Xe(t){return F.post("roles",{json:t,ignoreLoading:!0}).json()}function Ye(t){const{id:e,...r}=t;return F.patch(`roles/${e}`,{json:r,ignoreLoading:!0}).json()}function Ze(t){return F.delete(`roles/${t}`,{ignoreLoading:!0}).json()}function et(){return F.get("menus/list",{ignoreLoading:!0}).json()}function tt(t){return F.get(`roles/${t.id}`,{ignoreLoading:!1}).json()}var R,a,$,m,T,M,x,S,z,P,_,L,D,w,k,o,W,Z,ee,te,se,re,ie,ae,Ce,ve,Le=(ve=class extends Oe{constructor(e,r){super();f(this,o);f(this,R);f(this,a);f(this,$);f(this,m);f(this,T);f(this,M);f(this,x);f(this,S);f(this,z);f(this,P);f(this,_);f(this,L);f(this,D);f(this,w);f(this,k,new Set);this.options=r,u(this,R,e),u(this,S,null),u(this,x,de()),this.options.experimental_prefetchInRender||s(this,x).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(r)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(s(this,a).addObserver(this),be(s(this,a),this.options)?l(this,o,W).call(this):this.updateResult(),l(this,o,se).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return ne(s(this,a),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return ne(s(this,a),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,l(this,o,re).call(this),l(this,o,ie).call(this),s(this,a).removeObserver(this)}setOptions(e,r){const i=this.options,d=s(this,a);if(this.options=s(this,R).defaultQueryOptions(e),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof O(this.options.enabled,s(this,a))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");l(this,o,ae).call(this),s(this,a).setOptions(this.options),i._defaulted&&!X(this.options,i)&&s(this,R).getQueryCache().notify({type:"observerOptionsUpdated",query:s(this,a),observer:this});const c=this.hasListeners();c&&me(s(this,a),d,this.options,i)&&l(this,o,W).call(this),this.updateResult(r),c&&(s(this,a)!==d||O(this.options.enabled,s(this,a))!==O(i.enabled,s(this,a))||q(this.options.staleTime,s(this,a))!==q(i.staleTime,s(this,a)))&&l(this,o,Z).call(this);const n=l(this,o,ee).call(this);c&&(s(this,a)!==d||O(this.options.enabled,s(this,a))!==O(i.enabled,s(this,a))||n!==s(this,w))&&l(this,o,te).call(this,n)}getOptimisticResult(e){const r=s(this,R).getQueryCache().build(s(this,R),e),i=this.createResult(r,e);return Fe(this,i)&&(u(this,m,i),u(this,M,this.options),u(this,T,s(this,a).state)),i}getCurrentResult(){return s(this,m)}trackResult(e,r){const i={};return Object.keys(e).forEach(d=>{Object.defineProperty(i,d,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(d),r==null||r(d),e[d])})}),i}trackProp(e){s(this,k).add(e)}getCurrentQuery(){return s(this,a)}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){const r=s(this,R).defaultQueryOptions(e),i=s(this,R).getQueryCache().build(s(this,R),r);return i.fetch().then(()=>this.createResult(i,r))}fetch(e){return l(this,o,W).call(this,{...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),s(this,m)))}createResult(e,r){var ce;const i=s(this,a),d=this.options,c=s(this,m),n=s(this,T),E=s(this,M),y=e!==i?e.state:s(this,$),{state:I}=e;let h={...I},B=!1,g;if(r._optimisticResults){const b=this.hasListeners(),U=!b&&be(e,r),j=b&&me(e,i,r,d);(U||j)&&(h={...h,...xe(I.data,e.options)}),r._optimisticResults==="isRestoring"&&(h.fetchStatus="idle")}let{error:A,errorUpdatedAt:H,status:v}=h;if(r.select&&h.data!==void 0)if(c&&h.data===(n==null?void 0:n.data)&&r.select===s(this,z))g=s(this,P);else try{u(this,z,r.select),g=r.select(h.data),g=pe(c==null?void 0:c.data,g,r),u(this,P,g),u(this,S,null)}catch(b){u(this,S,b)}else g=h.data;if(r.placeholderData!==void 0&&g===void 0&&v==="pending"){let b;if(c!=null&&c.isPlaceholderData&&r.placeholderData===(E==null?void 0:E.placeholderData))b=c.data;else if(b=typeof r.placeholderData=="function"?r.placeholderData((ce=s(this,_))==null?void 0:ce.state.data,s(this,_)):r.placeholderData,r.select&&b!==void 0)try{b=r.select(b),u(this,S,null)}catch(U){u(this,S,U)}b!==void 0&&(v="success",g=pe(c==null?void 0:c.data,b,r),B=!0)}s(this,S)&&(A=s(this,S),g=s(this,P),H=Date.now(),v="error");const V=h.fetchStatus==="fetching",K=v==="pending",G=v==="error",he=K&&V,ue=g!==void 0,C={status:v,fetchStatus:h.fetchStatus,isPending:K,isSuccess:v==="success",isError:G,isInitialLoading:he,isLoading:he,data:g,dataUpdatedAt:h.dataUpdatedAt,error:A,errorUpdatedAt:H,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>y.dataUpdateCount||h.errorUpdateCount>y.errorUpdateCount,isFetching:V,isRefetching:V&&!K,isLoadingError:G&&!ue,isPaused:h.fetchStatus==="paused",isPlaceholderData:B,isRefetchError:G&&ue,isStale:oe(e,r),refetch:this.refetch,promise:s(this,x)};if(this.options.experimental_prefetchInRender){const b=N=>{C.status==="error"?N.reject(C.error):C.data!==void 0&&N.resolve(C.data)},U=()=>{const N=u(this,x,C.promise=de());b(N)},j=s(this,x);switch(j.status){case"pending":e.queryHash===i.queryHash&&b(j);break;case"fulfilled":(C.status==="error"||C.data!==j.value)&&U();break;case"rejected":(C.status!=="error"||C.error!==j.reason)&&U();break}}return C}updateResult(e){const r=s(this,m),i=this.createResult(s(this,a),this.options);if(u(this,T,s(this,a).state),u(this,M,this.options),s(this,T).data!==void 0&&u(this,_,s(this,a)),X(i,r))return;u(this,m,i);const d={},c=()=>{if(!r)return!0;const{notifyOnChangeProps:n}=this.options,E=typeof n=="function"?n():n;if(E==="all"||!E&&!s(this,k).size)return!0;const p=new Set(E??s(this,k));return this.options.throwOnError&&p.add("error"),Object.keys(s(this,m)).some(y=>{const I=y;return s(this,m)[I]!==r[I]&&p.has(I)})};(e==null?void 0:e.listeners)!==!1&&c()&&(d.listeners=!0),l(this,o,Ce).call(this,{...d,...e})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&l(this,o,se).call(this)}},R=new WeakMap,a=new WeakMap,$=new WeakMap,m=new WeakMap,T=new WeakMap,M=new WeakMap,x=new WeakMap,S=new WeakMap,z=new WeakMap,P=new WeakMap,_=new WeakMap,L=new WeakMap,D=new WeakMap,w=new WeakMap,k=new WeakMap,o=new WeakSet,W=function(e){l(this,o,ae).call(this);let r=s(this,a).fetch(this.options,e);return e!=null&&e.throwOnError||(r=r.catch(Ee)),r},Z=function(){l(this,o,re).call(this);const e=q(this.options.staleTime,s(this,a));if(Y||s(this,m).isStale||!fe(e))return;const i=Ie(s(this,m).dataUpdatedAt,e)+1;u(this,L,setTimeout(()=>{s(this,m).isStale||this.updateResult()},i))},ee=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(s(this,a)):this.options.refetchInterval)??!1},te=function(e){l(this,o,ie).call(this),u(this,w,e),!(Y||O(this.options.enabled,s(this,a))===!1||!fe(s(this,w))||s(this,w)===0)&&u(this,D,setInterval(()=>{(this.options.refetchIntervalInBackground||Qe.isFocused())&&l(this,o,W).call(this)},s(this,w)))},se=function(){l(this,o,Z).call(this),l(this,o,te).call(this,l(this,o,ee).call(this))},re=function(){s(this,L)&&(clearTimeout(s(this,L)),u(this,L,void 0))},ie=function(){s(this,D)&&(clearInterval(s(this,D)),u(this,D,void 0))},ae=function(){const e=s(this,R).getQueryCache().build(s(this,R),this.options);if(e===s(this,a))return;const r=s(this,a);u(this,a,e),u(this,$,e.state),this.hasListeners()&&(r==null||r.removeObserver(this),e.addObserver(this))},Ce=function(e){ye.batch(()=>{e.listeners&&this.listeners.forEach(r=>{r(s(this,m))}),s(this,R).getQueryCache().notify({query:s(this,a),type:"observerResultsUpdated"})})},ve);function De(t,e){return O(e.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&e.retryOnMount===!1)}function be(t,e){return De(t,e)||t.state.data!==void 0&&ne(t,e,e.refetchOnMount)}function ne(t,e,r){if(O(e.enabled,t)!==!1){const i=typeof r=="function"?r(t):r;return i==="always"||i!==!1&&oe(t,e)}return!1}function me(t,e,r,i){return(t!==e||O(i.enabled,t)===!1)&&(!r.suspense||t.state.status!=="error")&&oe(t,r)}function oe(t,e){return O(e.enabled,t)!==!1&&t.isStaleByTime(q(e.staleTime,t))}function Fe(t,e){return!X(t.getCurrentResult(),e)}var Se=Q.createContext(!1),Ue=()=>Q.useContext(Se);Se.Provider;function je(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var Me=Q.createContext(je()),Pe=()=>Q.useContext(Me),_e=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(e.isReset()||(t.retryOnMount=!1))},ke=t=>{Q.useEffect(()=>{t.clearReset()},[t])},Be=({result:t,errorResetBoundary:e,throwOnError:r,query:i,suspense:d})=>t.isError&&!e.isReset()&&!t.isFetching&&i&&(d&&t.data===void 0||Te(r,[t.error,i])),Ae=t=>{const e=t.staleTime;t.suspense&&(t.staleTime=typeof e=="function"?(...r)=>Math.max(e(...r),1e3):Math.max(e??1e3,1e3),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3)))},He=(t,e)=>t.isLoading&&t.isFetching&&!e,Ve=(t,e)=>(t==null?void 0:t.suspense)&&e.isPending,Re=(t,e,r)=>e.fetchOptimistic(t).catch(()=>{r.clearReset()});function We(t,e,r){var h,B,g,A,H;const i=we(),d=Ue(),c=Pe(),n=i.defaultQueryOptions(t);(B=(h=i.getDefaultOptions().queries)==null?void 0:h._experimental_beforeQuery)==null||B.call(h,n),n._optimisticResults=d?"isRestoring":"optimistic",Ae(n),_e(n,c),ke(c);const E=!i.getQueryCache().get(n.queryHash),[p]=Q.useState(()=>new e(i,n)),y=p.getOptimisticResult(n),I=!d&&t.subscribed!==!1;if(Q.useSyncExternalStore(Q.useCallback(v=>{const V=I?p.subscribe(ye.batchCalls(v)):ge;return p.updateResult(),V},[p,I]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),Q.useEffect(()=>{p.setOptions(n,{listeners:!1})},[n,p]),Ve(n,y))throw Re(n,p,c);if(Be({result:y,errorResetBoundary:c,throwOnError:n.throwOnError,query:i.getQueryCache().get(n.queryHash),suspense:n.suspense}))throw y.error;if((A=(g=i.getDefaultOptions().queries)==null?void 0:g._experimental_afterQuery)==null||A.call(g,n,y),n.experimental_prefetchInRender&&!Y&&He(y,d)){const v=E?Re(n,p,c):(H=i.getQueryCache().get(n.queryHash))==null?void 0:H.promise;v==null||v.catch(ge).finally(()=>{p.updateResult()})}return n.notifyOnChangeProps?y:p.trackResult(y)}function st(t,e){return We(t,Le)}export{Ye as a,tt as b,Je as c,Ze as d,et as e,Xe as f,Ge as g,st as u};
