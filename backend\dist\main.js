"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const helmet_1 = require("helmet");
const compression = require("compression");
const app_module_1 = require("./app.module");
const http_exception_filter_1 = require("./common/filters/http-exception.filter");
const transform_interceptor_1 = require("./common/interceptors/transform.interceptor");
const operators_1 = require("rxjs/operators");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.use((0, helmet_1.default)());
    app.use(compression());
    app.useGlobalInterceptors({
        intercept(context, next) {
            const request = context.switchToHttp().getRequest();
            if (request.url.includes('/channels')) {
                console.log('[GLOBAL_INTERCEPTOR] ===== 请求开始 =====');
                console.log('[GLOBAL_INTERCEPTOR] URL:', request.url);
                console.log('[GLOBAL_INTERCEPTOR] 方法:', request.method);
                console.log('[GLOBAL_INTERCEPTOR] 查询参数:', request.query);
                console.log('[GLOBAL_INTERCEPTOR] 请求体:', request.body);
                console.log('[GLOBAL_INTERCEPTOR] Headers:', {
                    'content-type': request.headers['content-type'],
                    'authorization': request.headers['authorization'] ? 'Bearer ***' : 'None'
                });
            }
            return next.handle().pipe((0, operators_1.tap)({
                next: (data) => {
                    if (request.url.includes('/channels')) {
                        console.log('[GLOBAL_INTERCEPTOR] 响应成功:', typeof data === 'object' ? JSON.stringify(data).substring(0, 200) + '...' : data);
                    }
                },
                error: (error) => {
                    if (request.url.includes('/channels')) {
                        console.log('[GLOBAL_INTERCEPTOR] 响应错误:', error.message);
                        console.log('[GLOBAL_INTERCEPTOR] 错误堆栈:', error.stack);
                    }
                }
            }));
        }
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        exceptionFactory: (errors) => {
            console.log('[VALIDATION_PIPE] ===== 验证失败 =====');
            console.log('[VALIDATION_PIPE] 错误详情:', JSON.stringify(errors, null, 2));
            const firstError = errors[0];
            const firstConstraint = Object.values(firstError.constraints || {})[0];
            console.log('[VALIDATION_PIPE] 第一个错误约束:', firstConstraint);
            console.log('[VALIDATION_PIPE] 错误字段:', firstError.property);
            console.log('[VALIDATION_PIPE] 错误值:', firstError.value);
            console.log('[VALIDATION_PIPE] 错误值类型:', typeof firstError.value);
            return new common_1.BadRequestException(firstConstraint);
        },
    }));
    app.use((req, res, next) => {
        if (req.url.includes('/channels')) {
            console.log('[REQUEST_MIDDLEWARE] ===== 渠道相关API请求 =====');
            console.log('[REQUEST_MIDDLEWARE] 方法:', req.method);
            console.log('[REQUEST_MIDDLEWARE] URL:', req.url);
            console.log('[REQUEST_MIDDLEWARE] 查询参数:', req.query);
            console.log('[REQUEST_MIDDLEWARE] 查询参数原型:', Object.getPrototypeOf(req.query));
            console.log('[REQUEST_MIDDLEWARE] 查询参数构造函数:', req.query.constructor);
            console.log('[REQUEST_MIDDLEWARE] 查询参数类型:', Object.entries(req.query).map(([key, value]) => ({
                key,
                value,
                type: typeof value
            })));
            console.log('[REQUEST_MIDDLEWARE] 查询参数是否为null原型:', Object.getPrototypeOf(req.query) === null);
        }
        next();
    });
    app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter());
    app.useGlobalInterceptors(new transform_interceptor_1.TransformInterceptor());
    app.enableCors({
        origin: configService.get('CORS_ORIGIN'),
        credentials: true,
    });
    app.setGlobalPrefix(configService.get('APP_PREFIX') || 'api');
    const config = new swagger_1.DocumentBuilder()
        .setTitle('InApp2 Backend API')
        .setDescription('InApp2 后台管理系统API文档 - 包含System管理中台和App业务端两大模块')
        .setVersion('1.0')
        .addBearerAuth()
        .addTag('系统认证', 'System模块 - 管理员认证相关接口')
        .addTag('系统用户管理', 'System模块 - 管理员用户管理')
        .addTag('系统角色管理', 'System模块 - 角色管理')
        .addTag('系统权限管理', 'System模块 - 权限管理')
        .addTag('系统菜单管理', 'System模块 - 菜单管理')
        .addTag('系统部门管理', 'System模块 - 部门管理')
        .addTag('系统动态路由', 'System模块 - 动态路由')
        .addTag('应用认证', 'App模块 - 用户认证相关接口')
        .addTag('应用用户管理', 'App模块 - 用户信息管理')
        .addTag('商品管理', 'App模块 - 商品相关接口')
        .addTag('订单管理', 'App模块 - 订单相关接口')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('docs', app, document);
    const port = configService.get('APP_PORT') || 3000;
    await app.listen(port);
    console.log(`Application is running on: http://localhost:${port}`);
    console.log(`Swagger docs: http://localhost:${port}/docs`);
}
bootstrap();
//# sourceMappingURL=main.js.map