export declare class AppUserDetailDto {
    id: number;
    uid: number;
    username: string;
    email: string;
    phone: string;
    googleId?: string;
    nickname: string;
    avatar: string;
    gender: number;
    birthday: Date;
    rechargeBalance: number;
    goldBalance: number;
    withdrawableBalance: number;
    vipLevel: number;
    vipExp: number;
    status: number;
    accountType: number;
    isVerified: number;
    riskScore: number;
    kycStatus: number;
    kycRejectReason?: string;
    inviterId?: number;
    invitationPath?: string;
    channelId?: number;
    adId?: number;
    adIdentifier?: string;
    promotionalPageId?: number;
    acquisitionTag?: string;
    tags?: any;
    lastLoginTime: Date;
    daysNotLoggedIn: number;
    lastLoginIp: string;
    registerIp: string;
    createTime: Date;
    updateTime: Date;
    inviter?: {
        id: number;
        username: string;
        nickname: string;
    };
    channel?: {
        id: number;
        name: string;
        identifier: string;
    };
    ad?: {
        id: number;
        name: string;
        identifier: string;
    };
    promotionalPage?: {
        id: number;
        name: string;
        identifier: string;
    };
}
