"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const application_service_1 = require("./application.service");
const create_application_dto_1 = require("./dto/create-application.dto");
const update_application_dto_1 = require("./dto/update-application.dto");
const query_application_dto_1 = require("./dto/query-application.dto");
let ApplicationController = class ApplicationController {
    applicationService;
    constructor(applicationService) {
        this.applicationService = applicationService;
    }
    async create(createApplicationDto) {
        const result = await this.applicationService.create(createApplicationDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(queryDto) {
        const result = await this.applicationService.findAll(queryDto);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.applicationService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findByUuid(uuid) {
        const result = await this.applicationService.findByUuid(uuid);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateApplicationDto) {
        const result = await this.applicationService.update(id, updateApplicationDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.applicationService.remove(id);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
    async toggleStatus(id, status) {
        const result = await this.applicationService.toggleStatus(id, status);
        return {
            code: 200,
            message: '状态切换成功',
            result,
        };
    }
};
exports.ApplicationController = ApplicationController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建应用' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '应用代码已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_application_dto_1.CreateApplicationDto]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取应用列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_application_dto_1.QueryApplicationDto]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取应用详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '应用ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '应用不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('uuid/:uuid'),
    (0, swagger_1.ApiOperation)({ summary: '根据UUID获取应用详情' }),
    (0, swagger_1.ApiParam)({ name: 'uuid', description: '应用UUID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '应用不存在' }),
    __param(0, (0, common_1.Param)('uuid')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "findByUuid", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新应用' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '应用ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '应用不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '应用代码已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_application_dto_1.UpdateApplicationDto]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除应用' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '应用ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '应用不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '切换应用状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '应用ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '应用不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", Promise)
], ApplicationController.prototype, "toggleStatus", null);
exports.ApplicationController = ApplicationController = __decorate([
    (0, swagger_1.ApiTags)('应用管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('applications'),
    __metadata("design:paramtypes", [application_service_1.ApplicationService])
], ApplicationController);
//# sourceMappingURL=application.controller.js.map