"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppAuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_auth_service_1 = require("./app-auth.service");
const app_login_dto_1 = require("./dto/app-login.dto");
let AppAuthController = class AppAuthController {
    appAuthService;
    constructor(appAuthService) {
        this.appAuthService = appAuthService;
    }
    async login(loginDto, ip, userAgent) {
        const result = await this.appAuthService.login(loginDto, ip, userAgent || '');
        return {
            code: 200,
            message: '登录成功',
            result,
        };
    }
    async register(registerDto, ip, userAgent) {
        const result = await this.appAuthService.register(registerDto, ip, userAgent || '');
        return {
            code: 201,
            message: '注册成功',
            result,
        };
    }
    async googleLogin(googleLoginDto, ip, userAgent) {
        return {
            code: 200,
            message: 'Google登录功能待实现',
            result: {},
        };
    }
    async refreshToken(refreshToken) {
        const result = await this.appAuthService.refreshToken(refreshToken);
        return {
            code: 200,
            message: '刷新成功',
            result,
        };
    }
    async logout() {
        return {
            code: 200,
            message: '登出成功',
            result: {},
        };
    }
};
exports.AppAuthController = AppAuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'APP用户登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功', type: app_login_dto_1.AppLoginResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '用户名或密码错误' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Ip)()),
    __param(2, (0, common_1.Headers)('user-agent')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [app_login_dto_1.AppLoginDto, String, String]),
    __metadata("design:returntype", Promise)
], AppAuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({ summary: 'APP用户注册' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '注册成功', type: app_login_dto_1.AppLoginResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '用户已存在' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Ip)()),
    __param(2, (0, common_1.Headers)('user-agent')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [app_login_dto_1.AppRegisterDto, String, String]),
    __metadata("design:returntype", Promise)
], AppAuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('google-login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Google登录' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登录成功', type: app_login_dto_1.AppLoginResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Google认证失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Ip)()),
    __param(2, (0, common_1.Headers)('user-agent')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [app_login_dto_1.AppGoogleLoginDto, String, String]),
    __metadata("design:returntype", Promise)
], AppAuthController.prototype, "googleLogin", null);
__decorate([
    (0, common_1.Post)('refresh-token'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '刷新访问令牌' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '刷新成功' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: '刷新令牌无效' }),
    __param(0, (0, common_1.Body)('refreshToken')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AppAuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'APP用户登出' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '登出成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppAuthController.prototype, "logout", null);
exports.AppAuthController = AppAuthController = __decorate([
    (0, swagger_1.ApiTags)('APP用户认证'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [app_auth_service_1.AppAuthService])
], AppAuthController);
//# sourceMappingURL=app-auth.controller.js.map