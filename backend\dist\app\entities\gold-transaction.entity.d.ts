import { AppUser } from './app-user.entity';
export declare enum GoldTransactionStatus {
    INCOME = 1,
    EXPENSE = 2
}
export declare enum GoldTransactionType {
    BET = 1,
    WIN = 2,
    VIP_CARD_CLAIM = 3,
    GIFT = 4,
    TRADE_ACQUIRE = 5,
    ACTIVITY_CLAIM = 6
}
export declare class GoldTransaction {
    id: number;
    userId: number;
    transactionId: string;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    status: GoldTransactionStatus;
    transactionType: GoldTransactionType;
    description: string;
    createdAt: Date;
    user: AppUser;
}
export declare const GoldTransactionTypeLabels: {
    1: string;
    2: string;
    3: string;
    4: string;
    5: string;
    6: string;
};
export declare const GoldTransactionStatusLabels: {
    1: string;
    2: string;
};
