import{a as r}from"./react-BUTTOX-3.js";import{f as E}from"./index-DosLe4-o.js";const g={APP_STATUS:"app_status",GAME_STATUS:"game_status",GAME_CATEGORY:"game_category",GAME_TYPE:"game_type",APP_TAG:"app_tag",APP_PLATFORM:"app_platform",APP_ORIENTATION:"app_orientation",VIRTUAL_CURRENCY:"virtual_currency",APP_VOLATILITY:"app_volatility",GAME_FEATURE:"game_feature",DEVICE_SUPPORT:"device_support",LAUNCH_MODE:"launch_mode",GAME_QUALITY:"game_quality"};function d(t){const[e,a]=r.useState({}),[s,l]=r.useState(!1),[p,u]=r.useState(null);return r.useEffect(()=>{if(!t||t.length===0)return;(async()=>{l(!0),u(null);try{const i=t.map(o=>E(o)),A=await Promise.all(i),n={};A.forEach((o,f)=>{const _=t[f];o.code===200?n[_]=o.result.map(c=>({label:c.text,value:c.value,disabled:c.status===0})):n[_]=[]}),a(n)}catch(i){u("获取字典数据失败"),console.error("Multiple dictionaries fetch error:",i)}finally{l(!1)}})()},[t.join(",")]),{dictionaries:e,loading:s,error:p}}function D(t,e){const a=t.find(s=>s.value===e);return a?a.label:e}export{g as D,D as g,d as u};
