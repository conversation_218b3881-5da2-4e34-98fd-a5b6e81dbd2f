import{j as e,O as d,a as m,P as p}from"./index-CHjq8S-S.js";import"./react-BUTTOX-3.js";import{B as f}from"./index-DDI4OfxQ.js";import{X as x,ad as h,d as j,aQ as w,av as g,I as F}from"./antd-CXPM1OiB.js";import{u as v}from"./useMutation-BkoFKhTK.js";import{P as N}from"./index-CZVy594C.js";import{W as u}from"./index-CreErp7h.js";import{F as U}from"./index-BvyzE_eE.js";import{P as $}from"./index-C2mpj1sP.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";function I({value:a,onChange:t}){return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"flex items-center gap-5",children:[e.jsx(x,{size:100,src:a}),e.jsx(d,{rotationSlider:!0,aspectSlider:!0,showReset:!0,showGrid:!0,cropShape:"rect",children:e.jsx(h,{accept:"image/*",showUploadList:!1,name:"file",action:"/api/upload",headers:{authorization:"authorization-text"},onChange:s=>{var i,l,r;s.file.status==="done"?((i=window.$message)==null||i.success(`${s.file.name} file uploaded successfully`),t==null||t((l=s.file.response)==null?void 0:l.result)):s.file.status==="error"&&((r=window.$message)==null||r.error(`${s.file.name} file upload failed.`))},children:e.jsx(j,{icon:e.jsx(w,{}),children:"更换头像"})})})]})})}function z(){const a=m(),{getUserInfo:t}=m(),s=()=>a?a.avatar?a.avatar:"https://avatar.vercel.sh/blur.svg?text=2":"",i=v({mutationFn:p,onSuccess:()=>{var r;(r=window.$message)==null||r.success("更新基本信息成功"),t()},onError:r=>{var o;(o=window.$message)==null||o.error((r==null?void 0:r.message)||"更新失败")}}),l=async r=>{const o=Date.now();console.log("[FRONTEND] 开始更新用户信息",r);try{await i.mutateAsync(r);const n=Date.now();console.log(`[FRONTEND] 更新用户信息成功 - 总耗时: ${n-o}ms`)}catch(n){const c=Date.now();console.error(`[FRONTEND] 更新用户信息失败 - 总耗时: ${c-o}ms`,n)}};return e.jsxs(f,{className:"max-w-md ml-10",children:[e.jsx("h3",{children:"我的资料"}),e.jsxs(N,{layout:"vertical",onFinish:l,initialValues:{...a,avatar:s()},requiredMark:!0,children:[e.jsx(g.Item,{name:"avatar",label:"头像",rules:[{required:!0,message:"请输入您的昵称!"}],children:e.jsx(I,{})}),e.jsx(u,{name:"username",label:"用户名",rules:[{required:!0,message:"请输入您的用户名!"}]}),e.jsx(u,{name:"email",label:"邮箱",rules:[{required:!0,message:"请输入您的邮箱!"}]}),e.jsx(U,{name:"phoneNumber",label:"联系电话",rules:[{required:!0,message:"请输入您的联系电话!"}],children:e.jsx(F,{type:"tel",allowClear:!0})}),e.jsx($,{allowClear:!0,name:"description",label:"个人简介",placeholder:"个人简介"})]})]})}export{z as default};
