import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>inCol<PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AppUser } from './app-user.entity';

// 现金交易状态枚举
export enum CashTransactionStatus {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
}

// 可提现余额交易类型枚举
export enum CashTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  DEPOSIT = 3, // 充值
  WITHDRAW = 4, // 提取
  GOLD_EXCHANGE = 5, // 金币兑换
  RECHARGE_EXCHANGE = 6, // 充值余额兑换
}

@Entity('cash_transactions')
export class CashTransaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'transaction_id' })
  transactionId: string;

  @Column({ type: 'decimal', precision: 18, scale: 4 })
  amount: number;

  @Column({ name: 'balance_before', type: 'decimal', precision: 18, scale: 4 })
  balanceBefore: number;

  @Column({ name: 'balance_after', type: 'decimal', precision: 18, scale: 4 })
  balanceAfter: number;

  @Column({ type: 'int', comment: '状态：1-收入，2-支出' })
  status: CashTransactionStatus;

  @Column({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-充值，4-提取，5-金币兑换，6-充值余额兑换' })
  transactionType: CashTransactionType;

  @Column({ nullable: true, length: 255 })
  description: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => AppUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: AppUser;
}

// 交易类型标签映射
export const CashTransactionTypeLabels = {
  [CashTransactionType.BET]: '下注',
  [CashTransactionType.WIN]: '赢金',
  [CashTransactionType.DEPOSIT]: '充值',
  [CashTransactionType.WITHDRAW]: '提取',
  [CashTransactionType.GOLD_EXCHANGE]: '金币兑换',
  [CashTransactionType.RECHARGE_EXCHANGE]: '充值余额兑换',
};

// 交易状态标签映射
export const CashTransactionStatusLabels = {
  [CashTransactionStatus.INCOME]: '收入',
  [CashTransactionStatus.EXPENSE]: '支出',
};
