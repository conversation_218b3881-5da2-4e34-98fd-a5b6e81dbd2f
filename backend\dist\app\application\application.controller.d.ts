import { ApplicationService } from './application.service';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { QueryApplicationDto } from './dto/query-application.dto';
export declare class ApplicationController {
    private readonly applicationService;
    constructor(applicationService: ApplicationService);
    create(createApplicationDto: CreateApplicationDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").Application;
    }>;
    findAll(queryDto: QueryApplicationDto): Promise<{
        code: number;
        message: string;
        result: import("./dto/query-application.dto").ApplicationListResponse;
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").Application;
    }>;
    findByUuid(uuid: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").Application;
    }>;
    update(id: number, updateApplicationDto: UpdateApplicationDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").Application;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
    toggleStatus(id: number, status: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").Application;
    }>;
}
