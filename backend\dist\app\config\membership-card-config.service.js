"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipCardConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const membership_card_config_entity_1 = require("./entities/membership-card-config.entity");
let MembershipCardConfigService = class MembershipCardConfigService {
    membershipCardConfigRepository;
    constructor(membershipCardConfigRepository) {
        this.membershipCardConfigRepository = membershipCardConfigRepository;
    }
    async create(createDto, userId) {
        if (createDto.activityStartTime && createDto.activityEndTime) {
            const startTime = new Date(createDto.activityStartTime);
            const endTime = new Date(createDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        const config = this.membershipCardConfigRepository.create();
        Object.assign(config, createDto, {
            activityStartTime: createDto.activityStartTime ? new Date(createDto.activityStartTime) : null,
            activityEndTime: createDto.activityEndTime ? new Date(createDto.activityEndTime) : null,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.membershipCardConfigRepository.save(config);
    }
    async findAll() {
        const queryBuilder = this.membershipCardConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater');
        queryBuilder.orderBy(`
      CASE config.cardType 
        WHEN 'silver' THEN 1 
        WHEN 'gold' THEN 2 
        WHEN 'diamond' THEN 3 
        WHEN 'king' THEN 4 
        ELSE 5 
      END
    `);
        const configs = await queryBuilder.getMany();
        return configs.map(config => ({
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        }));
    }
    async findOne(id) {
        const config = await this.membershipCardConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!config) {
            throw new common_1.NotFoundException('会员卡配置不存在');
        }
        return {
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        };
    }
    async update(id, updateDto, userId) {
        const config = await this.membershipCardConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('会员卡配置不存在');
        }
        if (updateDto.activityStartTime && updateDto.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            const endTime = new Date(updateDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityStartTime && !updateDto.activityEndTime && config.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            if (startTime >= config.activityEndTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityEndTime && !updateDto.activityStartTime && config.activityStartTime) {
            const endTime = new Date(updateDto.activityEndTime);
            if (config.activityStartTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        Object.assign(config, updateDto, {
            updatedBy: userId,
            activityStartTime: updateDto.activityStartTime ? new Date(updateDto.activityStartTime) : config.activityStartTime,
            activityEndTime: updateDto.activityEndTime ? new Date(updateDto.activityEndTime) : config.activityEndTime,
        });
        const savedConfig = await this.membershipCardConfigRepository.save(config);
        return savedConfig;
    }
    async remove(id) {
        const config = await this.membershipCardConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('会员卡配置不存在');
        }
        await this.membershipCardConfigRepository.remove(config);
        return { message: '删除成功' };
    }
    async findAllEffective() {
        const configs = await this.membershipCardConfigRepository.find({
            where: { status: 1 },
            order: {
                cardType: 'ASC'
            },
        });
        return configs.map(config => {
            const activityStatus = config.getActivityStatus();
            return {
                id: config.id,
                cardType: config.cardType,
                cardName: config.cardName,
                price: config.price,
                description: config.description,
                dailyGoldBase: config.dailyGoldBase,
                effectiveDailyGold: config.getEffectiveDailyGold(),
                cashDiscountBase: config.cashDiscountBase,
                effectiveCashDiscount: config.getEffectiveCashDiscount(),
                isActivityActive: config.isActivityActive(),
                activityStatusDescription: activityStatus.description,
            };
        });
    }
    async getActiveActivityConfigs() {
        const allConfigs = await this.membershipCardConfigRepository.find({
            where: { status: 1 },
        });
        return allConfigs.filter(config => config.isActivityActive());
    }
    async updateActivityTime(startTime, endTime, userId) {
        if (startTime >= endTime) {
            throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
        }
        await this.membershipCardConfigRepository.update({}, {
            activityStartTime: startTime,
            activityEndTime: endTime,
            updatedBy: userId,
        });
        return { message: '批量更新活动时间成功' };
    }
};
exports.MembershipCardConfigService = MembershipCardConfigService;
exports.MembershipCardConfigService = MembershipCardConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(membership_card_config_entity_1.MembershipCardConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], MembershipCardConfigService);
//# sourceMappingURL=membership-card-config.service.js.map