import { Repository } from 'typeorm';
import { SysRole } from '../entities/sys-role.entity';
import { SysPermission } from '../entities/sys-permission.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { QueryRoleDto } from './dto/query-role.dto';
export declare class SystemRoleService {
    private roleRepository;
    private permissionRepository;
    constructor(roleRepository: Repository<SysRole>, permissionRepository: Repository<SysPermission>);
    create(createRoleDto: CreateRoleDto): Promise<SysRole>;
    findSimpleRoles(): Promise<SysRole[]>;
    findAll(queryRoleDto: QueryRoleDto): Promise<{
        list: SysRole[];
        total: number;
        current: number;
        pageSize: number;
    }>;
    findOne(id: number): Promise<SysRole>;
    update(id: number, updateRoleDto: UpdateRoleDto): Promise<SysRole>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAllSimple(): Promise<SysRole[]>;
}
