"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipCardConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const membership_card_config_service_1 = require("./membership-card-config.service");
const membership_card_config_controller_1 = require("./membership-card-config.controller");
const membership_card_config_entity_1 = require("./entities/membership-card-config.entity");
let MembershipCardConfigModule = class MembershipCardConfigModule {
};
exports.MembershipCardConfigModule = MembershipCardConfigModule;
exports.MembershipCardConfigModule = MembershipCardConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                membership_card_config_entity_1.MembershipCardConfig,
            ]),
        ],
        controllers: [membership_card_config_controller_1.MembershipCardConfigController],
        providers: [membership_card_config_service_1.MembershipCardConfigService],
        exports: [membership_card_config_service_1.MembershipCardConfigService],
    })
], MembershipCardConfigModule);
//# sourceMappingURL=membership-card-config.module.js.map