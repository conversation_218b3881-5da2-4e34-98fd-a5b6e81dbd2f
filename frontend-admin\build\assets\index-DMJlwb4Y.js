import{r as s}from"./index-CHjq8S-S.js";function i(e){return s.get("permissions/list",{searchParams:e,ignoreLoading:!0}).json()}function r(){return s.get("permissions/simple",{ignoreLoading:!0}).json()}function o(e){return s.get(`permissions/type/${e}`,{ignoreLoading:!0}).json()}function t(e){return s.post("permissions",{json:e,ignoreLoading:!0}).json()}function m(e){return s.patch(`permissions/${e.id}`,{json:e,ignoreLoading:!0}).json()}function u(e){return s.delete(`permissions/${e}`,{ignoreLoading:!0}).json()}export{m as a,t as b,i as c,u as d,r as e,o as f};
