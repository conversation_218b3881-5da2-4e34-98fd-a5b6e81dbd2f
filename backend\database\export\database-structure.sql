-- InApp2 Database Structure
-- Generated on: 2025-06-26T11:13:31.052Z

-- This file contains the complete database structure for the InApp2 project
-- It includes all tables, relationships, and constraints

SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- System Tables
-- These tables manage the backend administration system

-- Users table for backend administrators
CREATE TABLE IF NOT EXISTS sys_users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL,
  password VARCHAR(255) NOT NULL,
  phone_number VARCHAR(20),
  avatar VARCHAR(500),
  description VARCHAR(200),
  status INTEGER DEFAULT 1,
  last_login_time TIMESTAMPTZ,
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW(),
  is_super_admin BOOLEAN DEFAULT false
);

-- App Users table for end users
CREATE TABLE IF NOT EXISTS app_users (
  id SERIAL PRIMARY KEY,
  uid BIGINT UNIQUE,
  username VA<PERSON>HA<PERSON>(50) UNIQUE,
  email VARCHAR(100),
  phone VARCHAR(20) UNIQUE,
  password VARCHAR(255) NOT NULL,
  nickname VARCHAR(50),
  avatar VARCHAR(500),
  balance DECIMAL(15,4) DEFAULT 0.0000,
  diamond_balance DECIMAL(15,4) DEFAULT 0.0000,
  gold_balance BIGINT DEFAULT 0,
  vip_level INTEGER DEFAULT 0,
  vip_exp BIGINT DEFAULT 0,
  status INTEGER DEFAULT 0,
  create_time TIMESTAMPTZ DEFAULT NOW(),
  update_time TIMESTAMPTZ DEFAULT NOW()
);

