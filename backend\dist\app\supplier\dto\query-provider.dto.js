"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryProviderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class QueryProviderDto {
    page = 1;
    pageSize = 10;
    name;
    providerCode;
    status;
    integrationType;
    commercialModelType;
    sortBy = 'createdAt';
    sortOrder = 'DESC';
}
exports.QueryProviderDto = QueryProviderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '页码',
        example: 1,
        default: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], QueryProviderDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每页数量',
        example: 10,
        default: 10,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], QueryProviderDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '供应商名称（模糊搜索）',
        example: 'PG',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '供应商代码（模糊搜索）',
        example: 'PG_SOFT',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "providerCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '合作状态筛选',
        example: 'active',
        enum: ['active', 'inactive', 'testing', 'suspended'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['active', 'inactive', 'testing', 'suspended']),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '技术集成方案筛选',
        example: 'api_integration',
        enum: ['api_integration', 'iframe_integration', 'redirect_integration'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['api_integration', 'iframe_integration', 'redirect_integration']),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "integrationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '合作模式筛选',
        example: 'ggr_share',
        enum: ['ggr_share', 'revenue_share', 'fixed_fee', 'cpa'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['ggr_share', 'revenue_share', 'fixed_fee', 'cpa']),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "commercialModelType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序字段',
        example: 'createdAt',
        enum: ['createdAt', 'updatedAt', 'name', 'providerCode'],
        default: 'createdAt',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['createdAt', 'updatedAt', 'name', 'providerCode']),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序方向',
        example: 'DESC',
        enum: ['ASC', 'DESC'],
        default: 'DESC',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['ASC', 'DESC']),
    __metadata("design:type", String)
], QueryProviderDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=query-provider.dto.js.map