export declare class VipConfigQueryDto {
    page?: number;
    pageSize?: number;
    vipLevel?: number;
    status?: number;
    levelName?: string;
}
export declare class VipConfigListDto {
    id: number;
    vipLevel: number;
    levelName: string;
    requiredPoints: number;
    balanceRatio: number;
    cashRatio: number;
    goldRatio: number;
    dailyGoldReward: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator?: {
        id: number;
        username: string;
    };
    updater?: {
        id: number;
        username: string;
    };
}
