const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库连接配置
const pool = new Pool({
  host: '**************',
  port: 5435,
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
  database: 'inapp2',
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 开始执行广告配置数据库迁移...');
    
    // 读取迁移脚本
    const migrationPath = path.join(__dirname, '../database/migrations/012-update-ad-config-image-jump-structure.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // 执行迁移
    await client.query(migrationSQL);
    
    console.log('✅ 数据库迁移执行成功！');
    
    // 验证迁移结果
    const result = await client.query(`
      SELECT 
        COUNT(*) as total_count,
        COUNT(CASE WHEN image_items IS NOT NULL AND jsonb_array_length(image_items) > 0 THEN 1 END) as migrated_count
      FROM ad_configs
    `);
    
    const { total_count, migrated_count } = result.rows[0];
    console.log(`📊 迁移统计: 总记录数 ${total_count}, 已迁移记录数 ${migrated_count}`);
    
    // 显示示例数据
    const sampleResult = await client.query(`
      SELECT ad_identifier, title, image_items 
      FROM ad_configs 
      WHERE ad_identifier IN ('home_carousel_multi', 'home_grid_4')
      ORDER BY ad_identifier
    `);
    
    console.log('\n📋 示例数据:');
    sampleResult.rows.forEach(row => {
      console.log(`- ${row.ad_identifier}: ${row.title}`);
      console.log(`  图片数量: ${row.image_items.length}`);
      row.image_items.forEach((item, index) => {
        console.log(`    ${index + 1}. ${item.title} -> ${item.jumpTarget} (类型: ${item.jumpType})`);
      });
    });
    
  } catch (error) {
    console.error('❌ 迁移执行失败:', error.message);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行迁移
runMigration()
  .then(() => {
    console.log('\n🎉 广告配置数据库迁移完成！');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 迁移失败:', error);
    process.exit(1);
  });
