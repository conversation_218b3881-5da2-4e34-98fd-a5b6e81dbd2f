import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { AppUser, MarketingChannel, MarketingAd, PromotionalPage, RiskEvent } from '../entities';
import {
  QueryAppUserDto,
  UpdateUserStatusDto,
  UpdateUserTagsDto,
  AppUserDetailDto,
  AppUserListResponseDto,
  AppUserListItemDto,
  InvitationRelationshipDto,
  InvitationUserDto,
  UserStatus,
} from './dto';

@Injectable()
export class UserManagementService {
  constructor(
    @InjectRepository(AppUser)
    private readonly userRepository: Repository<AppUser>,
    @InjectRepository(MarketingChannel)
    private readonly channelRepository: Repository<MarketingChannel>,
    @InjectRepository(MarketingAd)
    private readonly adRepository: Repository<MarketingAd>,
    @InjectRepository(PromotionalPage)
    private readonly pageRepository: Repository<PromotionalPage>,
    @InjectRepository(RiskEvent)
    private readonly riskEventRepository: Repository<RiskEvent>,
  ) {}

  /**
   * 查询用户列表
   */
  async findUsers(queryDto: QueryAppUserDto): Promise<AppUserListResponseDto> {
    const {
      page = 1,
      pageSize = 20,
      id,
      username,
      email,
      phone,
      nickname,
      status,
      accountType,
      kycStatus,
      channelId,
      adId,
      inviterId,
      minRiskScore,
      maxRiskScore,
      startDate,
      endDate,
      sortBy = 'createTime',
      sortOrder = 'DESC',
    } = queryDto;

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.channel', 'channel')
      .leftJoinAndSelect('user.ad', 'ad')
      .leftJoinAndSelect('user.inviter', 'inviter')
      .select([
        'user.id',
        'user.uid',
        'user.username',
        'user.email',
        'user.phone',
        'user.nickname',
        'user.avatar',
        'user.gender',
        'user.rechargeBalance',
        'user.goldBalance',
        'user.withdrawableBalance',
        'user.vipLevel',
        'user.status',
        'user.accountType',
        'user.isVerified',
        'user.riskScore',
        'user.kycStatus',
        'user.inviterId',
        'user.acquisitionTag',
        'user.tags',
        'user.lastLoginTime',
        'user.registerIp',
        'user.createTime',
        'channel.id',
        'channel.name',
        'ad.id',
        'ad.name',
        'ad.identifier',
        'inviter.id',
        'inviter.username',
        'inviter.nickname',
      ]);

    // 添加搜索条件
    this.addSearchConditions(queryBuilder, queryDto);

    // 添加排序
    queryBuilder.orderBy(`user.${sortBy}`, sortOrder);

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    const [users, total] = await queryBuilder.getManyAndCount();

    // 转换为DTO
    const list: AppUserListItemDto[] = users.map((user) => {
      // 计算未登录天数
      let daysNotLoggedIn = 0;
      if (user.lastLoginTime) {
        const lastLogin = new Date(user.lastLoginTime);
        const now = new Date();
        const diffTime = now.getTime() - lastLogin.getTime();
        daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      } else {
        // 如果从未登录，计算从注册到现在的天数
        const createTime = new Date(user.createTime);
        const now = new Date();
        const diffTime = now.getTime() - createTime.getTime();
        daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      }

      return {
        id: user.id,
        uid: user.uid,
        username: user.username,
        email: user.email,
        phone: user.phone,
        nickname: user.nickname,
        avatar: user.avatar,
        gender: user.gender,
        rechargeBalance: user.rechargeBalance,
        goldBalance: user.goldBalance,
        withdrawableBalance: user.withdrawableBalance,
        vipLevel: user.vipLevel,
        status: user.status,
        accountType: user.accountType,
        isVerified: user.isVerified,
        riskScore: user.riskScore,
        kycStatus: user.kycStatus,
        inviterId: user.inviterId,
        channelName: user.channel?.name,
        adName: user.ad?.name,
        adIdentifier: user.ad?.identifier,
        acquisitionTag: user.acquisitionTag,
        tags: user.tags ? (Array.isArray(user.tags) ? user.tags : [user.tags]) : [],
        lastLoginTime: user.lastLoginTime,
        daysNotLoggedIn,
        registerIp: user.registerIp,
        createTime: user.createTime,
      };
    });

    return {
      list,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  /**
   * 添加搜索条件
   */
  private addSearchConditions(
    queryBuilder: SelectQueryBuilder<AppUser>,
    queryDto: QueryAppUserDto,
  ): void {
    const {
      id,
      username,
      email,
      phone,
      nickname,
      status,
      accountType,
      kycStatus,
      channelId,
      adId,
      inviterId,
      minRiskScore,
      maxRiskScore,
      startDate,
      endDate,
    } = queryDto;

    if (id) {
      queryBuilder.andWhere('user.id = :id', { id });
    }

    if (username) {
      queryBuilder.andWhere('user.username ILIKE :username', {
        username: `%${username}%`,
      });
    }

    if (email) {
      queryBuilder.andWhere('user.email ILIKE :email', {
        email: `%${email}%`,
      });
    }

    if (phone) {
      queryBuilder.andWhere('user.phone ILIKE :phone', {
        phone: `%${phone}%`,
      });
    }

    if (nickname) {
      queryBuilder.andWhere('user.nickname ILIKE :nickname', {
        nickname: `%${nickname}%`,
      });
    }

    if (status !== undefined && status !== null) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (accountType !== undefined) {
      queryBuilder.andWhere('user.accountType = :accountType', { accountType });
    }

    if (kycStatus !== undefined) {
      queryBuilder.andWhere('user.kycStatus = :kycStatus', { kycStatus });
    }

    if (channelId) {
      queryBuilder.andWhere('user.channelId = :channelId', { channelId });
    }

    if (adId) {
      queryBuilder.andWhere('user.adId = :adId', { adId });
    }

    if (inviterId) {
      queryBuilder.andWhere('user.inviterId = :inviterId', { inviterId });
    }

    if (minRiskScore !== undefined) {
      queryBuilder.andWhere('user.riskScore >= :minRiskScore', { minRiskScore });
    }

    if (maxRiskScore !== undefined) {
      queryBuilder.andWhere('user.riskScore <= :maxRiskScore', { maxRiskScore });
    }

    if (startDate) {
      queryBuilder.andWhere('user.createTime >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('user.createTime <= :endDate', { endDate });
    }
  }

  /**
   * 获取用户详情
   */
  async findUserById(id: number): Promise<AppUserDetailDto> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['inviter', 'channel', 'ad', 'promotionalPage'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 计算未登录天数
    let daysNotLoggedIn = 0;
    if (user.lastLoginTime) {
      const lastLogin = new Date(user.lastLoginTime);
      const now = new Date();
      const diffTime = now.getTime() - lastLogin.getTime();
      daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    } else {
      // 如果从未登录，计算从注册到现在的天数
      const createTime = new Date(user.createTime);
      const now = new Date();
      const diffTime = now.getTime() - createTime.getTime();
      daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    }

    return {
      id: user.id,
      uid: user.uid,
      username: user.username,
      email: user.email,
      phone: user.phone,
      googleId: user.googleId,
      nickname: user.nickname,
      avatar: user.avatar,
      gender: user.gender,
      birthday: user.birthday,
      rechargeBalance: user.rechargeBalance,
      goldBalance: user.goldBalance,
      withdrawableBalance: user.withdrawableBalance,
      vipLevel: user.vipLevel,
      vipExp: user.vipExp,
      status: user.status,
      accountType: user.accountType,
      isVerified: user.isVerified,
      riskScore: user.riskScore,
      kycStatus: user.kycStatus,
      kycRejectReason: user.kycRejectReason,
      inviterId: user.inviterId,
      invitationPath: user.invitationPath,
      channelId: user.channelId,
      adId: user.adId,
      promotionalPageId: user.promotionalPageId,
      acquisitionTag: user.acquisitionTag,
      tags: user.tags,
      lastLoginTime: user.lastLoginTime,
      daysNotLoggedIn,
      lastLoginIp: user.lastLoginIp,
      registerIp: user.registerIp,
      createTime: user.createTime,
      updateTime: user.updateTime,
      inviter: user.inviter
        ? {
            id: user.inviter.id,
            username: user.inviter.username,
            nickname: user.inviter.nickname,
          }
        : undefined,
      channel: user.channel
        ? {
            id: user.channel.id,
            name: user.channel.name,
            identifier: user.channel.identifier,
          }
        : undefined,
      ad: user.ad
        ? {
            id: user.ad.id,
            name: user.ad.name,
            identifier: user.ad.identifier,
          }
        : undefined,
      adIdentifier: user.ad?.identifier,
      promotionalPage: user.promotionalPage
        ? {
            id: user.promotionalPage.id,
            name: user.promotionalPage.name,
            identifier: user.promotionalPage.identifier,
          }
        : undefined,
    };
  }
}
