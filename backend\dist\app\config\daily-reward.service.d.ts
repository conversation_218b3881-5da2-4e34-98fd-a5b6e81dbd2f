import { Repository } from 'typeorm';
import { VipConfig } from './entities/vip-config.entity';
import { AppUser } from '../entities/app-user.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';
export declare class DailyRewardService {
    private vipConfigRepository;
    private appUserRepository;
    private goldTransactionRepository;
    constructor(vipConfigRepository: Repository<VipConfig>, appUserRepository: Repository<AppUser>, goldTransactionRepository: Repository<GoldTransaction>);
    checkTodayRewardClaimed(userId: number): Promise<boolean>;
    claimDailyGoldReward(userId: number): Promise<{
        success: boolean;
        goldAmount: number;
        message: string;
    }>;
    getDailyRewardStatus(userId: number): Promise<{
        canClaim: boolean;
        goldAmount: number;
        vipLevel: number;
        levelName: string;
        alreadyClaimed: boolean;
    }>;
    getDailyRewardHistory(userId: number, page?: number, pageSize?: number): Promise<{
        list: {
            id: number;
            amount: number;
            vipLevel: string;
            claimTime: Date;
            remark: string;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
}
