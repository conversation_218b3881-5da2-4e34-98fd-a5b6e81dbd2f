import {
  __assign,
  __awaiter,
  __generator,
  __read,
  __rest,
  __spreadArray,
  __values
} from "./chunk-KHMXZX7K.js";
import {
  require_isPlainObject
} from "./chunk-4E7WY5EB.js";
import {
  require_debounce,
  require_throttle
} from "./chunk-XINKDDUY.js";
import {
  require_dayjs_min
} from "./chunk-S3T4N3OW.js";
import {
  ResizeObserver_es_default
} from "./chunk-MRWV3FAC.js";
import {
  require_react
} from "./chunk-THYVJR3I.js";
import {
  __commonJS,
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/.pnpm/react-fast-compare@3.2.2/node_modules/react-fast-compare/index.js
var require_react_fast_compare = __commonJS({
  "node_modules/.pnpm/react-fast-compare@3.2.2/node_modules/react-fast-compare/index.js"(exports, module) {
    var hasElementType = typeof Element !== "undefined";
    var hasMap = typeof Map === "function";
    var hasSet = typeof Set === "function";
    var hasArrayBuffer = typeof ArrayBuffer === "function" && !!ArrayBuffer.isView;
    function equal(a, b) {
      if (a === b) return true;
      if (a && b && typeof a == "object" && typeof b == "object") {
        if (a.constructor !== b.constructor) return false;
        var length, i, keys;
        if (Array.isArray(a)) {
          length = a.length;
          if (length != b.length) return false;
          for (i = length; i-- !== 0; )
            if (!equal(a[i], b[i])) return false;
          return true;
        }
        var it;
        if (hasMap && a instanceof Map && b instanceof Map) {
          if (a.size !== b.size) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0])) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!equal(i.value[1], b.get(i.value[0]))) return false;
          return true;
        }
        if (hasSet && a instanceof Set && b instanceof Set) {
          if (a.size !== b.size) return false;
          it = a.entries();
          while (!(i = it.next()).done)
            if (!b.has(i.value[0])) return false;
          return true;
        }
        if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
          length = a.length;
          if (length != b.length) return false;
          for (i = length; i-- !== 0; )
            if (a[i] !== b[i]) return false;
          return true;
        }
        if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
        if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === "function" && typeof b.valueOf === "function") return a.valueOf() === b.valueOf();
        if (a.toString !== Object.prototype.toString && typeof a.toString === "function" && typeof b.toString === "function") return a.toString() === b.toString();
        keys = Object.keys(a);
        length = keys.length;
        if (length !== Object.keys(b).length) return false;
        for (i = length; i-- !== 0; )
          if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;
        if (hasElementType && a instanceof Element) return false;
        for (i = length; i-- !== 0; ) {
          if ((keys[i] === "_owner" || keys[i] === "__v" || keys[i] === "__o") && a.$$typeof) {
            continue;
          }
          if (!equal(a[keys[i]], b[keys[i]])) return false;
        }
        return true;
      }
      return a !== a && b !== b;
    }
    module.exports = function isEqual2(a, b) {
      try {
        return equal(a, b);
      } catch (error) {
        if ((error.message || "").match(/stack|recursion/i)) {
          console.warn("react-fast-compare cannot handle circular refs");
          return false;
        }
        throw error;
      }
    };
  }
});

// node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js
var require_screenfull = __commonJS({
  "node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js"(exports, module) {
    (function() {
      "use strict";
      var document2 = typeof window !== "undefined" && typeof window.document !== "undefined" ? window.document : {};
      var isCommonjs = typeof module !== "undefined" && module.exports;
      var fn = function() {
        var val;
        var fnMap = [
          [
            "requestFullscreen",
            "exitFullscreen",
            "fullscreenElement",
            "fullscreenEnabled",
            "fullscreenchange",
            "fullscreenerror"
          ],
          // New WebKit
          [
            "webkitRequestFullscreen",
            "webkitExitFullscreen",
            "webkitFullscreenElement",
            "webkitFullscreenEnabled",
            "webkitfullscreenchange",
            "webkitfullscreenerror"
          ],
          // Old WebKit
          [
            "webkitRequestFullScreen",
            "webkitCancelFullScreen",
            "webkitCurrentFullScreenElement",
            "webkitCancelFullScreen",
            "webkitfullscreenchange",
            "webkitfullscreenerror"
          ],
          [
            "mozRequestFullScreen",
            "mozCancelFullScreen",
            "mozFullScreenElement",
            "mozFullScreenEnabled",
            "mozfullscreenchange",
            "mozfullscreenerror"
          ],
          [
            "msRequestFullscreen",
            "msExitFullscreen",
            "msFullscreenElement",
            "msFullscreenEnabled",
            "MSFullscreenChange",
            "MSFullscreenError"
          ]
        ];
        var i = 0;
        var l = fnMap.length;
        var ret = {};
        for (; i < l; i++) {
          val = fnMap[i];
          if (val && val[1] in document2) {
            for (i = 0; i < val.length; i++) {
              ret[fnMap[0][i]] = val[i];
            }
            return ret;
          }
        }
        return false;
      }();
      var eventNameMap = {
        change: fn.fullscreenchange,
        error: fn.fullscreenerror
      };
      var screenfull2 = {
        request: function(element, options) {
          return new Promise((function(resolve, reject) {
            var onFullScreenEntered = (function() {
              this.off("change", onFullScreenEntered);
              resolve();
            }).bind(this);
            this.on("change", onFullScreenEntered);
            element = element || document2.documentElement;
            var returnPromise = element[fn.requestFullscreen](options);
            if (returnPromise instanceof Promise) {
              returnPromise.then(onFullScreenEntered).catch(reject);
            }
          }).bind(this));
        },
        exit: function() {
          return new Promise((function(resolve, reject) {
            if (!this.isFullscreen) {
              resolve();
              return;
            }
            var onFullScreenExit = (function() {
              this.off("change", onFullScreenExit);
              resolve();
            }).bind(this);
            this.on("change", onFullScreenExit);
            var returnPromise = document2[fn.exitFullscreen]();
            if (returnPromise instanceof Promise) {
              returnPromise.then(onFullScreenExit).catch(reject);
            }
          }).bind(this));
        },
        toggle: function(element, options) {
          return this.isFullscreen ? this.exit() : this.request(element, options);
        },
        onchange: function(callback) {
          this.on("change", callback);
        },
        onerror: function(callback) {
          this.on("error", callback);
        },
        on: function(event, callback) {
          var eventName = eventNameMap[event];
          if (eventName) {
            document2.addEventListener(eventName, callback, false);
          }
        },
        off: function(event, callback) {
          var eventName = eventNameMap[event];
          if (eventName) {
            document2.removeEventListener(eventName, callback, false);
          }
        },
        raw: fn
      };
      if (!fn) {
        if (isCommonjs) {
          module.exports = { isEnabled: false };
        } else {
          window.screenfull = { isEnabled: false };
        }
        return;
      }
      Object.defineProperties(screenfull2, {
        isFullscreen: {
          get: function() {
            return Boolean(document2[fn.fullscreenElement]);
          }
        },
        element: {
          enumerable: true,
          get: function() {
            return document2[fn.fullscreenElement];
          }
        },
        isEnabled: {
          enumerable: true,
          get: function() {
            return Boolean(document2[fn.fullscreenEnabled]);
          }
        }
      });
      if (isCommonjs) {
        module.exports = screenfull2;
      } else {
        window.screenfull = screenfull2;
      }
    })();
  }
});

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/createUpdateEffect/index.js
var import_react = __toESM(require_react());
var createUpdateEffect = function(hook) {
  return function(effect, deps) {
    var isMounted = (0, import_react.useRef)(false);
    hook(function() {
      return function() {
        isMounted.current = false;
      };
    }, []);
    hook(function() {
      if (!isMounted.current) {
        isMounted.current = true;
      } else {
        return effect();
      }
    }, deps);
  };
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useAntdTable/index.js
var import_react18 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMemoizedFn/index.js
var import_react2 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/index.js
var isObject = function(value) {
  return value !== null && typeof value === "object";
};
var isFunction = function(value) {
  return typeof value === "function";
};
var isString = function(value) {
  return typeof value === "string";
};
var isBoolean = function(value) {
  return typeof value === "boolean";
};
var isNumber = function(value) {
  return typeof value === "number";
};
var isUndef = function(value) {
  return typeof value === "undefined";
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/isDev.js
var isDev = true;
var isDev_default = isDev;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMemoizedFn/index.js
function useMemoizedFn(fn) {
  if (isDev_default) {
    if (!isFunction(fn)) {
      console.error("useMemoizedFn expected parameter is a function, got ".concat(typeof fn));
    }
  }
  var fnRef = (0, import_react2.useRef)(fn);
  fnRef.current = (0, import_react2.useMemo)(function() {
    return fn;
  }, [fn]);
  var memoizedFn = (0, import_react2.useRef)();
  if (!memoizedFn.current) {
    memoizedFn.current = function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return fnRef.current.apply(this, args);
    };
  }
  return memoizedFn.current;
}
var useMemoizedFn_default = useMemoizedFn;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/usePagination/index.js
var import_react17 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js
var import_react4 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUpdateEffect/index.js
var import_react3 = __toESM(require_react());
var useUpdateEffect_default = createUpdateEffect(import_react3.useEffect);

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useAutoRunPlugin.js
var useAutoRunPlugin = function(fetchInstance, _a) {
  var manual = _a.manual, _b = _a.ready, ready = _b === void 0 ? true : _b, _c = _a.defaultParams, defaultParams = _c === void 0 ? [] : _c, _d = _a.refreshDeps, refreshDeps = _d === void 0 ? [] : _d, refreshDepsAction = _a.refreshDepsAction;
  var hasAutoRun = (0, import_react4.useRef)(false);
  hasAutoRun.current = false;
  useUpdateEffect_default(function() {
    if (!manual && ready) {
      hasAutoRun.current = true;
      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(defaultParams), false));
    }
  }, [ready]);
  useUpdateEffect_default(function() {
    if (hasAutoRun.current) {
      return;
    }
    if (!manual) {
      hasAutoRun.current = true;
      if (refreshDepsAction) {
        refreshDepsAction();
      } else {
        fetchInstance.refresh();
      }
    }
  }, __spreadArray([], __read(refreshDeps), false));
  return {
    onBefore: function() {
      if (!ready) {
        return {
          stopNow: true
        };
      }
    }
  };
};
useAutoRunPlugin.onInit = function(_a) {
  var _b = _a.ready, ready = _b === void 0 ? true : _b, manual = _a.manual;
  return {
    loading: !manual && ready
  };
};
var useAutoRunPlugin_default = useAutoRunPlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js
var import_react8 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useCreation/index.js
var import_react5 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/depsAreSame.js
function depsAreSame(oldDeps, deps) {
  if (oldDeps === deps) return true;
  for (var i = 0; i < oldDeps.length; i++) {
    if (!Object.is(oldDeps[i], deps[i])) return false;
  }
  return true;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useCreation/index.js
function useCreation(factory, deps) {
  var current = (0, import_react5.useRef)({
    deps,
    obj: void 0,
    initialized: false
  }).current;
  if (current.initialized === false || !depsAreSame(current.deps, deps)) {
    current.deps = deps;
    current.obj = factory();
    current.initialized = true;
  }
  return current.obj;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUnmount/index.js
var import_react7 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useLatest/index.js
var import_react6 = __toESM(require_react());
function useLatest(value) {
  var ref = (0, import_react6.useRef)(value);
  ref.current = value;
  return ref;
}
var useLatest_default = useLatest;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUnmount/index.js
var useUnmount = function(fn) {
  if (isDev_default) {
    if (!isFunction(fn)) {
      console.error("useUnmount expected parameter is a function, got ".concat(typeof fn));
    }
  }
  var fnRef = useLatest_default(fn);
  (0, import_react7.useEffect)(function() {
    return function() {
      fnRef.current();
    };
  }, []);
};
var useUnmount_default = useUnmount;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cache.js
var cache = /* @__PURE__ */ new Map();
var setCache = function(key, cacheTime, cachedData) {
  var currentCache = cache.get(key);
  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {
    clearTimeout(currentCache.timer);
  }
  var timer = void 0;
  if (cacheTime > -1) {
    timer = setTimeout(function() {
      cache.delete(key);
    }, cacheTime);
  }
  cache.set(key, __assign(__assign({}, cachedData), {
    timer
  }));
};
var getCache = function(key) {
  return cache.get(key);
};
var clearCache = function(key) {
  if (key) {
    var cacheKeys = Array.isArray(key) ? key : [key];
    cacheKeys.forEach(function(cacheKey) {
      return cache.delete(cacheKey);
    });
  } else {
    cache.clear();
  }
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cachePromise.js
var cachePromise = /* @__PURE__ */ new Map();
var getCachePromise = function(cacheKey) {
  return cachePromise.get(cacheKey);
};
var setCachePromise = function(cacheKey, promise) {
  cachePromise.set(cacheKey, promise);
  promise.then(function(res) {
    cachePromise.delete(cacheKey);
    return res;
  }).catch(function() {
    cachePromise.delete(cacheKey);
  });
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/cacheSubscribe.js
var listeners = {};
var trigger = function(key, data) {
  if (listeners[key]) {
    listeners[key].forEach(function(item) {
      return item(data);
    });
  }
};
var subscribe = function(key, listener) {
  if (!listeners[key]) {
    listeners[key] = [];
  }
  listeners[key].push(listener);
  return function unsubscribe() {
    var index = listeners[key].indexOf(listener);
    listeners[key].splice(index, 1);
  };
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useCachePlugin.js
var useCachePlugin = function(fetchInstance, _a) {
  var cacheKey = _a.cacheKey, _b = _a.cacheTime, cacheTime = _b === void 0 ? 5 * 60 * 1e3 : _b, _c = _a.staleTime, staleTime = _c === void 0 ? 0 : _c, customSetCache = _a.setCache, customGetCache = _a.getCache;
  var unSubscribeRef = (0, import_react8.useRef)();
  var currentPromiseRef = (0, import_react8.useRef)();
  var _setCache = function(key, cachedData) {
    if (customSetCache) {
      customSetCache(cachedData);
    } else {
      setCache(key, cacheTime, cachedData);
    }
    trigger(key, cachedData.data);
  };
  var _getCache = function(key, params) {
    if (params === void 0) {
      params = [];
    }
    if (customGetCache) {
      return customGetCache(params);
    }
    return getCache(key);
  };
  useCreation(function() {
    if (!cacheKey) {
      return;
    }
    var cacheData = _getCache(cacheKey);
    if (cacheData && Object.hasOwnProperty.call(cacheData, "data")) {
      fetchInstance.state.data = cacheData.data;
      fetchInstance.state.params = cacheData.params;
      if (staleTime === -1 || (/* @__PURE__ */ new Date()).getTime() - cacheData.time <= staleTime) {
        fetchInstance.state.loading = false;
      }
    }
    unSubscribeRef.current = subscribe(cacheKey, function(data) {
      fetchInstance.setState({
        data
      });
    });
  }, []);
  useUnmount_default(function() {
    var _a2;
    (_a2 = unSubscribeRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(unSubscribeRef);
  });
  if (!cacheKey) {
    return {};
  }
  return {
    onBefore: function(params) {
      var cacheData = _getCache(cacheKey, params);
      if (!cacheData || !Object.hasOwnProperty.call(cacheData, "data")) {
        return {};
      }
      if (staleTime === -1 || (/* @__PURE__ */ new Date()).getTime() - cacheData.time <= staleTime) {
        return {
          loading: false,
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,
          error: void 0,
          returnNow: true
        };
      } else {
        return {
          data: cacheData === null || cacheData === void 0 ? void 0 : cacheData.data,
          error: void 0
        };
      }
    },
    onRequest: function(service, args) {
      var servicePromise = getCachePromise(cacheKey);
      if (servicePromise && servicePromise !== currentPromiseRef.current) {
        return {
          servicePromise
        };
      }
      servicePromise = service.apply(void 0, __spreadArray([], __read(args), false));
      currentPromiseRef.current = servicePromise;
      setCachePromise(cacheKey, servicePromise);
      return {
        servicePromise
      };
    },
    onSuccess: function(data, params) {
      var _a2;
      if (cacheKey) {
        (_a2 = unSubscribeRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(unSubscribeRef);
        _setCache(cacheKey, {
          data,
          params,
          time: (/* @__PURE__ */ new Date()).getTime()
        });
        unSubscribeRef.current = subscribe(cacheKey, function(d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    },
    onMutate: function(data) {
      var _a2;
      if (cacheKey) {
        (_a2 = unSubscribeRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(unSubscribeRef);
        _setCache(cacheKey, {
          data,
          params: fetchInstance.state.params,
          time: (/* @__PURE__ */ new Date()).getTime()
        });
        unSubscribeRef.current = subscribe(cacheKey, function(d) {
          fetchInstance.setState({
            data: d
          });
        });
      }
    }
  };
};
var useCachePlugin_default = useCachePlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useDebouncePlugin.js
var import_debounce = __toESM(require_debounce());
var import_react9 = __toESM(require_react());
var useDebouncePlugin = function(fetchInstance, _a) {
  var debounceWait = _a.debounceWait, debounceLeading = _a.debounceLeading, debounceTrailing = _a.debounceTrailing, debounceMaxWait = _a.debounceMaxWait;
  var debouncedRef = (0, import_react9.useRef)();
  var options = (0, import_react9.useMemo)(function() {
    var ret = {};
    if (debounceLeading !== void 0) {
      ret.leading = debounceLeading;
    }
    if (debounceTrailing !== void 0) {
      ret.trailing = debounceTrailing;
    }
    if (debounceMaxWait !== void 0) {
      ret.maxWait = debounceMaxWait;
    }
    return ret;
  }, [debounceLeading, debounceTrailing, debounceMaxWait]);
  (0, import_react9.useEffect)(function() {
    if (debounceWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);
      debouncedRef.current = (0, import_debounce.default)(function(callback) {
        callback();
      }, debounceWait, options);
      fetchInstance.runAsync = function() {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }
        return new Promise(function(resolve, reject) {
          var _a2;
          (_a2 = debouncedRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(debouncedRef, function() {
            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);
          });
        });
      };
      return function() {
        var _a2;
        (_a2 = debouncedRef.current) === null || _a2 === void 0 ? void 0 : _a2.cancel();
        fetchInstance.runAsync = _originRunAsync_1;
      };
    }
  }, [debounceWait, options]);
  if (!debounceWait) {
    return {};
  }
  return {
    onCancel: function() {
      var _a2;
      (_a2 = debouncedRef.current) === null || _a2 === void 0 ? void 0 : _a2.cancel();
    }
  };
};
var useDebouncePlugin_default = useDebouncePlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useLoadingDelayPlugin.js
var import_react10 = __toESM(require_react());
var useLoadingDelayPlugin = function(fetchInstance, _a) {
  var loadingDelay = _a.loadingDelay, ready = _a.ready;
  var timerRef = (0, import_react10.useRef)();
  if (!loadingDelay) {
    return {};
  }
  var cancelTimeout = function() {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  };
  return {
    onBefore: function() {
      cancelTimeout();
      if (ready !== false) {
        timerRef.current = setTimeout(function() {
          fetchInstance.setState({
            loading: true
          });
        }, loadingDelay);
      }
      return {
        loading: false
      };
    },
    onFinally: function() {
      cancelTimeout();
    },
    onCancel: function() {
      cancelTimeout();
    }
  };
};
var useLoadingDelayPlugin_default = useLoadingDelayPlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js
var import_react11 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/isBrowser.js
var isBrowser = !!(typeof window !== "undefined" && window.document && window.document.createElement);
var isBrowser_default = isBrowser;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/isDocumentVisible.js
function isDocumentVisible() {
  if (isBrowser_default) {
    return document.visibilityState !== "hidden";
  }
  return true;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/subscribeReVisible.js
var listeners2 = [];
function subscribe2(listener) {
  listeners2.push(listener);
  return function unsubscribe() {
    var index = listeners2.indexOf(listener);
    listeners2.splice(index, 1);
  };
}
if (isBrowser_default) {
  revalidate = function() {
    if (!isDocumentVisible()) return;
    for (var i = 0; i < listeners2.length; i++) {
      var listener = listeners2[i];
      listener();
    }
  };
  window.addEventListener("visibilitychange", revalidate, false);
}
var revalidate;
var subscribeReVisible_default = subscribe2;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js
var usePollingPlugin = function(fetchInstance, _a) {
  var pollingInterval = _a.pollingInterval, _b = _a.pollingWhenHidden, pollingWhenHidden = _b === void 0 ? true : _b, _c = _a.pollingErrorRetryCount, pollingErrorRetryCount = _c === void 0 ? -1 : _c;
  var timerRef = (0, import_react11.useRef)();
  var unsubscribeRef = (0, import_react11.useRef)();
  var countRef = (0, import_react11.useRef)(0);
  var stopPolling = function() {
    var _a2;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    (_a2 = unsubscribeRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(unsubscribeRef);
  };
  useUpdateEffect_default(function() {
    if (!pollingInterval) {
      stopPolling();
    }
  }, [pollingInterval]);
  if (!pollingInterval) {
    return {};
  }
  return {
    onBefore: function() {
      stopPolling();
    },
    onError: function() {
      countRef.current += 1;
    },
    onSuccess: function() {
      countRef.current = 0;
    },
    onFinally: function() {
      if (pollingErrorRetryCount === -1 || // When an error occurs, the request is not repeated after pollingErrorRetryCount retries
      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {
        timerRef.current = setTimeout(function() {
          if (!pollingWhenHidden && !isDocumentVisible()) {
            unsubscribeRef.current = subscribeReVisible_default(function() {
              fetchInstance.refresh();
            });
          } else {
            fetchInstance.refresh();
          }
        }, pollingInterval);
      } else {
        countRef.current = 0;
      }
    },
    onCancel: function() {
      stopPolling();
    }
  };
};
var usePollingPlugin_default = usePollingPlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js
var import_react12 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/limit.js
function limit(fn, timespan) {
  var pending = false;
  return function() {
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    if (pending) return;
    pending = true;
    fn.apply(void 0, __spreadArray([], __read(args), false));
    setTimeout(function() {
      pending = false;
    }, timespan);
  };
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/isOnline.js
function isOnline() {
  if (isBrowser_default && typeof navigator.onLine !== "undefined") {
    return navigator.onLine;
  }
  return true;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/utils/subscribeFocus.js
var listeners3 = [];
function subscribe3(listener) {
  listeners3.push(listener);
  return function unsubscribe() {
    var index = listeners3.indexOf(listener);
    if (index > -1) {
      listeners3.splice(index, 1);
    }
  };
}
if (isBrowser_default) {
  revalidate = function() {
    if (!isDocumentVisible() || !isOnline()) return;
    for (var i = 0; i < listeners3.length; i++) {
      var listener = listeners3[i];
      listener();
    }
  };
  window.addEventListener("visibilitychange", revalidate, false);
  window.addEventListener("focus", revalidate, false);
}
var revalidate;
var subscribeFocus_default = subscribe3;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useRefreshOnWindowFocusPlugin.js
var useRefreshOnWindowFocusPlugin = function(fetchInstance, _a) {
  var refreshOnWindowFocus = _a.refreshOnWindowFocus, _b = _a.focusTimespan, focusTimespan = _b === void 0 ? 5e3 : _b;
  var unsubscribeRef = (0, import_react12.useRef)();
  var stopSubscribe = function() {
    var _a2;
    (_a2 = unsubscribeRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(unsubscribeRef);
  };
  (0, import_react12.useEffect)(function() {
    if (refreshOnWindowFocus) {
      var limitRefresh_1 = limit(fetchInstance.refresh.bind(fetchInstance), focusTimespan);
      unsubscribeRef.current = subscribeFocus_default(function() {
        limitRefresh_1();
      });
    }
    return function() {
      stopSubscribe();
    };
  }, [refreshOnWindowFocus, focusTimespan]);
  useUnmount_default(function() {
    stopSubscribe();
  });
  return {};
};
var useRefreshOnWindowFocusPlugin_default = useRefreshOnWindowFocusPlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useRetryPlugin.js
var import_react13 = __toESM(require_react());
var useRetryPlugin = function(fetchInstance, _a) {
  var retryInterval = _a.retryInterval, retryCount = _a.retryCount;
  var timerRef = (0, import_react13.useRef)();
  var countRef = (0, import_react13.useRef)(0);
  var triggerByRetry = (0, import_react13.useRef)(false);
  if (!retryCount) {
    return {};
  }
  return {
    onBefore: function() {
      if (!triggerByRetry.current) {
        countRef.current = 0;
      }
      triggerByRetry.current = false;
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    },
    onSuccess: function() {
      countRef.current = 0;
    },
    onError: function() {
      countRef.current += 1;
      if (retryCount === -1 || countRef.current <= retryCount) {
        var timeout = retryInterval !== null && retryInterval !== void 0 ? retryInterval : Math.min(1e3 * Math.pow(2, countRef.current), 3e4);
        timerRef.current = setTimeout(function() {
          triggerByRetry.current = true;
          fetchInstance.refresh();
        }, timeout);
      } else {
        countRef.current = 0;
      }
    },
    onCancel: function() {
      countRef.current = 0;
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    }
  };
};
var useRetryPlugin_default = useRetryPlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/plugins/useThrottlePlugin.js
var import_throttle = __toESM(require_throttle());
var import_react14 = __toESM(require_react());
var useThrottlePlugin = function(fetchInstance, _a) {
  var throttleWait = _a.throttleWait, throttleLeading = _a.throttleLeading, throttleTrailing = _a.throttleTrailing;
  var throttledRef = (0, import_react14.useRef)();
  var options = {};
  if (throttleLeading !== void 0) {
    options.leading = throttleLeading;
  }
  if (throttleTrailing !== void 0) {
    options.trailing = throttleTrailing;
  }
  (0, import_react14.useEffect)(function() {
    if (throttleWait) {
      var _originRunAsync_1 = fetchInstance.runAsync.bind(fetchInstance);
      throttledRef.current = (0, import_throttle.default)(function(callback) {
        callback();
      }, throttleWait, options);
      fetchInstance.runAsync = function() {
        var args = [];
        for (var _i = 0; _i < arguments.length; _i++) {
          args[_i] = arguments[_i];
        }
        return new Promise(function(resolve, reject) {
          var _a2;
          (_a2 = throttledRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(throttledRef, function() {
            _originRunAsync_1.apply(void 0, __spreadArray([], __read(args), false)).then(resolve).catch(reject);
          });
        });
      };
      return function() {
        var _a2;
        fetchInstance.runAsync = _originRunAsync_1;
        (_a2 = throttledRef.current) === null || _a2 === void 0 ? void 0 : _a2.cancel();
      };
    }
  }, [throttleWait, throttleLeading, throttleTrailing]);
  if (!throttleWait) {
    return {};
  }
  return {
    onCancel: function() {
      var _a2;
      (_a2 = throttledRef.current) === null || _a2 === void 0 ? void 0 : _a2.cancel();
    }
  };
};
var useThrottlePlugin_default = useThrottlePlugin;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMount/index.js
var import_react15 = __toESM(require_react());
var useMount = function(fn) {
  if (isDev_default) {
    if (!isFunction(fn)) {
      console.error('useMount: parameter `fn` expected to be a function, but got "'.concat(typeof fn, '".'));
    }
  }
  (0, import_react15.useEffect)(function() {
    fn === null || fn === void 0 ? void 0 : fn();
  }, []);
};
var useMount_default = useMount;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUpdate/index.js
var import_react16 = __toESM(require_react());
var useUpdate = function() {
  var _a = __read((0, import_react16.useState)({}), 2), setState = _a[1];
  return (0, import_react16.useCallback)(function() {
    return setState({});
  }, []);
};
var useUpdate_default = useUpdate;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/Fetch.js
var Fetch = (
  /** @class */
  function() {
    function Fetch2(serviceRef, options, subscribe4, initState3) {
      if (initState3 === void 0) {
        initState3 = {};
      }
      this.serviceRef = serviceRef;
      this.options = options;
      this.subscribe = subscribe4;
      this.initState = initState3;
      this.count = 0;
      this.state = {
        loading: false,
        params: void 0,
        data: void 0,
        error: void 0
      };
      this.state = __assign(__assign(__assign({}, this.state), {
        loading: !options.manual
      }), initState3);
    }
    Fetch2.prototype.setState = function(s) {
      if (s === void 0) {
        s = {};
      }
      this.state = __assign(__assign({}, this.state), s);
      this.subscribe();
    };
    Fetch2.prototype.runPluginHandler = function(event) {
      var rest = [];
      for (var _i = 1; _i < arguments.length; _i++) {
        rest[_i - 1] = arguments[_i];
      }
      var r = this.pluginImpls.map(function(i) {
        var _a;
        return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));
      }).filter(Boolean);
      return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));
    };
    Fetch2.prototype.runAsync = function() {
      var params = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        params[_i] = arguments[_i];
      }
      return __awaiter(this, void 0, void 0, function() {
        var currentCount, _a, _b, stopNow, _c, returnNow, state, servicePromise, res, error_1;
        var _d;
        var _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;
        return __generator(this, function(_q) {
          switch (_q.label) {
            case 0:
              this.count += 1;
              currentCount = this.count;
              _a = this.runPluginHandler("onBefore", params), _b = _a.stopNow, stopNow = _b === void 0 ? false : _b, _c = _a.returnNow, returnNow = _c === void 0 ? false : _c, state = __rest(_a, ["stopNow", "returnNow"]);
              if (stopNow) {
                return [2, new Promise(function() {
                })];
              }
              this.setState(__assign({
                loading: true,
                params
              }, state));
              if (returnNow) {
                return [2, Promise.resolve(state.data)];
              }
              (_f = (_e = this.options).onBefore) === null || _f === void 0 ? void 0 : _f.call(_e, params);
              _q.label = 1;
            case 1:
              _q.trys.push([1, 3, , 4]);
              servicePromise = this.runPluginHandler("onRequest", this.serviceRef.current, params).servicePromise;
              if (!servicePromise) {
                servicePromise = (_d = this.serviceRef).current.apply(_d, __spreadArray([], __read(params), false));
              }
              return [4, servicePromise];
            case 2:
              res = _q.sent();
              if (currentCount !== this.count) {
                return [2, new Promise(function() {
                })];
              }
              this.setState({
                data: res,
                error: void 0,
                loading: false
              });
              (_h = (_g = this.options).onSuccess) === null || _h === void 0 ? void 0 : _h.call(_g, res, params);
              this.runPluginHandler("onSuccess", res, params);
              (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, res, void 0);
              if (currentCount === this.count) {
                this.runPluginHandler("onFinally", params, res, void 0);
              }
              return [2, res];
            case 3:
              error_1 = _q.sent();
              if (currentCount !== this.count) {
                return [2, new Promise(function() {
                })];
              }
              this.setState({
                error: error_1,
                loading: false
              });
              (_m = (_l = this.options).onError) === null || _m === void 0 ? void 0 : _m.call(_l, error_1, params);
              this.runPluginHandler("onError", error_1, params);
              (_p = (_o = this.options).onFinally) === null || _p === void 0 ? void 0 : _p.call(_o, params, void 0, error_1);
              if (currentCount === this.count) {
                this.runPluginHandler("onFinally", params, void 0, error_1);
              }
              throw error_1;
            case 4:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    };
    Fetch2.prototype.run = function() {
      var _this = this;
      var params = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        params[_i] = arguments[_i];
      }
      this.runAsync.apply(this, __spreadArray([], __read(params), false)).catch(function(error) {
        if (!_this.options.onError) {
          console.error(error);
        }
      });
    };
    Fetch2.prototype.cancel = function() {
      this.count += 1;
      this.setState({
        loading: false
      });
      this.runPluginHandler("onCancel");
    };
    Fetch2.prototype.refresh = function() {
      this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));
    };
    Fetch2.prototype.refreshAsync = function() {
      return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));
    };
    Fetch2.prototype.mutate = function(data) {
      var targetData = isFunction(data) ? data(this.state.data) : data;
      this.runPluginHandler("onMutate", targetData);
      this.setState({
        data: targetData
      });
    };
    return Fetch2;
  }()
);
var Fetch_default = Fetch;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/useRequestImplement.js
function useRequestImplement(service, options, plugins) {
  if (options === void 0) {
    options = {};
  }
  if (plugins === void 0) {
    plugins = [];
  }
  var _a = options.manual, manual = _a === void 0 ? false : _a, _b = options.ready, ready = _b === void 0 ? true : _b, rest = __rest(options, ["manual", "ready"]);
  if (isDev_default) {
    if (options.defaultParams && !Array.isArray(options.defaultParams)) {
      console.warn("expected defaultParams is array, got ".concat(typeof options.defaultParams));
    }
  }
  var fetchOptions = __assign({
    manual,
    ready
  }, rest);
  var serviceRef = useLatest_default(service);
  var update = useUpdate_default();
  var fetchInstance = useCreation(function() {
    var initState3 = plugins.map(function(p) {
      var _a2;
      return (_a2 = p === null || p === void 0 ? void 0 : p.onInit) === null || _a2 === void 0 ? void 0 : _a2.call(p, fetchOptions);
    }).filter(Boolean);
    return new Fetch_default(serviceRef, fetchOptions, update, Object.assign.apply(Object, __spreadArray([{}], __read(initState3), false)));
  }, []);
  fetchInstance.options = fetchOptions;
  fetchInstance.pluginImpls = plugins.map(function(p) {
    return p(fetchInstance, fetchOptions);
  });
  useMount_default(function() {
    if (!manual && ready) {
      var params = fetchInstance.state.params || options.defaultParams || [];
      fetchInstance.run.apply(fetchInstance, __spreadArray([], __read(params), false));
    }
  });
  useUnmount_default(function() {
    fetchInstance.cancel();
  });
  return {
    loading: fetchInstance.state.loading,
    data: fetchInstance.state.data,
    error: fetchInstance.state.error,
    params: fetchInstance.state.params || [],
    cancel: useMemoizedFn_default(fetchInstance.cancel.bind(fetchInstance)),
    refresh: useMemoizedFn_default(fetchInstance.refresh.bind(fetchInstance)),
    refreshAsync: useMemoizedFn_default(fetchInstance.refreshAsync.bind(fetchInstance)),
    run: useMemoizedFn_default(fetchInstance.run.bind(fetchInstance)),
    runAsync: useMemoizedFn_default(fetchInstance.runAsync.bind(fetchInstance)),
    mutate: useMemoizedFn_default(fetchInstance.mutate.bind(fetchInstance))
  };
}
var useRequestImplement_default = useRequestImplement;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/src/useRequest.js
function useRequest(service, options, plugins) {
  return useRequestImplement_default(service, options, __spreadArray(__spreadArray([], __read(plugins || []), false), [useDebouncePlugin_default, useLoadingDelayPlugin_default, usePollingPlugin_default, useRefreshOnWindowFocusPlugin_default, useThrottlePlugin_default, useAutoRunPlugin_default, useCachePlugin_default, useRetryPlugin_default], false));
}
var useRequest_default = useRequest;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRequest/index.js
var useRequest_default2 = useRequest_default;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/usePagination/index.js
var usePagination = function(service, options) {
  var _a;
  if (options === void 0) {
    options = {};
  }
  var _b = options.defaultPageSize, defaultPageSize = _b === void 0 ? 10 : _b, _c = options.defaultCurrent, defaultCurrent = _c === void 0 ? 1 : _c, rest = __rest(options, ["defaultPageSize", "defaultCurrent"]);
  var result = useRequest_default2(service, __assign({
    defaultParams: [{
      current: defaultCurrent,
      pageSize: defaultPageSize
    }],
    refreshDepsAction: function() {
      changeCurrent(1);
    }
  }, rest));
  var _d = result.params[0] || {}, _e = _d.current, current = _e === void 0 ? 1 : _e, _f = _d.pageSize, pageSize = _f === void 0 ? defaultPageSize : _f;
  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;
  var totalPage = (0, import_react17.useMemo)(function() {
    return Math.ceil(total / pageSize);
  }, [pageSize, total]);
  var onChange = function(c, p) {
    var toCurrent = c <= 0 ? 1 : c;
    var toPageSize = p <= 0 ? 1 : p;
    var tempTotalPage = Math.ceil(total / toPageSize);
    if (toCurrent > tempTotalPage) {
      toCurrent = Math.max(1, tempTotalPage);
    }
    var _a2 = __read(result.params || []), _b2 = _a2[0], oldPaginationParams = _b2 === void 0 ? {} : _b2, restParams = _a2.slice(1);
    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {
      current: toCurrent,
      pageSize: toPageSize
    })], __read(restParams), false));
  };
  var changeCurrent = function(c) {
    onChange(c, pageSize);
  };
  var changePageSize = function(p) {
    onChange(current, p);
  };
  return __assign(__assign({}, result), {
    pagination: {
      current,
      pageSize,
      total,
      totalPage,
      onChange: useMemoizedFn_default(onChange),
      changeCurrent: useMemoizedFn_default(changeCurrent),
      changePageSize: useMemoizedFn_default(changePageSize)
    }
  });
};
var usePagination_default = usePagination;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useAntdTable/index.js
var useAntdTable = function(service, options) {
  var _a;
  if (options === void 0) {
    options = {};
  }
  var form = options.form, _b = options.defaultType, defaultType = _b === void 0 ? "simple" : _b, defaultParams = options.defaultParams, _c = options.manual, manual = _c === void 0 ? false : _c, _d = options.refreshDeps, refreshDeps = _d === void 0 ? [] : _d, _e = options.ready, ready = _e === void 0 ? true : _e, rest = __rest(options, ["form", "defaultType", "defaultParams", "manual", "refreshDeps", "ready"]);
  var result = usePagination_default(service, __assign(__assign({
    ready,
    manual: true
  }, rest), {
    onSuccess: function() {
      var _a2;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      runSuccessRef.current = true;
      (_a2 = rest.onSuccess) === null || _a2 === void 0 ? void 0 : _a2.call.apply(_a2, __spreadArray([rest], __read(args), false));
    }
  }));
  var _f = result.params, params = _f === void 0 ? [] : _f, run = result.run;
  var cacheFormTableData = params[2] || {};
  var _g = __read((0, import_react18.useState)((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2), type = _g[0], setType = _g[1];
  var allFormDataRef = (0, import_react18.useRef)({});
  var defaultDataSourceRef = (0, import_react18.useRef)([]);
  var runSuccessRef = (0, import_react18.useRef)(false);
  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);
  var getActiveFieldValues = function() {
    if (!form) {
      return {};
    }
    if (isAntdV4) {
      return form.getFieldsValue(null, function() {
        return true;
      });
    }
    var allFieldsValue = form.getFieldsValue();
    var activeFieldsValue = {};
    Object.keys(allFieldsValue).forEach(function(key) {
      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {
        activeFieldsValue[key] = allFieldsValue[key];
      }
    });
    return activeFieldsValue;
  };
  var validateFields = function() {
    if (!form) {
      return Promise.resolve({});
    }
    var activeFieldsValue = getActiveFieldValues();
    var fields = Object.keys(activeFieldsValue);
    if (isAntdV4) {
      return form.validateFields(fields);
    }
    return new Promise(function(resolve, reject) {
      form.validateFields(fields, function(errors, values) {
        if (errors) {
          reject(errors);
        } else {
          resolve(values);
        }
      });
    });
  };
  var restoreForm = function() {
    if (!form) {
      return;
    }
    if (isAntdV4) {
      return form.setFieldsValue(allFormDataRef.current);
    }
    var activeFieldsValue = {};
    Object.keys(allFormDataRef.current).forEach(function(key) {
      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {
        activeFieldsValue[key] = allFormDataRef.current[key];
      }
    });
    form.setFieldsValue(activeFieldsValue);
  };
  var changeType = function() {
    var activeFieldsValue = getActiveFieldValues();
    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);
    setType(function(t) {
      return t === "simple" ? "advance" : "simple";
    });
  };
  var _submit = function(initPagination) {
    if (!ready) {
      return;
    }
    setTimeout(function() {
      validateFields().then(function(values) {
        if (values === void 0) {
          values = {};
        }
        var pagination = initPagination || __assign(__assign({
          pageSize: options.defaultPageSize || 10
        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {
          current: 1
        });
        if (!form) {
          run(pagination);
          return;
        }
        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);
        run(pagination, values, {
          allFormData: allFormDataRef.current,
          type
        });
      }).catch(function(err) {
        return err;
      });
    });
  };
  var reset = function() {
    var _a2, _b2;
    if (form) {
      form.resetFields();
    }
    _submit(__assign(__assign({}, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}), {
      pageSize: options.defaultPageSize || ((_b2 = (_a2 = options.defaultParams) === null || _a2 === void 0 ? void 0 : _a2[0]) === null || _b2 === void 0 ? void 0 : _b2.pageSize) || 10,
      current: 1
    }));
  };
  var submit = function(e) {
    var _a2, _b2, _c2;
    (_a2 = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a2 === void 0 ? void 0 : _a2.call(e);
    _submit(runSuccessRef.current ? void 0 : __assign({
      pageSize: options.defaultPageSize || ((_c2 = (_b2 = options.defaultParams) === null || _b2 === void 0 ? void 0 : _b2[0]) === null || _c2 === void 0 ? void 0 : _c2.pageSize) || 10,
      current: 1
    }, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}));
  };
  var onTableChange = function(pagination, filters, sorter, extra) {
    var _a2 = __read(params || []), oldPaginationParams = _a2[0], restParams = _a2.slice(1);
    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {
      current: pagination.current,
      pageSize: pagination.pageSize,
      filters,
      sorter,
      extra
    })], __read(restParams), false));
  };
  (0, import_react18.useEffect)(function() {
    if (params.length > 0) {
      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};
      restoreForm();
      run.apply(void 0, __spreadArray([], __read(params), false));
      return;
    }
    if (ready) {
      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};
      restoreForm();
      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);
    }
  }, []);
  useUpdateEffect_default(function() {
    if (!ready) {
      return;
    }
    restoreForm();
  }, [type]);
  var hasAutoRun = (0, import_react18.useRef)(false);
  hasAutoRun.current = false;
  useUpdateEffect_default(function() {
    if (!manual && ready) {
      hasAutoRun.current = true;
      if (form) {
        form.resetFields();
      }
      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};
      restoreForm();
      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);
    }
  }, [ready]);
  useUpdateEffect_default(function() {
    if (hasAutoRun.current) {
      return;
    }
    if (!ready) {
      return;
    }
    if (!manual) {
      hasAutoRun.current = true;
      result.pagination.changeCurrent(1);
    }
  }, __spreadArray([], __read(refreshDeps), false));
  return __assign(__assign({}, result), {
    tableProps: {
      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,
      loading: result.loading,
      onChange: useMemoizedFn_default(onTableChange),
      pagination: {
        current: result.pagination.current,
        pageSize: result.pagination.pageSize,
        total: result.pagination.total
      }
    },
    search: {
      submit: useMemoizedFn_default(submit),
      type,
      changeType: useMemoizedFn_default(changeType),
      reset: useMemoizedFn_default(reset)
    }
  });
};
var useAntdTable_default = useAntdTable;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useAsyncEffect/index.js
var import_react19 = __toESM(require_react());
function isAsyncGenerator(val) {
  return isFunction(val[Symbol.asyncIterator]);
}
function useAsyncEffect(effect, deps) {
  (0, import_react19.useEffect)(function() {
    var e = effect();
    var cancelled = false;
    function execute() {
      return __awaiter(this, void 0, void 0, function() {
        var result;
        return __generator(this, function(_a) {
          switch (_a.label) {
            case 0:
              if (!isAsyncGenerator(e)) return [3, 4];
              _a.label = 1;
            case 1:
              if (false) return [3, 3];
              return [4, e.next()];
            case 2:
              result = _a.sent();
              if (result.done || cancelled) {
                return [3, 3];
              }
              return [3, 1];
            case 3:
              return [3, 6];
            case 4:
              return [4, e];
            case 5:
              _a.sent();
              _a.label = 6;
            case 6:
              return [
                2
                /*return*/
              ];
          }
        });
      });
    }
    execute();
    return function() {
      cancelled = true;
    };
  }, deps);
}
var useAsyncEffect_default = useAsyncEffect;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useBoolean/index.js
var import_react21 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useToggle/index.js
var import_react20 = __toESM(require_react());
function useToggle(defaultValue, reverseValue) {
  if (defaultValue === void 0) {
    defaultValue = false;
  }
  var _a = __read((0, import_react20.useState)(defaultValue), 2), state = _a[0], setState = _a[1];
  var actions = (0, import_react20.useMemo)(function() {
    var reverseValueOrigin = reverseValue === void 0 ? !defaultValue : reverseValue;
    var toggle = function() {
      return setState(function(s) {
        return s === defaultValue ? reverseValueOrigin : defaultValue;
      });
    };
    var set = function(value) {
      return setState(value);
    };
    var setLeft = function() {
      return setState(defaultValue);
    };
    var setRight = function() {
      return setState(reverseValueOrigin);
    };
    return {
      toggle,
      set,
      setLeft,
      setRight
    };
  }, []);
  return [state, actions];
}
var useToggle_default = useToggle;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useBoolean/index.js
function useBoolean(defaultValue) {
  if (defaultValue === void 0) {
    defaultValue = false;
  }
  var _a = __read(useToggle_default(!!defaultValue), 2), state = _a[0], _b = _a[1], toggle = _b.toggle, set = _b.set;
  var actions = (0, import_react21.useMemo)(function() {
    var setTrue = function() {
      return set(true);
    };
    var setFalse = function() {
      return set(false);
    };
    return {
      toggle,
      set: function(v) {
        return set(!!v);
      },
      setTrue,
      setFalse
    };
  }, []);
  return [state, actions];
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/domTarget.js
function getTargetElement(target, defaultElement) {
  if (!isBrowser_default) {
    return void 0;
  }
  if (!target) {
    return defaultElement;
  }
  var targetElement;
  if (isFunction(target)) {
    targetElement = target();
  } else if ("current" in target) {
    targetElement = target.current;
  } else {
    targetElement = target;
  }
  return targetElement;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/getDocumentOrShadow.js
var checkIfAllInShadow = function(targets) {
  return targets.every(function(item) {
    var targetElement = getTargetElement(item);
    if (!targetElement) return false;
    if (targetElement.getRootNode() instanceof ShadowRoot) return true;
    return false;
  });
};
var getShadow = function(node) {
  if (!node) {
    return document;
  }
  return node.getRootNode();
};
var getDocumentOrShadow = function(target) {
  if (!target || !document.getRootNode) {
    return document;
  }
  var targets = Array.isArray(target) ? target : [target];
  if (checkIfAllInShadow(targets)) {
    return getShadow(getTargetElement(targets[0]));
  }
  return document;
};
var getDocumentOrShadow_default = getDocumentOrShadow;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/useEffectWithTarget.js
var import_react23 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/createEffectWithTarget.js
var import_react22 = __toESM(require_react());
var createEffectWithTarget = function(useEffectType) {
  var useEffectWithTarget3 = function(effect, deps, target) {
    var hasInitRef = (0, import_react22.useRef)(false);
    var lastElementRef = (0, import_react22.useRef)([]);
    var lastDepsRef = (0, import_react22.useRef)([]);
    var unLoadRef = (0, import_react22.useRef)();
    useEffectType(function() {
      var _a;
      var targets = Array.isArray(target) ? target : [target];
      var els = targets.map(function(item) {
        return getTargetElement(item);
      });
      if (!hasInitRef.current) {
        hasInitRef.current = true;
        lastElementRef.current = els;
        lastDepsRef.current = deps;
        unLoadRef.current = effect();
        return;
      }
      if (els.length !== lastElementRef.current.length || !depsAreSame(lastElementRef.current, els) || !depsAreSame(lastDepsRef.current, deps)) {
        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);
        lastElementRef.current = els;
        lastDepsRef.current = deps;
        unLoadRef.current = effect();
      }
    });
    useUnmount_default(function() {
      var _a;
      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);
      hasInitRef.current = false;
    });
  };
  return useEffectWithTarget3;
};
var createEffectWithTarget_default = createEffectWithTarget;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/useEffectWithTarget.js
var useEffectWithTarget = createEffectWithTarget_default(import_react23.useEffect);
var useEffectWithTarget_default = useEffectWithTarget;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useClickAway/index.js
function useClickAway(onClickAway, target, eventName) {
  if (eventName === void 0) {
    eventName = "click";
  }
  var onClickAwayRef = useLatest_default(onClickAway);
  useEffectWithTarget_default(function() {
    var handler = function(event) {
      var targets = Array.isArray(target) ? target : [target];
      if (targets.some(function(item) {
        var targetElement = getTargetElement(item);
        return !targetElement || targetElement.contains(event.target);
      })) {
        return;
      }
      onClickAwayRef.current(event);
    };
    var documentOrShadow = getDocumentOrShadow_default(target);
    var eventNames = Array.isArray(eventName) ? eventName : [eventName];
    eventNames.forEach(function(event) {
      return documentOrShadow.addEventListener(event, handler);
    });
    return function() {
      eventNames.forEach(function(event) {
        return documentOrShadow.removeEventListener(event, handler);
      });
    };
  }, Array.isArray(eventName) ? eventName : [eventName], target);
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useControllableValue/index.js
var import_react24 = __toESM(require_react());
function useControllableValue(defaultProps, options) {
  if (options === void 0) {
    options = {};
  }
  var props = defaultProps !== null && defaultProps !== void 0 ? defaultProps : {};
  var defaultValue = options.defaultValue, _a = options.defaultValuePropName, defaultValuePropName = _a === void 0 ? "defaultValue" : _a, _b = options.valuePropName, valuePropName = _b === void 0 ? "value" : _b, _c = options.trigger, trigger2 = _c === void 0 ? "onChange" : _c;
  var value = props[valuePropName];
  var isControlled = Object.prototype.hasOwnProperty.call(props, valuePropName);
  var initialValue = (0, import_react24.useMemo)(function() {
    if (isControlled) {
      return value;
    }
    if (Object.prototype.hasOwnProperty.call(props, defaultValuePropName)) {
      return props[defaultValuePropName];
    }
    return defaultValue;
  }, []);
  var stateRef = (0, import_react24.useRef)(initialValue);
  if (isControlled) {
    stateRef.current = value;
  }
  var update = useUpdate_default();
  function setState(v) {
    var args = [];
    for (var _i = 1; _i < arguments.length; _i++) {
      args[_i - 1] = arguments[_i];
    }
    var r = isFunction(v) ? v(stateRef.current) : v;
    if (!isControlled) {
      stateRef.current = r;
      update();
    }
    if (props[trigger2]) {
      props[trigger2].apply(props, __spreadArray([r], __read(args), false));
    }
  }
  return [stateRef.current, useMemoizedFn_default(setState)];
}
var useControllableValue_default = useControllableValue;

// node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs
function assign(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i];
    for (var key in source) {
      target[key] = source[key];
    }
  }
  return target;
}
var defaultConverter = {
  read: function(value) {
    if (value[0] === '"') {
      value = value.slice(1, -1);
    }
    return value.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
  },
  write: function(value) {
    return encodeURIComponent(value).replace(
      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,
      decodeURIComponent
    );
  }
};
function init(converter, defaultAttributes) {
  function set(name, value, attributes) {
    if (typeof document === "undefined") {
      return;
    }
    attributes = assign({}, defaultAttributes, attributes);
    if (typeof attributes.expires === "number") {
      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);
    }
    if (attributes.expires) {
      attributes.expires = attributes.expires.toUTCString();
    }
    name = encodeURIComponent(name).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
    var stringifiedAttributes = "";
    for (var attributeName in attributes) {
      if (!attributes[attributeName]) {
        continue;
      }
      stringifiedAttributes += "; " + attributeName;
      if (attributes[attributeName] === true) {
        continue;
      }
      stringifiedAttributes += "=" + attributes[attributeName].split(";")[0];
    }
    return document.cookie = name + "=" + converter.write(value, name) + stringifiedAttributes;
  }
  function get(name) {
    if (typeof document === "undefined" || arguments.length && !name) {
      return;
    }
    var cookies = document.cookie ? document.cookie.split("; ") : [];
    var jar = {};
    for (var i = 0; i < cookies.length; i++) {
      var parts = cookies[i].split("=");
      var value = parts.slice(1).join("=");
      try {
        var found = decodeURIComponent(parts[0]);
        jar[found] = converter.read(value, found);
        if (name === found) {
          break;
        }
      } catch (e) {
      }
    }
    return name ? jar[name] : jar;
  }
  return Object.create(
    {
      set,
      get,
      remove: function(name, attributes) {
        set(
          name,
          "",
          assign({}, attributes, {
            expires: -1
          })
        );
      },
      withAttributes: function(attributes) {
        return init(this.converter, assign({}, this.attributes, attributes));
      },
      withConverter: function(converter2) {
        return init(assign({}, this.converter, converter2), this.attributes);
      }
    },
    {
      attributes: { value: Object.freeze(defaultAttributes) },
      converter: { value: Object.freeze(converter) }
    }
  );
}
var api = init(defaultConverter, { path: "/" });

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useCookieState/index.js
var import_react25 = __toESM(require_react());
function useCookieState(cookieKey, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = __read((0, import_react25.useState)(function() {
    var cookieValue = api.get(cookieKey);
    if (isString(cookieValue)) return cookieValue;
    if (isFunction(options.defaultValue)) {
      return options.defaultValue();
    }
    return options.defaultValue;
  }), 2), state = _a[0], setState = _a[1];
  var updateState = useMemoizedFn_default(function(newValue, newOptions) {
    if (newOptions === void 0) {
      newOptions = {};
    }
    var _a2 = __assign(__assign({}, options), newOptions), defaultValue = _a2.defaultValue, restOptions = __rest(_a2, ["defaultValue"]);
    var value = isFunction(newValue) ? newValue(state) : newValue;
    setState(value);
    if (value === void 0) {
      api.remove(cookieKey);
    } else {
      api.set(cookieKey, value, restOptions);
    }
  });
  return [state, updateState];
}
var useCookieState_default = useCookieState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useCountDown/index.js
var import_dayjs = __toESM(require_dayjs_min());
var import_react26 = __toESM(require_react());
var calcLeft = function(target) {
  if (!target) {
    return 0;
  }
  var left = (0, import_dayjs.default)(target).valueOf() - Date.now();
  return left < 0 ? 0 : left;
};
var parseMs = function(milliseconds) {
  return {
    days: Math.floor(milliseconds / 864e5),
    hours: Math.floor(milliseconds / 36e5) % 24,
    minutes: Math.floor(milliseconds / 6e4) % 60,
    seconds: Math.floor(milliseconds / 1e3) % 60,
    milliseconds: Math.floor(milliseconds) % 1e3
  };
};
var useCountdown = function(options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options || {}, leftTime = _a.leftTime, targetDate = _a.targetDate, _b = _a.interval, interval = _b === void 0 ? 1e3 : _b, onEnd = _a.onEnd;
  var memoLeftTime = (0, import_react26.useMemo)(function() {
    return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : void 0;
  }, [leftTime]);
  var target = "leftTime" in options ? memoLeftTime : targetDate;
  var _c = __read((0, import_react26.useState)(function() {
    return calcLeft(target);
  }), 2), timeLeft = _c[0], setTimeLeft = _c[1];
  var onEndRef = useLatest_default(onEnd);
  (0, import_react26.useEffect)(function() {
    if (!target) {
      setTimeLeft(0);
      return;
    }
    setTimeLeft(calcLeft(target));
    var timer = setInterval(function() {
      var _a2;
      var targetLeft = calcLeft(target);
      setTimeLeft(targetLeft);
      if (targetLeft === 0) {
        clearInterval(timer);
        (_a2 = onEndRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onEndRef);
      }
    }, interval);
    return function() {
      return clearInterval(timer);
    };
  }, [target, interval]);
  var formattedRes = (0, import_react26.useMemo)(function() {
    return parseMs(timeLeft);
  }, [timeLeft]);
  return [timeLeft, formattedRes];
};
var useCountDown_default = useCountdown;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useCounter/index.js
var import_react27 = __toESM(require_react());
function getTargetValue(val, options) {
  if (options === void 0) {
    options = {};
  }
  var min = options.min, max = options.max;
  var target = val;
  if (isNumber(max)) {
    target = Math.min(max, target);
  }
  if (isNumber(min)) {
    target = Math.max(min, target);
  }
  return target;
}
function useCounter(initialValue, options) {
  if (initialValue === void 0) {
    initialValue = 0;
  }
  if (options === void 0) {
    options = {};
  }
  var min = options.min, max = options.max;
  var _a = __read((0, import_react27.useState)(function() {
    return getTargetValue(initialValue, {
      min,
      max
    });
  }), 2), current = _a[0], setCurrent = _a[1];
  var setValue = function(value) {
    setCurrent(function(c) {
      var target = isNumber(value) ? value : value(c);
      return getTargetValue(target, {
        max,
        min
      });
    });
  };
  var inc = function(delta) {
    if (delta === void 0) {
      delta = 1;
    }
    setValue(function(c) {
      return c + delta;
    });
  };
  var dec = function(delta) {
    if (delta === void 0) {
      delta = 1;
    }
    setValue(function(c) {
      return c - delta;
    });
  };
  var set = function(value) {
    setValue(value);
  };
  var reset = function() {
    setValue(initialValue);
  };
  return [current, {
    inc: useMemoizedFn_default(inc),
    dec: useMemoizedFn_default(dec),
    set: useMemoizedFn_default(set),
    reset: useMemoizedFn_default(reset)
  }];
}
var useCounter_default = useCounter;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDebounce/index.js
var import_react29 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/lodash-polyfill.js
var import_debounce2 = __toESM(require_debounce());
function isNodeOrWeb() {
  var freeGlobal = (typeof global === "undefined" ? "undefined" : typeof global) == "object" && global && global.Object === Object && global;
  var freeSelf = typeof self == "object" && self && self.Object === Object && self;
  return freeGlobal || freeSelf;
}
if (!isNodeOrWeb()) {
  global.Date = Date;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDebounceFn/index.js
var import_react28 = __toESM(require_react());
function useDebounceFn(fn, options) {
  var _a;
  if (isDev_default) {
    if (!isFunction(fn)) {
      console.error("useDebounceFn expected parameter is a function, got ".concat(typeof fn));
    }
  }
  var fnRef = useLatest_default(fn);
  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1e3;
  var debounced = (0, import_react28.useMemo)(function() {
    return (0, import_debounce2.default)(function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));
    }, wait, options);
  }, []);
  useUnmount_default(function() {
    debounced.cancel();
  });
  return {
    run: debounced,
    cancel: debounced.cancel,
    flush: debounced.flush
  };
}
var useDebounceFn_default = useDebounceFn;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDebounce/index.js
function useDebounce(value, options) {
  var _a = __read((0, import_react29.useState)(value), 2), debounced = _a[0], setDebounced = _a[1];
  var run = useDebounceFn_default(function() {
    setDebounced(value);
  }, options).run;
  (0, import_react29.useEffect)(function() {
    run();
  }, [value]);
  return debounced;
}
var useDebounce_default = useDebounce;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDebounceEffect/index.js
var import_react30 = __toESM(require_react());
function useDebounceEffect(effect, deps, options) {
  var _a = __read((0, import_react30.useState)({}), 2), flag = _a[0], setFlag = _a[1];
  var run = useDebounceFn_default(function() {
    setFlag({});
  }, options).run;
  (0, import_react30.useEffect)(function() {
    return run();
  }, deps);
  useUpdateEffect_default(effect, [flag]);
}
var useDebounceEffect_default = useDebounceEffect;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDeepCompareEffect/index.js
var import_react32 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/createDeepCompareEffect/index.js
var import_react31 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/depsEqual.js
var import_react_fast_compare = __toESM(require_react_fast_compare());
var depsEqual = function(aDeps, bDeps) {
  if (aDeps === void 0) {
    aDeps = [];
  }
  if (bDeps === void 0) {
    bDeps = [];
  }
  return (0, import_react_fast_compare.default)(aDeps, bDeps);
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/createDeepCompareEffect/index.js
var createDeepCompareEffect = function(hook) {
  return function(effect, deps) {
    var ref = (0, import_react31.useRef)();
    var signalRef = (0, import_react31.useRef)(0);
    if (deps === void 0 || !depsEqual(deps, ref.current)) {
      signalRef.current += 1;
    }
    ref.current = deps;
    hook(effect, [signalRef.current]);
  };
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDeepCompareEffect/index.js
var useDeepCompareEffect_default = createDeepCompareEffect(import_react32.useEffect);

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDeepCompareLayoutEffect/index.js
var import_react33 = __toESM(require_react());
var useDeepCompareLayoutEffect_default = createDeepCompareEffect(import_react33.useLayoutEffect);

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDocumentVisibility/index.js
var import_react34 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useEventListener/index.js
function useEventListener(eventName, handler, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.enable, enable = _a === void 0 ? true : _a;
  var handlerRef = useLatest_default(handler);
  useEffectWithTarget_default(function() {
    if (!enable) {
      return;
    }
    var targetElement = getTargetElement(options.target, window);
    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }
    var eventListener = function(event) {
      return handlerRef.current(event);
    };
    var eventNameArray = Array.isArray(eventName) ? eventName : [eventName];
    eventNameArray.forEach(function(event) {
      targetElement.addEventListener(event, eventListener, {
        capture: options.capture,
        once: options.once,
        passive: options.passive
      });
    });
    return function() {
      eventNameArray.forEach(function(event) {
        targetElement.removeEventListener(event, eventListener, {
          capture: options.capture
        });
      });
    };
  }, [eventName, options.capture, options.once, options.passive, enable], options.target);
}
var useEventListener_default = useEventListener;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDocumentVisibility/index.js
var getVisibility = function() {
  if (!isBrowser_default) {
    return "visible";
  }
  return document.visibilityState;
};
function useDocumentVisibility() {
  var _a = __read((0, import_react34.useState)(getVisibility), 2), documentVisibility = _a[0], setDocumentVisibility = _a[1];
  useEventListener_default("visibilitychange", function() {
    setDocumentVisibility(getVisibility());
  }, {
    target: function() {
      return document;
    }
  });
  return documentVisibility;
}
var useDocumentVisibility_default = useDocumentVisibility;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDrag/index.js
var import_react35 = __toESM(require_react());
var useDrag = function(data, target, options) {
  if (options === void 0) {
    options = {};
  }
  var optionsRef = useLatest_default(options);
  var dataRef = useLatest_default(data);
  var imageElementRef = (0, import_react35.useRef)();
  var dragImage = optionsRef.current.dragImage;
  useMount_default(function() {
    if (dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) {
      var image = dragImage.image;
      if (isString(image)) {
        var imageElement = new Image();
        imageElement.src = image;
        imageElementRef.current = imageElement;
      } else {
        imageElementRef.current = image;
      }
    }
  });
  useEffectWithTarget_default(function() {
    var targetElement = getTargetElement(target);
    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }
    var onDragStart = function(event) {
      var _a, _b;
      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);
      event.dataTransfer.setData("custom", JSON.stringify(dataRef.current));
      if ((dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) && imageElementRef.current) {
        var _c = dragImage.offsetX, offsetX = _c === void 0 ? 0 : _c, _d = dragImage.offsetY, offsetY = _d === void 0 ? 0 : _d;
        event.dataTransfer.setDragImage(imageElementRef.current, offsetX, offsetY);
      }
    };
    var onDragEnd = function(event) {
      var _a, _b;
      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };
    targetElement.setAttribute("draggable", "true");
    targetElement.addEventListener("dragstart", onDragStart);
    targetElement.addEventListener("dragend", onDragEnd);
    return function() {
      targetElement.removeEventListener("dragstart", onDragStart);
      targetElement.removeEventListener("dragend", onDragEnd);
    };
  }, [], target);
};
var useDrag_default = useDrag;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDrop/index.js
var import_react36 = __toESM(require_react());
var useDrop = function(target, options) {
  if (options === void 0) {
    options = {};
  }
  var optionsRef = useLatest_default(options);
  var dragEnterTarget = (0, import_react36.useRef)();
  useEffectWithTarget_default(function() {
    var targetElement = getTargetElement(target);
    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }
    var onData = function(dataTransfer, event) {
      var uri = dataTransfer.getData("text/uri-list");
      var dom = dataTransfer.getData("custom");
      if (dom && optionsRef.current.onDom) {
        var data = dom;
        try {
          data = JSON.parse(dom);
        } catch (e) {
          data = dom;
        }
        optionsRef.current.onDom(data, event);
        return;
      }
      if (uri && optionsRef.current.onUri) {
        optionsRef.current.onUri(uri, event);
        return;
      }
      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {
        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);
        return;
      }
      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {
        dataTransfer.items[0].getAsString(function(text) {
          optionsRef.current.onText(text, event);
        });
      }
    };
    var onDragEnter = function(event) {
      var _a, _b;
      event.preventDefault();
      event.stopPropagation();
      dragEnterTarget.current = event.target;
      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };
    var onDragOver = function(event) {
      var _a, _b;
      event.preventDefault();
      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };
    var onDragLeave = function(event) {
      var _a, _b;
      if (event.target === dragEnterTarget.current) {
        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);
      }
    };
    var onDrop = function(event) {
      var _a, _b;
      event.preventDefault();
      onData(event.dataTransfer, event);
      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };
    var onPaste = function(event) {
      var _a, _b;
      onData(event.clipboardData, event);
      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);
    };
    targetElement.addEventListener("dragenter", onDragEnter);
    targetElement.addEventListener("dragover", onDragOver);
    targetElement.addEventListener("dragleave", onDragLeave);
    targetElement.addEventListener("drop", onDrop);
    targetElement.addEventListener("paste", onPaste);
    return function() {
      targetElement.removeEventListener("dragenter", onDragEnter);
      targetElement.removeEventListener("dragover", onDragOver);
      targetElement.removeEventListener("dragleave", onDragLeave);
      targetElement.removeEventListener("drop", onDrop);
      targetElement.removeEventListener("paste", onPaste);
    };
  }, [], target);
};
var useDrop_default = useDrop;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useDynamicList/index.js
var import_react37 = __toESM(require_react());
var useDynamicList = function(initialList) {
  if (initialList === void 0) {
    initialList = [];
  }
  var counterRef = (0, import_react37.useRef)(-1);
  var keyList = (0, import_react37.useRef)([]);
  var setKey = (0, import_react37.useCallback)(function(index) {
    counterRef.current += 1;
    keyList.current.splice(index, 0, counterRef.current);
  }, []);
  var _a = __read((0, import_react37.useState)(function() {
    initialList.forEach(function(_, index) {
      setKey(index);
    });
    return initialList;
  }), 2), list = _a[0], setList = _a[1];
  var resetList = (0, import_react37.useCallback)(function(newList) {
    keyList.current = [];
    setList(function() {
      newList.forEach(function(_, index) {
        setKey(index);
      });
      return newList;
    });
  }, []);
  var insert = (0, import_react37.useCallback)(function(index, item) {
    setList(function(l) {
      var temp = __spreadArray([], __read(l), false);
      temp.splice(index, 0, item);
      setKey(index);
      return temp;
    });
  }, []);
  var getKey = (0, import_react37.useCallback)(function(index) {
    return keyList.current[index];
  }, []);
  var getIndex = (0, import_react37.useCallback)(function(key) {
    return keyList.current.findIndex(function(ele) {
      return ele === key;
    });
  }, []);
  var merge = (0, import_react37.useCallback)(function(index, items) {
    setList(function(l) {
      var temp = __spreadArray([], __read(l), false);
      items.forEach(function(_, i) {
        setKey(index + i);
      });
      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));
      return temp;
    });
  }, []);
  var replace = (0, import_react37.useCallback)(function(index, item) {
    setList(function(l) {
      var temp = __spreadArray([], __read(l), false);
      temp[index] = item;
      return temp;
    });
  }, []);
  var remove = (0, import_react37.useCallback)(function(index) {
    setList(function(l) {
      var temp = __spreadArray([], __read(l), false);
      temp.splice(index, 1);
      try {
        keyList.current.splice(index, 1);
      } catch (e) {
        console.error(e);
      }
      return temp;
    });
  }, []);
  var batchRemove = (0, import_react37.useCallback)(function(indexes) {
    if (!Array.isArray(indexes)) {
      if (isDev_default) {
        console.error('`indexes` parameter of `batchRemove` function expected to be an array, but got "'.concat(typeof indexes, '".'));
      }
      return;
    }
    if (!indexes.length) {
      return;
    }
    setList(function(prevList) {
      var newKeyList = [];
      var newList = prevList.filter(function(item, index) {
        var shouldKeep = !indexes.includes(index);
        if (shouldKeep) {
          newKeyList.push(getKey(index));
        }
        return shouldKeep;
      });
      keyList.current = newKeyList;
      return newList;
    });
  }, []);
  var move = (0, import_react37.useCallback)(function(oldIndex, newIndex) {
    if (oldIndex === newIndex) {
      return;
    }
    setList(function(l) {
      var newList = __spreadArray([], __read(l), false);
      var temp = newList.filter(function(_, index) {
        return index !== oldIndex;
      });
      temp.splice(newIndex, 0, newList[oldIndex]);
      try {
        var keyTemp = keyList.current.filter(function(_, index) {
          return index !== oldIndex;
        });
        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);
        keyList.current = keyTemp;
      } catch (e) {
        console.error(e);
      }
      return temp;
    });
  }, []);
  var push = (0, import_react37.useCallback)(function(item) {
    setList(function(l) {
      setKey(l.length);
      return l.concat([item]);
    });
  }, []);
  var pop = (0, import_react37.useCallback)(function() {
    try {
      keyList.current = keyList.current.slice(0, keyList.current.length - 1);
    } catch (e) {
      console.error(e);
    }
    setList(function(l) {
      return l.slice(0, l.length - 1);
    });
  }, []);
  var unshift = (0, import_react37.useCallback)(function(item) {
    setList(function(l) {
      setKey(0);
      return [item].concat(l);
    });
  }, []);
  var shift = (0, import_react37.useCallback)(function() {
    try {
      keyList.current = keyList.current.slice(1, keyList.current.length);
    } catch (e) {
      console.error(e);
    }
    setList(function(l) {
      return l.slice(1, l.length);
    });
  }, []);
  var sortList = (0, import_react37.useCallback)(
    function(result) {
      return result.map(function(item, index) {
        return {
          key: index,
          item
        };
      }).sort(function(a, b) {
        return getIndex(a.key) - getIndex(b.key);
      }).filter(function(item) {
        return !!item.item;
      }).map(function(item) {
        return item.item;
      });
    },
    // retrive the data
    []
  );
  return {
    list,
    insert,
    merge,
    replace,
    remove,
    batchRemove,
    getKey,
    getIndex,
    move,
    push,
    pop,
    unshift,
    shift,
    sortList,
    resetList
  };
};
var useDynamicList_default = useDynamicList;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useEventEmitter/index.js
var import_react38 = __toESM(require_react());
var EventEmitter = (
  /** @class */
  /* @__PURE__ */ function() {
    function EventEmitter2() {
      var _this = this;
      this.subscriptions = /* @__PURE__ */ new Set();
      this.emit = function(val) {
        var e_1, _a;
        try {
          for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {
            var subscription = _c.value;
            subscription(val);
          }
        } catch (e_1_1) {
          e_1 = {
            error: e_1_1
          };
        } finally {
          try {
            if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
          } finally {
            if (e_1) throw e_1.error;
          }
        }
      };
      this.useSubscription = function(callback) {
        var callbackRef = (0, import_react38.useRef)();
        callbackRef.current = callback;
        (0, import_react38.useEffect)(function() {
          function subscription(val) {
            if (callbackRef.current) {
              callbackRef.current(val);
            }
          }
          _this.subscriptions.add(subscription);
          return function() {
            _this.subscriptions.delete(subscription);
          };
        }, []);
      };
    }
    return EventEmitter2;
  }()
);
function useEventEmitter() {
  var ref = (0, import_react38.useRef)();
  if (!ref.current) {
    ref.current = new EventEmitter();
  }
  return ref.current;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useEventTarget/index.js
var import_react39 = __toESM(require_react());
function useEventTarget(options) {
  var _a = options || {}, initialValue = _a.initialValue, transformer = _a.transformer;
  var _b = __read((0, import_react39.useState)(initialValue), 2), value = _b[0], setValue = _b[1];
  var transformerRef = useLatest_default(transformer);
  var reset = (0, import_react39.useCallback)(function() {
    return setValue(initialValue);
  }, []);
  var onChange = (0, import_react39.useCallback)(function(e) {
    var _value = e.target.value;
    if (isFunction(transformerRef.current)) {
      return setValue(transformerRef.current(_value));
    }
    return setValue(_value);
  }, []);
  return [value, {
    onChange,
    reset
  }];
}
var useEventTarget_default = useEventTarget;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useExternal/index.js
var import_react40 = __toESM(require_react());
var EXTERNAL_USED_COUNT = {};
var loadScript = function(path, props) {
  if (props === void 0) {
    props = {};
  }
  var script = document.querySelector('script[src="'.concat(path, '"]'));
  if (!script) {
    var newScript_1 = document.createElement("script");
    newScript_1.src = path;
    Object.keys(props).forEach(function(key) {
      newScript_1[key] = props[key];
    });
    newScript_1.setAttribute("data-status", "loading");
    document.body.appendChild(newScript_1);
    return {
      ref: newScript_1,
      status: "loading"
    };
  }
  return {
    ref: script,
    status: script.getAttribute("data-status") || "ready"
  };
};
var loadCss = function(path, props) {
  if (props === void 0) {
    props = {};
  }
  var css = document.querySelector('link[href="'.concat(path, '"]'));
  if (!css) {
    var newCss_1 = document.createElement("link");
    newCss_1.rel = "stylesheet";
    newCss_1.href = path;
    Object.keys(props).forEach(function(key) {
      newCss_1[key] = props[key];
    });
    var isLegacyIECss = "hideFocus" in newCss_1;
    if (isLegacyIECss && newCss_1.relList) {
      newCss_1.rel = "preload";
      newCss_1.as = "style";
    }
    newCss_1.setAttribute("data-status", "loading");
    document.head.appendChild(newCss_1);
    return {
      ref: newCss_1,
      status: "loading"
    };
  }
  return {
    ref: css,
    status: css.getAttribute("data-status") || "ready"
  };
};
var useExternal = function(path, options) {
  var _a = __read((0, import_react40.useState)(path ? "loading" : "unset"), 2), status = _a[0], setStatus = _a[1];
  var ref = (0, import_react40.useRef)();
  (0, import_react40.useEffect)(function() {
    if (!path) {
      setStatus("unset");
      return;
    }
    var pathname = path.replace(/[|#].*$/, "");
    if ((options === null || options === void 0 ? void 0 : options.type) === "css" || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\.css$)/.test(pathname)) {
      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);
      ref.current = result.ref;
      setStatus(result.status);
    } else if ((options === null || options === void 0 ? void 0 : options.type) === "js" || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\.js$)/.test(pathname)) {
      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);
      ref.current = result.ref;
      setStatus(result.status);
    } else {
      console.error("Cannot infer the type of external resource, and please provide a type ('js' | 'css'). Refer to the https://ahooks.js.org/hooks/dom/use-external/#options");
    }
    if (!ref.current) {
      return;
    }
    if (EXTERNAL_USED_COUNT[path] === void 0) {
      EXTERNAL_USED_COUNT[path] = 1;
    } else {
      EXTERNAL_USED_COUNT[path] += 1;
    }
    var handler = function(event) {
      var _a2;
      var targetStatus = event.type === "load" ? "ready" : "error";
      (_a2 = ref.current) === null || _a2 === void 0 ? void 0 : _a2.setAttribute("data-status", targetStatus);
      setStatus(targetStatus);
    };
    ref.current.addEventListener("load", handler);
    ref.current.addEventListener("error", handler);
    return function() {
      var _a2, _b, _c;
      (_a2 = ref.current) === null || _a2 === void 0 ? void 0 : _a2.removeEventListener("load", handler);
      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener("error", handler);
      EXTERNAL_USED_COUNT[path] -= 1;
      if (EXTERNAL_USED_COUNT[path] === 0 && !(options === null || options === void 0 ? void 0 : options.keepWhenUnused)) {
        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();
      }
      ref.current = void 0;
    };
  }, [path]);
  return status;
};
var useExternal_default = useExternal;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useFavicon/index.js
var import_react41 = __toESM(require_react());
var ImgTypeMap = {
  SVG: "image/svg+xml",
  ICO: "image/x-icon",
  GIF: "image/gif",
  PNG: "image/png"
};
var useFavicon = function(href) {
  (0, import_react41.useEffect)(function() {
    if (!href) return;
    var cutUrl = href.split(".");
    var imgSuffix = cutUrl[cutUrl.length - 1].toLocaleUpperCase();
    var link = document.querySelector("link[rel*='icon']") || document.createElement("link");
    link.type = ImgTypeMap[imgSuffix];
    link.href = href;
    link.rel = "shortcut icon";
    document.getElementsByTagName("head")[0].appendChild(link);
  }, [href]);
};
var useFavicon_default = useFavicon;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useFocusWithin/index.js
var import_react42 = __toESM(require_react());
function useFocusWithin(target, options) {
  var _a = __read((0, import_react42.useState)(false), 2), isFocusWithin = _a[0], setIsFocusWithin = _a[1];
  var _b = options || {}, onFocus = _b.onFocus, onBlur = _b.onBlur, onChange = _b.onChange;
  useEventListener_default("focusin", function(e) {
    if (!isFocusWithin) {
      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);
      onChange === null || onChange === void 0 ? void 0 : onChange(true);
      setIsFocusWithin(true);
    }
  }, {
    target
  });
  useEventListener_default("focusout", function(e) {
    var _a2, _b2;
    if (isFocusWithin && !((_b2 = (_a2 = e.currentTarget) === null || _a2 === void 0 ? void 0 : _a2.contains) === null || _b2 === void 0 ? void 0 : _b2.call(_a2, e.relatedTarget))) {
      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);
      onChange === null || onChange === void 0 ? void 0 : onChange(false);
      setIsFocusWithin(false);
    }
  }, {
    target
  });
  return isFocusWithin;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useFullscreen/index.js
var import_react43 = __toESM(require_react());
var import_screenfull = __toESM(require_screenfull());
var useFullscreen = function(target, options) {
  var _a = options || {}, onExit = _a.onExit, onEnter = _a.onEnter, _b = _a.pageFullscreen, pageFullscreen = _b === void 0 ? false : _b;
  var _c = isBoolean(pageFullscreen) || !pageFullscreen ? {} : pageFullscreen, _d = _c.className, className = _d === void 0 ? "ahooks-page-fullscreen" : _d, _e = _c.zIndex, zIndex = _e === void 0 ? 999999 : _e;
  var onExitRef = useLatest_default(onExit);
  var onEnterRef = useLatest_default(onEnter);
  var _f = __read((0, import_react43.useState)(getIsFullscreen), 2), state = _f[0], setState = _f[1];
  var stateRef = (0, import_react43.useRef)(getIsFullscreen());
  function getIsFullscreen() {
    return import_screenfull.default.isEnabled && !!import_screenfull.default.element && import_screenfull.default.element === getTargetElement(target);
  }
  var invokeCallback = function(fullscreen) {
    var _a2, _b2;
    if (fullscreen) {
      (_a2 = onEnterRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onEnterRef);
    } else {
      (_b2 = onExitRef.current) === null || _b2 === void 0 ? void 0 : _b2.call(onExitRef);
    }
  };
  var updateFullscreenState = function(fullscreen) {
    if (stateRef.current !== fullscreen) {
      invokeCallback(fullscreen);
      setState(fullscreen);
      stateRef.current = fullscreen;
    }
  };
  var onScreenfullChange = function() {
    var fullscreen = getIsFullscreen();
    updateFullscreenState(fullscreen);
  };
  var togglePageFullscreen = function(fullscreen) {
    var el = getTargetElement(target);
    if (!el) {
      return;
    }
    var styleElem = document.getElementById(className);
    if (fullscreen) {
      el.classList.add(className);
      if (!styleElem) {
        styleElem = document.createElement("style");
        styleElem.setAttribute("id", className);
        styleElem.textContent = "\n          .".concat(className, " {\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\n            width: 100% !important; height: 100% !important;\n            z-index: ").concat(zIndex, ";\n          }");
        el.appendChild(styleElem);
      }
    } else {
      el.classList.remove(className);
      if (styleElem) {
        styleElem.remove();
      }
    }
    updateFullscreenState(fullscreen);
  };
  var enterFullscreen = function() {
    var el = getTargetElement(target);
    if (!el) {
      return;
    }
    if (pageFullscreen) {
      togglePageFullscreen(true);
      return;
    }
    if (import_screenfull.default.isEnabled) {
      try {
        import_screenfull.default.request(el);
      } catch (error) {
        console.error(error);
      }
    }
  };
  var exitFullscreen = function() {
    var el = getTargetElement(target);
    if (!el) {
      return;
    }
    if (pageFullscreen) {
      togglePageFullscreen(false);
      return;
    }
    if (import_screenfull.default.isEnabled && import_screenfull.default.element === el) {
      import_screenfull.default.exit();
    }
  };
  var toggleFullscreen = function() {
    if (state) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  };
  (0, import_react43.useEffect)(function() {
    if (!import_screenfull.default.isEnabled || pageFullscreen) {
      return;
    }
    import_screenfull.default.on("change", onScreenfullChange);
    return function() {
      import_screenfull.default.off("change", onScreenfullChange);
    };
  }, []);
  return [state, {
    enterFullscreen: useMemoizedFn_default(enterFullscreen),
    exitFullscreen: useMemoizedFn_default(exitFullscreen),
    toggleFullscreen: useMemoizedFn_default(toggleFullscreen),
    isEnabled: import_screenfull.default.isEnabled
  }];
};
var useFullscreen_default = useFullscreen;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useFusionTable/fusionAdapter.js
var fieldAdapter = function(field) {
  return {
    getFieldInstance: function(name) {
      return field.getNames().includes(name);
    },
    setFieldsValue: field.setValues,
    getFieldsValue: field.getValues,
    resetFields: field.resetToDefault,
    validateFields: function(fields, callback) {
      field.validate(fields, callback);
    }
  };
};
var resultAdapter = function(result) {
  var tableProps = {
    dataSource: result.tableProps.dataSource,
    loading: result.tableProps.loading,
    onSort: function(dataIndex, order) {
      var _a;
      result.tableProps.onChange({
        current: result.pagination.current,
        pageSize: result.pagination.pageSize
      }, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.filters, {
        field: dataIndex,
        order
      });
    },
    onFilter: function(filterParams) {
      var _a;
      result.tableProps.onChange({
        current: result.pagination.current,
        pageSize: result.pagination.pageSize
      }, filterParams, (_a = result.params[0]) === null || _a === void 0 ? void 0 : _a.sorter);
    }
  };
  var paginationProps = {
    onChange: result.pagination.changeCurrent,
    onPageSizeChange: result.pagination.changePageSize,
    current: result.pagination.current,
    pageSize: result.pagination.pageSize,
    total: result.pagination.total
  };
  return __assign(__assign({}, result), {
    tableProps,
    paginationProps
  });
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useFusionTable/index.js
var useFusionTable = function(service, options) {
  if (options === void 0) {
    options = {};
  }
  var ret = useAntdTable_default(service, __assign(__assign({}, options), {
    form: options.field ? fieldAdapter(options.field) : void 0
  }));
  return resultAdapter(ret);
};
var useFusionTable_default = useFusionTable;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useGetState/index.js
var import_react44 = __toESM(require_react());
function useGetState(initialState) {
  var _a = __read((0, import_react44.useState)(initialState), 2), state = _a[0], setState = _a[1];
  var stateRef = useLatest_default(state);
  var getState = (0, import_react44.useCallback)(function() {
    return stateRef.current;
  }, []);
  return [state, setState, getState];
}
var useGetState_default = useGetState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useHistoryTravel/index.js
var import_react45 = __toESM(require_react());
var dumpIndex = function(step, arr) {
  var index = step > 0 ? step - 1 : arr.length + step;
  if (index >= arr.length - 1) {
    index = arr.length - 1;
  }
  if (index < 0) {
    index = 0;
  }
  return index;
};
var split = function(step, targetArr) {
  var index = dumpIndex(step, targetArr);
  return {
    _current: targetArr[index],
    _before: targetArr.slice(0, index),
    _after: targetArr.slice(index + 1)
  };
};
function useHistoryTravel(initialValue, maxLength) {
  if (maxLength === void 0) {
    maxLength = 0;
  }
  var _a = __read((0, import_react45.useState)({
    present: initialValue,
    past: [],
    future: []
  }), 2), history = _a[0], setHistory = _a[1];
  var present = history.present, past = history.past, future = history.future;
  var initialValueRef = (0, import_react45.useRef)(initialValue);
  var reset = function() {
    var params = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      params[_i] = arguments[_i];
    }
    var _initial = params.length > 0 ? params[0] : initialValueRef.current;
    initialValueRef.current = _initial;
    setHistory({
      present: _initial,
      future: [],
      past: []
    });
  };
  var updateValue = function(val) {
    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);
    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);
    if (maxLengthNum > 0 && _past.length > maxLengthNum) {
      _past.splice(0, 1);
    }
    setHistory({
      present: val,
      future: [],
      past: _past
    });
  };
  var _forward = function(step) {
    if (step === void 0) {
      step = 1;
    }
    if (future.length === 0) {
      return;
    }
    var _a2 = split(step, future), _before = _a2._before, _current = _a2._current, _after = _a2._after;
    setHistory({
      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),
      present: _current,
      future: _after
    });
  };
  var _backward = function(step) {
    if (step === void 0) {
      step = -1;
    }
    if (past.length === 0) {
      return;
    }
    var _a2 = split(step, past), _before = _a2._before, _current = _a2._current, _after = _a2._after;
    setHistory({
      past: _before,
      present: _current,
      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)
    });
  };
  var go = function(step) {
    var stepNum = isNumber(step) ? step : Number(step);
    if (stepNum === 0) {
      return;
    }
    if (stepNum > 0) {
      return _forward(stepNum);
    }
    _backward(stepNum);
  };
  return {
    value: present,
    backLength: past.length,
    forwardLength: future.length,
    setValue: useMemoizedFn_default(updateValue),
    go: useMemoizedFn_default(go),
    back: useMemoizedFn_default(function() {
      go(-1);
    }),
    forward: useMemoizedFn_default(function() {
      go(1);
    }),
    reset: useMemoizedFn_default(reset)
  };
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useHover/index.js
var useHover_default = function(target, options) {
  var _a = options || {}, onEnter = _a.onEnter, onLeave = _a.onLeave, onChange = _a.onChange;
  var _b = __read(useBoolean(false), 2), state = _b[0], _c = _b[1], setTrue = _c.setTrue, setFalse = _c.setFalse;
  useEventListener_default("mouseenter", function() {
    onEnter === null || onEnter === void 0 ? void 0 : onEnter();
    setTrue();
    onChange === null || onChange === void 0 ? void 0 : onChange(true);
  }, {
    target
  });
  useEventListener_default("mouseleave", function() {
    onLeave === null || onLeave === void 0 ? void 0 : onLeave();
    setFalse();
    onChange === null || onChange === void 0 ? void 0 : onChange(false);
  }, {
    target
  });
  return state;
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useInfiniteScroll/index.js
var import_react46 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/rect.js
var getScrollTop = function(el) {
  if (el === document || el === document.documentElement || el === document.body) {
    return Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop);
  }
  return el.scrollTop;
};
var getScrollHeight = function(el) {
  return el.scrollHeight || Math.max(document.documentElement.scrollHeight, document.body.scrollHeight);
};
var getClientHeight = function(el) {
  return el.clientHeight || Math.max(document.documentElement.clientHeight, document.body.clientHeight);
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useInfiniteScroll/index.js
var useInfiniteScroll = function(service, options) {
  if (options === void 0) {
    options = {};
  }
  var target = options.target, isNoMore = options.isNoMore, _a = options.threshold, threshold = _a === void 0 ? 100 : _a, _b = options.direction, direction = _b === void 0 ? "bottom" : _b, _c = options.reloadDeps, reloadDeps = _c === void 0 ? [] : _c, manual = options.manual, onBefore = options.onBefore, onSuccess = options.onSuccess, onError = options.onError, onFinally = options.onFinally;
  var _d = __read((0, import_react46.useState)(), 2), finalData = _d[0], setFinalData = _d[1];
  var _e = __read((0, import_react46.useState)(false), 2), loadingMore = _e[0], setLoadingMore = _e[1];
  var isScrollToTop = direction === "top";
  var lastScrollTop = (0, import_react46.useRef)();
  var scrollBottom = (0, import_react46.useRef)(0);
  var noMore = (0, import_react46.useMemo)(function() {
    if (!isNoMore) return false;
    return isNoMore(finalData);
  }, [finalData]);
  var _f = useRequest_default2(function(lastData) {
    return __awaiter(void 0, void 0, void 0, function() {
      var currentData;
      var _a2, _b2, _c2;
      return __generator(this, function(_d2) {
        switch (_d2.label) {
          case 0:
            return [4, service(lastData)];
          case 1:
            currentData = _d2.sent();
            if (!lastData) {
              setFinalData(__assign(__assign({}, currentData), {
                list: __spreadArray([], __read((_a2 = currentData.list) !== null && _a2 !== void 0 ? _a2 : []), false)
              }));
            } else {
              setFinalData(__assign(__assign({}, currentData), {
                list: isScrollToTop ? __spreadArray(__spreadArray([], __read(currentData.list), false), __read((_b2 = lastData.list) !== null && _b2 !== void 0 ? _b2 : []), false) : __spreadArray(__spreadArray([], __read((_c2 = lastData.list) !== null && _c2 !== void 0 ? _c2 : []), false), __read(currentData.list), false)
              }));
            }
            return [2, currentData];
        }
      });
    });
  }, {
    manual,
    onFinally: function(_, d, e) {
      setLoadingMore(false);
      onFinally === null || onFinally === void 0 ? void 0 : onFinally(d, e);
    },
    onBefore: function() {
      return onBefore === null || onBefore === void 0 ? void 0 : onBefore();
    },
    onSuccess: function(d) {
      setTimeout(function() {
        if (isScrollToTop) {
          var el = getTargetElement(target);
          el = el === document ? document.documentElement : el;
          if (el) {
            var scrollHeight = getScrollHeight(el);
            el.scrollTo(0, scrollHeight - scrollBottom.current);
          }
        } else {
          scrollMethod();
        }
      });
      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(d);
    },
    onError: function(e) {
      return onError === null || onError === void 0 ? void 0 : onError(e);
    }
  }), loading = _f.loading, error = _f.error, run = _f.run, runAsync = _f.runAsync, cancel = _f.cancel;
  var loadMore = useMemoizedFn_default(function() {
    if (noMore) return;
    setLoadingMore(true);
    run(finalData);
  });
  var loadMoreAsync = useMemoizedFn_default(function() {
    if (noMore) return Promise.reject();
    setLoadingMore(true);
    return runAsync(finalData);
  });
  var reload = function() {
    setLoadingMore(false);
    return run();
  };
  var reloadAsync = function() {
    setLoadingMore(false);
    return runAsync();
  };
  var scrollMethod = function() {
    var el = getTargetElement(target);
    if (!el) return;
    var targetEl = el === document ? document.documentElement : el;
    var scrollTop = getScrollTop(targetEl);
    var scrollHeight = getScrollHeight(targetEl);
    var clientHeight = getClientHeight(targetEl);
    if (isScrollToTop) {
      if (lastScrollTop.current !== void 0 && lastScrollTop.current > scrollTop && scrollTop <= threshold) {
        loadMore();
      }
      lastScrollTop.current = scrollTop;
      scrollBottom.current = scrollHeight - scrollTop;
    } else if (scrollHeight - scrollTop <= clientHeight + threshold) {
      loadMore();
    }
  };
  useEventListener_default("scroll", function() {
    if (loading || loadingMore) {
      return;
    }
    scrollMethod();
  }, {
    target
  });
  useUpdateEffect_default(function() {
    run();
  }, __spreadArray([], __read(reloadDeps), false));
  return {
    data: finalData,
    loading: !loadingMore && loading,
    error,
    loadingMore,
    noMore,
    loadMore,
    loadMoreAsync,
    reload: useMemoizedFn_default(reload),
    reloadAsync: useMemoizedFn_default(reloadAsync),
    mutate: setFinalData,
    cancel
  };
};
var useInfiniteScroll_default = useInfiniteScroll;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useInterval/index.js
var import_react47 = __toESM(require_react());
var useInterval = function(fn, delay, options) {
  if (options === void 0) {
    options = {};
  }
  var timerCallback = useMemoizedFn_default(fn);
  var timerRef = (0, import_react47.useRef)(null);
  var clear = (0, import_react47.useCallback)(function() {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  }, []);
  (0, import_react47.useEffect)(function() {
    if (!isNumber(delay) || delay < 0) {
      return;
    }
    if (options.immediate) {
      timerCallback();
    }
    timerRef.current = setInterval(timerCallback, delay);
    return clear;
  }, [delay, options.immediate]);
  return clear;
};
var useInterval_default = useInterval;

// node_modules/.pnpm/intersection-observer@0.12.2/node_modules/intersection-observer/intersection-observer.js
(function() {
  "use strict";
  if (typeof window !== "object") {
    return;
  }
  if ("IntersectionObserver" in window && "IntersectionObserverEntry" in window && "intersectionRatio" in window.IntersectionObserverEntry.prototype) {
    if (!("isIntersecting" in window.IntersectionObserverEntry.prototype)) {
      Object.defineProperty(
        window.IntersectionObserverEntry.prototype,
        "isIntersecting",
        {
          get: function() {
            return this.intersectionRatio > 0;
          }
        }
      );
    }
    return;
  }
  function getFrameElement(doc) {
    try {
      return doc.defaultView && doc.defaultView.frameElement || null;
    } catch (e) {
      return null;
    }
  }
  var document2 = function(startDoc) {
    var doc = startDoc;
    var frame = getFrameElement(doc);
    while (frame) {
      doc = frame.ownerDocument;
      frame = getFrameElement(doc);
    }
    return doc;
  }(window.document);
  var registry = [];
  var crossOriginUpdater = null;
  var crossOriginRect = null;
  function IntersectionObserverEntry(entry) {
    this.time = entry.time;
    this.target = entry.target;
    this.rootBounds = ensureDOMRect(entry.rootBounds);
    this.boundingClientRect = ensureDOMRect(entry.boundingClientRect);
    this.intersectionRect = ensureDOMRect(entry.intersectionRect || getEmptyRect());
    this.isIntersecting = !!entry.intersectionRect;
    var targetRect = this.boundingClientRect;
    var targetArea = targetRect.width * targetRect.height;
    var intersectionRect = this.intersectionRect;
    var intersectionArea = intersectionRect.width * intersectionRect.height;
    if (targetArea) {
      this.intersectionRatio = Number((intersectionArea / targetArea).toFixed(4));
    } else {
      this.intersectionRatio = this.isIntersecting ? 1 : 0;
    }
  }
  function IntersectionObserver2(callback, opt_options) {
    var options = opt_options || {};
    if (typeof callback != "function") {
      throw new Error("callback must be a function");
    }
    if (options.root && options.root.nodeType != 1 && options.root.nodeType != 9) {
      throw new Error("root must be a Document or Element");
    }
    this._checkForIntersections = throttle3(
      this._checkForIntersections.bind(this),
      this.THROTTLE_TIMEOUT
    );
    this._callback = callback;
    this._observationTargets = [];
    this._queuedEntries = [];
    this._rootMarginValues = this._parseRootMargin(options.rootMargin);
    this.thresholds = this._initThresholds(options.threshold);
    this.root = options.root || null;
    this.rootMargin = this._rootMarginValues.map(function(margin) {
      return margin.value + margin.unit;
    }).join(" ");
    this._monitoringDocuments = [];
    this._monitoringUnsubscribes = [];
  }
  IntersectionObserver2.prototype.THROTTLE_TIMEOUT = 100;
  IntersectionObserver2.prototype.POLL_INTERVAL = null;
  IntersectionObserver2.prototype.USE_MUTATION_OBSERVER = true;
  IntersectionObserver2._setupCrossOriginUpdater = function() {
    if (!crossOriginUpdater) {
      crossOriginUpdater = function(boundingClientRect, intersectionRect) {
        if (!boundingClientRect || !intersectionRect) {
          crossOriginRect = getEmptyRect();
        } else {
          crossOriginRect = convertFromParentRect(boundingClientRect, intersectionRect);
        }
        registry.forEach(function(observer2) {
          observer2._checkForIntersections();
        });
      };
    }
    return crossOriginUpdater;
  };
  IntersectionObserver2._resetCrossOriginUpdater = function() {
    crossOriginUpdater = null;
    crossOriginRect = null;
  };
  IntersectionObserver2.prototype.observe = function(target) {
    var isTargetAlreadyObserved = this._observationTargets.some(function(item) {
      return item.element == target;
    });
    if (isTargetAlreadyObserved) {
      return;
    }
    if (!(target && target.nodeType == 1)) {
      throw new Error("target must be an Element");
    }
    this._registerInstance();
    this._observationTargets.push({ element: target, entry: null });
    this._monitorIntersections(target.ownerDocument);
    this._checkForIntersections();
  };
  IntersectionObserver2.prototype.unobserve = function(target) {
    this._observationTargets = this._observationTargets.filter(function(item) {
      return item.element != target;
    });
    this._unmonitorIntersections(target.ownerDocument);
    if (this._observationTargets.length == 0) {
      this._unregisterInstance();
    }
  };
  IntersectionObserver2.prototype.disconnect = function() {
    this._observationTargets = [];
    this._unmonitorAllIntersections();
    this._unregisterInstance();
  };
  IntersectionObserver2.prototype.takeRecords = function() {
    var records = this._queuedEntries.slice();
    this._queuedEntries = [];
    return records;
  };
  IntersectionObserver2.prototype._initThresholds = function(opt_threshold) {
    var threshold = opt_threshold || [0];
    if (!Array.isArray(threshold)) threshold = [threshold];
    return threshold.sort().filter(function(t, i, a) {
      if (typeof t != "number" || isNaN(t) || t < 0 || t > 1) {
        throw new Error("threshold must be a number between 0 and 1 inclusively");
      }
      return t !== a[i - 1];
    });
  };
  IntersectionObserver2.prototype._parseRootMargin = function(opt_rootMargin) {
    var marginString = opt_rootMargin || "0px";
    var margins = marginString.split(/\s+/).map(function(margin) {
      var parts = /^(-?\d*\.?\d+)(px|%)$/.exec(margin);
      if (!parts) {
        throw new Error("rootMargin must be specified in pixels or percent");
      }
      return { value: parseFloat(parts[1]), unit: parts[2] };
    });
    margins[1] = margins[1] || margins[0];
    margins[2] = margins[2] || margins[0];
    margins[3] = margins[3] || margins[1];
    return margins;
  };
  IntersectionObserver2.prototype._monitorIntersections = function(doc) {
    var win = doc.defaultView;
    if (!win) {
      return;
    }
    if (this._monitoringDocuments.indexOf(doc) != -1) {
      return;
    }
    var callback = this._checkForIntersections;
    var monitoringInterval = null;
    var domObserver = null;
    if (this.POLL_INTERVAL) {
      monitoringInterval = win.setInterval(callback, this.POLL_INTERVAL);
    } else {
      addEvent(win, "resize", callback, true);
      addEvent(doc, "scroll", callback, true);
      if (this.USE_MUTATION_OBSERVER && "MutationObserver" in win) {
        domObserver = new win.MutationObserver(callback);
        domObserver.observe(doc, {
          attributes: true,
          childList: true,
          characterData: true,
          subtree: true
        });
      }
    }
    this._monitoringDocuments.push(doc);
    this._monitoringUnsubscribes.push(function() {
      var win2 = doc.defaultView;
      if (win2) {
        if (monitoringInterval) {
          win2.clearInterval(monitoringInterval);
        }
        removeEvent(win2, "resize", callback, true);
      }
      removeEvent(doc, "scroll", callback, true);
      if (domObserver) {
        domObserver.disconnect();
      }
    });
    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document2;
    if (doc != rootDoc) {
      var frame = getFrameElement(doc);
      if (frame) {
        this._monitorIntersections(frame.ownerDocument);
      }
    }
  };
  IntersectionObserver2.prototype._unmonitorIntersections = function(doc) {
    var index = this._monitoringDocuments.indexOf(doc);
    if (index == -1) {
      return;
    }
    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document2;
    var hasDependentTargets = this._observationTargets.some(function(item) {
      var itemDoc = item.element.ownerDocument;
      if (itemDoc == doc) {
        return true;
      }
      while (itemDoc && itemDoc != rootDoc) {
        var frame2 = getFrameElement(itemDoc);
        itemDoc = frame2 && frame2.ownerDocument;
        if (itemDoc == doc) {
          return true;
        }
      }
      return false;
    });
    if (hasDependentTargets) {
      return;
    }
    var unsubscribe = this._monitoringUnsubscribes[index];
    this._monitoringDocuments.splice(index, 1);
    this._monitoringUnsubscribes.splice(index, 1);
    unsubscribe();
    if (doc != rootDoc) {
      var frame = getFrameElement(doc);
      if (frame) {
        this._unmonitorIntersections(frame.ownerDocument);
      }
    }
  };
  IntersectionObserver2.prototype._unmonitorAllIntersections = function() {
    var unsubscribes = this._monitoringUnsubscribes.slice(0);
    this._monitoringDocuments.length = 0;
    this._monitoringUnsubscribes.length = 0;
    for (var i = 0; i < unsubscribes.length; i++) {
      unsubscribes[i]();
    }
  };
  IntersectionObserver2.prototype._checkForIntersections = function() {
    if (!this.root && crossOriginUpdater && !crossOriginRect) {
      return;
    }
    var rootIsInDom = this._rootIsInDom();
    var rootRect = rootIsInDom ? this._getRootRect() : getEmptyRect();
    this._observationTargets.forEach(function(item) {
      var target = item.element;
      var targetRect = getBoundingClientRect(target);
      var rootContainsTarget = this._rootContainsTarget(target);
      var oldEntry = item.entry;
      var intersectionRect = rootIsInDom && rootContainsTarget && this._computeTargetAndRootIntersection(target, targetRect, rootRect);
      var rootBounds = null;
      if (!this._rootContainsTarget(target)) {
        rootBounds = getEmptyRect();
      } else if (!crossOriginUpdater || this.root) {
        rootBounds = rootRect;
      }
      var newEntry = item.entry = new IntersectionObserverEntry({
        time: now(),
        target,
        boundingClientRect: targetRect,
        rootBounds,
        intersectionRect
      });
      if (!oldEntry) {
        this._queuedEntries.push(newEntry);
      } else if (rootIsInDom && rootContainsTarget) {
        if (this._hasCrossedThreshold(oldEntry, newEntry)) {
          this._queuedEntries.push(newEntry);
        }
      } else {
        if (oldEntry && oldEntry.isIntersecting) {
          this._queuedEntries.push(newEntry);
        }
      }
    }, this);
    if (this._queuedEntries.length) {
      this._callback(this.takeRecords(), this);
    }
  };
  IntersectionObserver2.prototype._computeTargetAndRootIntersection = function(target, targetRect, rootRect) {
    if (window.getComputedStyle(target).display == "none") return;
    var intersectionRect = targetRect;
    var parent = getParentNode(target);
    var atRoot = false;
    while (!atRoot && parent) {
      var parentRect = null;
      var parentComputedStyle = parent.nodeType == 1 ? window.getComputedStyle(parent) : {};
      if (parentComputedStyle.display == "none") return null;
      if (parent == this.root || parent.nodeType == /* DOCUMENT */
      9) {
        atRoot = true;
        if (parent == this.root || parent == document2) {
          if (crossOriginUpdater && !this.root) {
            if (!crossOriginRect || crossOriginRect.width == 0 && crossOriginRect.height == 0) {
              parent = null;
              parentRect = null;
              intersectionRect = null;
            } else {
              parentRect = crossOriginRect;
            }
          } else {
            parentRect = rootRect;
          }
        } else {
          var frame = getParentNode(parent);
          var frameRect = frame && getBoundingClientRect(frame);
          var frameIntersect = frame && this._computeTargetAndRootIntersection(frame, frameRect, rootRect);
          if (frameRect && frameIntersect) {
            parent = frame;
            parentRect = convertFromParentRect(frameRect, frameIntersect);
          } else {
            parent = null;
            intersectionRect = null;
          }
        }
      } else {
        var doc = parent.ownerDocument;
        if (parent != doc.body && parent != doc.documentElement && parentComputedStyle.overflow != "visible") {
          parentRect = getBoundingClientRect(parent);
        }
      }
      if (parentRect) {
        intersectionRect = computeRectIntersection(parentRect, intersectionRect);
      }
      if (!intersectionRect) break;
      parent = parent && getParentNode(parent);
    }
    return intersectionRect;
  };
  IntersectionObserver2.prototype._getRootRect = function() {
    var rootRect;
    if (this.root && !isDoc(this.root)) {
      rootRect = getBoundingClientRect(this.root);
    } else {
      var doc = isDoc(this.root) ? this.root : document2;
      var html = doc.documentElement;
      var body = doc.body;
      rootRect = {
        top: 0,
        left: 0,
        right: html.clientWidth || body.clientWidth,
        width: html.clientWidth || body.clientWidth,
        bottom: html.clientHeight || body.clientHeight,
        height: html.clientHeight || body.clientHeight
      };
    }
    return this._expandRectByRootMargin(rootRect);
  };
  IntersectionObserver2.prototype._expandRectByRootMargin = function(rect) {
    var margins = this._rootMarginValues.map(function(margin, i) {
      return margin.unit == "px" ? margin.value : margin.value * (i % 2 ? rect.width : rect.height) / 100;
    });
    var newRect = {
      top: rect.top - margins[0],
      right: rect.right + margins[1],
      bottom: rect.bottom + margins[2],
      left: rect.left - margins[3]
    };
    newRect.width = newRect.right - newRect.left;
    newRect.height = newRect.bottom - newRect.top;
    return newRect;
  };
  IntersectionObserver2.prototype._hasCrossedThreshold = function(oldEntry, newEntry) {
    var oldRatio = oldEntry && oldEntry.isIntersecting ? oldEntry.intersectionRatio || 0 : -1;
    var newRatio = newEntry.isIntersecting ? newEntry.intersectionRatio || 0 : -1;
    if (oldRatio === newRatio) return;
    for (var i = 0; i < this.thresholds.length; i++) {
      var threshold = this.thresholds[i];
      if (threshold == oldRatio || threshold == newRatio || threshold < oldRatio !== threshold < newRatio) {
        return true;
      }
    }
  };
  IntersectionObserver2.prototype._rootIsInDom = function() {
    return !this.root || containsDeep(document2, this.root);
  };
  IntersectionObserver2.prototype._rootContainsTarget = function(target) {
    var rootDoc = this.root && (this.root.ownerDocument || this.root) || document2;
    return containsDeep(rootDoc, target) && (!this.root || rootDoc == target.ownerDocument);
  };
  IntersectionObserver2.prototype._registerInstance = function() {
    if (registry.indexOf(this) < 0) {
      registry.push(this);
    }
  };
  IntersectionObserver2.prototype._unregisterInstance = function() {
    var index = registry.indexOf(this);
    if (index != -1) registry.splice(index, 1);
  };
  function now() {
    return window.performance && performance.now && performance.now();
  }
  function throttle3(fn, timeout) {
    var timer = null;
    return function() {
      if (!timer) {
        timer = setTimeout(function() {
          fn();
          timer = null;
        }, timeout);
      }
    };
  }
  function addEvent(node, event, fn, opt_useCapture) {
    if (typeof node.addEventListener == "function") {
      node.addEventListener(event, fn, opt_useCapture || false);
    } else if (typeof node.attachEvent == "function") {
      node.attachEvent("on" + event, fn);
    }
  }
  function removeEvent(node, event, fn, opt_useCapture) {
    if (typeof node.removeEventListener == "function") {
      node.removeEventListener(event, fn, opt_useCapture || false);
    } else if (typeof node.detachEvent == "function") {
      node.detachEvent("on" + event, fn);
    }
  }
  function computeRectIntersection(rect1, rect2) {
    var top = Math.max(rect1.top, rect2.top);
    var bottom = Math.min(rect1.bottom, rect2.bottom);
    var left = Math.max(rect1.left, rect2.left);
    var right = Math.min(rect1.right, rect2.right);
    var width = right - left;
    var height = bottom - top;
    return width >= 0 && height >= 0 && {
      top,
      bottom,
      left,
      right,
      width,
      height
    } || null;
  }
  function getBoundingClientRect(el) {
    var rect;
    try {
      rect = el.getBoundingClientRect();
    } catch (err) {
    }
    if (!rect) return getEmptyRect();
    if (!(rect.width && rect.height)) {
      rect = {
        top: rect.top,
        right: rect.right,
        bottom: rect.bottom,
        left: rect.left,
        width: rect.right - rect.left,
        height: rect.bottom - rect.top
      };
    }
    return rect;
  }
  function getEmptyRect() {
    return {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      width: 0,
      height: 0
    };
  }
  function ensureDOMRect(rect) {
    if (!rect || "x" in rect) {
      return rect;
    }
    return {
      top: rect.top,
      y: rect.top,
      bottom: rect.bottom,
      left: rect.left,
      x: rect.left,
      right: rect.right,
      width: rect.width,
      height: rect.height
    };
  }
  function convertFromParentRect(parentBoundingRect, parentIntersectionRect) {
    var top = parentIntersectionRect.top - parentBoundingRect.top;
    var left = parentIntersectionRect.left - parentBoundingRect.left;
    return {
      top,
      left,
      height: parentIntersectionRect.height,
      width: parentIntersectionRect.width,
      bottom: top + parentIntersectionRect.height,
      right: left + parentIntersectionRect.width
    };
  }
  function containsDeep(parent, child) {
    var node = child;
    while (node) {
      if (node == parent) return true;
      node = getParentNode(node);
    }
    return false;
  }
  function getParentNode(node) {
    var parent = node.parentNode;
    if (node.nodeType == /* DOCUMENT */
    9 && node != document2) {
      return getFrameElement(node);
    }
    if (parent && parent.assignedSlot) {
      parent = parent.assignedSlot.parentNode;
    }
    if (parent && parent.nodeType == 11 && parent.host) {
      return parent.host;
    }
    return parent;
  }
  function isDoc(node) {
    return node && node.nodeType === 9;
  }
  window.IntersectionObserver = IntersectionObserver2;
  window.IntersectionObserverEntry = IntersectionObserverEntry;
})();

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useInViewport/index.js
var import_react48 = __toESM(require_react());
function useInViewport(target, options) {
  var _a = options || {}, callback = _a.callback, option = __rest(_a, ["callback"]);
  var _b = __read((0, import_react48.useState)(), 2), state = _b[0], setState = _b[1];
  var _c = __read((0, import_react48.useState)(), 2), ratio = _c[0], setRatio = _c[1];
  useEffectWithTarget_default(function() {
    var targets = Array.isArray(target) ? target : [target];
    var els = targets.map(function(element) {
      return getTargetElement(element);
    }).filter(Boolean);
    if (!els.length) {
      return;
    }
    var observer2 = new IntersectionObserver(function(entries) {
      var e_1, _a2;
      try {
        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {
          var entry = entries_1_1.value;
          setRatio(entry.intersectionRatio);
          setState(entry.isIntersecting);
          callback === null || callback === void 0 ? void 0 : callback(entry);
        }
      } catch (e_1_1) {
        e_1 = {
          error: e_1_1
        };
      } finally {
        try {
          if (entries_1_1 && !entries_1_1.done && (_a2 = entries_1.return)) _a2.call(entries_1);
        } finally {
          if (e_1) throw e_1.error;
        }
      }
    }, __assign(__assign({}, option), {
      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)
    }));
    els.forEach(function(el) {
      return observer2.observe(el);
    });
    return function() {
      observer2.disconnect();
    };
  }, [options === null || options === void 0 ? void 0 : options.rootMargin, options === null || options === void 0 ? void 0 : options.threshold, callback], target);
  return [state, ratio];
}
var useInViewport_default = useInViewport;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js
var import_react49 = __toESM(require_react());
var useIsomorphicLayoutEffect = isBrowser_default ? import_react49.useLayoutEffect : import_react49.useEffect;
var useIsomorphicLayoutEffect_default = useIsomorphicLayoutEffect;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/useDeepCompareWithTarget.js
var import_react50 = __toESM(require_react());
var useDeepCompareEffectWithTarget = function(effect, deps, target) {
  var ref = (0, import_react50.useRef)();
  var signalRef = (0, import_react50.useRef)(0);
  if (!depsEqual(deps, ref.current)) {
    signalRef.current += 1;
  }
  ref.current = deps;
  useEffectWithTarget_default(effect, [signalRef.current], target);
};
var useDeepCompareWithTarget_default = useDeepCompareEffectWithTarget;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/isAppleDevice.js
var isAppleDevice = /(mac|iphone|ipod|ipad)/i.test(typeof navigator !== "undefined" ? navigator === null || navigator === void 0 ? void 0 : navigator.platform : "");
var isAppleDevice_default = isAppleDevice;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useKeyPress/index.js
var aliasKeyCodeMap = {
  "0": 48,
  "1": 49,
  "2": 50,
  "3": 51,
  "4": 52,
  "5": 53,
  "6": 54,
  "7": 55,
  "8": 56,
  "9": 57,
  backspace: 8,
  tab: 9,
  enter: 13,
  shift: 16,
  ctrl: 17,
  alt: 18,
  pausebreak: 19,
  capslock: 20,
  esc: 27,
  space: 32,
  pageup: 33,
  pagedown: 34,
  end: 35,
  home: 36,
  leftarrow: 37,
  uparrow: 38,
  rightarrow: 39,
  downarrow: 40,
  insert: 45,
  delete: 46,
  a: 65,
  b: 66,
  c: 67,
  d: 68,
  e: 69,
  f: 70,
  g: 71,
  h: 72,
  i: 73,
  j: 74,
  k: 75,
  l: 76,
  m: 77,
  n: 78,
  o: 79,
  p: 80,
  q: 81,
  r: 82,
  s: 83,
  t: 84,
  u: 85,
  v: 86,
  w: 87,
  x: 88,
  y: 89,
  z: 90,
  leftwindowkey: 91,
  rightwindowkey: 92,
  meta: isAppleDevice_default ? [91, 93] : [91, 92],
  selectkey: 93,
  numpad0: 96,
  numpad1: 97,
  numpad2: 98,
  numpad3: 99,
  numpad4: 100,
  numpad5: 101,
  numpad6: 102,
  numpad7: 103,
  numpad8: 104,
  numpad9: 105,
  multiply: 106,
  add: 107,
  subtract: 109,
  decimalpoint: 110,
  divide: 111,
  f1: 112,
  f2: 113,
  f3: 114,
  f4: 115,
  f5: 116,
  f6: 117,
  f7: 118,
  f8: 119,
  f9: 120,
  f10: 121,
  f11: 122,
  f12: 123,
  numlock: 144,
  scrolllock: 145,
  semicolon: 186,
  equalsign: 187,
  comma: 188,
  dash: 189,
  period: 190,
  forwardslash: 191,
  graveaccent: 192,
  openbracket: 219,
  backslash: 220,
  closebracket: 221,
  singlequote: 222
};
var modifierKey = {
  ctrl: function(event) {
    return event.ctrlKey;
  },
  shift: function(event) {
    return event.shiftKey;
  },
  alt: function(event) {
    return event.altKey;
  },
  meta: function(event) {
    if (event.type === "keyup") {
      return aliasKeyCodeMap.meta.includes(event.keyCode);
    }
    return event.metaKey;
  }
};
function isValidKeyType(value) {
  return isString(value) || isNumber(value);
}
function countKeyByEvent(event) {
  var countOfModifier = Object.keys(modifierKey).reduce(function(total, key) {
    if (modifierKey[key](event)) {
      return total + 1;
    }
    return total;
  }, 0);
  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;
}
function genFilterKey(event, keyFilter, exactMatch) {
  var e_1, _a;
  if (!event.key) {
    return false;
  }
  if (isNumber(keyFilter)) {
    return event.keyCode === keyFilter ? keyFilter : false;
  }
  var genArr = keyFilter.split(".");
  var genLen = 0;
  try {
    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {
      var key = genArr_1_1.value;
      var genModifier = modifierKey[key];
      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];
      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {
        genLen++;
      }
    }
  } catch (e_1_1) {
    e_1 = {
      error: e_1_1
    };
  } finally {
    try {
      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1.return)) _a.call(genArr_1);
    } finally {
      if (e_1) throw e_1.error;
    }
  }
  if (exactMatch) {
    return genLen === genArr.length && countKeyByEvent(event) === genArr.length ? keyFilter : false;
  }
  return genLen === genArr.length ? keyFilter : false;
}
function genKeyFormatter(keyFilter, exactMatch) {
  if (isFunction(keyFilter)) {
    return keyFilter;
  }
  if (isValidKeyType(keyFilter)) {
    return function(event) {
      return genFilterKey(event, keyFilter, exactMatch);
    };
  }
  if (Array.isArray(keyFilter)) {
    return function(event) {
      return keyFilter.find(function(item) {
        return genFilterKey(event, item, exactMatch);
      });
    };
  }
  return function() {
    return Boolean(keyFilter);
  };
}
var defaultEvents = ["keydown"];
function useKeyPress(keyFilter, eventHandler, option) {
  var _a = option || {}, _b = _a.events, events = _b === void 0 ? defaultEvents : _b, target = _a.target, _c = _a.exactMatch, exactMatch = _c === void 0 ? false : _c, _d = _a.useCapture, useCapture = _d === void 0 ? false : _d;
  var eventHandlerRef = useLatest_default(eventHandler);
  var keyFilterRef = useLatest_default(keyFilter);
  useDeepCompareWithTarget_default(function() {
    var e_2, _a2;
    var _b2;
    var el = getTargetElement(target, window);
    if (!el) {
      return;
    }
    var callbackHandler = function(event) {
      var _a3;
      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);
      var keyGuard = genGuard(event);
      var firedKey = isValidKeyType(keyGuard) ? keyGuard : event.key;
      if (keyGuard) {
        return (_a3 = eventHandlerRef.current) === null || _a3 === void 0 ? void 0 : _a3.call(eventHandlerRef, event, firedKey);
      }
    };
    try {
      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {
        var eventName = events_1_1.value;
        (_b2 = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b2 === void 0 ? void 0 : _b2.call(el, eventName, callbackHandler, useCapture);
      }
    } catch (e_2_1) {
      e_2 = {
        error: e_2_1
      };
    } finally {
      try {
        if (events_1_1 && !events_1_1.done && (_a2 = events_1.return)) _a2.call(events_1);
      } finally {
        if (e_2) throw e_2.error;
      }
    }
    return function() {
      var e_3, _a3;
      var _b3;
      try {
        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {
          var eventName2 = events_2_1.value;
          (_b3 = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b3 === void 0 ? void 0 : _b3.call(el, eventName2, callbackHandler, useCapture);
        }
      } catch (e_3_1) {
        e_3 = {
          error: e_3_1
        };
      } finally {
        try {
          if (events_2_1 && !events_2_1.done && (_a3 = events_2.return)) _a3.call(events_2);
        } finally {
          if (e_3) throw e_3.error;
        }
      }
    };
  }, [events], target);
}
var useKeyPress_default = useKeyPress;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/createUseStorageState/index.js
var import_react51 = __toESM(require_react());
var SYNC_STORAGE_EVENT_NAME = "AHOOKS_SYNC_STORAGE_EVENT_NAME";
function createUseStorageState(getStorage) {
  function useStorageState(key, options) {
    if (options === void 0) {
      options = {};
    }
    var storage;
    var _a = options.listenStorageChange, listenStorageChange = _a === void 0 ? false : _a, _b = options.onError, onError = _b === void 0 ? function(e) {
      console.error(e);
    } : _b;
    try {
      storage = getStorage();
    } catch (err) {
      onError(err);
    }
    var serializer = function(value) {
      if (options.serializer) {
        return options.serializer(value);
      }
      return JSON.stringify(value);
    };
    var deserializer = function(value) {
      if (options.deserializer) {
        return options.deserializer(value);
      }
      return JSON.parse(value);
    };
    function getStoredValue() {
      try {
        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);
        if (raw) {
          return deserializer(raw);
        }
      } catch (e) {
        onError(e);
      }
      if (isFunction(options.defaultValue)) {
        return options.defaultValue();
      }
      return options.defaultValue;
    }
    var _c = __read((0, import_react51.useState)(getStoredValue), 2), state = _c[0], setState = _c[1];
    useUpdateEffect_default(function() {
      setState(getStoredValue());
    }, [key]);
    var updateState = function(value) {
      var currentState = isFunction(value) ? value(state) : value;
      if (!listenStorageChange) {
        setState(currentState);
      }
      try {
        var newValue = void 0;
        var oldValue = storage === null || storage === void 0 ? void 0 : storage.getItem(key);
        if (isUndef(currentState)) {
          newValue = null;
          storage === null || storage === void 0 ? void 0 : storage.removeItem(key);
        } else {
          newValue = serializer(currentState);
          storage === null || storage === void 0 ? void 0 : storage.setItem(key, newValue);
        }
        dispatchEvent(
          // send custom event to communicate within same page
          // importantly this should not be a StorageEvent since those cannot
          // be constructed with a non-built-in storage area
          new CustomEvent(SYNC_STORAGE_EVENT_NAME, {
            detail: {
              key,
              newValue,
              oldValue,
              storageArea: storage
            }
          })
        );
      } catch (e) {
        onError(e);
      }
    };
    var syncState = function(event) {
      if (event.key !== key || event.storageArea !== storage) {
        return;
      }
      setState(getStoredValue());
    };
    var syncStateFromCustomEvent = function(event) {
      syncState(event.detail);
    };
    useEventListener_default("storage", syncState, {
      enable: listenStorageChange
    });
    useEventListener_default(SYNC_STORAGE_EVENT_NAME, syncStateFromCustomEvent, {
      enable: listenStorageChange
    });
    return [state, useMemoizedFn_default(updateState)];
  }
  return useStorageState;
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useLocalStorageState/index.js
var useLocalStorageState = createUseStorageState(function() {
  return isBrowser_default ? localStorage : void 0;
});
var useLocalStorageState_default = useLocalStorageState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useLockFn/index.js
var import_react52 = __toESM(require_react());
function useLockFn(fn) {
  var _this = this;
  var lockRef = (0, import_react52.useRef)(false);
  return (0, import_react52.useCallback)(function() {
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    return __awaiter(_this, void 0, void 0, function() {
      var ret, e_1;
      return __generator(this, function(_a) {
        switch (_a.label) {
          case 0:
            if (lockRef.current) return [
              2
              /*return*/
            ];
            lockRef.current = true;
            _a.label = 1;
          case 1:
            _a.trys.push([1, 3, 4, 5]);
            return [4, fn.apply(void 0, __spreadArray([], __read(args), false))];
          case 2:
            ret = _a.sent();
            return [2, ret];
          case 3:
            e_1 = _a.sent();
            throw e_1;
          case 4:
            lockRef.current = false;
            return [
              7
              /*endfinally*/
            ];
          case 5:
            return [
              2
              /*return*/
            ];
        }
      });
    });
  }, [fn]);
}
var useLockFn_default = useLockFn;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useLongPress/index.js
var import_react53 = __toESM(require_react());
var touchSupported = isBrowser_default && // @ts-ignore
("ontouchstart" in window || window.DocumentTouch && document instanceof DocumentTouch);
function useLongPress(onLongPress, target, _a) {
  var _b = _a === void 0 ? {} : _a, _c = _b.delay, delay = _c === void 0 ? 300 : _c, moveThreshold = _b.moveThreshold, onClick = _b.onClick, onLongPressEnd = _b.onLongPressEnd;
  var onLongPressRef = useLatest_default(onLongPress);
  var onClickRef = useLatest_default(onClick);
  var onLongPressEndRef = useLatest_default(onLongPressEnd);
  var timerRef = (0, import_react53.useRef)();
  var isTriggeredRef = (0, import_react53.useRef)(false);
  var pervPositionRef = (0, import_react53.useRef)({
    x: 0,
    y: 0
  });
  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);
  useEffectWithTarget_default(function() {
    var targetElement = getTargetElement(target);
    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {
      return;
    }
    var overThreshold = function(event) {
      var _a2 = getClientPosition(event), clientX = _a2.clientX, clientY = _a2.clientY;
      var offsetX = Math.abs(clientX - pervPositionRef.current.x);
      var offsetY = Math.abs(clientY - pervPositionRef.current.y);
      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);
    };
    function getClientPosition(event) {
      if ("TouchEvent" in window && event instanceof TouchEvent) {
        return {
          clientX: event.touches[0].clientX,
          clientY: event.touches[0].clientY
        };
      }
      if (event instanceof MouseEvent) {
        return {
          clientX: event.clientX,
          clientY: event.clientY
        };
      }
      console.warn("Unsupported event type");
      return {
        clientX: 0,
        clientY: 0
      };
    }
    var onStart = function(event) {
      if (hasMoveThreshold) {
        var _a2 = getClientPosition(event), clientX = _a2.clientX, clientY = _a2.clientY;
        pervPositionRef.current.x = clientX;
        pervPositionRef.current.y = clientY;
      }
      timerRef.current = setTimeout(function() {
        onLongPressRef.current(event);
        isTriggeredRef.current = true;
      }, delay);
    };
    var onMove = function(event) {
      if (timerRef.current && overThreshold(event)) {
        clearTimeout(timerRef.current);
        timerRef.current = void 0;
      }
    };
    var onEnd = function(event, shouldTriggerClick) {
      var _a2;
      if (shouldTriggerClick === void 0) {
        shouldTriggerClick = false;
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (isTriggeredRef.current) {
        (_a2 = onLongPressEndRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onLongPressEndRef, event);
      }
      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {
        onClickRef.current(event);
      }
      isTriggeredRef.current = false;
    };
    var onEndWithClick = function(event) {
      return onEnd(event, true);
    };
    if (!touchSupported) {
      targetElement.addEventListener("mousedown", onStart);
      targetElement.addEventListener("mouseup", onEndWithClick);
      targetElement.addEventListener("mouseleave", onEnd);
      if (hasMoveThreshold) targetElement.addEventListener("mousemove", onMove);
    } else {
      targetElement.addEventListener("touchstart", onStart);
      targetElement.addEventListener("touchend", onEndWithClick);
      if (hasMoveThreshold) targetElement.addEventListener("touchmove", onMove);
    }
    return function() {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        isTriggeredRef.current = false;
      }
      if (!touchSupported) {
        targetElement.removeEventListener("mousedown", onStart);
        targetElement.removeEventListener("mouseup", onEndWithClick);
        targetElement.removeEventListener("mouseleave", onEnd);
        if (hasMoveThreshold) targetElement.removeEventListener("mousemove", onMove);
      } else {
        targetElement.removeEventListener("touchstart", onStart);
        targetElement.removeEventListener("touchend", onEndWithClick);
        if (hasMoveThreshold) targetElement.removeEventListener("touchmove", onMove);
      }
    };
  }, [], target);
}
var useLongPress_default = useLongPress;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMap/index.js
var import_react54 = __toESM(require_react());
function useMap(initialValue) {
  var getInitValue = function() {
    return new Map(initialValue);
  };
  var _a = __read((0, import_react54.useState)(getInitValue), 2), map = _a[0], setMap = _a[1];
  var set = function(key, entry) {
    setMap(function(prev) {
      var temp = new Map(prev);
      temp.set(key, entry);
      return temp;
    });
  };
  var setAll = function(newMap) {
    setMap(new Map(newMap));
  };
  var remove = function(key) {
    setMap(function(prev) {
      var temp = new Map(prev);
      temp.delete(key);
      return temp;
    });
  };
  var reset = function() {
    return setMap(getInitValue());
  };
  var get = function(key) {
    return map.get(key);
  };
  return [map, {
    set: useMemoizedFn_default(set),
    setAll: useMemoizedFn_default(setAll),
    remove: useMemoizedFn_default(remove),
    reset: useMemoizedFn_default(reset),
    get: useMemoizedFn_default(get)
  }];
}
var useMap_default = useMap;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRafState/index.js
var import_react55 = __toESM(require_react());
function useRafState(initialState) {
  var ref = (0, import_react55.useRef)(0);
  var _a = __read((0, import_react55.useState)(initialState), 2), state = _a[0], setState = _a[1];
  var setRafState = (0, import_react55.useCallback)(function(value) {
    cancelAnimationFrame(ref.current);
    ref.current = requestAnimationFrame(function() {
      setState(value);
    });
  }, []);
  useUnmount_default(function() {
    cancelAnimationFrame(ref.current);
  });
  return [state, setRafState];
}
var useRafState_default = useRafState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMouse/index.js
var initState = {
  screenX: NaN,
  screenY: NaN,
  clientX: NaN,
  clientY: NaN,
  pageX: NaN,
  pageY: NaN,
  elementX: NaN,
  elementY: NaN,
  elementH: NaN,
  elementW: NaN,
  elementPosX: NaN,
  elementPosY: NaN
};
var useMouse_default = function(target) {
  var _a = __read(useRafState_default(initState), 2), state = _a[0], setState = _a[1];
  useEventListener_default("mousemove", function(event) {
    var screenX = event.screenX, screenY = event.screenY, clientX = event.clientX, clientY = event.clientY, pageX = event.pageX, pageY = event.pageY;
    var newState = {
      screenX,
      screenY,
      clientX,
      clientY,
      pageX,
      pageY,
      elementX: NaN,
      elementY: NaN,
      elementH: NaN,
      elementW: NaN,
      elementPosX: NaN,
      elementPosY: NaN
    };
    var targetElement = getTargetElement(target);
    if (targetElement) {
      var _a2 = targetElement.getBoundingClientRect(), left = _a2.left, top_1 = _a2.top, width = _a2.width, height = _a2.height;
      newState.elementPosX = left + window.pageXOffset;
      newState.elementPosY = top_1 + window.pageYOffset;
      newState.elementX = pageX - newState.elementPosX;
      newState.elementY = pageY - newState.elementPosY;
      newState.elementW = width;
      newState.elementH = height;
    }
    setState(newState);
  }, {
    target: function() {
      return document;
    }
  });
  return state;
};

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useNetwork/index.js
var import_react56 = __toESM(require_react());
var NetworkEventType;
(function(NetworkEventType2) {
  NetworkEventType2["ONLINE"] = "online";
  NetworkEventType2["OFFLINE"] = "offline";
  NetworkEventType2["CHANGE"] = "change";
})(NetworkEventType || (NetworkEventType = {}));
function getConnection() {
  var nav = navigator;
  if (!isObject(nav)) return null;
  return nav.connection || nav.mozConnection || nav.webkitConnection;
}
function getConnectionProperty() {
  var c = getConnection();
  if (!c) return {};
  return {
    rtt: c.rtt,
    type: c.type,
    saveData: c.saveData,
    downlink: c.downlink,
    downlinkMax: c.downlinkMax,
    effectiveType: c.effectiveType
  };
}
function useNetwork() {
  var _a = __read((0, import_react56.useState)(function() {
    return __assign({
      since: void 0,
      online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine
    }, getConnectionProperty());
  }), 2), state = _a[0], setState = _a[1];
  (0, import_react56.useEffect)(function() {
    var onOnline = function() {
      setState(function(prevState) {
        return __assign(__assign({}, prevState), {
          online: true,
          since: /* @__PURE__ */ new Date()
        });
      });
    };
    var onOffline = function() {
      setState(function(prevState) {
        return __assign(__assign({}, prevState), {
          online: false,
          since: /* @__PURE__ */ new Date()
        });
      });
    };
    var onConnectionChange = function() {
      setState(function(prevState) {
        return __assign(__assign({}, prevState), getConnectionProperty());
      });
    };
    window.addEventListener(NetworkEventType.ONLINE, onOnline);
    window.addEventListener(NetworkEventType.OFFLINE, onOffline);
    var connection = getConnection();
    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);
    return function() {
      window.removeEventListener(NetworkEventType.ONLINE, onOnline);
      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);
      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);
    };
  }, []);
  return state;
}
var useNetwork_default = useNetwork;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/usePrevious/index.js
var import_react57 = __toESM(require_react());
var defaultShouldUpdate = function(a, b) {
  return !Object.is(a, b);
};
function usePrevious(state, shouldUpdate) {
  if (shouldUpdate === void 0) {
    shouldUpdate = defaultShouldUpdate;
  }
  var prevRef = (0, import_react57.useRef)();
  var curRef = (0, import_react57.useRef)();
  if (shouldUpdate(curRef.current, state)) {
    prevRef.current = curRef.current;
    curRef.current = state;
  }
  return prevRef.current;
}
var usePrevious_default = usePrevious;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRafInterval/index.js
var import_react58 = __toESM(require_react());
var setRafInterval = function(callback, delay) {
  if (delay === void 0) {
    delay = 0;
  }
  if (typeof requestAnimationFrame === "undefined") {
    return {
      id: setInterval(callback, delay)
    };
  }
  var start = Date.now();
  var handle = {
    id: 0
  };
  var loop = function() {
    var current = Date.now();
    if (current - start >= delay) {
      callback();
      start = Date.now();
    }
    handle.id = requestAnimationFrame(loop);
  };
  handle.id = requestAnimationFrame(loop);
  return handle;
};
function cancelAnimationFrameIsNotDefined(t) {
  return typeof cancelAnimationFrame === "undefined";
}
var clearRafInterval = function(handle) {
  if (cancelAnimationFrameIsNotDefined(handle.id)) {
    return clearInterval(handle.id);
  }
  cancelAnimationFrame(handle.id);
};
function useRafInterval(fn, delay, options) {
  var immediate = options === null || options === void 0 ? void 0 : options.immediate;
  var fnRef = useLatest_default(fn);
  var timerRef = (0, import_react58.useRef)();
  var clear = (0, import_react58.useCallback)(function() {
    if (timerRef.current) {
      clearRafInterval(timerRef.current);
    }
  }, []);
  (0, import_react58.useEffect)(function() {
    if (!isNumber(delay) || delay < 0) {
      return;
    }
    if (immediate) {
      fnRef.current();
    }
    timerRef.current = setRafInterval(function() {
      fnRef.current();
    }, delay);
    return clear;
  }, [delay]);
  return clear;
}
var useRafInterval_default = useRafInterval;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useRafTimeout/index.js
var import_react59 = __toESM(require_react());
var setRafTimeout = function(callback, delay) {
  if (delay === void 0) {
    delay = 0;
  }
  if (typeof requestAnimationFrame === "undefined") {
    return {
      id: setTimeout(callback, delay)
    };
  }
  var handle = {
    id: 0
  };
  var startTime = (/* @__PURE__ */ new Date()).getTime();
  var loop = function() {
    var current = (/* @__PURE__ */ new Date()).getTime();
    if (current - startTime >= delay) {
      callback();
    } else {
      handle.id = requestAnimationFrame(loop);
    }
  };
  handle.id = requestAnimationFrame(loop);
  return handle;
};
function cancelAnimationFrameIsNotDefined2(t) {
  return typeof cancelAnimationFrame === "undefined";
}
var clearRafTimeout = function(handle) {
  if (cancelAnimationFrameIsNotDefined2(handle.id)) {
    return clearTimeout(handle.id);
  }
  cancelAnimationFrame(handle.id);
};
function useRafTimeout(fn, delay) {
  var fnRef = useLatest_default(fn);
  var timerRef = (0, import_react59.useRef)();
  var clear = (0, import_react59.useCallback)(function() {
    if (timerRef.current) {
      clearRafTimeout(timerRef.current);
    }
  }, []);
  (0, import_react59.useEffect)(function() {
    if (!isNumber(delay) || delay < 0) return;
    timerRef.current = setRafTimeout(function() {
      fnRef.current();
    }, delay);
    return clear;
  }, [delay]);
  return clear;
}
var useRafTimeout_default = useRafTimeout;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useReactive/index.js
var import_react60 = __toESM(require_react());
var import_isPlainObject = __toESM(require_isPlainObject());
var proxyMap = /* @__PURE__ */ new WeakMap();
var rawMap = /* @__PURE__ */ new WeakMap();
function observer(initialVal, cb) {
  var existingProxy = proxyMap.get(initialVal);
  if (existingProxy) {
    return existingProxy;
  }
  if (rawMap.has(initialVal)) {
    return initialVal;
  }
  var proxy = new Proxy(initialVal, {
    get: function(target, key, receiver) {
      var res = Reflect.get(target, key, receiver);
      var descriptor = Reflect.getOwnPropertyDescriptor(target, key);
      if (!(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable) && !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.writable)) {
        return res;
      }
      return (0, import_isPlainObject.default)(res) || Array.isArray(res) ? observer(res, cb) : res;
    },
    set: function(target, key, val) {
      var ret = Reflect.set(target, key, val);
      cb();
      return ret;
    },
    deleteProperty: function(target, key) {
      var ret = Reflect.deleteProperty(target, key);
      cb();
      return ret;
    }
  });
  proxyMap.set(initialVal, proxy);
  rawMap.set(proxy, initialVal);
  return proxy;
}
function useReactive(initialState) {
  var update = useUpdate_default();
  var stateRef = (0, import_react60.useRef)(initialState);
  var state = useCreation(function() {
    return observer(stateRef.current, function() {
      update();
    });
  }, []);
  return state;
}
var useReactive_default = useReactive;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useResetState/index.js
var import_react61 = __toESM(require_react());
var useResetState = function(initialState) {
  var initialStateRef = (0, import_react61.useRef)(initialState);
  var initialStateMemo = useCreation(function() {
    return isFunction(initialStateRef.current) ? initialStateRef.current() : initialStateRef.current;
  }, []);
  var _a = __read((0, import_react61.useState)(initialStateMemo), 2), state = _a[0], setState = _a[1];
  var resetState = useMemoizedFn_default(function() {
    setState(initialStateMemo);
  });
  return [state, setState, resetState];
};
var useResetState_default = useResetState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useResponsive/index.js
var import_react62 = __toESM(require_react());
var subscribers = /* @__PURE__ */ new Set();
var info;
var responsiveConfig = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200
};
function handleResize() {
  var e_1, _a;
  var oldInfo = info;
  calculate();
  if (oldInfo === info) return;
  try {
    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {
      var subscriber = subscribers_1_1.value;
      subscriber();
    }
  } catch (e_1_1) {
    e_1 = {
      error: e_1_1
    };
  } finally {
    try {
      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1.return)) _a.call(subscribers_1);
    } finally {
      if (e_1) throw e_1.error;
    }
  }
}
var listening = false;
function calculate() {
  var e_2, _a;
  var width = window.innerWidth;
  var newInfo = {};
  var shouldUpdate = false;
  try {
    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {
      var key = _c.value;
      newInfo[key] = width >= responsiveConfig[key];
      if (newInfo[key] !== info[key]) {
        shouldUpdate = true;
      }
    }
  } catch (e_2_1) {
    e_2 = {
      error: e_2_1
    };
  } finally {
    try {
      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
    } finally {
      if (e_2) throw e_2.error;
    }
  }
  if (shouldUpdate) {
    info = newInfo;
  }
}
function configResponsive(config) {
  responsiveConfig = config;
  if (info) calculate();
}
function useResponsive() {
  if (isBrowser_default && !listening) {
    info = {};
    calculate();
    window.addEventListener("resize", handleResize);
    listening = true;
  }
  var _a = __read((0, import_react62.useState)(info), 2), state = _a[0], setState = _a[1];
  (0, import_react62.useEffect)(function() {
    if (!isBrowser_default) return;
    if (!listening) {
      window.addEventListener("resize", handleResize);
    }
    var subscriber = function() {
      setState(info);
    };
    subscribers.add(subscriber);
    return function() {
      subscribers.delete(subscriber);
      if (subscribers.size === 0) {
        window.removeEventListener("resize", handleResize);
        listening = false;
      }
    };
  }, []);
  return state;
}
var useResponsive_default = useResponsive;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSafeState/index.js
var import_react64 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUnmountedRef/index.js
var import_react63 = __toESM(require_react());
var useUnmountedRef = function() {
  var unmountedRef = (0, import_react63.useRef)(false);
  (0, import_react63.useEffect)(function() {
    unmountedRef.current = false;
    return function() {
      unmountedRef.current = true;
    };
  }, []);
  return unmountedRef;
};
var useUnmountedRef_default = useUnmountedRef;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSafeState/index.js
function useSafeState(initialState) {
  var unmountedRef = useUnmountedRef_default();
  var _a = __read((0, import_react64.useState)(initialState), 2), state = _a[0], setState = _a[1];
  var setCurrentState = (0, import_react64.useCallback)(function(currentState) {
    if (unmountedRef.current) return;
    setState(currentState);
  }, []);
  return [state, setCurrentState];
}
var useSafeState_default = useSafeState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useScroll/index.js
function useScroll(target, shouldUpdate) {
  if (shouldUpdate === void 0) {
    shouldUpdate = function() {
      return true;
    };
  }
  var _a = __read(useRafState_default(), 2), position = _a[0], setPosition = _a[1];
  var shouldUpdateRef = useLatest_default(shouldUpdate);
  useEffectWithTarget_default(function() {
    var el = getTargetElement(target, document);
    if (!el) {
      return;
    }
    var updatePosition = function() {
      var newPosition;
      if (el === document) {
        if (document.scrollingElement) {
          newPosition = {
            left: document.scrollingElement.scrollLeft,
            top: document.scrollingElement.scrollTop
          };
        } else {
          newPosition = {
            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),
            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)
          };
        }
      } else {
        newPosition = {
          left: el.scrollLeft,
          top: el.scrollTop
        };
      }
      if (shouldUpdateRef.current(newPosition)) {
        setPosition(newPosition);
      }
    };
    updatePosition();
    el.addEventListener("scroll", updatePosition);
    return function() {
      el.removeEventListener("scroll", updatePosition);
    };
  }, [], target);
  return position;
}
var useScroll_default = useScroll;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSelections/index.js
var import_react65 = __toESM(require_react());
var import_isPlainObject2 = __toESM(require_isPlainObject());
function useSelections(items, options) {
  var _a, _b;
  var defaultSelected = [];
  var itemKey;
  if (Array.isArray(options)) {
    defaultSelected = options;
  } else if ((0, import_isPlainObject2.default)(options)) {
    defaultSelected = (_a = options === null || options === void 0 ? void 0 : options.defaultSelected) !== null && _a !== void 0 ? _a : defaultSelected;
    itemKey = (_b = options === null || options === void 0 ? void 0 : options.itemKey) !== null && _b !== void 0 ? _b : itemKey;
  }
  var getKey = function(item) {
    if (isFunction(itemKey)) {
      return itemKey(item);
    }
    if (isString(itemKey) && (0, import_isPlainObject2.default)(item)) {
      return item[itemKey];
    }
    return item;
  };
  var _c = __read((0, import_react65.useState)(defaultSelected), 2), selected = _c[0], setSelected = _c[1];
  var selectedMap = (0, import_react65.useMemo)(function() {
    var keyToItemMap = /* @__PURE__ */ new Map();
    if (!Array.isArray(selected)) {
      return keyToItemMap;
    }
    selected.forEach(function(item) {
      keyToItemMap.set(getKey(item), item);
    });
    return keyToItemMap;
  }, [selected]);
  var isSelected = function(item) {
    return selectedMap.has(getKey(item));
  };
  var select = function(item) {
    selectedMap.set(getKey(item), item);
    setSelected(Array.from(selectedMap.values()));
  };
  var unSelect = function(item) {
    selectedMap.delete(getKey(item));
    setSelected(Array.from(selectedMap.values()));
  };
  var toggle = function(item) {
    if (isSelected(item)) {
      unSelect(item);
    } else {
      select(item);
    }
  };
  var selectAll = function() {
    items.forEach(function(item) {
      selectedMap.set(getKey(item), item);
    });
    setSelected(Array.from(selectedMap.values()));
  };
  var unSelectAll = function() {
    items.forEach(function(item) {
      selectedMap.delete(getKey(item));
    });
    setSelected(Array.from(selectedMap.values()));
  };
  var noneSelected = (0, import_react65.useMemo)(function() {
    return items.every(function(item) {
      return !selectedMap.has(getKey(item));
    });
  }, [items, selectedMap]);
  var allSelected = (0, import_react65.useMemo)(function() {
    return items.every(function(item) {
      return selectedMap.has(getKey(item));
    }) && !noneSelected;
  }, [items, selectedMap, noneSelected]);
  var partiallySelected = (0, import_react65.useMemo)(function() {
    return !noneSelected && !allSelected;
  }, [noneSelected, allSelected]);
  var toggleAll = function() {
    return allSelected ? unSelectAll() : selectAll();
  };
  var clearAll = function() {
    selectedMap.clear();
    setSelected([]);
  };
  return {
    selected,
    noneSelected,
    allSelected,
    partiallySelected,
    setSelected,
    isSelected,
    select: useMemoizedFn_default(select),
    unSelect: useMemoizedFn_default(unSelect),
    toggle: useMemoizedFn_default(toggle),
    selectAll: useMemoizedFn_default(selectAll),
    unSelectAll: useMemoizedFn_default(unSelectAll),
    clearAll: useMemoizedFn_default(clearAll),
    toggleAll: useMemoizedFn_default(toggleAll)
  };
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSessionStorageState/index.js
var useSessionStorageState = createUseStorageState(function() {
  return isBrowser_default ? sessionStorage : void 0;
});
var useSessionStorageState_default = useSessionStorageState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSet/index.js
var import_react66 = __toESM(require_react());
function useSet(initialValue) {
  var getInitValue = function() {
    return new Set(initialValue);
  };
  var _a = __read((0, import_react66.useState)(getInitValue), 2), set = _a[0], setSet = _a[1];
  var add = function(key) {
    if (set.has(key)) {
      return;
    }
    setSet(function(prevSet) {
      var temp = new Set(prevSet);
      temp.add(key);
      return temp;
    });
  };
  var remove = function(key) {
    if (!set.has(key)) {
      return;
    }
    setSet(function(prevSet) {
      var temp = new Set(prevSet);
      temp.delete(key);
      return temp;
    });
  };
  var reset = function() {
    return setSet(getInitValue());
  };
  return [set, {
    add: useMemoizedFn_default(add),
    remove: useMemoizedFn_default(remove),
    reset: useMemoizedFn_default(reset)
  }];
}
var useSet_default = useSet;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSetState/index.js
var import_react67 = __toESM(require_react());
var useSetState = function(initialState) {
  var _a = __read((0, import_react67.useState)(initialState), 2), state = _a[0], setState = _a[1];
  var setMergeState = useMemoizedFn_default(function(patch) {
    setState(function(prevState) {
      var newState = isFunction(patch) ? patch(prevState) : patch;
      return newState ? __assign(__assign({}, prevState), newState) : prevState;
    });
  });
  return [state, setMergeState];
};
var useSetState_default = useSetState;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js
var import_react68 = __toESM(require_react());
var useEffectWithTarget2 = createEffectWithTarget_default(import_react68.useLayoutEffect);
var useLayoutEffectWithTarget_default = useEffectWithTarget2;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js
var useIsomorphicLayoutEffectWithTarget = isBrowser_default ? useLayoutEffectWithTarget_default : useEffectWithTarget_default;
var useIsomorphicLayoutEffectWithTarget_default = useIsomorphicLayoutEffectWithTarget;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useSize/index.js
function useSize(target) {
  var _a = __read(useRafState_default(function() {
    var el = getTargetElement(target);
    return el ? {
      width: el.clientWidth,
      height: el.clientHeight
    } : void 0;
  }), 2), state = _a[0], setState = _a[1];
  useIsomorphicLayoutEffectWithTarget_default(function() {
    var el = getTargetElement(target);
    if (!el) {
      return;
    }
    var resizeObserver = new ResizeObserver_es_default(function(entries) {
      entries.forEach(function(entry) {
        var _a2 = entry.target, clientWidth = _a2.clientWidth, clientHeight = _a2.clientHeight;
        setState({
          width: clientWidth,
          height: clientHeight
        });
      });
    });
    resizeObserver.observe(el);
    return function() {
      resizeObserver.disconnect();
    };
  }, [], target);
  return state;
}
var useSize_default = useSize;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useTextSelection/index.js
var import_react69 = __toESM(require_react());
var initRect = {
  top: NaN,
  left: NaN,
  bottom: NaN,
  right: NaN,
  height: NaN,
  width: NaN
};
var initState2 = __assign({
  text: ""
}, initRect);
function getRectFromSelection(selection) {
  if (!selection) {
    return initRect;
  }
  if (selection.rangeCount < 1) {
    return initRect;
  }
  var range = selection.getRangeAt(0);
  var _a = range.getBoundingClientRect(), height = _a.height, width = _a.width, top = _a.top, left = _a.left, right = _a.right, bottom = _a.bottom;
  return {
    height,
    width,
    top,
    left,
    right,
    bottom
  };
}
function useTextSelection(target) {
  var _a = __read((0, import_react69.useState)(initState2), 2), state = _a[0], setState = _a[1];
  var stateRef = (0, import_react69.useRef)(state);
  var isInRangeRef = (0, import_react69.useRef)(false);
  stateRef.current = state;
  useEffectWithTarget_default(function() {
    var el = getTargetElement(target, document);
    if (!el) {
      return;
    }
    var mouseupHandler = function() {
      var selObj = null;
      var text = "";
      var rect = initRect;
      if (!window.getSelection) return;
      selObj = window.getSelection();
      text = selObj ? selObj.toString() : "";
      if (text && isInRangeRef.current) {
        rect = getRectFromSelection(selObj);
        setState(__assign(__assign(__assign({}, state), {
          text
        }), rect));
      }
    };
    var mousedownHandler = function(e) {
      if (e.button === 2) return;
      if (!window.getSelection) return;
      if (stateRef.current.text) {
        setState(__assign({}, initState2));
      }
      isInRangeRef.current = false;
      var selObj = window.getSelection();
      if (!selObj) return;
      selObj.removeAllRanges();
      isInRangeRef.current = el.contains(e.target);
    };
    el.addEventListener("mouseup", mouseupHandler);
    document.addEventListener("mousedown", mousedownHandler);
    return function() {
      el.removeEventListener("mouseup", mouseupHandler);
      document.removeEventListener("mousedown", mousedownHandler);
    };
  }, [], target);
  return state;
}
var useTextSelection_default = useTextSelection;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useThrottle/index.js
var import_react71 = __toESM(require_react());

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useThrottleFn/index.js
var import_throttle2 = __toESM(require_throttle());
var import_react70 = __toESM(require_react());
function useThrottleFn(fn, options) {
  var _a;
  if (isDev_default) {
    if (!isFunction(fn)) {
      console.error("useThrottleFn expected parameter is a function, got ".concat(typeof fn));
    }
  }
  var fnRef = useLatest_default(fn);
  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1e3;
  var throttled = (0, import_react70.useMemo)(function() {
    return (0, import_throttle2.default)(function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));
    }, wait, options);
  }, []);
  useUnmount_default(function() {
    throttled.cancel();
  });
  return {
    run: throttled,
    cancel: throttled.cancel,
    flush: throttled.flush
  };
}
var useThrottleFn_default = useThrottleFn;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useThrottle/index.js
function useThrottle(value, options) {
  var _a = __read((0, import_react71.useState)(value), 2), throttled = _a[0], setThrottled = _a[1];
  var run = useThrottleFn_default(function() {
    setThrottled(value);
  }, options).run;
  (0, import_react71.useEffect)(function() {
    run();
  }, [value]);
  return throttled;
}
var useThrottle_default = useThrottle;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useThrottleEffect/index.js
var import_react72 = __toESM(require_react());
function useThrottleEffect(effect, deps, options) {
  var _a = __read((0, import_react72.useState)({}), 2), flag = _a[0], setFlag = _a[1];
  var run = useThrottleFn_default(function() {
    setFlag({});
  }, options).run;
  (0, import_react72.useEffect)(function() {
    return run();
  }, deps);
  useUpdateEffect_default(effect, [flag]);
}
var useThrottleEffect_default = useThrottleEffect;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useTimeout/index.js
var import_react73 = __toESM(require_react());
var useTimeout = function(fn, delay) {
  var timerCallback = useMemoizedFn_default(fn);
  var timerRef = (0, import_react73.useRef)(null);
  var clear = (0, import_react73.useCallback)(function() {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  }, []);
  (0, import_react73.useEffect)(function() {
    if (!isNumber(delay) || delay < 0) {
      return;
    }
    timerRef.current = setTimeout(timerCallback, delay);
    return clear;
  }, [delay]);
  return clear;
};
var useTimeout_default = useTimeout;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useTitle/index.js
var import_react74 = __toESM(require_react());
var DEFAULT_OPTIONS = {
  restoreOnUnmount: false
};
function useTitle(title, options) {
  if (options === void 0) {
    options = DEFAULT_OPTIONS;
  }
  var titleRef = (0, import_react74.useRef)(isBrowser_default ? document.title : "");
  (0, import_react74.useEffect)(function() {
    document.title = title;
  }, [title]);
  useUnmount_default(function() {
    if (options.restoreOnUnmount) {
      document.title = titleRef.current;
    }
  });
}
var useTitle_default = useTitle;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useTrackedEffect/index.js
var import_react75 = __toESM(require_react());
var diffTwoDeps = function(deps1, deps2) {
  return deps1 ? deps1.map(function(_ele, idx) {
    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;
  }).filter(function(ele) {
    return ele >= 0;
  }) : deps2 ? deps2.map(function(_ele, idx) {
    return idx;
  }) : [];
};
var useTrackedEffect = function(effect, deps) {
  var previousDepsRef = (0, import_react75.useRef)();
  (0, import_react75.useEffect)(function() {
    var changes = diffTwoDeps(previousDepsRef.current, deps);
    var previousDeps = previousDepsRef.current;
    previousDepsRef.current = deps;
    return effect(changes, previousDeps, deps);
  }, deps);
};
var useTrackedEffect_default = useTrackedEffect;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useUpdateLayoutEffect/index.js
var import_react76 = __toESM(require_react());
var useUpdateLayoutEffect_default = createUpdateEffect(import_react76.useLayoutEffect);

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useVirtualList/index.js
var import_react77 = __toESM(require_react());
var useVirtualList = function(list, options) {
  var containerTarget = options.containerTarget, wrapperTarget = options.wrapperTarget, itemHeight = options.itemHeight, _a = options.overscan, overscan = _a === void 0 ? 5 : _a;
  var itemHeightRef = useLatest_default(itemHeight);
  var size = useSize_default(containerTarget);
  var scrollTriggerByScrollToFunc = (0, import_react77.useRef)(false);
  var _b = __read((0, import_react77.useState)([]), 2), targetList = _b[0], setTargetList = _b[1];
  var _c = __read((0, import_react77.useState)({}), 2), wrapperStyle = _c[0], setWrapperStyle = _c[1];
  var getVisibleCount = function(containerHeight, fromIndex) {
    if (isNumber(itemHeightRef.current)) {
      return Math.ceil(containerHeight / itemHeightRef.current);
    }
    var sum = 0;
    var endIndex = 0;
    for (var i = fromIndex; i < list.length; i++) {
      var height = itemHeightRef.current(i, list[i]);
      sum += height;
      endIndex = i;
      if (sum >= containerHeight) {
        break;
      }
    }
    return endIndex - fromIndex;
  };
  var getOffset = function(scrollTop) {
    if (isNumber(itemHeightRef.current)) {
      return Math.floor(scrollTop / itemHeightRef.current);
    }
    var sum = 0;
    var offset = 0;
    for (var i = 0; i < list.length; i++) {
      var height = itemHeightRef.current(i, list[i]);
      sum += height;
      if (sum >= scrollTop) {
        offset = i;
        break;
      }
    }
    return offset + 1;
  };
  var getDistanceTop = function(index) {
    if (isNumber(itemHeightRef.current)) {
      var height_1 = index * itemHeightRef.current;
      return height_1;
    }
    var height = list.slice(0, index).reduce(function(sum, _, i) {
      return sum + itemHeightRef.current(i, list[i]);
    }, 0);
    return height;
  };
  var totalHeight = (0, import_react77.useMemo)(function() {
    if (isNumber(itemHeightRef.current)) {
      return list.length * itemHeightRef.current;
    }
    return list.reduce(function(sum, _, index) {
      return sum + itemHeightRef.current(index, list[index]);
    }, 0);
  }, [list]);
  var calculateRange = function() {
    var container = getTargetElement(containerTarget);
    if (container) {
      var scrollTop = container.scrollTop, clientHeight = container.clientHeight;
      var offset = getOffset(scrollTop);
      var visibleCount = getVisibleCount(clientHeight, offset);
      var start_1 = Math.max(0, offset - overscan);
      var end = Math.min(list.length, offset + visibleCount + overscan);
      var offsetTop = getDistanceTop(start_1);
      setWrapperStyle({
        height: totalHeight - offsetTop + "px",
        marginTop: offsetTop + "px"
      });
      setTargetList(list.slice(start_1, end).map(function(ele, index) {
        return {
          data: ele,
          index: index + start_1
        };
      }));
    }
  };
  useUpdateEffect_default(function() {
    var wrapper = getTargetElement(wrapperTarget);
    if (wrapper) {
      Object.keys(wrapperStyle).forEach(function(key) {
        return wrapper.style[key] = wrapperStyle[key];
      });
    }
  }, [wrapperStyle]);
  (0, import_react77.useEffect)(function() {
    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {
      return;
    }
    calculateRange();
  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);
  useEventListener_default("scroll", function(e) {
    if (scrollTriggerByScrollToFunc.current) {
      scrollTriggerByScrollToFunc.current = false;
      return;
    }
    e.preventDefault();
    calculateRange();
  }, {
    target: containerTarget
  });
  var scrollTo = function(index) {
    var container = getTargetElement(containerTarget);
    if (container) {
      scrollTriggerByScrollToFunc.current = true;
      container.scrollTop = getDistanceTop(index);
      calculateRange();
    }
  };
  return [targetList, useMemoizedFn_default(scrollTo)];
};
var useVirtualList_default = useVirtualList;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useWebSocket/index.js
var import_react78 = __toESM(require_react());
var ReadyState;
(function(ReadyState2) {
  ReadyState2[ReadyState2["Connecting"] = 0] = "Connecting";
  ReadyState2[ReadyState2["Open"] = 1] = "Open";
  ReadyState2[ReadyState2["Closing"] = 2] = "Closing";
  ReadyState2[ReadyState2["Closed"] = 3] = "Closed";
})(ReadyState || (ReadyState = {}));
function useWebSocket(socketUrl, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.reconnectLimit, reconnectLimit = _a === void 0 ? 3 : _a, _b = options.reconnectInterval, reconnectInterval = _b === void 0 ? 3 * 1e3 : _b, _c = options.manual, manual = _c === void 0 ? false : _c, onOpen = options.onOpen, onClose = options.onClose, onMessage = options.onMessage, onError = options.onError, protocols = options.protocols;
  var onOpenRef = useLatest_default(onOpen);
  var onCloseRef = useLatest_default(onClose);
  var onMessageRef = useLatest_default(onMessage);
  var onErrorRef = useLatest_default(onError);
  var reconnectTimesRef = (0, import_react78.useRef)(0);
  var reconnectTimerRef = (0, import_react78.useRef)();
  var websocketRef = (0, import_react78.useRef)();
  var _d = __read((0, import_react78.useState)(), 2), latestMessage = _d[0], setLatestMessage = _d[1];
  var _e = __read((0, import_react78.useState)(ReadyState.Closed), 2), readyState = _e[0], setReadyState = _e[1];
  var reconnect = function() {
    var _a2;
    if (reconnectTimesRef.current < reconnectLimit && ((_a2 = websocketRef.current) === null || _a2 === void 0 ? void 0 : _a2.readyState) !== ReadyState.Open) {
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current);
      }
      reconnectTimerRef.current = setTimeout(function() {
        connectWs();
        reconnectTimesRef.current++;
      }, reconnectInterval);
    }
  };
  var connectWs = function() {
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }
    if (websocketRef.current) {
      websocketRef.current.close();
    }
    var ws = new WebSocket(socketUrl, protocols);
    setReadyState(ReadyState.Connecting);
    ws.onerror = function(event) {
      var _a2;
      if (websocketRef.current !== ws) {
        return;
      }
      reconnect();
      (_a2 = onErrorRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onErrorRef, event, ws);
      setReadyState(ws.readyState || ReadyState.Closed);
    };
    ws.onopen = function(event) {
      var _a2;
      if (websocketRef.current !== ws) {
        return;
      }
      (_a2 = onOpenRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onOpenRef, event, ws);
      reconnectTimesRef.current = 0;
      setReadyState(ws.readyState || ReadyState.Open);
    };
    ws.onmessage = function(message) {
      var _a2;
      if (websocketRef.current !== ws) {
        return;
      }
      (_a2 = onMessageRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onMessageRef, message, ws);
      setLatestMessage(message);
    };
    ws.onclose = function(event) {
      var _a2;
      (_a2 = onCloseRef.current) === null || _a2 === void 0 ? void 0 : _a2.call(onCloseRef, event, ws);
      if (websocketRef.current === ws) {
        reconnect();
      }
      if (!websocketRef.current || websocketRef.current === ws) {
        setReadyState(ws.readyState || ReadyState.Closed);
      }
    };
    websocketRef.current = ws;
  };
  var sendMessage = function(message) {
    var _a2;
    if (readyState === ReadyState.Open) {
      (_a2 = websocketRef.current) === null || _a2 === void 0 ? void 0 : _a2.send(message);
    } else {
      throw new Error("WebSocket disconnected");
    }
  };
  var connect = function() {
    reconnectTimesRef.current = 0;
    connectWs();
  };
  var disconnect = function() {
    var _a2;
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
    }
    reconnectTimesRef.current = reconnectLimit;
    (_a2 = websocketRef.current) === null || _a2 === void 0 ? void 0 : _a2.close();
    websocketRef.current = void 0;
  };
  (0, import_react78.useEffect)(function() {
    if (!manual && socketUrl) {
      connect();
    }
  }, [socketUrl, manual]);
  useUnmount_default(function() {
    disconnect();
  });
  return {
    latestMessage,
    sendMessage: useMemoizedFn_default(sendMessage),
    connect: useMemoizedFn_default(connect),
    disconnect: useMemoizedFn_default(disconnect),
    readyState,
    webSocketIns: websocketRef.current
  };
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useWhyDidYouUpdate/index.js
var import_react79 = __toESM(require_react());
function useWhyDidYouUpdate(componentName, props) {
  var prevProps = (0, import_react79.useRef)({});
  (0, import_react79.useEffect)(function() {
    if (prevProps.current) {
      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));
      var changedProps_1 = {};
      allKeys.forEach(function(key) {
        if (!Object.is(prevProps.current[key], props[key])) {
          changedProps_1[key] = {
            from: prevProps.current[key],
            to: props[key]
          };
        }
      });
      if (Object.keys(changedProps_1).length) {
        console.log("[why-did-you-update]", componentName, changedProps_1);
      }
    }
    prevProps.current = props;
  });
}

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useMutationObserver/index.js
var useMutationObserver = function(callback, target, options) {
  if (options === void 0) {
    options = {};
  }
  var callbackRef = useLatest_default(callback);
  useDeepCompareWithTarget_default(function() {
    var element = getTargetElement(target);
    if (!element) {
      return;
    }
    var observer2 = new MutationObserver(callbackRef.current);
    observer2.observe(element, options);
    return function() {
      observer2 === null || observer2 === void 0 ? void 0 : observer2.disconnect();
    };
  }, [options], target);
};
var useMutationObserver_default = useMutationObserver;

// node_modules/.pnpm/ahooks@3.8.4_react@18.3.1/node_modules/ahooks/es/useTheme/index.js
var import_react80 = __toESM(require_react());
var ThemeMode;
(function(ThemeMode2) {
  ThemeMode2["LIGHT"] = "light";
  ThemeMode2["DARK"] = "dark";
  ThemeMode2["SYSTEM"] = "system";
})(ThemeMode || (ThemeMode = {}));
function useCurrentTheme() {
  var matchMedia = isBrowser_default ? window.matchMedia("(prefers-color-scheme: dark)") : void 0;
  var _a = __read((0, import_react80.useState)(function() {
    if (isBrowser_default) {
      return (matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.matches) ? ThemeMode.DARK : ThemeMode.LIGHT;
    } else {
      return ThemeMode.LIGHT;
    }
  }), 2), theme = _a[0], setTheme = _a[1];
  (0, import_react80.useEffect)(function() {
    var onThemeChange = function(event) {
      if (event.matches) {
        setTheme(ThemeMode.DARK);
      } else {
        setTheme(ThemeMode.LIGHT);
      }
    };
    matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.addEventListener("change", onThemeChange);
    return function() {
      matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.removeEventListener("change", onThemeChange);
    };
  }, []);
  return theme;
}
function useTheme(options) {
  if (options === void 0) {
    options = {};
  }
  var localStorageKey = options.localStorageKey;
  var _a = __read((0, import_react80.useState)(function() {
    var preferredThemeMode = (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) && localStorage.getItem(localStorageKey);
    return preferredThemeMode ? preferredThemeMode : ThemeMode.SYSTEM;
  }), 2), themeMode = _a[0], setThemeMode = _a[1];
  var setThemeModeWithLocalStorage = function(mode) {
    setThemeMode(mode);
    if (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) {
      localStorage.setItem(localStorageKey, mode);
    }
  };
  var currentTheme = useCurrentTheme();
  var theme = themeMode === ThemeMode.SYSTEM ? currentTheme : themeMode;
  return {
    theme,
    themeMode,
    setThemeMode: useMemoizedFn_default(setThemeModeWithLocalStorage)
  };
}
export {
  clearCache,
  configResponsive,
  createUpdateEffect,
  useAntdTable_default as useAntdTable,
  useAsyncEffect_default as useAsyncEffect,
  useBoolean,
  useClickAway,
  useControllableValue_default as useControllableValue,
  useCookieState_default as useCookieState,
  useCountDown_default as useCountDown,
  useCounter_default as useCounter,
  useCreation,
  useDebounce_default as useDebounce,
  useDebounceEffect_default as useDebounceEffect,
  useDebounceFn_default as useDebounceFn,
  useDeepCompareEffect_default as useDeepCompareEffect,
  useDeepCompareLayoutEffect_default as useDeepCompareLayoutEffect,
  useDocumentVisibility_default as useDocumentVisibility,
  useDrag_default as useDrag,
  useDrop_default as useDrop,
  useDynamicList_default as useDynamicList,
  useEventEmitter,
  useEventListener_default as useEventListener,
  useEventTarget_default as useEventTarget,
  useExternal_default as useExternal,
  useFavicon_default as useFavicon,
  useFocusWithin,
  useFullscreen_default as useFullscreen,
  useFusionTable_default as useFusionTable,
  useGetState_default as useGetState,
  useHistoryTravel,
  useHover_default as useHover,
  useInViewport_default as useInViewport,
  useInfiniteScroll_default as useInfiniteScroll,
  useInterval_default as useInterval,
  useIsomorphicLayoutEffect_default as useIsomorphicLayoutEffect,
  useKeyPress_default as useKeyPress,
  useLatest_default as useLatest,
  useLocalStorageState_default as useLocalStorageState,
  useLockFn_default as useLockFn,
  useLongPress_default as useLongPress,
  useMap_default as useMap,
  useMemoizedFn_default as useMemoizedFn,
  useMount_default as useMount,
  useMouse_default as useMouse,
  useMutationObserver_default as useMutationObserver,
  useNetwork_default as useNetwork,
  usePagination_default as usePagination,
  usePrevious_default as usePrevious,
  useRafInterval_default as useRafInterval,
  useRafState_default as useRafState,
  useRafTimeout_default as useRafTimeout,
  useReactive_default as useReactive,
  useRequest_default2 as useRequest,
  useResetState_default as useResetState,
  useResponsive_default as useResponsive,
  useSafeState_default as useSafeState,
  useScroll_default as useScroll,
  useSelections,
  useSessionStorageState_default as useSessionStorageState,
  useSet_default as useSet,
  useSetState_default as useSetState,
  useSize_default as useSize,
  useTextSelection_default as useTextSelection,
  useTheme,
  useThrottle_default as useThrottle,
  useThrottleEffect_default as useThrottleEffect,
  useThrottleFn_default as useThrottleFn,
  useTimeout_default as useTimeout,
  useTitle_default as useTitle,
  useToggle_default as useToggle,
  useTrackedEffect_default as useTrackedEffect,
  useUnmount_default as useUnmount,
  useUnmountedRef_default as useUnmountedRef,
  useUpdate_default as useUpdate,
  useUpdateEffect_default as useUpdateEffect,
  useUpdateLayoutEffect_default as useUpdateLayoutEffect,
  useVirtualList_default as useVirtualList,
  useWebSocket,
  useWhyDidYouUpdate
};
/*! Bundled license information:

screenfull/dist/screenfull.js:
  (*!
  * screenfull
  * v5.2.0 - 2021-11-03
  * (c) Sindre Sorhus; MIT License
  *)

js-cookie/dist/js.cookie.mjs:
  (*! js-cookie v3.0.5 | MIT *)
*/
//# sourceMappingURL=ahooks.js.map
