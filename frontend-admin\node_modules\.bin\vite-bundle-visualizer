#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vite-bundle-visualizer@1.2.1_rollup@4.37.0/node_modules/vite-bundle-visualizer/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vite-bundle-visualizer@1.2.1_rollup@4.37.0/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vite-bundle-visualizer@1.2.1_rollup@4.37.0/node_modules/vite-bundle-visualizer/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vite-bundle-visualizer@1.2.1_rollup@4.37.0/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite-bundle-visualizer/bin.js" "$@"
else
  exec node  "$basedir/../vite-bundle-visualizer/bin.js" "$@"
fi
