import{u as I,j as n,F as R,a4 as M}from"./index-CHjq8S-S.js";import{u as S,b as A,c as F,d as q,e as E}from"./useQuery-C2zD5aNt.js";import{u as L}from"./index-BJOYrg_r.js";import{a as c}from"./react-BUTTOX-3.js";import{B as $}from"./index-DDI4OfxQ.js";import{B as z}from"./index-S5qMhsyX.js";import{u as O}from"./useMutation-BkoFKhTK.js";import{Detail as v}from"./detail-BT_JaQZ5.js";import{getConstantColumns as K}from"./constants-CiHLom79.js";import{aD as N,d as P,bB as Q}from"./antd-CXPM1OiB.js";import{a as f}from"./constants-BR8N8nwY.js";import"./Table-DTyH0rpt.js";import"./index-CZVy594C.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";import"./index-CnuJ2TqD.js";import"./index-DVrd-Evt.js";import"./index-aswgGRDw.js";import"./index-DMJlwb4Y.js";import"./index-CreErp7h.js";import"./index-C2mpj1sP.js";function de(){const{t:s}=I(),{hasAccessByCodes:d}=L(),{data:w}=S({queryKey:["role-menu"],queryFn:async()=>{var r;const t=await E();return(((r=t==null?void 0:t.result)==null?void 0:r.list)||(t==null?void 0:t.result)||[]).map(o=>({...o,title:o.title||o.name,key:o.id,id:String(o.id)}))},initialData:[]}),g=O({mutationFn:q}),[B,u]=c.useState(!1),[C,y]=c.useState(""),[j,h]=c.useState({}),x=c.useRef(null),D=async(t,e)=>{var r,o,i,l,m;try{const a=await g.mutateAsync(t);await((r=e==null?void 0:e.reload)==null?void 0:r.call(e)),(o=window.$message)==null||o.success(a.message||s("common.deleteSuccess"))}catch(a){const p=((l=(i=a==null?void 0:a.response)==null?void 0:i.data)==null?void 0:l.message)||(a==null?void 0:a.message)||s("common.deleteFailed");(m=window.$message)==null||m.error(p)}},T=[...K(s),{title:s("common.action"),valueType:"option",key:"option",width:120,fixed:"right",render:(t,e,r,o)=>[n.jsx(R,{type:"link",size:"small",disabled:!d(f.roleEdit),onClick:async()=>{var i,l;try{const m=await A({id:e.id}),a=((i=e.permissions)==null?void 0:i.map(p=>p.id))||[];u(!0),y(s("system.role.editRole")),h({...e,menus:m.result,permissionIds:a})}catch{(l=window.$message)==null||l.error("获取角色信息失败")}},children:s("common.edit")},"editable"),n.jsx(N,{title:s("common.confirmDelete"),onConfirm:()=>D(e.id,o),okText:s("common.confirm"),cancelText:s("common.cancel"),children:n.jsx(R,{type:"link",size:"small",disabled:!d(f.roleDelete),children:s("common.delete")})},"delete")]}],b=()=>{u(!1),h({})},k=()=>{var t;(t=x.current)==null||t.reload()};return n.jsxs($,{className:"h-full",children:[n.jsx(z,{columns:T,actionRef:x,request:async t=>{const e=await F(t);return{...e,data:e.result.list,total:e.result.total}},headerTitle:s("common.menu.role"),toolBarRender:()=>[n.jsx(P,{icon:n.jsx(Q,{}),type:"primary",disabled:!d(f.roleAdd),onClick:()=>{u(!0),y(s("system.role.addRole"))},children:s("common.add")},"add-role")]}),n.jsx(v,{title:C,open:B,onCloseChange:b,detailData:j,refreshTable:k,treeData:M(w||[])})]})}export{de as default};
