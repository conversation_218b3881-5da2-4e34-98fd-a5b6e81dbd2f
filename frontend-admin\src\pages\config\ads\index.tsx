import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Form, 
  Input, 
  Select, 
  InputNumber, 
  message, 
  Space, 
  Tag, 
  Modal, 
  Upload, 
  Image,
  Switch,
  Tooltip,
  Popconfirm
} from 'antd';
import { 
  PictureOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined,
  UploadOutlined,
  EyeOutlined,
  SortAscendingOutlined
} from '@ant-design/icons';
import { BasicContent } from '#src/components';
import {
  fetchAdConfigs,
  fetchCreateAdConfig,
  fetchUpdateAdConfig,
  fetchDeleteAdConfig,
  fetchToggleAdConfigStatus,
  fetchAdConfigStats,
  type AdConfig,
  type AdConfigQueryParams,
  type AdConfigFormData,
  type ImageItem
} from '#src/api/config';
import { uploadCompressedImageToSupabase } from '#src/utils/supabase-upload';

const { Option } = Select;
const { TextArea } = Input;

// 广告类型选项
const AD_TYPE_OPTIONS = [
  { value: 1, label: '轮播' },
  { value: 2, label: '弹窗广告' },
  { value: 3, label: '浮点弹窗' },
  { value: 4, label: '嵌入广告' },
  { value: 5, label: 'Banner' },
  { value: 6, label: '插屏广告' },
  { value: 7, label: '首页4宫格' },
];

// 跳转类型选项
const JUMP_TYPE_OPTIONS = [
  { value: 1, label: '内部路由' },
  { value: 2, label: 'iframe页面' },
];

const AdConfigPage: React.FC = () => {
  const [configs, setConfigs] = useState<AdConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<AdConfig | null>(null);
  const [form] = Form.useForm();
  const [queryParams, setQueryParams] = useState<AdConfigQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);
  const [imageUploading, setImageUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [selectedAdType, setSelectedAdType] = useState<number | null>(null);
  const [imageItems, setImageItems] = useState<ImageItem[]>([]);

  // 获取广告配置列表
  const fetchConfigs = async (params?: AdConfigQueryParams) => {
    setLoading(true);
    try {
      const response = await fetchAdConfigs(params || queryParams);
      if (response.code === 200) {
        setConfigs(response.result.list || []);
        setTotal(response.result.total || 0);
      } else {
        message.error(response.message || '获取广告配置失败');
      }
    } catch (error) {
      message.error('获取广告配置失败');
      console.error('获取广告配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 创建广告配置
  const createConfig = async (values: AdConfigFormData) => {
    try {
      const response = await fetchCreateAdConfig(values);
      if (response.code === 200) {
        message.success('创建成功');
        setEditModalVisible(false);
        form.resetFields();
        fetchConfigs();
      } else {
        message.error(response.message || '创建失败');
      }
    } catch (error) {
      message.error('创建失败');
      console.error('创建广告配置失败:', error);
    }
  };

  // 更新广告配置
  const updateConfig = async (id: number, values: Partial<AdConfigFormData>) => {
    try {
      const response = await fetchUpdateAdConfig(id, values);
      if (response.code === 200) {
        message.success('更新成功');
        setEditModalVisible(false);
        fetchConfigs();
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error) {
      message.error('更新失败');
      console.error('更新广告配置失败:', error);
    }
  };

  // 删除广告配置
  const deleteConfig = async (id: number) => {
    try {
      const response = await fetchDeleteAdConfig(id);
      if (response.code === 200) {
        message.success('删除成功');
        fetchConfigs();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
      console.error('删除广告配置失败:', error);
    }
  };

  // 切换状态
  const toggleStatus = async (id: number) => {
    try {
      const response = await fetchToggleAdConfigStatus(id);
      if (response.code === 200) {
        message.success(response.result.message);
        fetchConfigs();
      } else {
        message.error(response.message || '状态切换失败');
      }
    } catch (error) {
      message.error('状态切换失败');
      console.error('状态切换失败:', error);
    }
  };

  // 图片上传处理
  const handleImageUpload = async (file: File) => {
    setImageUploading(true);
    try {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        throw new Error('只能上传图片文件');
      }

      // 验证文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        throw new Error('图片大小不能超过5MB');
      }

      const imageUrl = await uploadCompressedImageToSupabase(file, 'ads', {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8
      });

      // 创建新的图片项
      const newImageItem: ImageItem = {
        imageUrl,
        jumpType: 1, // 默认内部路由
        jumpTarget: '',
        title: '',
        description: ''
      };

      // 添加到图片项列表
      const currentImageItems = [...imageItems, newImageItem];

      // 根据广告类型验证图片数量
      const adType = form.getFieldValue('adType');
      if (adType === 7 && currentImageItems.length > 4) { // 首页4宫格
        throw new Error('首页4宫格最多只能上传4张图片');
      }

      setImageItems(currentImageItems);
      setUploadedImages(currentImageItems.map(item => item.imageUrl));
      form.setFieldsValue({ imageItems: currentImageItems });
      message.success('图片上传成功');
      return imageUrl;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '图片上传失败';
      message.error(errorMessage);
      console.error('图片上传失败:', error);
      throw error;
    } finally {
      setImageUploading(false);
    }
  };

  // 删除图片
  const handleImageRemove = (index: number) => {
    const newImageItems = imageItems.filter((_, i) => i !== index);
    setImageItems(newImageItems);
    setUploadedImages(newImageItems.map(item => item.imageUrl));
    form.setFieldsValue({ imageItems: newImageItems });
  };

  // 更新图片项配置
  const handleImageItemUpdate = (index: number, field: keyof ImageItem, value: any) => {
    const newImageItems = [...imageItems];
    newImageItems[index] = { ...newImageItems[index], [field]: value };
    setImageItems(newImageItems);
    form.setFieldsValue({ imageItems: newImageItems });
  };

  // 获取上传限制信息
  const getUploadLimits = (adType: number) => {
    switch (adType) {
      case 1: // 轮播
        return { min: 2, max: 10, text: '轮播广告至少需要2张图片' };
      case 7: // 首页4宫格
        return { min: 4, max: 4, text: '首页4宫格必须上传4张图片' };
      default:
        return { min: 1, max: 5, text: '至少需要1张图片' };
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  const columns = [
    {
      title: '广告标识',
      dataIndex: 'adIdentifier',
      key: 'adIdentifier',
      width: 150,
    },
    {
      title: '广告类型',
      dataIndex: 'adTypeName',
      key: 'adTypeName',
      width: 100,
      render: (typeName: string, record: AdConfig) => (
        <Tag color="blue">{typeName}</Tag>
      ),
    },
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
    },
    {
      title: '图片',
      dataIndex: 'imageItems',
      key: 'imageItems',
      width: 120,
      render: (imageItems: any[], record: AdConfig) => (
        <div style={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
          {imageItems && imageItems.length > 0 ? (
            <>
              <Image
                width={40}
                height={30}
                src={imageItems[0].imageUrl}
                style={{ objectFit: 'cover', borderRadius: 4 }}
                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
              />
              {imageItems.length > 1 && (
                <span style={{
                  fontSize: '12px',
                  color: '#666',
                  alignSelf: 'center',
                  background: '#f0f0f0',
                  padding: '2px 6px',
                  borderRadius: '4px'
                }}>
                  +{imageItems.length - 1}
                </span>
              )}
            </>
          ) : (
            <span style={{ color: '#ccc', fontSize: '12px' }}>无图片</span>
          )}
        </div>
      ),
    },
    {
      title: '跳转配置',
      dataIndex: 'imageItems',
      key: 'jumpConfig',
      width: 200,
      render: (imageItems: any[]) => {
        if (!imageItems || imageItems.length === 0) {
          return <span style={{ color: '#ccc' }}>无配置</span>;
        }

        // 检查是否所有图片都有相同的跳转配置
        const firstItem = imageItems[0];
        const allSame = imageItems.every(item =>
          item.jumpType === firstItem.jumpType &&
          item.jumpTarget === firstItem.jumpTarget
        );

        if (allSame) {
          return (
            <div>
              <Tag color="green">
                {firstItem.jumpType === 1 ? '内部路由' : '外部链接'}
              </Tag>
              <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                {firstItem.jumpTarget}
              </div>
            </div>
          );
        } else {
          return (
            <div>
              <Tag color="blue">多跳转配置</Tag>
              <div style={{ fontSize: '12px', color: '#666', marginTop: 2 }}>
                {imageItems.length} 个不同配置
              </div>
            </div>
          );
        }
      },
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      sorter: (a: AdConfig, b: AdConfig) => a.sortOrder - b.sortOrder,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number, record: AdConfig) => (
        <Switch
          checked={status === 1}
          onChange={() => toggleStatus(record.id)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: AdConfig) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                setCurrentConfig(record);
                // 设置图片项和图片URL列表
                const recordImageItems = record.imageItems || [];
                const imageUrls = recordImageItems.map(item => item.imageUrl);
                setImageItems(recordImageItems);
                setUploadedImages(imageUrls);
                setSelectedAdType(record.adType);
                form.setFieldsValue({
                  ...record,
                  imageItems: recordImageItems
                });
                setEditModalVisible(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个广告配置吗？"
            onConfirm={() => deleteConfig(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleSubmit = async (values: AdConfigFormData) => {
    if (currentConfig) {
      await updateConfig(currentConfig.id, values);
    } else {
      await createConfig(values);
    }
  };

  const handleModalClose = () => {
    setEditModalVisible(false);
    setCurrentConfig(null);
    setUploadedImages([]);
    setImageItems([]);
    setSelectedAdType(null);
    form.resetFields();
  };

  return (
    <BasicContent>
      <Card
        title={
          <Space>
            <PictureOutlined />
            广告配置管理
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setCurrentConfig(null);
                setUploadedImages([]);
                setImageItems([]);
                setSelectedAdType(null);
                form.resetFields();
                setEditModalVisible(true);
              }}
            >
              新增广告
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchConfigs()}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={configs}
          rowKey="id"
          loading={loading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              const newParams = { ...queryParams, page, pageSize };
              setQueryParams(newParams);
              fetchConfigs(newParams);
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      <Modal
        title={currentConfig ? '编辑广告配置' : '新增广告配置'}
        open={editModalVisible}
        onCancel={handleModalClose}
        onOk={() => form.submit()}
        width={800}
        confirmLoading={imageUploading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="广告标识"
            name="adIdentifier"
            rules={[
              { required: true, message: '请输入广告标识' },
              { pattern: /^[a-zA-Z0-9_]+$/, message: '广告标识只能包含字母、数字和下划线' }
            ]}
          >
            <Input placeholder="例如: banner_home_1" />
          </Form.Item>

          <Form.Item
            label="广告类型"
            name="adType"
            rules={[{ required: true, message: '请选择广告类型' }]}
          >
            <Select
              placeholder="请选择广告类型"
              onChange={(value) => {
                setSelectedAdType(value);
                // 清空已上传的图片，因为不同类型有不同要求
                setUploadedImages([]);
                form.setFieldsValue({ images: [] });
              }}
            >
              {AD_TYPE_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="广告标题"
            name="title"
            rules={[{ required: true, message: '请输入广告标题' }]}
          >
            <Input placeholder="请输入广告标题" />
          </Form.Item>

          <Form.Item
            label="广告图片配置"
            name="imageItems"
            rules={[{ required: true, message: '请上传广告图片' }]}
          >
            <div>
              {selectedAdType && (
                <div style={{ marginBottom: 8, color: '#666', fontSize: '12px' }}>
                  {getUploadLimits(selectedAdType).text}
                </div>
              )}

              {/* 显示已配置的图片项 */}
              {imageItems.map((item, index) => (
                <Card
                  key={index}
                  size="small"
                  style={{ marginBottom: 16 }}
                  title={`图片 ${index + 1}`}
                  extra={
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleImageRemove(index)}
                    >
                      删除
                    </Button>
                  }
                >
                  <div style={{ display: 'flex', gap: 16 }}>
                    {/* 图片预览 */}
                    <div>
                      <Image
                        width={80}
                        height={80}
                        src={item.imageUrl}
                        style={{ objectFit: 'cover', borderRadius: 4 }}
                      />
                    </div>

                    {/* 跳转配置 */}
                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', gap: 8, marginBottom: 8 }}>
                        <div style={{ flex: 1 }}>
                          <label style={{ fontSize: '12px', color: '#666' }}>标题</label>
                          <Input
                            size="small"
                            placeholder="图片标题"
                            value={item.title}
                            onChange={(e) => handleImageItemUpdate(index, 'title', e.target.value)}
                          />
                        </div>
                        <div style={{ flex: 1 }}>
                          <label style={{ fontSize: '12px', color: '#666' }}>跳转类型</label>
                          <Select
                            size="small"
                            value={item.jumpType}
                            onChange={(value) => handleImageItemUpdate(index, 'jumpType', value)}
                            style={{ width: '100%' }}
                          >
                            {JUMP_TYPE_OPTIONS.map(option => (
                              <Option key={option.value} value={option.value}>
                                {option.label}
                              </Option>
                            ))}
                          </Select>
                        </div>
                      </div>

                      <div style={{ marginBottom: 8 }}>
                        <label style={{ fontSize: '12px', color: '#666' }}>跳转目标</label>
                        <Input
                          size="small"
                          placeholder="内部路由: /games/hot 或 外部链接: https://example.com"
                          value={item.jumpTarget}
                          onChange={(e) => handleImageItemUpdate(index, 'jumpTarget', e.target.value)}
                        />
                      </div>

                      <div>
                        <label style={{ fontSize: '12px', color: '#666' }}>描述</label>
                        <Input
                          size="small"
                          placeholder="图片描述（可选）"
                          value={item.description}
                          onChange={(e) => handleImageItemUpdate(index, 'description', e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                </Card>
              ))}

              {/* 上传按钮 */}
              {(!selectedAdType || imageItems.length < getUploadLimits(selectedAdType).max) && (
                <Upload
                  name="image"
                  listType="picture-card"
                  className="avatar-uploader"
                  showUploadList={false}
                  beforeUpload={(file) => {
                    handleImageUpload(file);
                    return false; // 阻止默认上传行为
                  }}
                  accept="image/*"
                  disabled={imageUploading}
                >
                  <div style={{ width: 100, height: 100, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                    <UploadOutlined />
                    <div style={{ marginTop: 8, fontSize: '12px' }}>
                      {imageUploading ? '上传中...' : '上传图片'}
                    </div>
                  </div>
                </Upload>
              )}

              {selectedAdType && (
                <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
                  已配置 {imageItems.length} / {getUploadLimits(selectedAdType).max} 张图片
                </div>
              )}
            </div>
          </Form.Item>



          <Form.Item
            label="排序"
            name="sortOrder"
            initialValue={0}
          >
            <InputNumber min={0} max={9999} placeholder="数字越小越靠前" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="备注"
            name="remark"
          >
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Modal>
    </BasicContent>
  );
};

export default AdConfigPage;
