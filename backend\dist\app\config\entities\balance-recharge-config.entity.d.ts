import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class BalanceRechargeConfig {
    id: number;
    tierName: string;
    rechargeAmount: number;
    activityBonusAmount: number;
    activityStartTime: Date;
    activityEndTime: Date;
    sortOrder: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    isActivityActive(): boolean;
    getEffectiveRechargeAmount(): number;
    getActivityStatus(): {
        isActive: boolean;
        description: string;
    };
    getActivityBonusAmount(): number;
}
