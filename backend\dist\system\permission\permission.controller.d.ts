import { SystemPermissionService } from './permission.service';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { QueryPermissionDto } from './dto/query-permission.dto';
export declare class SystemPermissionController {
    private readonly permissionService;
    constructor(permissionService: SystemPermissionService);
    create(createPermissionDto: CreatePermissionDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysPermission;
    }>;
    findAll(queryPermissionDto: QueryPermissionDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").SysPermission[];
            total: number;
            current: number;
            pageSize: number;
        };
    }>;
    findAllSimple(): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysPermission[];
    }>;
    findByType(type: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysPermission[];
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysPermission;
    }>;
    update(id: string, updatePermissionDto: UpdatePermissionDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysPermission;
    }>;
    remove(id: string): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
}
