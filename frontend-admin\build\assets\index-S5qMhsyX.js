import{f as E,i as C,a5 as H,a6 as p,l as W,a7 as x,a8 as _,u as A,j as m,a9 as y}from"./index-CHjq8S-S.js";import{bC as N,bh as P}from"./antd-CXPM1OiB.js";import{a as o}from"./react-BUTTOX-3.js";import{P as j}from"./Table-DTyH0rpt.js";function L(t){var i=o.useRef(0),s=E(o.useState(t),2),r=s[0],e=s[1],a=o.useCallback(function(n){cancelAnimationFrame(i.current),i.current=requestAnimationFrame(function(){e(n)})},[]);return C(function(){cancelAnimationFrame(i.current)}),[r,a]}var F=H(o.useLayoutEffect),O=W?F:p;function z(t){var i=E(L(function(){var e=x(t);return e?{width:e.clientWidth,height:e.clientHeight}:void 0}),2),s=i[0],r=i[1];return O(function(){var e=x(t);if(e){var a=new N(function(n){n.forEach(function(u){var c=u.target,l=c.clientWidth,f=c.clientHeight;r({width:l,height:f})})});return a.observe(e),function(){a.disconnect()}}},[],t),s}const B="basic-table";function Y(t){const i=_(),{t:s}=A(),{autoHeight:r=!0,offsetBottom:e}=t,a=o.useRef(null),n=z(a),[u,c]=o.useState(r?0:void 0);o.useEffect(()=>{const g=t.pagination===!1;if(r&&a.current&&(n!=null&&n.height)){const R=n.height,b=a.current.getElementsByClassName(B)[0];if(!b)return;const v=a.current.getBoundingClientRect();if(v.top>window.innerHeight){c(void 0);return}const h=b.querySelector("div.ant-table-body");if(!h)return;const S=h.getBoundingClientRect(),T=S.top-v.top,w=e||(g?16:56),d=Math.max(400,R-T-w);if(d-S.height<=10)return;h.setAttribute("style",`overflow-y: auto;min-height: ${d}px;max-height: ${d}px;`)}},[n,r,e,t.pagination]);const l=()=>t.loading===!1?!1:t.loading===!0?!0:{indicator:m.jsx(P,{spin:!0}),...t.loading},f=()=>t.pagination===!1?!1:{position:["bottomRight"],defaultPageSize:10,showQuickJumper:!0,showSizeChanger:!0,showTotal:g=>s("common.pagination",{total:g}),...t.pagination};return m.jsx("div",{className:"h-full",ref:a,children:m.jsx(j,{cardBordered:!0,rowKey:"id",dateFormatter:"string",...t,options:{fullScreen:!0,...t.options},rootClassName:y(B,t.rootClassName),className:y(i.basicTable,t.className),scroll:{y:u,...t.scroll},loading:l(),pagination:f(),expandable:{...t.expandable}})})}export{Y as B};
