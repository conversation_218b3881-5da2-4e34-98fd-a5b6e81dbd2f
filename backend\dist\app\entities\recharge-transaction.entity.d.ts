import { AppUser } from './app-user.entity';
export declare enum RechargeTransactionStatus {
    INCOME = 1,
    EXPENSE = 2
}
export declare enum RechargeTransactionType {
    BET = 1,
    WIN = 2,
    DEPOSIT = 3,
    BALANCE_EXCHANGE = 4
}
export declare class RechargeTransaction {
    id: number;
    userId: number;
    uid: number;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    status: RechargeTransactionStatus;
    transactionType: RechargeTransactionType;
    orderId: string;
    description: string;
    remark: string;
    operatorId: number;
    createTime: Date;
    updateTime: Date;
    user: AppUser;
}
export declare const RechargeTransactionTypeLabels: {
    1: string;
    2: string;
    3: string;
    4: string;
};
export declare const RechargeTransactionStatusLabels: {
    1: string;
    2: string;
};
