"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketingChannel = void 0;
const typeorm_1 = require("typeorm");
const marketing_ad_entity_1 = require("./marketing-ad.entity");
const app_user_entity_1 = require("./app-user.entity");
let MarketingChannel = class MarketingChannel {
    id;
    name;
    identifier;
    status;
    description;
    ads;
    users;
    createdAt;
    updatedAt;
};
exports.MarketingChannel = MarketingChannel;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], MarketingChannel.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, unique: true }),
    __metadata("design:type", String)
], MarketingChannel.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], MarketingChannel.prototype, "identifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], MarketingChannel.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], MarketingChannel.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => marketing_ad_entity_1.MarketingAd, (ad) => ad.channel),
    __metadata("design:type", Array)
], MarketingChannel.prototype, "ads", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_user_entity_1.AppUser, (user) => user.channel),
    __metadata("design:type", Array)
], MarketingChannel.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], MarketingChannel.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], MarketingChannel.prototype, "updatedAt", void 0);
exports.MarketingChannel = MarketingChannel = __decorate([
    (0, typeorm_1.Entity)('marketing_channels')
], MarketingChannel);
//# sourceMappingURL=marketing-channel.entity.js.map