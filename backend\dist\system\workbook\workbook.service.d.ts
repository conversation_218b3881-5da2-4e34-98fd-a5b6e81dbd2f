import { Repository } from 'typeorm';
import { SysWorkbook } from '../entities/sys-workbook.entity';
import { CreateWorkbookDto } from './dto/create-workbook.dto';
import { UpdateWorkbookDto } from './dto/update-workbook.dto';
import { QueryWorkbookDto } from './dto/query-workbook.dto';
export declare class SystemWorkbookService {
    private workbookRepository;
    constructor(workbookRepository: Repository<SysWorkbook>);
    create(createWorkbookDto: CreateWorkbookDto): Promise<SysWorkbook>;
    findAll(queryWorkbookDto: QueryWorkbookDto): Promise<{
        list: SysWorkbook[];
        total: number;
        current: number;
        pageSize: number;
    }>;
    findOne(id: number): Promise<SysWorkbook>;
    update(id: number, updateWorkbookDto: UpdateWorkbookDto): Promise<SysWorkbook>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findByType(type: string): Promise<SysWorkbook[]>;
    getTypes(): Promise<any[]>;
    updateOrder(items: {
        id: number;
        order: number;
    }[]): Promise<{
        message: string;
    }>;
}
