import { DataSource } from 'typeorm';
export declare class DatabaseMonitorService {
    private dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    checkConnectionPoolStatus(): Promise<void>;
    performHealthCheck(): Promise<{
        isHealthy: boolean;
        latency: number;
        connectionCount: number;
        error?: string;
    }>;
    getDatabaseStats(): Promise<any>;
    optimizeConnections(): Promise<void>;
    startPeriodicMonitoring(): void;
}
