import{j as i}from"./index-CHjq8S-S.js";import{an as d}from"./antd-CXPM1OiB.js";import"./react-BUTTOX-3.js";function m(e){return[{dataIndex:"index",title:e("common.index"),valueType:"indexBorder",width:80},{title:e("system.role.name"),dataIndex:"name",disable:!0,ellipsis:!0,width:120,formItemProps:{rules:[{required:!0,message:e("form.required")}]}},{disable:!0,title:e("system.role.id"),dataIndex:"code",width:120,filters:!0,onFilter:!0,ellipsis:!0},{disable:!0,title:e("common.status"),dataIndex:"status",valueType:"select",width:80,render:(a,t)=>i.jsx(d,{color:t.status===1?"success":"default",children:a}),valueEnum:{1:{text:e("common.enabled")},0:{text:e("common.deactivated")}}},{title:e("common.remark"),dataIndex:"remark",search:!1,ellipsis:!0},{title:"权限数量",dataIndex:"permissions",search:!1,width:100,render:(a,t)=>{var s;const r=((s=t.permissions)==null?void 0:s.length)||0;return i.jsx(d,{color:r>0?"blue":"default",children:r})}},{title:e("common.createTime"),dataIndex:"createTime",valueType:"date",width:100,search:!1},{title:e("common.updateTime"),dataIndex:"updateTime",valueType:"dateTime",width:170,search:!1}]}export{m as getConstantColumns};
