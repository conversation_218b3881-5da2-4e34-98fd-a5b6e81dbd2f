import{u as j,a3 as E,j as a}from"./index-CHjq8S-S.js";import{a as N}from"./react-BUTTOX-3.js";import{u as F}from"./useMutation-BkoFKhTK.js";import{C as T,N as b,a as D}from"./rules-Br-WHaJV.js";import{av as o,aq as I,au as C,T as O,I as w,d as y,Q as _,bz as M}from"./antd-CXPM1OiB.js";const{Title:q,Text:v}=O,A={currentPassword:"",newPassword:"",confirmPassword:""};function V(){const[d]=o.useForm(),{t:e}=j(),[x,i]=N.useState(!1),[m,u]=N.useState(!1),c=F({mutationFn:E,retry:!1,onSuccess:()=>{var s;u(!1),(s=window.$message)==null||s.success(e("personal-center.changePasswordSuccess")),i(!0),d.resetFields(),setTimeout(()=>i(!1),5e3)},onError:s=>{var t,l,p,r,g,f,h,P;u(!1),console.error("[FRONTEND] 密码修改失败:",s),console.log("[FRONTEND] 错误对象详情:",{error:s,response:s==null?void 0:s.response,errorMessage:(t=s==null?void 0:s.response)==null?void 0:t.errorMessage,data:(l=s==null?void 0:s.response)==null?void 0:l.data,message:s==null?void 0:s.message,name:s==null?void 0:s.name});let n=e("personal-center.changePasswordFailed");(p=s==null?void 0:s.response)!=null&&p.errorMessage?n=s.response.errorMessage:(g=(r=s==null?void 0:s.response)==null?void 0:r.data)!=null&&g.message?n=s.response.data.message:(h=(f=s==null?void 0:s.response)==null?void 0:f.data)!=null&&h.error?n=s.response.data.error:s!=null&&s.message&&!s.message.includes("Request failed with status code")&&(n=s.message),console.log("[FRONTEND] 最终显示错误消息:",n),(P=window.$message)==null||P.error(n)}}),S=s=>{if(m||c.isPending){console.log("[FRONTEND] 正在提交中，忽略重复请求");return}u(!0);const n=Date.now();console.log("[FRONTEND] 开始修改密码"),c.mutate(s,{onSuccess:()=>{const t=Date.now();console.log(`[FRONTEND] 修改密码成功 - 总耗时: ${t-n}ms`)},onError:t=>{const l=Date.now();console.error(`[FRONTEND] 修改密码失败 - 总耗时: ${l-n}ms`,t)}})},R=(s,n)=>{const t=d.getFieldValue("currentPassword");return n&&t&&n===t?Promise.reject(new Error(e("form.newPassword.same"))):Promise.resolve()};return a.jsxs(I,{title:a.jsxs(_,{children:[a.jsx(M,{}),a.jsx("span",{children:e("personal-center.passwordSettings")})]}),className:"w-full max-w-md",children:[x&&a.jsx(C,{message:e("personal-center.changePasswordSuccess"),type:"success",showIcon:!0,closable:!0,onClose:()=>i(!1),className:"mb-4"}),a.jsxs("div",{className:"mb-4",children:[a.jsx(q,{level:5,className:"mb-2",children:e("personal-center.passwordRequirements")}),a.jsx(v,{type:"secondary",className:"text-sm",children:e("personal-center.passwordRequirementsList")})]}),a.jsxs(o,{form:d,layout:"vertical",initialValues:A,onFinish:S,autoComplete:"off",children:[a.jsx(o.Item,{label:e("personal-center.currentPassword"),name:"currentPassword",rules:T(e),children:a.jsx(w.Password,{placeholder:e("form.currentPassword.required"),autoComplete:"current-password"})}),a.jsx(o.Item,{label:e("personal-center.newPassword"),name:"newPassword",rules:[...b(e),{validator:R}],hasFeedback:!0,children:a.jsx(w.Password,{placeholder:e("form.newPassword.required"),autoComplete:"new-password"})}),a.jsx(o.Item,{label:e("personal-center.confirmNewPassword"),name:"confirmPassword",rules:D(e),dependencies:["newPassword"],hasFeedback:!0,children:a.jsx(w.Password,{placeholder:e("form.confirmPassword.required"),autoComplete:"new-password"})}),a.jsx(o.Item,{className:"mb-0",children:a.jsx(y,{type:"primary",htmlType:"submit",loading:m||c.isPending,disabled:m||c.isPending,block:!0,children:e("personal-center.saveChanges")})})]})]})}export{V as PasswordChange};
