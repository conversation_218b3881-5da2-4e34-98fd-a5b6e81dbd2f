"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemUserController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const user_service_1 = require("./user.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const query_user_dto_1 = require("./dto/query-user.dto");
const reset_password_dto_1 = require("./dto/reset-password.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const super_admin_guard_1 = require("../auth/guards/super-admin.guard");
let SystemUserController = class SystemUserController {
    userService;
    constructor(userService) {
        this.userService = userService;
    }
    async create(createUserDto, req) {
        const result = await this.userService.create(createUserDto, req.user.userId);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(queryUserDto) {
        const result = await this.userService.findAll(queryUserDto);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getUserInfo(req) {
        const result = await this.userService.getUserInfo(req.user.userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.userService.findOne(+id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateUserDto, req) {
        const result = await this.userService.update(+id, updateUserDto, req.user.userId);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id, req) {
        const result = await this.userService.remove(+id, req.user.userId);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
    async resetPassword(resetPasswordDto, req) {
        const result = await this.userService.resetPassword(resetPasswordDto.userId, req.user.userId);
        return {
            code: 200,
            message: '密码重置成功',
            result,
        };
    }
    async checkSuperAdmin(req) {
        const isSuperAdmin = await this.userService.isSuperAdmin(req.user.userId);
        return {
            code: 200,
            message: '检查成功',
            result: { isSuperAdmin },
        };
    }
    async setSuperAdmin(req) {
        const existingSuperAdmin = await this.userService.getSuperAdmin();
        if (!existingSuperAdmin) {
            await this.userService.setSuperAdmin(req.user.userId);
            return {
                code: 200,
                message: '超级管理员设置成功',
                result: null,
            };
        }
        else {
            return {
                code: 200,
                message: '系统已存在超级管理员',
                result: { existingSuperAdmin: existingSuperAdmin.username },
            };
        }
    }
};
exports.SystemUserController = SystemUserController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(super_admin_guard_1.SuperAdminGuard),
    (0, swagger_1.ApiOperation)({ summary: '创建管理员用户（仅超级管理员）' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto, Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取管理员用户列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_user_dto_1.QueryUserDto]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('info'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前管理员信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "getUserInfo", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取管理员用户详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(super_admin_guard_1.SuperAdminGuard),
    (0, swagger_1.ApiOperation)({ summary: '更新管理员用户（仅超级管理员）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(super_admin_guard_1.SuperAdminGuard),
    (0, swagger_1.ApiOperation)({ summary: '删除管理员用户（仅超级管理员）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.UseGuards)(super_admin_guard_1.SuperAdminGuard),
    (0, swagger_1.ApiOperation)({ summary: '重置用户密码（仅超级管理员）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重置成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [reset_password_dto_1.ResetPasswordDto, Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Get)('check/super-admin'),
    (0, swagger_1.ApiOperation)({ summary: '检查当前用户是否为超级管理员' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '检查成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "checkSuperAdmin", null);
__decorate([
    (0, common_1.Post)('set-super-admin'),
    (0, swagger_1.ApiOperation)({ summary: '设置当前用户为超级管理员（仅用于初始化）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '设置成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemUserController.prototype, "setSuperAdmin", null);
exports.SystemUserController = SystemUserController = __decorate([
    (0, swagger_1.ApiTags)('系统用户管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [user_service_1.SystemUserService])
], SystemUserController);
//# sourceMappingURL=user.controller.js.map