import { DataSource } from 'typeorm';
export declare class DatabaseHealthService {
    private dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    checkHealth(): Promise<{
        status: 'healthy' | 'unhealthy';
        latency: number;
        connectionCount: number;
        details: any;
    }>;
    private getConnectionCount;
    getPerformanceStats(): Promise<any>;
    private getConnectionStats;
    private getSlowQueries;
    testLatency(iterations?: number): Promise<{
        min: number;
        max: number;
        avg: number;
        results: number[];
    }>;
}
