"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const channel_service_1 = require("./channel.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
let ChannelController = class ChannelController {
    channelService;
    constructor(channelService) {
        this.channelService = channelService;
    }
    async createChannel(createChannelDto) {
        const result = await this.channelService.createChannel(createChannelDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findChannels(queryDto) {
        console.log('[CHANNEL_CONTROLLER] ===== 渠道列表请求开始 =====');
        console.log('[CHANNEL_CONTROLLER] 收到渠道列表请求:', queryDto);
        console.log('[CHANNEL_CONTROLLER] 查询参数详细信息:', {
            page: { value: queryDto.page, type: typeof queryDto.page },
            pageSize: { value: queryDto.pageSize, type: typeof queryDto.pageSize },
            sortBy: { value: queryDto.sortBy, type: typeof queryDto.sortBy },
            sortOrder: { value: queryDto.sortOrder, type: typeof queryDto.sortOrder }
        });
        console.log('[CHANNEL_CONTROLLER] ValidationPipe 已通过，进入控制器方法');
        try {
            const result = await this.channelService.findChannels(queryDto);
            console.log('[CHANNEL_CONTROLLER] 渠道服务调用完成，返回结果条数:', result.list?.length || 0);
            return {
                code: 200,
                message: '获取成功',
                result,
            };
        }
        catch (error) {
            console.log('[CHANNEL_CONTROLLER] 渠道服务调用失败:', error.message);
            throw error;
        }
    }
    async findSimpleChannels() {
        const result = await this.channelService.findSimpleChannels();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async createAd(createAdDto) {
        const result = await this.channelService.createAd(createAdDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAds(queryDto) {
        console.log('[CHANNEL_CONTROLLER] ===== 广告列表请求开始 =====');
        console.log('[CHANNEL_CONTROLLER] 收到广告列表请求:', queryDto);
        console.log('[CHANNEL_CONTROLLER] 查询参数详细信息:', {
            page: { value: queryDto.page, type: typeof queryDto.page },
            pageSize: { value: queryDto.pageSize, type: typeof queryDto.pageSize },
            sortBy: { value: queryDto.sortBy, type: typeof queryDto.sortBy },
            sortOrder: { value: queryDto.sortOrder, type: typeof queryDto.sortOrder }
        });
        console.log('[CHANNEL_CONTROLLER] ValidationPipe 已通过，进入控制器方法');
        try {
            const result = await this.channelService.findAds(queryDto);
            console.log('[CHANNEL_CONTROLLER] 服务调用完成，返回结果条数:', result.list?.length || 0);
            return {
                code: 200,
                message: '获取成功',
                result,
            };
        }
        catch (error) {
            console.log('[CHANNEL_CONTROLLER] 服务调用失败:', error.message);
            throw error;
        }
    }
    async findAdById(id) {
        const result = await this.channelService.findAdById(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateAd(id, updateAdDto) {
        const result = await this.channelService.updateAd(id, updateAdDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async toggleAdStatus(id) {
        const result = await this.channelService.toggleAdStatus(id);
        return {
            code: 200,
            message: '状态切换成功',
            result,
        };
    }
    async findChannelById(id) {
        const result = await this.channelService.findChannelById(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateChannel(id, updateChannelDto) {
        const result = await this.channelService.updateChannel(id, updateChannelDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async toggleChannelStatus(id) {
        const result = await this.channelService.toggleChannelStatus(id);
        return {
            code: 200,
            message: '状态切换成功',
            result,
        };
    }
};
exports.ChannelController = ChannelController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建渠道' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '渠道名称或标识已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateChannelDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "createChannel", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取渠道列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryChannelDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "findChannels", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '获取简化渠道列表（用于下拉选择）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "findSimpleChannels", null);
__decorate([
    (0, common_1.Post)('ads'),
    (0, swagger_1.ApiOperation)({ summary: '创建广告' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '广告标识已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAdDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "createAd", null);
__decorate([
    (0, common_1.Get)('ads'),
    (0, swagger_1.ApiOperation)({ summary: '获取广告列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryAdDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "findAds", null);
__decorate([
    (0, common_1.Get)('ads/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取广告详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "findAdById", null);
__decorate([
    (0, common_1.Patch)('ads/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新广告信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '广告标识已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateAdDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "updateAd", null);
__decorate([
    (0, common_1.Patch)('ads/:id/toggle-status'),
    (0, swagger_1.ApiOperation)({ summary: '切换广告状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "toggleAdStatus", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取渠道详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '渠道不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "findChannelById", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新渠道信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '渠道不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '渠道名称或标识已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateChannelDto]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "updateChannel", null);
__decorate([
    (0, common_1.Patch)(':id/toggle-status'),
    (0, swagger_1.ApiOperation)({ summary: '切换渠道状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '渠道不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ChannelController.prototype, "toggleChannelStatus", null);
exports.ChannelController = ChannelController = __decorate([
    (0, swagger_1.ApiTags)('渠道管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('channels'),
    __metadata("design:paramtypes", [channel_service_1.ChannelService])
], ChannelController);
//# sourceMappingURL=channel.controller.js.map