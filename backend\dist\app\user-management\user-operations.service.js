"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOperationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
const dto_1 = require("./dto");
let UserOperationsService = class UserOperationsService {
    userRepository;
    channelRepository;
    adRepository;
    riskEventRepository;
    constructor(userRepository, channelRepository, adRepository, riskEventRepository) {
        this.userRepository = userRepository;
        this.channelRepository = channelRepository;
        this.adRepository = adRepository;
        this.riskEventRepository = riskEventRepository;
    }
    async updateUserStatus(id, updateDto, operatorId) {
        const user = await this.userRepository.findOne({ where: { id } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const { status, reason } = updateDto;
        await this.userRepository.update(id, { status });
        if (status === dto_1.UserStatus.BANNED) {
            await this.riskEventRepository.save({
                userId: id,
                eventType: 'USER_BANNED',
                riskIncrement: 0,
                details: {
                    reason: reason || '管理员操作',
                    operatorId,
                    previousStatus: user.status,
                    newStatus: status,
                },
                status: 3,
                operatorId,
            });
        }
    }
    async updateUserTags(id, updateDto, operatorId) {
        const user = await this.userRepository.findOne({ where: { id } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const { tags, reason } = updateDto;
        await this.userRepository.update(id, { tags });
        console.log(`用户 ${id} 标签更新`, {
            operatorId,
            reason,
            oldTags: user.tags,
            newTags: tags,
        });
    }
    async getUserInvitationRelationship(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['inviter'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const directInvitees = await this.userRepository.find({
            where: { inviterId: id },
            select: ['id', 'uid', 'username', 'nickname', 'avatar', 'status', 'createTime', 'lastLoginTime'],
            order: { createTime: 'DESC' },
        });
        const allInvitees = await this.userRepository
            .createQueryBuilder('user')
            .where('user.invitationPath LIKE :path', { path: `%>${id}>%` })
            .orWhere('user.inviterId = :id', { id })
            .select(['user.id', 'user.uid', 'user.username', 'user.nickname', 'user.avatar', 'user.status', 'user.createTime', 'user.lastLoginTime'])
            .orderBy('user.createTime', 'DESC')
            .getRawMany();
        const invitationLevel = user.invitationPath
            ? user.invitationPath.split('>').filter(Boolean).length
            : 0;
        const inviter = user.inviter
            ? {
                id: user.inviter.id,
                uid: user.inviter.uid,
                username: user.inviter.username,
                nickname: user.inviter.nickname,
                avatar: user.inviter.avatar,
                status: user.inviter.status,
                createTime: user.inviter.createTime,
                lastLoginTime: user.inviter.lastLoginTime,
            }
            : undefined;
        const invitees = directInvitees.map((invitee) => ({
            id: invitee.id,
            uid: invitee.uid,
            username: invitee.username,
            nickname: invitee.nickname,
            avatar: invitee.avatar,
            status: invitee.status,
            createTime: invitee.createTime,
            lastLoginTime: invitee.lastLoginTime,
        }));
        return {
            inviter,
            invitees,
            invitationPath: user.invitationPath || '',
            invitationLevel,
            totalInvitees: allInvitees.length,
            directInvitees: directInvitees.length,
            indirectInvitees: allInvitees.length - directInvitees.length,
        };
    }
    async getUserRiskEvents(userId, page = 1, pageSize = 20) {
        const [events, total] = await this.riskEventRepository.findAndCount({
            where: { userId },
            order: { createdAt: 'DESC' },
            skip: (page - 1) * pageSize,
            take: pageSize,
        });
        return {
            list: events,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    async getMarketingChannels() {
        return this.channelRepository.find({
            where: { status: 1 },
            order: { createdAt: 'DESC' },
        });
    }
    async getMarketingAds(channelId) {
        const where = { status: 1 };
        if (channelId) {
            where.channelId = channelId;
        }
        return this.adRepository.find({
            where,
            relations: ['channel'],
            order: { createdAt: 'DESC' },
        });
    }
};
exports.UserOperationsService = UserOperationsService;
exports.UserOperationsService = UserOperationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.AppUser)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.MarketingChannel)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.MarketingAd)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.RiskEvent)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UserOperationsService);
//# sourceMappingURL=user-operations.service.js.map