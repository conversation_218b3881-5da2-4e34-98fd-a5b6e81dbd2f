import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsNumber, IsString, IsDateString, Min, Max, Length } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateBalanceRechargeConfigDto {
  @ApiProperty({ description: '充值挡位名称', example: '小额充值' })
  @IsNotEmpty({ message: '挡位名称不能为空' })
  @IsString({ message: '挡位名称必须是字符串' })
  @Length(1, 100, { message: '挡位名称长度必须在1-100个字符之间' })
  tierName: string;

  @ApiProperty({ description: '充值金额', example: 10.00 })
  @IsNotEmpty({ message: '充值金额不能为空' })
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Min(0.01, { message: '充值金额必须大于0' })
  rechargeAmount: number;

  @ApiProperty({ description: '活动赠送金额', example: 2.00, required: false })
  @IsOptional()
  @IsNumber({}, { message: '活动赠送金额必须是数字' })
  @Min(0, { message: '活动赠送金额不能小于0' })
  activityBonusAmount?: number;

  @ApiProperty({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false })
  @IsOptional()
  @IsDateString({}, { message: '活动开始时间格式不正确' })
  activityStartTime?: string;

  @ApiProperty({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false })
  @IsOptional()
  @IsDateString({}, { message: '活动结束时间格式不正确' })
  activityEndTime?: string;

  @ApiProperty({ description: '排序顺序', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序顺序必须是数字' })
  @Min(0, { message: '排序顺序不能小于0' })
  sortOrder?: number;

  @ApiProperty({ description: '状态：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @Min(0, { message: '状态值必须是0或1' })
  @Max(1, { message: '状态值必须是0或1' })
  status?: number;
}

export class UpdateBalanceRechargeConfigDto {
  @ApiProperty({ description: '充值挡位名称', example: '小额充值', required: false })
  @IsOptional()
  @IsString({ message: '挡位名称必须是字符串' })
  @Length(1, 100, { message: '挡位名称长度必须在1-100个字符之间' })
  tierName?: string;

  @ApiProperty({ description: '充值金额', example: 10.00, required: false })
  @IsOptional()
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Min(0.01, { message: '充值金额必须大于0' })
  rechargeAmount?: number;

  @ApiProperty({ description: '活动赠送金额', example: 2.00, required: false })
  @IsOptional()
  @IsNumber({}, { message: '活动赠送金额必须是数字' })
  @Min(0, { message: '活动赠送金额不能小于0' })
  activityBonusAmount?: number;

  @ApiProperty({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false })
  @IsOptional()
  @IsDateString({}, { message: '活动开始时间格式不正确' })
  activityStartTime?: string;

  @ApiProperty({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false })
  @IsOptional()
  @IsDateString({}, { message: '活动结束时间格式不正确' })
  activityEndTime?: string;

  @ApiProperty({ description: '排序顺序', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '排序顺序必须是数字' })
  @Min(0, { message: '排序顺序不能小于0' })
  sortOrder?: number;

  @ApiProperty({ description: '状态：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @Min(0, { message: '状态值必须是0或1' })
  @Max(1, { message: '状态值必须是0或1' })
  status?: number;
}

export class BalanceRechargeConfigQueryDto {
  @ApiProperty({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber({}, { message: '状态必须是数字' })
  @Min(0, { message: '状态值必须是0或1' })
  @Max(1, { message: '状态值必须是0或1' })
  status?: number;
}

export class UpdateBalanceRechargeLimitDto {
  @ApiProperty({ description: '限制配置名称', example: '默认充值限制' })
  @IsOptional()
  @IsString({ message: '限制配置名称必须是字符串' })
  @Length(1, 50, { message: '限制配置名称长度必须在1-50字符之间' })
  limitName?: string;

  @ApiProperty({ description: '最低充值金额', example: 1.00 })
  @IsNotEmpty({ message: '最低充值金额不能为空' })
  @IsNumber({}, { message: '最低充值金额必须是数字' })
  @Min(0.01, { message: '最低充值金额必须大于0' })
  minAmount: number;

  @ApiProperty({ description: '最高充值金额，0表示不限制', example: 10000.00 })
  @IsNotEmpty({ message: '最高充值金额不能为空' })
  @IsNumber({}, { message: '最高充值金额必须是数字' })
  @Min(0, { message: '最高充值金额不能小于0' })
  maxAmount: number;

  @ApiProperty({ description: '备注信息', required: false })
  @IsOptional()
  @IsString({ message: '备注信息必须是字符串' })
  @Length(0, 500, { message: '备注信息长度不能超过500字符' })
  remark?: string;
}

export class EffectiveBalanceRechargeConfigDto {
  @ApiProperty({ description: '配置ID' })
  id: number;

  @ApiProperty({ description: '挡位名称' })
  tierName: string;

  @ApiProperty({ description: '基础充值金额' })
  rechargeAmount: number;

  @ApiProperty({ description: '当前生效的充值金额（基础+活动赠送）' })
  effectiveRechargeAmount: number;

  @ApiProperty({ description: '活动赠送金额（仅活动期间有效）' })
  activityBonusAmount: number;

  @ApiProperty({ description: '是否在活动期间' })
  isActivityActive: boolean;

  @ApiProperty({ description: '活动状态描述' })
  activityStatusDescription: string;

  @ApiProperty({ description: '排序顺序' })
  sortOrder: number;
}
