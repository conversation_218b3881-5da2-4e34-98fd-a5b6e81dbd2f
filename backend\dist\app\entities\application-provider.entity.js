"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationProvider = void 0;
const typeorm_1 = require("typeorm");
const provider_environment_entity_1 = require("./provider-environment.entity");
let ApplicationProvider = class ApplicationProvider {
    id;
    providerCode;
    name;
    status;
    integrationType;
    commercialModelType;
    rateValue;
    billingCycle;
    billingCurrency;
    minimumGuarantee;
    amName;
    amEmail;
    techSupportEmail;
    financeEmail;
    notes;
    environments;
    createdAt;
    updatedAt;
    apiBaseUrl;
    apiKey;
    apiSecret;
    lobbyUrl;
    depositUrl;
    webhookUrl;
    supportedCurrencies;
    supportedLanguages;
    integrationConfig;
};
exports.ApplicationProvider = ApplicationProvider;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ApplicationProvider.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider_code', unique: true, length: 50 }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "providerCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'integration_type', length: 100 }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "integrationType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'commercial_model_type', length: 50, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "commercialModelType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'rate_value', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], ApplicationProvider.prototype, "rateValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'billing_cycle', length: 50, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "billingCycle", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'billing_currency', length: 10, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "billingCurrency", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'minimum_guarantee', type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], ApplicationProvider.prototype, "minimumGuarantee", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'am_name', length: 100, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "amName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'am_email', length: 255, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "amEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tech_support_email', length: 255, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "techSupportEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'finance_email', length: 255, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "financeEmail", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => provider_environment_entity_1.ProviderEnvironment, (environment) => environment.provider),
    __metadata("design:type", Array)
], ApplicationProvider.prototype, "environments", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], ApplicationProvider.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], ApplicationProvider.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_base_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "apiBaseUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_key', length: 255, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "apiKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_secret', length: 255, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "apiSecret", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'lobby_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "lobbyUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'deposit_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "depositUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'webhook_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], ApplicationProvider.prototype, "webhookUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'supported_currencies', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ApplicationProvider.prototype, "supportedCurrencies", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'supported_languages', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ApplicationProvider.prototype, "supportedLanguages", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'integration_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ApplicationProvider.prototype, "integrationConfig", void 0);
exports.ApplicationProvider = ApplicationProvider = __decorate([
    (0, typeorm_1.Entity)('application_providers')
], ApplicationProvider);
//# sourceMappingURL=application-provider.entity.js.map