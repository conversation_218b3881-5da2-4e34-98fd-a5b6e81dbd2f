import { AdConfigService } from './ad-config.service';
import { CreateAdConfigDto } from './dto/create-ad-config.dto';
import { UpdateAdConfigDto } from './dto/update-ad-config.dto';
import { AdConfigQueryDto } from './dto/ad-config-query.dto';
import { AdType } from './entities/ad-config.entity';
export declare class AdConfigController {
    private readonly adConfigService;
    constructor(adConfigService: AdConfigService);
    create(createAdConfigDto: CreateAdConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities/ad-config.entity").AdConfig;
    }>;
    findAll(query: AdConfigQueryDto): Promise<{
        code: number;
        message: string;
        result: {
            list: {
                adTypeName: string;
                creator: {
                    id: number;
                    username: string;
                } | null;
                updater: {
                    id: number;
                    username: string;
                } | null;
                id: number;
                adIdentifier: string;
                adType: AdType;
                title: string;
                imageItems: import("./entities/ad-config.entity").ImageItem[];
                sortOrder: number;
                status: number;
                remark: string;
                createdBy: number;
                updatedBy: number;
                createTime: Date;
                updateTime: Date;
            }[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    getAdTypeStats(): Promise<{
        code: number;
        message: string;
        result: {
            adType: number;
            adTypeName: string;
            total: number;
            enabled: number;
            disabled: number;
        }[];
    }>;
    findByType(adType: AdType): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            adIdentifier: string;
            adType: AdType;
            title: string;
            imageItems: import("./entities/ad-config.entity").ImageItem[];
            sortOrder: number;
        }[];
    }>;
    findByIdentifier(adIdentifier: string): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            adIdentifier: string;
            adType: AdType;
            title: string;
            imageItems: import("./entities/ad-config.entity").ImageItem[];
            sortOrder: number;
        };
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: {
            adTypeName: string;
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            adIdentifier: string;
            adType: AdType;
            title: string;
            imageItems: import("./entities/ad-config.entity").ImageItem[];
            sortOrder: number;
            status: number;
            remark: string;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    update(id: number, updateAdConfigDto: UpdateAdConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities/ad-config.entity").AdConfig;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
    }>;
    toggleStatus(id: number, req: any): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            status: number;
            message: string;
        };
    }>;
    updateSortOrder(updates: Array<{
        id: number;
        sortOrder: number;
    }>, req: any): Promise<{
        code: number;
        message: string;
    }>;
}
