"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysUser = void 0;
const typeorm_1 = require("typeorm");
const sys_role_entity_1 = require("./sys-role.entity");
let SysUser = class SysUser {
    id;
    username;
    email;
    password;
    phoneNumber;
    avatar;
    description;
    status;
    lastLoginTime;
    isSuperAdmin;
    roles;
    createTime;
    updateTime;
};
exports.SysUser = SysUser;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SysUser.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 50 }),
    __metadata("design:type", String)
], SysUser.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], SysUser.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ select: false }),
    __metadata("design:type", String)
], SysUser.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'phone_number', nullable: true, length: 20 }),
    __metadata("design:type", String)
], SysUser.prototype, "phoneNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], SysUser.prototype, "avatar", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 200 }),
    __metadata("design:type", String)
], SysUser.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], SysUser.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_login_time', nullable: true }),
    __metadata("design:type", Date)
], SysUser.prototype, "lastLoginTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_super_admin', default: false, comment: '是否为超级管理员：true-是，false-否' }),
    __metadata("design:type", Boolean)
], SysUser.prototype, "isSuperAdmin", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => sys_role_entity_1.SysRole, (role) => role.users),
    (0, typeorm_1.JoinTable)({
        name: 'sys_user_roles',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], SysUser.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], SysUser.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], SysUser.prototype, "updateTime", void 0);
exports.SysUser = SysUser = __decorate([
    (0, typeorm_1.Entity)('sys_users')
], SysUser);
//# sourceMappingURL=sys-user.entity.js.map