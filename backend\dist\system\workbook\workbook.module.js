"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemWorkbookModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const workbook_service_1 = require("./workbook.service");
const workbook_controller_1 = require("./workbook.controller");
const sys_workbook_entity_1 = require("../entities/sys-workbook.entity");
let SystemWorkbookModule = class SystemWorkbookModule {
};
exports.SystemWorkbookModule = SystemWorkbookModule;
exports.SystemWorkbookModule = SystemWorkbookModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sys_workbook_entity_1.SysWorkbook])],
        controllers: [workbook_controller_1.SystemWorkbookController],
        providers: [workbook_service_1.SystemWorkbookService],
        exports: [workbook_service_1.SystemWorkbookService],
    })
], SystemWorkbookModule);
//# sourceMappingURL=workbook.module.js.map