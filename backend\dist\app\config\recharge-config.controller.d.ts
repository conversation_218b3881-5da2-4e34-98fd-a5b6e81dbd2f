import { GoldRechargeConfigService } from './gold-recharge-config.service';
import { BalanceRechargeConfigService } from './balance-recharge-config.service';
import { CreateGoldRechargeConfigDto, UpdateGoldRechargeConfigDto, GoldRechargeConfigQueryDto } from './dto/gold-recharge-config.dto';
import { CreateBalanceRechargeConfigDto, UpdateBalanceRechargeConfigDto, BalanceRechargeConfigQueryDto, UpdateBalanceRechargeLimitDto } from './dto/balance-recharge-config.dto';
export declare class RechargeConfigController {
    private readonly goldRechargeConfigService;
    private readonly balanceRechargeConfigService;
    constructor(goldRechargeConfigService: GoldRechargeConfigService, balanceRechargeConfigService: BalanceRechargeConfigService);
    createGoldConfig(createDto: CreateGoldRechargeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").GoldRechargeConfig;
    }>;
    findAllGoldConfigs(query: GoldRechargeConfigQueryDto): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            tierName: string;
            goldAmount: number;
            price: number;
            activityBonusGold: number;
            activityStartTime: Date;
            activityEndTime: Date;
            sortOrder: number;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        }[];
    }>;
    findAllEffectiveGoldConfigs(): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            tierName: string;
            goldAmount: number;
            price: number;
            effectiveGoldAmount: number;
            activityBonusGold: number;
            isActivityActive: boolean;
            activityStatusDescription: string;
            sortOrder: number;
        }[];
    }>;
    getActiveGoldActivityConfigs(): Promise<{
        code: number;
        message: string;
        result: import("./entities").GoldRechargeConfig[];
    }>;
    findOneGoldConfig(id: number): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            tierName: string;
            goldAmount: number;
            price: number;
            activityBonusGold: number;
            activityStartTime: Date;
            activityEndTime: Date;
            sortOrder: number;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    updateGoldConfig(id: number, updateDto: UpdateGoldRechargeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").GoldRechargeConfig;
    }>;
    removeGoldConfig(id: number): Promise<{
        code: number;
        message: string;
    }>;
    updateGoldActivityTime(body: {
        startTime: string;
        endTime: string;
    }, req: any): Promise<{
        code: number;
        message: string;
    }>;
    createBalanceConfig(createDto: CreateBalanceRechargeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").BalanceRechargeConfig;
    }>;
    findAllBalanceConfigs(query: BalanceRechargeConfigQueryDto): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            tierName: string;
            rechargeAmount: number;
            activityBonusAmount: number;
            activityStartTime: Date;
            activityEndTime: Date;
            sortOrder: number;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        }[];
    }>;
    findAllEffectiveBalanceConfigs(): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            tierName: string;
            rechargeAmount: number;
            effectiveRechargeAmount: number;
            activityBonusAmount: number;
            isActivityActive: boolean;
            activityStatusDescription: string;
            sortOrder: number;
        }[];
    }>;
    getActiveBalanceActivityConfigs(): Promise<{
        code: number;
        message: string;
        result: import("./entities").BalanceRechargeConfig[];
    }>;
    getBalanceRechargeLimit(): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            limitName: string;
            minAmount: number;
            maxAmount: number;
            status: number;
            remark: string;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
            limitDescription: string;
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
        };
    }>;
    updateBalanceRechargeLimit(updateDto: UpdateBalanceRechargeLimitDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").BalanceRechargeLimit;
    }>;
    validateBalanceRechargeAmount(body: {
        amount: number;
    }): Promise<{
        code: number;
        message: string;
        result: {
            isValid: boolean;
            error?: string;
        };
    }>;
    findOneBalanceConfig(id: number): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            tierName: string;
            rechargeAmount: number;
            activityBonusAmount: number;
            activityStartTime: Date;
            activityEndTime: Date;
            sortOrder: number;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    updateBalanceConfig(id: number, updateDto: UpdateBalanceRechargeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").BalanceRechargeConfig;
    }>;
    removeBalanceConfig(id: number): Promise<{
        code: number;
        message: string;
    }>;
    updateBalanceActivityTime(body: {
        startTime: string;
        endTime: string;
    }, req: any): Promise<{
        code: number;
        message: string;
    }>;
}
