import { JwtService } from '@nestjs/jwt';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { SysUser } from '../entities/sys-user.entity';
export declare class SystemAuthService {
    private userRepository;
    private jwtService;
    private configService;
    constructor(userRepository: Repository<SysUser>, jwtService: JwtService, configService: ConfigService);
    validateUser(username: string, password: string): Promise<any>;
    login(user: any): Promise<{
        token: string;
        refreshToken: string;
        userInfo: {
            id: any;
            username: any;
            email: any;
            roles: any;
            isSuperAdmin: any;
        };
    }>;
    refreshToken(refreshToken: string): Promise<{
        token: string;
        refreshToken: string;
    }>;
    getUserInfo(userId: number, includeRoles?: boolean): Promise<any>;
    updateUserInfo(userId: number, updateData: any): Promise<any>;
    changePassword(userId: number, currentPassword: string, newPassword: string): Promise<{
        message: string;
    }>;
    resetAdminPassword(newPassword: string): Promise<{
        message: string;
    }>;
}
