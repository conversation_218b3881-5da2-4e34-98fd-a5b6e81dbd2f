import{j as m}from"./index-CHjq8S-S.js";import{an as u}from"./antd-CXPM1OiB.js";import"./react-BUTTOX-3.js";function a(e){return[{label:e("common.yes"),value:1},{label:e("common.no"),value:0}]}function i(e){return[{label:"父菜单",value:0},{label:"子菜单",value:1}]}function s(e){return[{dataIndex:"index",title:e("common.index"),valueType:"indexBorder",width:80},{title:e("system.menu.name"),dataIndex:"name",ellipsis:!0,width:200,render:(n,t)=>e(t.name),formItemProps:{rules:[{required:!0,message:e("form.required")}]}},{title:e("system.menu.routePath"),dataIndex:"path",width:120,filters:!0,onFilter:!0,ellipsis:!0},{title:e("system.menu.menuOrder"),dataIndex:"order",valueType:"digit",width:80},{title:e("system.menu.menuIcon"),dataIndex:"icon",width:130},{disable:!0,title:e("common.status"),dataIndex:"status",valueType:"select",width:80,render:(n,t)=>m.jsx(u,{color:t.status===1?"success":"default",children:n}),valueEnum:{1:{text:e("common.enabled")},0:{text:e("common.deactivated")}}},{title:e("system.menu.menuType"),dataIndex:"menuType",width:100,valueEnum:i().reduce((n,t)=>(n[t.value]=t.label,n),{})},{title:e("system.menu.componentUrl"),dataIndex:"component",width:120,search:!1},{title:e("system.menu.keepAlive"),dataIndex:"keepAlive",valueType:"select",width:80,render:(n,t)=>e(t.keepAlive?"common.yes":"common.no"),valueEnum:a(e).reduce((n,t)=>(n.set(t.value,t.label),n),new Map)},{title:e("system.menu.hideInMenu"),dataIndex:"hideInMenu",valueType:"select",width:120,render:(n,t)=>e(t.hideInMenu?"common.yes":"common.no"),valueEnum:a(e).reduce((n,t)=>(n.set(t.value,t.label),n),new Map)},{title:e("system.menu.currentActiveMenu"),dataIndex:"currentActiveMenu",width:120},{title:e("system.menu.iframeLink"),dataIndex:"iframeLink",width:120},{title:e("system.menu.externalLink"),dataIndex:"externalLink",width:120},{title:e("common.createTime"),dataIndex:"createTime",valueType:"date",width:150,search:!1},{title:e("common.updateTime"),dataIndex:"updateTime",valueType:"dateTime",width:170,search:!1}]}export{s as getConstantColumns,i as getMenuTypeOptions};
