{"version": 3, "file": "query-provider.dto.js", "sourceRoot": "", "sources": ["../../../../src/app/supplier/dto/query-provider.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAA4E;AAC5E,yDAAyC;AAEzC,MAAa,gBAAgB;IAW3B,IAAI,GAAY,CAAC,CAAC;IAYlB,QAAQ,GAAY,EAAE,CAAC;IASvB,IAAI,CAAU;IASd,YAAY,CAAU;IAWtB,MAAM,CAAU;IAWhB,eAAe,CAAU;IAWzB,mBAAmB,CAAU;IAY7B,MAAM,GAAY,WAAW,CAAC;IAY9B,SAAS,GAAoB,MAAM,CAAC;CACrC;AAnGD,4CAmGC;AAxFC;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,IAAI;QACjB,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,CAAC;QACV,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;8CACW;AAYlB;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;kDACgB;AASvB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACG;AASd;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACW;AAWtB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;QACpD,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;;gDACrC;AAWhB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,sBAAsB,CAAC;QACvE,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,iBAAiB,EAAE,oBAAoB,EAAE,sBAAsB,CAAC,CAAC;;yDAC/C;AAWzB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC;QACxD,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,WAAW,EAAE,eAAe,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;;6DAC5B;AAY7B;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC;QACxD,OAAO,EAAE,WAAW;QACpB,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;;gDAC3B;AAY9B;IAVC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,MAAM;QACnB,OAAO,EAAE,MAAM;QACf,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;QACrB,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,KAAK;KAChB,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;;mDACc"}