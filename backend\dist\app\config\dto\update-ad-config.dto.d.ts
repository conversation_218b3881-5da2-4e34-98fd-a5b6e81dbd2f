import { CreateAdConfigDto, CreateImageItemDto } from './create-ad-config.dto';
import { AdType } from '../entities/ad-config.entity';
declare const UpdateAdConfigDto_base: import("@nestjs/common").Type<Partial<CreateAdConfigDto>>;
export declare class UpdateAdConfigDto extends UpdateAdConfigDto_base {
    adIdentifier?: string;
    adType?: AdType;
    title?: string;
    imageItems?: CreateImageItemDto[];
    sortOrder?: number;
    status?: number;
    remark?: string;
}
export {};
