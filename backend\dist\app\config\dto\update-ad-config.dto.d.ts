import { CreateAdConfigDto } from './create-ad-config.dto';
import { AdType, JumpType } from '../entities/ad-config.entity';
declare const UpdateAdConfigDto_base: import("@nestjs/common").Type<Partial<CreateAdConfigDto>>;
export declare class UpdateAdConfigDto extends UpdateAdConfigDto_base {
    adIdentifier?: string;
    adType?: AdType;
    title?: string;
    images?: string[];
    jumpType?: JumpType;
    jumpTarget?: string;
    sortOrder?: number;
    status?: number;
    remark?: string;
}
export {};
