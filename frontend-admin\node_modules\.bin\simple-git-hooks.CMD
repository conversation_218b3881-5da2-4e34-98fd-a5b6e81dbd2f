@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\simple-git-hooks@2.12.1\node_modules\simple-git-hooks\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\simple-git-hooks@2.12.1\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\simple-git-hooks@2.12.1\node_modules\simple-git-hooks\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\simple-git-hooks@2.12.1\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\simple-git-hooks@2.12.1\node_modules\simple-git-hooks\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\simple-git-hooks@2.12.1\node_modules\simple-git-hooks\cli.js" %*
)
