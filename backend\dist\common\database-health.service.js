"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseHealthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let DatabaseHealthService = DatabaseHealthService_1 = class DatabaseHealthService {
    dataSource;
    logger = new common_1.Logger(DatabaseHealthService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async checkHealth() {
        const startTime = Date.now();
        try {
            const result = await this.dataSource.query('SELECT 1 as test');
            const latency = Date.now() - startTime;
            const connectionCount = await this.getConnectionCount();
            this.logger.log(`Database health check passed - Latency: ${latency}ms, Connections: ${connectionCount}`);
            return {
                status: 'healthy',
                latency,
                connectionCount,
                details: {
                    query_result: result,
                    timestamp: new Date().toISOString(),
                },
            };
        }
        catch (error) {
            const latency = Date.now() - startTime;
            this.logger.error(`Database health check failed - Latency: ${latency}ms, Error: ${error.message}`);
            return {
                status: 'unhealthy',
                latency,
                connectionCount: 0,
                details: {
                    error: error.message,
                    timestamp: new Date().toISOString(),
                },
            };
        }
    }
    async getConnectionCount() {
        try {
            const result = await this.dataSource.query("SELECT count(*) as count FROM pg_stat_activity WHERE state != 'idle'");
            return parseInt(result[0]?.count || '0');
        }
        catch (error) {
            this.logger.warn(`Failed to get connection count: ${error.message}`);
            return 0;
        }
    }
    async getPerformanceStats() {
        try {
            const [connectionStats, slowQueries] = await Promise.all([
                this.getConnectionStats(),
                this.getSlowQueries(),
            ]);
            return {
                connection_stats: connectionStats,
                slow_queries: slowQueries,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            this.logger.error(`Failed to get performance stats: ${error.message}`);
            return {
                error: error.message,
                timestamp: new Date().toISOString(),
            };
        }
    }
    async getConnectionStats() {
        try {
            const result = await this.dataSource.query(`
        SELECT 
          state,
          count(*) as count,
          avg(extract(epoch from (now() - query_start))) as avg_duration_seconds
        FROM pg_stat_activity 
        WHERE state IS NOT NULL
        GROUP BY state
      `);
            return result;
        }
        catch (error) {
            this.logger.warn(`Failed to get connection stats: ${error.message}`);
            return [];
        }
    }
    async getSlowQueries() {
        try {
            const result = await this.dataSource.query(`
        SELECT 
          pid,
          now() - pg_stat_activity.query_start AS duration,
          query,
          state
        FROM pg_stat_activity 
        WHERE (now() - pg_stat_activity.query_start) > interval '10 seconds'
        AND state != 'idle'
        ORDER BY duration DESC
        LIMIT 5
      `);
            return result;
        }
        catch (error) {
            this.logger.warn(`Failed to get slow queries: ${error.message}`);
            return [];
        }
    }
    async testLatency(iterations = 5) {
        const results = [];
        for (let i = 0; i < iterations; i++) {
            const startTime = Date.now();
            try {
                await this.dataSource.query('SELECT 1');
                const latency = Date.now() - startTime;
                results.push(latency);
            }
            catch (error) {
                this.logger.warn(`Latency test ${i + 1} failed: ${error.message}`);
                results.push(-1);
            }
            if (i < iterations - 1) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        const validResults = results.filter(r => r > 0);
        return {
            min: Math.min(...validResults),
            max: Math.max(...validResults),
            avg: validResults.reduce((a, b) => a + b, 0) / validResults.length,
            results,
        };
    }
};
exports.DatabaseHealthService = DatabaseHealthService;
exports.DatabaseHealthService = DatabaseHealthService = DatabaseHealthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], DatabaseHealthService);
//# sourceMappingURL=database-health.service.js.map