import{u as y,j as s,F as m}from"./index-CHjq8S-S.js";import{c as C,d as B,e as T}from"./index-DosLe4-o.js";import{a}from"./react-BUTTOX-3.js";import{B as w}from"./index-DDI4OfxQ.js";import{B as W}from"./index-S5qMhsyX.js";import{u as f}from"./useMutation-BkoFKhTK.js";import{Detail as D}from"./detail-xGlCjF-R.js";import{getConstantColumns as F}from"./constants-DSj366lU.js";import{aD as R,d as E,bB as M,s as i}from"./antd-CXPM1OiB.js";import"./Table-DTyH0rpt.js";import"./index-CZVy594C.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";import"./index-CnuJ2TqD.js";import"./index-DVrd-Evt.js";import"./index-CG1eZs-g.js";function Y(){const{t}=y(),u=a.useRef(),[h,r]=a.useState(!1),[b,d]=a.useState(""),[x,l]=a.useState(null),p=f({mutationFn:T,onSuccess:()=>{i.success(t("common.deleteSuccess")),c()},onError:o=>{i.error(o.message||t("common.deleteFailed"))}}),k=f({mutationFn:({id:o,status:e})=>B(o,e),onSuccess:()=>{i.success(t("common.updateSuccess")),c()},onError:o=>{i.error(o.message||t("common.updateFailed"))}}),c=()=>{var o;(o=u.current)==null||o.reload()},g=o=>{r(o),o||l(null)},j=[...F(t),{title:t("common.action"),valueType:"option",key:"option",width:250,fixed:"right",render:(o,e,z,P)=>{const n=[];return n.push(s.jsx(m,{type:"link",size:"small",onClick:()=>{l(e),r(!0),d(t("workbook.editWorkbook"))},children:t("common.edit")},"edit")),n.push(s.jsx(m,{type:"link",size:"small",onClick:()=>{const S=e.status===1?0:1;k.mutate({id:e.id,status:S})},loading:k.isPending,children:e.status===1?t("common.disable"):t("common.enable")},"toggle-status")),n.push(s.jsx(R,{title:t("common.deleteConfirm"),onConfirm:()=>p.mutate(e.id),okText:t("common.confirm"),cancelText:t("common.cancel"),children:s.jsx(m,{type:"link",size:"small",danger:!0,loading:p.isPending,children:t("common.delete")})},"delete")),n}}];return s.jsxs(w,{className:"h-full",children:[s.jsx(W,{columns:j,actionRef:u,request:async o=>{const e=await C(o);return{...e,data:e.result.list,total:e.result.total}},headerTitle:t("workbook.title"),toolBarRender:()=>[s.jsx(E,{icon:s.jsx(M,{}),type:"primary",onClick:()=>{l(null),r(!0),d(t("workbook.addWorkbook"))},children:t("common.add")},"add-workbook")],rowKey:"id",search:{labelWidth:"auto"}}),s.jsx(D,{title:b,open:h,onCloseChange:g,detailData:x,refreshTable:c})]})}export{Y as default};
