import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { AppUser, MarketingChannel, MarketingAd, PromotionalPage, DeviceLogRealtime } from '../../app/entities';
import { AppLoginDto, AppGoogleLoginDto, AppRegisterDto, AppLoginResponseDto } from './dto/app-login.dto';

@Injectable()
export class AppAuthService {
  constructor(
    @InjectRepository(AppUser)
    private readonly userRepository: Repository<AppUser>,
    @InjectRepository(MarketingChannel)
    private readonly channelRepository: Repository<MarketingChannel>,
    @InjectRepository(MarketingAd)
    private readonly adRepository: Repository<MarketingAd>,
    @InjectRepository(PromotionalPage)
    private readonly pageRepository: Repository<PromotionalPage>,
    @InjectRepository(DeviceLogRealtime)
    private readonly deviceLogRepository: Repository<DeviceLogRealtime>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 用户登录
   */
  async login(loginDto: AppLoginDto, ip: string, userAgent: string): Promise<AppLoginResponseDto> {
    const { loginType, identifier, password, deviceId, deviceInfo } = loginDto;

    // 根据登录类型查找用户
    let whereCondition: any = {};
    switch (loginType) {
      case 'username':
        whereCondition = { username: identifier };
        break;
      case 'email':
        whereCondition = { email: identifier };
        break;
      case 'phone':
        whereCondition = { phone: identifier };
        break;
      default:
        throw new BadRequestException('不支持的登录方式');
    }

    const user = await this.userRepository.findOne({
      where: whereCondition,
      select: ['id', 'uid', 'username', 'email', 'phone', 'password', 'nickname', 'avatar', 'status', 'vipLevel', 'rechargeBalance', 'goldBalance'],
    });

    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 检查用户状态
    if (user.status === 1) {
      throw new UnauthorizedException('账户已被封禁');
    }
    if (user.status === 2) {
      throw new UnauthorizedException('账户已注销');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 记录设备日志
    if (deviceId) {
      await this.recordDeviceLog(user.id, deviceId, ip, userAgent, 2); // 2: 登录
    }

    // 更新最后登录信息
    await this.userRepository.update(user.id, {
      lastLoginTime: new Date(),
      lastLoginIp: ip,
    });

    // 生成JWT令牌
    const tokens = await this.generateTokens(user);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      tokenType: 'Bearer',
      expiresIn: 7 * 24 * 60 * 60, // 7天
      user: {
        id: user.id,
        uid: user.uid,
        username: user.username,
        email: user.email,
        nickname: user.nickname,
        avatar: user.avatar,
        vipLevel: user.vipLevel,
        rechargeBalance: user.rechargeBalance,
        goldBalance: user.goldBalance,
      },
    };
  }

  /**
   * 用户注册
   */
  async register(registerDto: AppRegisterDto, ip: string, userAgent: string): Promise<AppLoginResponseDto> {
    const {
      username,
      email,
      phone,
      password,
      nickname,
      inviteCode,
      channelIdentifier,
      adIdentifier,
      pageIdentifier,
      deviceId,
      deviceInfo,
    } = registerDto;

    // 检查用户名是否已存在
    if (username) {
      const existingUser = await this.userRepository.findOne({ where: { username } });
      if (existingUser) {
        throw new ConflictException('用户名已存在');
      }
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingUser = await this.userRepository.findOne({ where: { email } });
      if (existingUser) {
        throw new ConflictException('邮箱已存在');
      }
    }

    // 检查手机号是否已存在
    if (phone) {
      const existingUser = await this.userRepository.findOne({ where: { phone } });
      if (existingUser) {
        throw new ConflictException('手机号已存在');
      }
    }

    // 处理邀请关系
    let inviter: AppUser | null = null;
    let invitationPath = '';
    if (inviteCode) {
      // 这里可以实现邀请码逻辑，暂时简化为直接查找用户ID
      const inviterId = parseInt(inviteCode);
      if (!isNaN(inviterId)) {
        inviter = await this.userRepository.findOne({ where: { id: inviterId } });
        if (inviter) {
          invitationPath = inviter.invitationPath ? `${inviter.invitationPath}${inviter.id}>` : `${inviter.id}>`;
        }
      }
    }

    // 处理营销归因
    let channelId: number | undefined;
    let adId: number | undefined;
    let promotionalPageId: number | undefined;
    let acquisitionTag = '';

    if (channelIdentifier) {
      const channel = await this.channelRepository.findOne({ where: { identifier: channelIdentifier } });
      if (channel) {
        channelId = channel.id;
        acquisitionTag = channel.identifier;
      }
    }

    if (adIdentifier && channelId) {
      const ad = await this.adRepository.findOne({ where: { identifier: adIdentifier, channelId } });
      if (ad) {
        adId = ad.id;
        acquisitionTag += `-${ad.identifier}`;
      }
    }

    if (pageIdentifier && adId) {
      const page = await this.pageRepository.findOne({ where: { identifier: pageIdentifier, adId } });
      if (page) {
        promotionalPageId = page.id;
        acquisitionTag += `-${page.identifier}`;
      }
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10);

    // 生成UID
    const lastUser = await this.userRepository.findOne({
      order: { id: 'DESC' },
      select: ['id'],
    });
    const uid = (lastUser?.id || 0) + 100000;

    // 创建用户
    const user = await this.userRepository.save({
      uid,
      username,
      email,
      phone,
      password: hashedPassword,
      nickname: nickname || username,
      inviterId: inviter?.id,
      invitationPath,
      channelId,
      adId,
      promotionalPageId,
      acquisitionTag,
      registerIp: ip,
      lastLoginIp: ip,
      lastLoginTime: new Date(),
    });

    // 记录设备日志
    if (deviceId) {
      await this.recordDeviceLog(user.id, deviceId, ip, userAgent, 1); // 1: 注册
    }

    // 生成JWT令牌
    const tokens = await this.generateTokens(user);

    return {
      accessToken: tokens.accessToken,
      refreshToken: tokens.refreshToken,
      tokenType: 'Bearer',
      expiresIn: 7 * 24 * 60 * 60, // 7天
      user: {
        id: user.id,
        uid: user.uid,
        username: user.username,
        email: user.email,
        nickname: user.nickname,
        avatar: user.avatar || '',
        vipLevel: user.vipLevel,
        rechargeBalance: user.rechargeBalance,
        goldBalance: user.goldBalance,
      },
    };
  }

  /**
   * 生成JWT令牌
   */
  private async generateTokens(user: AppUser) {
    const payload = {
      sub: user.id,
      uid: user.uid,
      username: user.username,
      type: 'app',
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN'),
    });

    return { accessToken, refreshToken };
  }

  /**
   * 记录设备日志
   */
  private async recordDeviceLog(
    userId: number,
    deviceId: string,
    ipAddress: string,
    userAgent: string,
    logType: number,
  ) {
    await this.deviceLogRepository.save({
      userId,
      deviceId,
      ipAddress,
      userAgent,
      logType,
    });
  }

  /**
   * 刷新令牌
   */
  async refreshToken(refreshToken: string) {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      const user = await this.userRepository.findOne({
        where: { id: payload.sub },
        select: ['id', 'uid', 'username', 'email', 'status'],
      });

      if (!user || user.status !== 0) {
        throw new UnauthorizedException('用户不存在或已被禁用');
      }

      const tokens = await this.generateTokens(user);
      return tokens;
    } catch (error) {
      throw new UnauthorizedException('刷新令牌无效');
    }
  }
}
