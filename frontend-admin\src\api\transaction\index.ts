import { request } from '#src/utils';
import type {
  UserAssetStatistics,
  TransactionStatistics,
  TransactionListQuery,
  TransactionListResponse,
  CreateTransactionParams,
} from './types';

// API基础路径
const API_PREFIX = 'transactions';

export const transactionApi = {
  /**
   * 获取用户资产统计
   */
  getUserAssetStatistics: (userId: number): Promise<UserAssetStatistics> => {
    return request.get<ApiResponse<UserAssetStatistics>>(`${API_PREFIX}/users/${userId}/statistics`).json().then(res => res.result);
  },

  /**
   * 获取用户现金统计
   */
  getCashStatistics: (userId: number): Promise<TransactionStatistics> => {
    return request.get<ApiResponse<TransactionStatistics>>(`${API_PREFIX}/users/${userId}/cash/statistics`).json().then(res => res.result);
  },

  /**
   * 获取用户金币统计
   */
  getGoldStatistics: (userId: number): Promise<TransactionStatistics> => {
    return request.get<ApiResponse<TransactionStatistics>>(`${API_PREFIX}/users/${userId}/gold/statistics`).json().then(res => res.result);
  },

  /**
   * 获取用户充值余额统计
   */
  getRechargeStatistics: (userId: number): Promise<TransactionStatistics> => {
    return request.get<ApiResponse<TransactionStatistics>>(`${API_PREFIX}/users/${userId}/recharge/statistics`).json().then(res => res.result);
  },

  /**
   * 获取用户现金交易明细
   */
  getCashTransactionList: (userId: number, params?: Omit<TransactionListQuery, 'userId'>): Promise<TransactionListResponse> => {
    const searchParams = params ? Object.fromEntries(
      Object.entries(params).map(([key, value]) => [key, String(value)])
    ) : undefined;
    return request.get<ApiResponse<TransactionListResponse>>(`${API_PREFIX}/users/${userId}/cash/list`, { searchParams }).json().then(res => res.result);
  },

  /**
   * 获取用户金币交易明细
   */
  getGoldTransactionList: (userId: number, params?: Omit<TransactionListQuery, 'userId'>): Promise<TransactionListResponse> => {
    const searchParams = params ? Object.fromEntries(
      Object.entries(params).map(([key, value]) => [key, String(value)])
    ) : undefined;
    return request.get<ApiResponse<TransactionListResponse>>(`${API_PREFIX}/users/${userId}/gold/list`, { searchParams }).json().then(res => res.result);
  },

  /**
   * 获取用户充值余额交易明细
   */
  getRechargeTransactionList: (userId: number, params?: Omit<TransactionListQuery, 'userId'>): Promise<TransactionListResponse> => {
    const searchParams = params ? Object.fromEntries(
      Object.entries(params).map(([key, value]) => [key, String(value)])
    ) : undefined;
    return request.get<ApiResponse<TransactionListResponse>>(`${API_PREFIX}/users/${userId}/recharge/list`, { searchParams }).json().then(res => res.result);
  },

  /**
   * 创建现金交易记录
   */
  createCashTransaction: (params: CreateTransactionParams): Promise<any> => {
    return request.post<ApiResponse<any>>(`${API_PREFIX}/cash`, { json: params }).json().then(res => res.result);
  },

  /**
   * 创建金币交易记录
   */
  createGoldTransaction: (params: CreateTransactionParams): Promise<any> => {
    return request.post<ApiResponse<any>>(`${API_PREFIX}/gold`, { json: params }).json().then(res => res.result);
  },

  /**
   * 创建充值余额交易记录
   */
  createRechargeTransaction: (params: CreateTransactionParams): Promise<any> => {
    return request.post<ApiResponse<any>>(`${API_PREFIX}/recharge`, { json: params }).json().then(res => res.result);
  },
};
