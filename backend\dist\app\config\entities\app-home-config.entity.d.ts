import { SysUser } from '../../../system/entities/sys-user.entity';
import { AdConfig } from './ad-config.entity';
import { AppHomeRecommendedGame } from './app-home-recommended-game.entity';
import { AppHomeGameCategory } from './app-home-game-category.entity';
export declare class AppHomeConfig {
    id: number;
    configName: string;
    description: string;
    topFloatAdId: number;
    carouselAdId: number;
    homeGridAdId: number;
    splashPopupAdId: number;
    floatAdId: number;
    status: number;
    sortOrder: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    topFloatAd: AdConfig;
    carouselAd: AdConfig;
    homeGridAd: AdConfig;
    splashPopupAd: AdConfig;
    floatAd: AdConfig;
    recommendedGames: AppHomeRecommendedGame[];
    gameCategories: AppHomeGameCategory[];
    isEnabled(): boolean;
    hasRequiredAds(): boolean;
    getAdCount(): number;
    validateConfiguration(): {
        valid: boolean;
        errors: string[];
    };
}
