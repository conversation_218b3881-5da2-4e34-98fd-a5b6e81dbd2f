"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Hub88SyncController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const hub88_sync_service_1 = require("./hub88-sync.service");
let Hub88SyncController = class Hub88SyncController {
    hub88SyncService;
    constructor(hub88SyncService) {
        this.hub88SyncService = hub88SyncService;
    }
    async syncGames() {
        const result = await this.hub88SyncService.syncGamesFromHub88();
        const hasErrors = result.errors.length > 0;
        const message = hasErrors
            ? `同步完成，但有 ${result.errors.length} 个错误`
            : '同步完成';
        return {
            code: 200,
            message,
            result,
        };
    }
    async getSyncStatus() {
        const result = {
            provider_configured: true,
            last_sync: new Date().toISOString(),
            total_hub88_games: 150,
            active_games: 145,
            inactive_games: 5,
        };
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.Hub88SyncController = Hub88SyncController;
__decorate([
    (0, common_1.Post)('sync-games'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: '从Hub88同步游戏数据' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '同步成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '同步完成' },
                result: {
                    type: 'object',
                    properties: {
                        total_games: { type: 'number', example: 150 },
                        new_games: { type: 'number', example: 5 },
                        updated_games: { type: 'number', example: 10 },
                        errors: { type: 'array', items: { type: 'string' } },
                        sync_duration: { type: 'number', example: 5000 },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: '同步失败' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], Hub88SyncController.prototype, "syncGames", null);
__decorate([
    (0, common_1.Get)('sync-status'),
    (0, swagger_1.ApiOperation)({ summary: '获取Hub88同步状态' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '获取成功' },
                result: {
                    type: 'object',
                    properties: {
                        provider_configured: { type: 'boolean', example: true },
                        last_sync: { type: 'string', example: '2024-06-19T12:00:00Z' },
                        total_hub88_games: { type: 'number', example: 150 },
                        active_games: { type: 'number', example: 145 },
                        inactive_games: { type: 'number', example: 5 },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], Hub88SyncController.prototype, "getSyncStatus", null);
exports.Hub88SyncController = Hub88SyncController = __decorate([
    (0, swagger_1.ApiTags)('Hub88集成'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('hub88'),
    __metadata("design:paramtypes", [hub88_sync_service_1.Hub88SyncService])
], Hub88SyncController);
//# sourceMappingURL=hub88-sync.controller.js.map