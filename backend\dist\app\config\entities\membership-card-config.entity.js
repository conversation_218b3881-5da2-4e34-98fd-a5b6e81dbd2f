"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipCardConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
let MembershipCardConfig = class MembershipCardConfig {
    id;
    cardType;
    cardName;
    price;
    description;
    dailyGoldBase;
    dailyGoldActivityRatio;
    cashDiscountBase;
    cashDiscountActivity;
    activityStartTime;
    activityEndTime;
    status;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    isActivityActive() {
        const now = new Date();
        if (!this.activityStartTime || !this.activityEndTime) {
            return false;
        }
        return now >= this.activityStartTime && now <= this.activityEndTime;
    }
    getEffectiveDailyGold() {
        if (this.isActivityActive()) {
            return Math.floor(this.dailyGoldBase * this.dailyGoldActivityRatio);
        }
        return this.dailyGoldBase;
    }
    getEffectiveCashDiscount() {
        if (this.isActivityActive()) {
            return this.cashDiscountBase + this.cashDiscountActivity;
        }
        return this.cashDiscountBase;
    }
    getActivityStatus() {
        const now = new Date();
        if (!this.activityStartTime || !this.activityEndTime) {
            return { isActive: false, description: '暂无活动' };
        }
        if (now < this.activityStartTime) {
            return {
                isActive: false,
                description: `活动未开始，将于 ${this.activityStartTime.toLocaleString()} 开始`
            };
        }
        if (now > this.activityEndTime) {
            return {
                isActive: false,
                description: `活动已结束，于 ${this.activityEndTime.toLocaleString()} 结束`
            };
        }
        return {
            isActive: true,
            description: `活动进行中，将于 ${this.activityEndTime.toLocaleString()} 结束`
        };
    }
    getActivityBonusGold() {
        if (this.isActivityActive()) {
            return Math.floor(this.dailyGoldBase * (this.dailyGoldActivityRatio - 1));
        }
        return 0;
    }
    getActivityBonusDiscount() {
        return this.isActivityActive() ? this.cashDiscountActivity : 0;
    }
};
exports.MembershipCardConfig = MembershipCardConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'card_type',
        type: 'varchar',
        length: 20,
        unique: true,
        comment: '会员卡类型：silver-银卡，gold-金卡，diamond-钻石卡，king-王者卡'
    }),
    __metadata("design:type", String)
], MembershipCardConfig.prototype, "cardType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'card_name', length: 50, comment: '会员卡显示名称' }),
    __metadata("design:type", String)
], MembershipCardConfig.prototype, "cardName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 10,
        scale: 2,
        comment: '会员卡价格（元）'
    }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: '会员功能介绍'
    }),
    __metadata("design:type", String)
], MembershipCardConfig.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'daily_gold_base',
        type: 'bigint',
        default: 0,
        comment: '每天基础金币数量'
    }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "dailyGoldBase", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'daily_gold_activity_ratio',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 1.00,
        comment: '活动期间金币赠送比例，如1.5表示150%'
    }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "dailyGoldActivityRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cash_discount_base',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 0.00,
        comment: '充值现金基础优惠百分比，如0.1表示10%'
    }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "cashDiscountBase", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cash_discount_activity',
        type: 'decimal',
        precision: 5,
        scale: 2,
        default: 0.00,
        comment: '活动期间额外优惠百分比'
    }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "cashDiscountActivity", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activity_start_time',
        type: 'timestamp with time zone',
        nullable: true,
        comment: '活动开始时间'
    }),
    __metadata("design:type", Date)
], MembershipCardConfig.prototype, "activityStartTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activity_end_time',
        type: 'timestamp with time zone',
        nullable: true,
        comment: '活动结束时间'
    }),
    __metadata("design:type", Date)
], MembershipCardConfig.prototype, "activityEndTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], MembershipCardConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], MembershipCardConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], MembershipCardConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], MembershipCardConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], MembershipCardConfig.prototype, "updater", void 0);
exports.MembershipCardConfig = MembershipCardConfig = __decorate([
    (0, typeorm_1.Entity)('membership_card_configs')
], MembershipCardConfig);
//# sourceMappingURL=membership-card-config.entity.js.map