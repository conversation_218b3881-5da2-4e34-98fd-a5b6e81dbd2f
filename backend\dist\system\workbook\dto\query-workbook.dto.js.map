{"version": 3, "file": "query-workbook.dto.js", "sourceRoot": "", "sources": ["../../../../src/system/workbook/dto/query-workbook.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAuE;AACvE,yDAAyC;AAEzC,MAAa,gBAAgB;IAK3B,OAAO,GAAY,CAAC,CAAC;IAMrB,QAAQ,GAAY,EAAE,CAAC;IAKvB,IAAI,CAAU;IAKd,IAAI,CAAU;IAKd,KAAK,CAAU;IAUf,SAAS,CAAU;IAOnB,MAAM,CAAU;CACjB;AA5CD,4CA4CC;AAvCC;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;;iDACU;AAMrB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;;kDACY;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IACrD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACG;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC3D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8CACG;AAKd;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC1D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACI;AAUf;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,KAAK;QAClB,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;KACjC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;;mDACd;AAOnB;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;gDACG"}