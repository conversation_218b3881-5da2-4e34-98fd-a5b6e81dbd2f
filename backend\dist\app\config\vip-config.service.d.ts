import { Repository, DataSource } from 'typeorm';
import { VipConfig } from './entities/vip-config.entity';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction } from '../entities/cash-transaction.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';
import { CreateVipConfigDto } from './dto/create-vip-config.dto';
import { UpdateVipConfigDto } from './dto/update-vip-config.dto';
import { VipConfigQueryDto } from './dto/vip-config-query.dto';
export declare class VipConfigService {
    private vipConfigRepository;
    private appUserRepository;
    private cashTransactionRepository;
    private goldTransactionRepository;
    private dataSource;
    constructor(vipConfigRepository: Repository<VipConfig>, appUserRepository: Repository<AppUser>, cashTransactionRepository: Repository<CashTransaction>, goldTransactionRepository: Repository<GoldTransaction>, dataSource: DataSource);
    create(createVipConfigDto: CreateVipConfigDto, userId: number): Promise<VipConfig>;
    findAll(query: VipConfigQueryDto): Promise<{
        list: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            vipLevel: number;
            levelName: string;
            requiredPoints: number;
            balanceRatio: number;
            cashRatio: number;
            goldRatio: number;
            dailyGoldReward: number;
            status: number;
            remark: string;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(id: number): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        vipLevel: number;
        levelName: string;
        requiredPoints: number;
        balanceRatio: number;
        cashRatio: number;
        goldRatio: number;
        dailyGoldReward: number;
        status: number;
        remark: string;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }>;
    update(id: number, updateVipConfigDto: UpdateVipConfigDto, userId: number): Promise<VipConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    private validatePointsIncrement;
    calculateUserPoints(userId: number): Promise<{
        totalPoints: number;
        breakdown: any;
    }>;
    updateUserVipLevel(userId: number): Promise<{
        oldLevel: number;
        newLevel: number;
        points: number;
    }>;
    recalculateAllUserVipLevels(): Promise<{
        processed: number;
        upgraded: number;
    }>;
    getActiveVipLevels(): Promise<VipConfig[]>;
}
