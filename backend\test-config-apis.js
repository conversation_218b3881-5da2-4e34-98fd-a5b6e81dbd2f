const http = require('http');

const BASE_URL = 'http://localhost:3000/api';

function makeRequest(url, method = 'GET', headers = {}, data = null) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: headers
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(body);
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve({ data: jsonData, status: res.statusCode });
          } else {
            reject({ response: { data: jsonData, status: res.statusCode } });
          }
        } catch (e) {
          reject({ message: 'Invalid JSON response', body });
        }
      });
    });

    req.on('error', (err) => {
      reject({ message: err.message });
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testConfigAPIs() {
  try {
    console.log('🔐 正在登录获取token...');

    // 登录获取token
    const loginResponse = await makeRequest(`${BASE_URL}/auth/login`, 'POST', {
      'Content-Type': 'application/json'
    }, {
      username: 'admin',
      password: 'admin123'
    });

    const token = loginResponse.data.result?.token || loginResponse.data.result?.accessToken || loginResponse.data.accessToken;
    if (!token) {
      console.error('登录响应:', JSON.stringify(loginResponse.data, null, 2));
      throw new Error('无法获取访问token');
    }
    console.log('✅ 登录成功，获取到token:', token.substring(0, 50) + '...');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('\n📋 测试配置管理API...');

    // 测试金币充值配置
    console.log('\n🪙 测试金币充值配置API:');
    try {
      const goldResponse = await makeRequest(`${BASE_URL}/config/recharge/gold`, 'GET', headers);
      console.log('✅ 金币充值配置API正常，返回数据数量:', goldResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ 金币充值配置API错误:', error.response?.data || error.message);
    }

    // 测试余额充值配置
    console.log('\n💰 测试余额充值配置API:');
    try {
      const balanceResponse = await makeRequest(`${BASE_URL}/config/recharge/balance`, 'GET', headers);
      console.log('✅ 余额充值配置API正常，返回数据数量:', balanceResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ 余额充值配置API错误:', error.response?.data || error.message);
    }

    // 测试余额充值限制
    console.log('\n🔒 测试余额充值限制API:');
    try {
      console.log('请求URL:', `${BASE_URL}/config/recharge/balance/limits`);
      console.log('请求头:', headers);
      const limitsResponse = await makeRequest(`${BASE_URL}/config/recharge/balance/limits`, 'GET', headers);
      console.log('✅ 余额充值限制API正常，返回数据:', limitsResponse.data.result);
    } catch (error) {
      console.error('❌ 余额充值限制API错误:', error.response?.data || error.message);
      console.error('错误状态码:', error.response?.status);
      console.error('错误详情:', JSON.stringify(error.response?.data, null, 2));
    }

    // 测试VIP配置
    console.log('\n👑 测试VIP配置API:');
    try {
      const vipResponse = await makeRequest(`${BASE_URL}/config/vip`, 'GET', headers);
      console.log('✅ VIP配置API正常，返回数据数量:', vipResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ VIP配置API错误:', error.response?.data || error.message);
    }

    // 测试会员卡配置
    console.log('\n💳 测试会员卡配置API:');
    try {
      const membershipResponse = await makeRequest(`${BASE_URL}/config/membership-cards`, 'GET', headers);
      console.log('✅ 会员卡配置API正常，返回数据数量:', membershipResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ 会员卡配置API错误:', error.response?.data || error.message);
    }

    // 测试广告配置
    console.log('\n📢 测试广告配置API:');
    try {
      const adResponse = await makeRequest(`${BASE_URL}/config/ads`, 'GET', headers);
      console.log('✅ 广告配置API正常，返回数据数量:', adResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ 广告配置API错误:', error.response?.data || error.message);
    }

    // 测试APP首页配置
    console.log('\n🏠 测试APP首页配置API:');
    try {
      const homeResponse = await makeRequest(`${BASE_URL}/config/app-home`, 'GET', headers);
      console.log('✅ APP首页配置API正常，返回数据数量:', homeResponse.data.result?.length || 0);
    } catch (error) {
      console.error('❌ APP首页配置API错误:', error.response?.data || error.message);
    }
    
    console.log('\n🎉 配置管理API测试完成!');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
  }
}

testConfigAPIs();
