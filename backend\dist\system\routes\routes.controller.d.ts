import { SystemRoutesService } from './routes.service';
export declare class SystemRoutesController {
    private readonly routesService;
    constructor(routesService: SystemRoutesService);
    getRoutes(req: any): Promise<{
        code: number;
        message: string;
        result: any[];
    }>;
    getDefaultRoutes(): Promise<{
        code: number;
        message: string;
        result: ({
            path: string;
            handle: {
                icon: string;
                title: string;
                order: number;
            };
            children?: undefined;
        } | {
            path: string;
            handle: {
                icon: string;
                title: string;
                order: number;
            };
            children: {
                path: string;
                handle: {
                    icon: string;
                    title: string;
                    order: number;
                };
            }[];
        })[];
    }>;
    getUserPermissions(req: any): Promise<{
        code: number;
        message: string;
        result: string[];
    }>;
}
