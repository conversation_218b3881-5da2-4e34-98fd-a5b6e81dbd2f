"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeGameCategory = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
const app_home_config_entity_1 = require("./app-home-config.entity");
const app_home_category_game_entity_1 = require("./app-home-category-game.entity");
let AppHomeGameCategory = class AppHomeGameCategory {
    id;
    homeConfigId;
    categoryTitle;
    sortOrder;
    status;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    homeConfig;
    categoryGames;
    creator;
    updater;
    isEnabled() {
        return this.status === 1;
    }
    getTitle(language = 'zh-CN') {
        return this.categoryTitle?.[language] || this.categoryTitle?.['zh-CN'] || '未命名分类';
    }
    getAllTitles() {
        return this.categoryTitle || {};
    }
    setTitle(language, title) {
        if (!this.categoryTitle) {
            this.categoryTitle = {};
        }
        this.categoryTitle[language] = title;
    }
    validateTitles() {
        const errors = [];
        if (!this.categoryTitle) {
            errors.push('分类标题不能为空');
            return { valid: false, errors };
        }
        const requiredLanguages = ['zh-CN', 'en-US'];
        for (const lang of requiredLanguages) {
            if (!this.categoryTitle[lang]?.trim()) {
                errors.push(`缺少${lang}语言的标题`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    getGameCount() {
        return this.categoryGames?.filter(game => game.isEnabled()).length || 0;
    }
    hasMinimumGames() {
        return this.getGameCount() >= 4;
    }
};
exports.AppHomeGameCategory = AppHomeGameCategory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'home_config_id', comment: '关联app_home_configs表' }),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "homeConfigId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'category_title',
        type: 'jsonb',
        comment: '分类标题（多语言支持）格式：{"zh-CN": "热门游戏", "en-US": "Hot Games"}'
    }),
    __metadata("design:type", Object)
], AppHomeGameCategory.prototype, "categoryTitle", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sort_order', default: 0, comment: '排序，数字越小越靠前' }),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], AppHomeGameCategory.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppHomeGameCategory.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppHomeGameCategory.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_home_config_entity_1.AppHomeConfig, (homeConfig) => homeConfig.gameCategories),
    (0, typeorm_1.JoinColumn)({ name: 'home_config_id' }),
    __metadata("design:type", app_home_config_entity_1.AppHomeConfig)
], AppHomeGameCategory.prototype, "homeConfig", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_home_category_game_entity_1.AppHomeCategoryGame, (categoryGame) => categoryGame.homeCategory),
    __metadata("design:type", Array)
], AppHomeGameCategory.prototype, "categoryGames", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeGameCategory.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeGameCategory.prototype, "updater", void 0);
exports.AppHomeGameCategory = AppHomeGameCategory = __decorate([
    (0, typeorm_1.Entity)('app_home_game_categories')
], AppHomeGameCategory);
//# sourceMappingURL=app-home-game-category.entity.js.map