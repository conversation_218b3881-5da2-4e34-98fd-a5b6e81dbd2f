"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUserListResponseDto = exports.AppUserListItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AppUserListItemDto {
    id;
    uid;
    username;
    email;
    phone;
    nickname;
    avatar;
    gender;
    rechargeBalance;
    goldBalance;
    withdrawableBalance;
    vipLevel;
    status;
    accountType;
    isVerified;
    riskScore;
    kycStatus;
    inviterId;
    channelName;
    adName;
    adIdentifier;
    acquisitionTag;
    tags;
    lastLoginTime;
    daysNotLoggedIn;
    registerIp;
    createTime;
}
exports.AppUserListItemDto = AppUserListItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '对外展示的用户ID' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "uid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '昵称' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像URL' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '性别：0-未知，1-男，2-女' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值余额：用户直接现金充值的钱' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "rechargeBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币余额：用户充值购买的金币，VIP卡领取,和各种活动赠送的游戏体验币' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "goldBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '可提现余额：用户各种推广奖励，或者游戏中的现金' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "withdrawableBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户状态：0-正常，1-已封禁，2-已注销' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '账户类型：0-普通用户，1-内部员工' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "accountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否认证：0-未认证，1-已认证' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "isVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '风险分值' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "riskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KYC状态：0-未认证，1-审核中，2-已通过，3-已拒绝' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "kycStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请人ID', required: false }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "inviterId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道名称', required: false }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "channelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告名称', required: false }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "adName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识', required: false }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获取标签', required: false }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "acquisitionTag", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户标签', required: false }),
    __metadata("design:type", Array)
], AppUserListItemDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    __metadata("design:type", Date)
], AppUserListItemDto.prototype, "lastLoginTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '未登录天数' }),
    __metadata("design:type", Number)
], AppUserListItemDto.prototype, "daysNotLoggedIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册IP' }),
    __metadata("design:type", String)
], AppUserListItemDto.prototype, "registerIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], AppUserListItemDto.prototype, "createTime", void 0);
class AppUserListResponseDto {
    list;
    total;
    page;
    pageSize;
    totalPages;
}
exports.AppUserListResponseDto = AppUserListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户列表', type: [AppUserListItemDto] }),
    __metadata("design:type", Array)
], AppUserListResponseDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数' }),
    __metadata("design:type", Number)
], AppUserListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页码' }),
    __metadata("design:type", Number)
], AppUserListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量' }),
    __metadata("design:type", Number)
], AppUserListResponseDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数' }),
    __metadata("design:type", Number)
], AppUserListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=app-user-list.dto.js.map