import{a as f,x as c}from"./index-CHjq8S-S.js";import{e as A}from"./react-BUTTOX-3.js";function B(){const n=A(),{roles:o,isSuperAdmin:u}=f(),a=n[n.length-1];return{hasAccessByCodes:s=>{var r;if(u)return!0;if(!s)return!1;const t=(r=a==null?void 0:a.handle)==null?void 0:r.permissions;return!t||!Array.isArray(t)?!1:(s=c(s)?[s]:s,s.some(h=>t.includes(h)))},hasAccessByRoles:s=>u?!0:!s||!o?!1:(s=c(s)?[s]:s,s=s.map(e=>e.toLowerCase()),o.some(e=>s.includes(e.toLowerCase()))),hasGlobalPermission:(s,t)=>{if(u)return!0;if(!s)return!1;const e=t||[];return s=c(s)?[s]:s,s.some(r=>e.includes(r))}}}export{B as u};
