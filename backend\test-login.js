const http = require('http');

// HTTP请求辅助函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            data: JSON.parse(body)
          };
          resolve(response);
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testLogin() {
  try {
    console.log('🔐 测试登录...');
    const loginOptions = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };
    
    const loginResponse = await makeRequest(loginOptions, {
      username: 'admin',
      password: 'admin123'
    });
    
    console.log('📊 登录响应状态:', loginResponse.status);
    console.log('📋 登录响应数据:', JSON.stringify(loginResponse.data, null, 2));
    
    if (loginResponse.status === 200 && loginResponse.data.result) {
      const token = loginResponse.data.result.accessToken;
      console.log('✅ 获取到token:', token.substring(0, 50) + '...');
      
      // 测试使用token访问受保护的API
      console.log('\n🔒 测试使用token访问受保护的API...');
      const testOptions = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/config/ads?page=1&pageSize=5',
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      };
      
      const testResponse = await makeRequest(testOptions);
      console.log('📊 API测试响应状态:', testResponse.status);
      console.log('📋 API测试响应数据:', JSON.stringify(testResponse.data, null, 2));
      
    } else {
      console.log('❌ 登录失败');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testLogin();
