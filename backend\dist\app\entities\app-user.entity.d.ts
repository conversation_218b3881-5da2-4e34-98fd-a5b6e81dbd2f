import { AppOrder } from './app-order.entity';
import { AppUserAddress } from './app-user-address.entity';
import { MarketingChannel } from './marketing-channel.entity';
import { MarketingAd } from './marketing-ad.entity';
import { PromotionalPage } from './promotional-page.entity';
export declare class AppUser {
    id: number;
    uid: number;
    username: string;
    email: string;
    phone: string;
    googleId: string;
    password: string;
    fundPasswordHash: string;
    nickname: string;
    avatar: string;
    gender: number;
    birthday: Date;
    rechargeBalance: number;
    goldBalance: number;
    withdrawableBalance: number;
    vipLevel: number;
    vipExp: number;
    status: number;
    accountType: number;
    isVerified: number;
    riskScore: number;
    kycStatus: number;
    kycRejectReason: string;
    inviterId: number;
    invitationPath: string;
    channelId: number;
    adId: number;
    promotionalPageId: number;
    acquisitionTag: string;
    tags: any;
    lastLoginTime: Date;
    lastLoginIp: string;
    registerIp: string;
    inviter: AppUser;
    channel: MarketingChannel;
    ad: MarketingAd;
    promotionalPage: PromotionalPage;
    orders: AppOrder[];
    addresses: AppUserAddress[];
    createTime: Date;
    updateTime: Date;
}
