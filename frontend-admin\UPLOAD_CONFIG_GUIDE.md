# 上传配置指南

## 概述

本项目支持多环境的图片上传配置，可以在本地开发、Supabase环境和生产环境之间灵活切换。

## 环境配置文件

### 1. 开发环境 (`.env`)
默认的开发环境配置，使用Supabase存储。

### 2. 本地环境 (`.env.local`)
本地开发专用配置，使用独立的文件夹和较小的文件限制。

### 3. 生产环境 (`.env.production`)
生产环境配置，支持更大的文件和更高的图片质量。

### 4. 示例配置 (`.env.example`)
配置模板，包含所有可用的配置选项。

## 配置选项说明

### 基础配置
```env
# 环境类型 (local | supabase | production)
VITE_ENVIRONMENT=supabase

# 后端API地址
VITE_API_BASE_URL=/api

# 应用标题
VITE_GLOB_APP_TITLE=InApp2 管理后台
```

### Supabase配置
```env
# Supabase项目URL
VITE_SUPABASE_URL=https://your-project.supabase.co

# Supabase匿名密钥
VITE_SUPABASE_ANON_KEY=your_anon_key
```

### S3存储配置
```env
# S3访问密钥
VITE_SUPABASE_S3_ACCESS_KEY_ID=your_access_key

# S3秘密密钥
VITE_SUPABASE_S3_SECRET_ACCESS_KEY=your_secret_key

# S3端点
VITE_SUPABASE_S3_ENDPOINT=https://your-project.supabase.co/storage/v1/s3

# S3区域
VITE_SUPABASE_S3_REGION=ap-southeast-1

# 存储桶名称
VITE_SUPABASE_S3_BUCKET=inda

# 文件夹名称
VITE_SUPABASE_S3_FOLDER=ads
```

### 文件上传配置
```env
# 最大文件大小 (字节)
VITE_MAX_FILE_SIZE=5242880

# 允许的文件类型
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/svg+xml,image/webp
```

### 图片压缩配置
```env
# 压缩后最大宽度
VITE_COMPRESSION_MAX_WIDTH=1920

# 压缩后最大高度
VITE_COMPRESSION_MAX_HEIGHT=1080

# 压缩质量 (0.1-1.0)
VITE_COMPRESSION_QUALITY=0.8
```

## 环境差异

### 本地环境 (local)
- 文件夹: `local-ads`
- 文件大小限制: 3MB
- 压缩质量: 0.7 (较低)
- 压缩尺寸: 1280x720

### Supabase环境 (supabase)
- 文件夹: `ads`
- 文件大小限制: 5MB
- 压缩质量: 0.8 (中等)
- 压缩尺寸: 1920x1080

### 生产环境 (production)
- 文件夹: `ads`
- 文件大小限制: 10MB
- 压缩质量: 0.9 (高质量)
- 压缩尺寸: 2560x1440

## 使用方法

### 1. 环境切换
通过修改 `VITE_ENVIRONMENT` 变量来切换环境：
```env
VITE_ENVIRONMENT=local     # 本地环境
VITE_ENVIRONMENT=supabase  # Supabase环境
VITE_ENVIRONMENT=production # 生产环境
```

### 2. 配置验证
系统会自动验证配置的完整性，如果配置无效会在控制台输出错误信息。

### 3. 调试信息
在开发环境下，可以通过以下方式查看当前配置：
```javascript
import { getConfigInfo } from '@/utils/upload-config';
console.log(getConfigInfo());
```

## API使用

### 基础上传
```javascript
import { uploadImageToSupabase } from '@/utils/supabase-upload';

const imageUrl = await uploadImageToSupabase(file);
```

### 压缩上传
```javascript
import { uploadCompressedImageToSupabase } from '@/utils/supabase-upload';

const imageUrl = await uploadCompressedImageToSupabase(file, 'ads', {
  maxWidth: 1920,
  maxHeight: 1080,
  quality: 0.8
});
```

### 批量上传
```javascript
import { uploadMultipleImagesToSupabase } from '@/utils/supabase-upload';

const results = await uploadMultipleImagesToSupabase(files, 'ads', (progress, current, total) => {
  console.log(`上传进度: ${progress}% (${current}/${total})`);
});
```

### 删除图片
```javascript
import { deleteImageFromSupabase } from '@/utils/supabase-upload';

const success = await deleteImageFromSupabase(imageUrl);
```

## 故障排除

### 1. 配置验证失败
检查环境变量是否正确设置，特别是S3相关的配置。

### 2. 上传失败
- 检查文件大小是否超过限制
- 检查文件类型是否在允许列表中
- 检查S3访问权限

### 3. 图片无法访问
- 检查存储桶是否为公共访问
- 检查文件路径是否正确
- 检查S3端点配置

## 安全注意事项

1. **不要在客户端暴露敏感信息**: S3密钥等敏感信息应该通过环境变量管理
2. **使用最小权限原则**: S3访问密钥应该只有必要的权限
3. **定期轮换密钥**: 建议定期更换S3访问密钥
4. **监控使用情况**: 定期检查存储使用情况和访问日志

## 后端配置

后端也需要相应的环境配置，请确保 `backend/.env.local` 中包含相同的Supabase配置：

```env
# Supabase存储配置
SUPABASE_URL=https://ytrftwscazjboxbwnrxp.supabase.co
SUPABASE_ANON_KEY=your_anon_key

# Supabase S3存储配置
SUPABASE_S3_ACCESS_KEY_ID=your_access_key
SUPABASE_S3_SECRET_ACCESS_KEY=your_secret_key
SUPABASE_S3_ENDPOINT=https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/s3
SUPABASE_S3_REGION=ap-southeast-1
SUPABASE_S3_BUCKET=inda
SUPABASE_S3_FOLDER=ads
```
