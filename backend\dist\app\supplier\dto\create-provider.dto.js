"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProviderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateProviderDto {
    providerCode;
    name;
    status;
    integrationType;
    commercialModelType;
    rateValue;
    billingCycle;
    billingCurrency;
    minimumGuarantee;
    amName;
    amEmail;
    techSupportEmail;
    financeEmail;
    notes;
}
exports.CreateProviderDto = CreateProviderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商唯一代码', example: 'PG_SOFT' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Length)(1, 50),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "providerCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商官方显示名称', example: 'PG Soft' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '合作状态',
        example: 'active',
        enum: ['active', 'inactive', 'testing', 'suspended']
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['active', 'inactive', 'testing', 'suspended']),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '技术集成方案',
        example: 'api_integration',
        enum: ['api_integration', 'iframe_integration', 'redirect_integration']
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['api_integration', 'iframe_integration', 'redirect_integration']),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "integrationType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '合作模式',
        example: 'ggr_share',
        enum: ['ggr_share', 'revenue_share', 'fixed_fee', 'cpa'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['ggr_share', 'revenue_share', 'fixed_fee', 'cpa']),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "commercialModelType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '费率或金额 (例如: 15.0000 表示 15%)',
        example: 15.0000,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 4 }),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateProviderDto.prototype, "rateValue", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结算周期',
        example: 'monthly',
        enum: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['daily', 'weekly', 'monthly', 'quarterly', 'yearly']),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "billingCycle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '结算币种',
        example: 'USD',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 10),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "billingCurrency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '每月最低保证金额',
        example: 10000.00,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProviderDto.prototype, "minimumGuarantee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '商务经理姓名',
        example: 'John Smith',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 100),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "amName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '商务经理邮箱',
        example: '<EMAIL>',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "amEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '技术支持邮箱',
        example: '<EMAIL>',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "techSupportEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '财务联系邮箱',
        example: '<EMAIL>',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "financeEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '内部备注',
        example: 'PG Soft是知名的游戏供应商，主要提供老虎机游戏',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProviderDto.prototype, "notes", void 0);
//# sourceMappingURL=create-provider.dto.js.map