"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoleModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const role_service_1 = require("./role.service");
const role_controller_1 = require("./role.controller");
const sys_role_entity_1 = require("../entities/sys-role.entity");
const sys_permission_entity_1 = require("../entities/sys-permission.entity");
let SystemRoleModule = class SystemRoleModule {
};
exports.SystemRoleModule = SystemRoleModule;
exports.SystemRoleModule = SystemRoleModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sys_role_entity_1.SysRole, sys_permission_entity_1.SysPermission])],
        controllers: [role_controller_1.SystemRoleController],
        providers: [role_service_1.SystemRoleService],
        exports: [role_service_1.SystemRoleService],
    })
], SystemRoleModule);
//# sourceMappingURL=role.module.js.map