import { Controller, Get, Post, Body, Query, Param, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TransactionService } from './transaction.service';
import { SystemJwtAuthGuard } from '../../system/auth/guards/jwt-auth.guard';
import {
  CreateTransactionDto,
  TransactionListQueryDto,
  TransactionStatisticsResponseDto,
  UserAssetStatisticsResponseDto,
  TransactionListResponseDto,
} from './dto/transaction.dto';

@ApiTags('交易流水管理')
@ApiBearerAuth()
@UseGuards(SystemJwtAuthGuard)
@Controller('transactions')
export class TransactionController {
  constructor(private readonly transactionService: TransactionService) {}

  @Post('cash')
  @ApiOperation({ summary: '创建现金交易记录' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createCashTransaction(@Body() createTransactionDto: CreateTransactionDto) {
    const result = await this.transactionService.createCashTransaction(createTransactionDto);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Post('gold')
  @ApiOperation({ summary: '创建金币交易记录' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createGoldTransaction(@Body() createTransactionDto: CreateTransactionDto) {
    const result = await this.transactionService.createGoldTransaction(createTransactionDto);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Post('recharge')
  @ApiOperation({ summary: '创建充值余额交易记录' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createRechargeTransaction(@Body() createTransactionDto: CreateTransactionDto) {
    const result = await this.transactionService.createRechargeTransaction(createTransactionDto);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Get('users/:userId/statistics')
  @ApiOperation({ summary: '获取用户资产统计' })
  @ApiResponse({ status: 200, description: '获取成功', type: UserAssetStatisticsResponseDto })
  async getUserAssetStatistics(@Param('userId', ParseIntPipe) userId: number) {
    const result = await this.transactionService.getUserAssetStatistics(userId);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/cash/statistics')
  @ApiOperation({ summary: '获取用户现金统计' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionStatisticsResponseDto })
  async getCashStatistics(@Param('userId', ParseIntPipe) userId: number) {
    const result = await this.transactionService.getCashStatistics(userId);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/gold/statistics')
  @ApiOperation({ summary: '获取用户金币统计' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionStatisticsResponseDto })
  async getGoldStatistics(@Param('userId', ParseIntPipe) userId: number) {
    const result = await this.transactionService.getGoldStatistics(userId);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/recharge/statistics')
  @ApiOperation({ summary: '获取用户充值余额统计' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionStatisticsResponseDto })
  async getRechargeStatistics(@Param('userId', ParseIntPipe) userId: number) {
    const result = await this.transactionService.getRechargeStatistics(userId);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/cash/list')
  @ApiOperation({ summary: '获取用户现金交易明细' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionListResponseDto })
  async getCashTransactionList(
    @Param('userId', ParseIntPipe) userId: number,
    @Query() query: Omit<TransactionListQueryDto, 'userId'>,
  ) {
    const result = await this.transactionService.getCashTransactionList({ ...query, userId });
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/gold/list')
  @ApiOperation({ summary: '获取用户金币交易明细' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionListResponseDto })
  async getGoldTransactionList(
    @Param('userId', ParseIntPipe) userId: number,
    @Query() query: Omit<TransactionListQueryDto, 'userId'>,
  ) {
    const result = await this.transactionService.getGoldTransactionList({ ...query, userId });
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('users/:userId/recharge/list')
  @ApiOperation({ summary: '获取用户充值余额交易明细' })
  @ApiResponse({ status: 200, description: '获取成功', type: TransactionListResponseDto })
  async getRechargeTransactionList(
    @Param('userId', ParseIntPipe) userId: number,
    @Query() query: Omit<TransactionListQueryDto, 'userId'>,
  ) {
    const result = await this.transactionService.getRechargeTransactionList({ ...query, userId });
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }
}
