import { Repository } from 'typeorm';
import { AdConfig, AdType } from './entities/ad-config.entity';
import { CreateAdConfigDto } from './dto/create-ad-config.dto';
import { UpdateAdConfigDto } from './dto/update-ad-config.dto';
import { AdConfigQueryDto } from './dto/ad-config-query.dto';
export declare class AdConfigService {
    private adConfigRepository;
    constructor(adConfigRepository: Repository<AdConfig>);
    create(createAdConfigDto: CreateAdConfigDto, userId: number): Promise<AdConfig>;
    findAll(query: AdConfigQueryDto): Promise<{
        list: {
            adTypeName: string;
            jumpTypeName: string;
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            adIdentifier: string;
            adType: AdType;
            title: string;
            images: string[];
            jumpType: import("./entities/ad-config.entity").JumpType;
            jumpTarget: string;
            sortOrder: number;
            status: number;
            remark: string;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        }[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findOne(id: number): Promise<{
        adTypeName: string;
        jumpTypeName: string;
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        adIdentifier: string;
        adType: AdType;
        title: string;
        images: string[];
        jumpType: import("./entities/ad-config.entity").JumpType;
        jumpTarget: string;
        sortOrder: number;
        status: number;
        remark: string;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }>;
    update(id: number, updateAdConfigDto: UpdateAdConfigDto, userId: number): Promise<AdConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    toggleStatus(id: number, userId: number): Promise<{
        id: number;
        status: number;
        message: string;
    }>;
    updateSortOrder(updates: Array<{
        id: number;
        sortOrder: number;
    }>, userId: number): Promise<{
        message: string;
    }>;
    findByIdentifier(adIdentifier: string): Promise<{
        id: number;
        adIdentifier: string;
        adType: AdType;
        title: string;
        images: string[];
        jumpType: import("./entities/ad-config.entity").JumpType;
        jumpTarget: string;
        sortOrder: number;
    }>;
    findByType(adType: AdType): Promise<{
        id: number;
        adIdentifier: string;
        adType: AdType;
        title: string;
        images: string[];
        jumpType: import("./entities/ad-config.entity").JumpType;
        jumpTarget: string;
        sortOrder: number;
    }[]>;
    private validateImagesByType;
    getAdTypeStats(): Promise<{
        adType: number;
        adTypeName: string;
        total: number;
        enabled: number;
        disabled: number;
    }[]>;
    private getAdTypeNameByValue;
}
