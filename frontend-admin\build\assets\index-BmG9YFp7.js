import{j as t}from"./index-CHjq8S-S.js";import{a as l}from"./react-BUTTOX-3.js";import{B as Q}from"./index-DDI4OfxQ.js";import{n as H,o as J,p as W,q as X,r as Y,s as Z}from"./index-jASoOOIw.js";import{av as i,az as A,an as d,x as B,d as g,aq as ee,a6 as te,Q as f,ak as se,aT as ie,M as E,I as ae,q as o,aF as F,S as re,s as a,al as c,aC as q}from"./antd-CXPM1OiB.js";const me=()=>{const[N,j]=l.useState([]),[R,p]=l.useState([]),[m,L]=l.useState(null),[k,u]=l.useState(!1),[O,y]=l.useState(!1),[V,v]=l.useState(!1),[n,w]=l.useState(null),[$,G]=l.useState("gold"),[h]=i.useForm(),[b]=i.useForm(),I=async()=>{u(!0);try{const e=await H();e.code===200?e.result&&e.result.list?j(Array.isArray(e.result.list)?e.result.list:[]):Array.isArray(e.result)?j(e.result):j([]):a.error(e.message||"获取金币充值配置失败")}catch(e){a.error("获取金币充值配置失败"),console.error("获取金币充值配置失败:",e)}finally{u(!1)}},S=async()=>{u(!0);try{const e=await J();e.code===200?e.result&&e.result.list?p(Array.isArray(e.result.list)?e.result.list:[]):Array.isArray(e.result)?p(e.result):p([]):a.error(e.message||"获取余额充值配置失败")}catch(e){a.error("获取余额充值配置失败"),console.error("获取余额充值配置失败:",e)}finally{u(!1)}},T=async()=>{try{const e=await W();e.code===200?L(e.result||null):a.error(e.message||"获取余额充值限制失败")}catch(e){a.error("获取余额充值限制失败"),console.error("获取余额充值限制失败:",e)}},M=async(e,s,r)=>{try{const x=r==="gold"?await X(e,s):await Y(e,s);x.code===200?(a.success("更新成功"),y(!1),r==="gold"?I():S()):a.error(x.message||"更新失败")}catch(x){a.error("更新失败"),console.error("更新配置失败:",x)}},z=async e=>{try{const s=await Z(e);s.code===200?(a.success("更新成功"),v(!1),T()):a.error(s.message||"更新失败")}catch(s){a.error("更新失败"),console.error("更新余额充值限制失败:",s)}};l.useEffect(()=>{I(),S(),T()},[]);const C=(e,s)=>{if(!e||!s)return!1;const r=c();return r.isAfter(c(e))&&r.isBefore(c(s))},D=[{title:"挡位名称",dataIndex:"tierName",key:"tierName"},{title:"金币数量",dataIndex:"goldAmount",key:"goldAmount",render:e=>`${Number(e||0).toLocaleString()} 金币`},{title:"价格",dataIndex:"price",key:"price",render:e=>`¥${Number(e||0).toFixed(2)}`},{title:"活动赠送金币",dataIndex:"activityBonusGold",key:"activityBonusGold",render:e=>`${Number(e||0).toLocaleString()} 金币`},{title:"活动状态",key:"activityStatus",render:(e,s)=>{const r=C(s.activityStartTime,s.activityEndTime);return t.jsx(d,{color:r?"green":"default",children:r?"活动中":"非活动期"})}},{title:"排序",dataIndex:"sortOrder",key:"sortOrder"},{title:"状态",dataIndex:"status",key:"status",render:e=>t.jsx(d,{color:e===1?"green":"red",children:e===1?"启用":"禁用"})},{title:"操作",key:"action",render:(e,s)=>t.jsx(f,{children:t.jsx(g,{type:"primary",size:"small",icon:t.jsx(q,{}),onClick:()=>{w({...s,type:"gold"}),h.setFieldsValue({...s,activityStartTime:s.activityStartTime?c(s.activityStartTime):null,activityEndTime:s.activityEndTime?c(s.activityEndTime):null}),y(!0)},children:"编辑"})})}],P=[{title:"挡位名称",dataIndex:"tierName",key:"tierName"},{title:"充值金额",dataIndex:"rechargeAmount",key:"rechargeAmount",render:e=>`¥${Number(e||0).toFixed(2)}`},{title:"活动赠送金额",dataIndex:"activityBonusAmount",key:"activityBonusAmount",render:e=>`¥${Number(e||0).toFixed(2)}`},{title:"活动状态",key:"activityStatus",render:(e,s)=>{const r=C(s.activityStartTime,s.activityEndTime);return t.jsx(d,{color:r?"green":"default",children:r?"活动中":"非活动期"})}},{title:"排序",dataIndex:"sortOrder",key:"sortOrder"},{title:"状态",dataIndex:"status",key:"status",render:e=>t.jsx(d,{color:e===1?"green":"red",children:e===1?"启用":"禁用"})},{title:"操作",key:"action",render:(e,s)=>t.jsx(f,{children:t.jsx(g,{type:"primary",size:"small",icon:t.jsx(q,{}),onClick:()=>{w({...s,type:"balance"}),h.setFieldsValue({...s,activityStartTime:s.activityStartTime?c(s.activityStartTime):null,activityEndTime:s.activityEndTime?c(s.activityEndTime):null}),y(!0)},children:"编辑"})})}],_=async e=>{if(n){const s={...e,activityStartTime:e.activityStartTime?e.activityStartTime.toISOString():null,activityEndTime:e.activityEndTime?e.activityEndTime.toISOString():null};await M(n.id,s,n.type)}},K=async e=>{await z(e)},U=[{key:"gold",label:"金币充值配置",children:t.jsx(A,{columns:D,dataSource:N,rowKey:"id",loading:k,pagination:!1,size:"middle"})},{key:"balance",label:"余额充值配置",children:t.jsxs("div",{children:[t.jsxs("div",{style:{marginBottom:16,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[t.jsx("div",{children:m&&t.jsx(d,{color:"blue",icon:t.jsx(B,{}),children:m.limitDescription})}),t.jsx(g,{type:"primary",icon:t.jsx(B,{}),onClick:()=>{m&&(b.setFieldsValue(m),v(!0))},children:"设置充值限制"})]}),t.jsx(A,{columns:P,dataSource:R,rowKey:"id",loading:k,pagination:!1,size:"middle"})]})}];return t.jsxs(Q,{children:[t.jsx(ee,{title:t.jsxs(f,{children:[t.jsx(ie,{}),"充值配置管理"]}),extra:t.jsx(f,{children:t.jsx(g,{icon:t.jsx(se,{}),onClick:()=>{I(),S(),T()},children:"刷新"})}),children:t.jsx(te,{activeKey:$,onChange:G,items:U})}),t.jsx(E,{title:`编辑${(n==null?void 0:n.type)==="gold"?"金币":"余额"}充值配置`,open:O,onCancel:()=>y(!1),onOk:()=>h.submit(),width:800,children:t.jsxs(i,{form:h,layout:"vertical",onFinish:_,children:[t.jsx(i.Item,{label:"挡位名称",name:"tierName",rules:[{required:!0,message:"请输入挡位名称"}],children:t.jsx(ae,{})}),(n==null?void 0:n.type)==="gold"?t.jsxs(t.Fragment,{children:[t.jsx(i.Item,{label:"金币数量",name:"goldAmount",rules:[{required:!0,message:"请输入金币数量"}],children:t.jsx(o,{min:1,style:{width:"100%"}})}),t.jsx(i.Item,{label:"价格（元）",name:"price",rules:[{required:!0,message:"请输入价格"}],children:t.jsx(o,{min:.01,step:.01,style:{width:"100%"}})}),t.jsx(i.Item,{label:"活动赠送金币数量",name:"activityBonusGold",rules:[{required:!0,message:"请输入活动赠送金币数量"}],children:t.jsx(o,{min:0,style:{width:"100%"}})})]}):t.jsxs(t.Fragment,{children:[t.jsx(i.Item,{label:"充值金额（元）",name:"rechargeAmount",rules:[{required:!0,message:"请输入充值金额"}],children:t.jsx(o,{min:.01,step:.01,style:{width:"100%"}})}),t.jsx(i.Item,{label:"活动赠送金额（元）",name:"activityBonusAmount",rules:[{required:!0,message:"请输入活动赠送金额"}],children:t.jsx(o,{min:0,step:.01,style:{width:"100%"}})})]}),t.jsx(i.Item,{label:"排序顺序",name:"sortOrder",rules:[{required:!0,message:"请输入排序顺序"}],children:t.jsx(o,{min:0,style:{width:"100%"}})}),t.jsx(i.Item,{label:"活动开始时间",name:"activityStartTime",children:t.jsx(F,{showTime:!0,style:{width:"100%"}})}),t.jsx(i.Item,{label:"活动结束时间",name:"activityEndTime",children:t.jsx(F,{showTime:!0,style:{width:"100%"}})}),t.jsx(i.Item,{label:"状态",name:"status",valuePropName:"checked",getValueFromEvent:e=>e?1:0,getValueProps:e=>({checked:e===1}),children:t.jsx(re,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})}),t.jsx(E,{title:"设置余额充值限制",open:V,onCancel:()=>v(!1),onOk:()=>b.submit(),width:600,children:t.jsxs(i,{form:b,layout:"vertical",onFinish:K,children:[t.jsx(i.Item,{label:"最低充值金额（元）",name:"minAmount",rules:[{required:!0,message:"请输入最低充值金额"}],children:t.jsx(o,{min:.01,step:.01,style:{width:"100%"}})}),t.jsx(i.Item,{label:"最高充值金额（元，0表示不限制）",name:"maxAmount",rules:[{required:!0,message:"请输入最高充值金额"}],children:t.jsx(o,{min:0,step:.01,style:{width:"100%"}})})]})})]})};export{me as default};
