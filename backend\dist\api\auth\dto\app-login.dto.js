"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppLoginResponseDto = exports.AppRegisterDto = exports.AppGoogleLoginDto = exports.AppLoginDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class AppLoginDto {
    loginType;
    identifier;
    password;
    deviceId;
    deviceInfo;
}
exports.AppLoginDto = AppLoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录方式：username/email/phone', example: 'username' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppLoginDto.prototype, "loginType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '登录标识（用户名/邮箱/手机号）', example: 'testuser1' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppLoginDto.prototype, "identifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码', example: '123456' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppLoginDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppLoginDto.prototype, "deviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppLoginDto.prototype, "deviceInfo", void 0);
class AppGoogleLoginDto {
    idToken;
    deviceId;
    deviceInfo;
}
exports.AppGoogleLoginDto = AppGoogleLoginDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Google ID Token' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppGoogleLoginDto.prototype, "idToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppGoogleLoginDto.prototype, "deviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppGoogleLoginDto.prototype, "deviceInfo", void 0);
class AppRegisterDto {
    username;
    email;
    phone;
    password;
    nickname;
    inviteCode;
    channelIdentifier;
    adIdentifier;
    pageIdentifier;
    deviceId;
    deviceInfo;
}
exports.AppRegisterDto = AppRegisterDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名', example: 'newuser' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱', example: '<EMAIL>' }),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号', example: '13800138888', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '密码', example: '123456' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '昵称', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请码', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "inviteCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道标识', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "channelIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推广页面标识', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "pageIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "deviceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '设备信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppRegisterDto.prototype, "deviceInfo", void 0);
class AppLoginResponseDto {
    accessToken;
    refreshToken;
    tokenType;
    expiresIn;
    user;
}
exports.AppLoginResponseDto = AppLoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '访问令牌' }),
    __metadata("design:type", String)
], AppLoginResponseDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '刷新令牌' }),
    __metadata("design:type", String)
], AppLoginResponseDto.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '令牌类型', example: 'Bearer' }),
    __metadata("design:type", String)
], AppLoginResponseDto.prototype, "tokenType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '过期时间（秒）' }),
    __metadata("design:type", Number)
], AppLoginResponseDto.prototype, "expiresIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户信息' }),
    __metadata("design:type", Object)
], AppLoginResponseDto.prototype, "user", void 0);
//# sourceMappingURL=app-login.dto.js.map