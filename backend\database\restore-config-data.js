const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: '**************',
  port: 5435,
  database: 'inapp2',
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
};

async function restoreConfigData() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔗 连接到数据库...');
    await client.connect();
    
    console.log('📋 开始恢复配置管理数据...');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'restore-config-data.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // 执行SQL
    await client.query(sqlContent);
    
    console.log('✅ 配置管理数据恢复完成！');
    
    // 验证数据恢复情况
    console.log('\n📊 验证数据恢复情况:');
    
    const tables = [
      'vip_configs',
      'membership_card_configs', 
      'gold_recharge_configs',
      'balance_recharge_configs',
      'balance_recharge_limits',
      'ad_configs',
      'app_home_configs',
      'app_home_recommended_games',
      'app_home_game_categories',
      'app_home_category_games'
    ];
    
    for (const table of tables) {
      const result = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
      const count = result.rows[0].count;
      console.log(`  ${table}: ${count} 条记录`);
    }
    
  } catch (error) {
    console.error('❌ 恢复失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await client.end();
  }
}

// 运行恢复
if (require.main === module) {
  restoreConfigData().catch(console.error);
}

module.exports = { restoreConfigData };
