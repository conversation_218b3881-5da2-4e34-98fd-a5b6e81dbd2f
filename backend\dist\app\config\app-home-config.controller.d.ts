import { AppHomeConfigService } from './app-home-config.service';
import { CreateAppHomeConfigDto, UpdateAppHomeConfigDto, QueryAppHomeConfigDto, AppHomeConfigDetailResponseDto, AppHomeConfigPageResponseDto } from './dto';
export declare class AppHomeConfigController {
    private readonly appHomeConfigService;
    constructor(appHomeConfigService: AppHomeConfigService);
    create(createDto: CreateAppHomeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").AppHomeConfig;
    }>;
    findAll(query: QueryAppHomeConfigDto): Promise<{
        code: number;
        message: string;
        result: AppHomeConfigPageResponseDto;
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: AppHomeConfigDetailResponseDto;
    }>;
    update(id: number, updateDto: UpdateAppHomeConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").AppHomeConfig;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
    }>;
    toggleStatus(id: number, req: any): Promise<{
        code: number;
        message: string;
        result: {
            status: number;
        };
    }>;
}
