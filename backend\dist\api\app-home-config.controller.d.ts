import { AppHomeConfigService } from '../app/config/app-home-config.service';
export declare class AppHomeConfigApiController {
    private readonly appHomeConfigService;
    constructor(appHomeConfigService: AppHomeConfigService);
    getAppHomeConfig(id: number): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            configName: string;
            ads: {
                topFloat: {
                    id: number;
                    adIdentifier: string;
                    title: string;
                    adType: import("../app/config/entities/ad-config.entity").AdType;
                    imageItems: import("../app/config/entities/ad-config.entity").ImageItem[];
                } | null;
                carousel: {
                    id: number;
                    adIdentifier: string;
                    title: string;
                    adType: import("../app/config/entities/ad-config.entity").AdType;
                    imageItems: import("../app/config/entities/ad-config.entity").ImageItem[];
                } | null;
                homeGrid: {
                    id: number;
                    adIdentifier: string;
                    title: string;
                    adType: import("../app/config/entities/ad-config.entity").AdType;
                    imageItems: import("../app/config/entities/ad-config.entity").ImageItem[];
                } | null;
                splashPopup: {
                    id: number;
                    adIdentifier: string;
                    title: string;
                    adType: import("../app/config/entities/ad-config.entity").AdType;
                    imageItems: import("../app/config/entities/ad-config.entity").ImageItem[];
                } | null;
                float: {
                    id: number;
                    adIdentifier: string;
                    title: string;
                    adType: import("../app/config/entities/ad-config.entity").AdType;
                    imageItems: import("../app/config/entities/ad-config.entity").ImageItem[];
                } | null;
            };
            recommendedGames: {
                id: number;
                name: string;
                iconUrl: string;
                posterUrl: string;
                categories: string[];
                rtp: number;
                volatility: string;
                status: string;
            }[];
            gameCategories: {
                id: number;
                categoryTitle: import("../app/config/entities").CategoryTitle;
                games: {
                    id: number;
                    name: string;
                    iconUrl: string;
                    posterUrl: string;
                    categories: string[];
                    rtp: number;
                    volatility: string;
                    status: string;
                }[];
            }[];
        };
    }>;
}
