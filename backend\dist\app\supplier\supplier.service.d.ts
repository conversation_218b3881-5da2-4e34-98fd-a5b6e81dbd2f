import { Repository } from 'typeorm';
import { ApplicationProvider, ProviderEnvironment } from '../entities';
import { CreateProviderDto, UpdateProviderDto, CreateEnvironmentDto, UpdateEnvironmentDto, QueryProviderDto } from './dto';
export declare class SupplierService {
    private providerRepository;
    private environmentRepository;
    constructor(providerRepository: Repository<ApplicationProvider>, environmentRepository: Repository<ProviderEnvironment>);
    createProvider(createProviderDto: CreateProviderDto): Promise<ApplicationProvider>;
    findSimpleProviders(): Promise<ApplicationProvider[]>;
    findProviders(queryDto: QueryProviderDto): Promise<{
        list: ApplicationProvider[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findProviderById(id: number): Promise<ApplicationProvider>;
    updateProvider(id: number, updateProviderDto: UpdateProviderDto): Promise<ApplicationProvider>;
    removeProvider(id: number): Promise<void>;
    createEnvironment(providerId: number, createEnvironmentDto: CreateEnvironmentDto): Promise<ProviderEnvironment>;
    findEnvironmentsByProviderId(providerId: number): Promise<ProviderEnvironment[]>;
    findEnvironmentById(id: number): Promise<ProviderEnvironment>;
    updateEnvironment(id: number, updateEnvironmentDto: UpdateEnvironmentDto): Promise<ProviderEnvironment>;
    removeEnvironment(id: number): Promise<void>;
    toggleEnvironmentStatus(id: number): Promise<ProviderEnvironment>;
}
