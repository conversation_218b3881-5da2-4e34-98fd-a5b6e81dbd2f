export declare enum UserStatus {
    NORMAL = 0,
    BANNED = 1,
    DEACTIVATED = 2
}
export declare enum AccountType {
    NORMAL_USER = 0,
    INTERNAL_EMPLOYEE = 1
}
export declare enum KycStatus {
    NOT_VERIFIED = 0,
    UNDER_REVIEW = 1,
    APPROVED = 2,
    REJECTED = 3
}
export declare class QueryAppUserDto {
    page?: number;
    pageSize?: number;
    id?: number;
    username?: string;
    email?: string;
    phone?: string;
    nickname?: string;
    status?: UserStatus;
    accountType?: AccountType;
    kycStatus?: KycStatus;
    channelId?: number;
    adId?: number;
    inviterId?: number;
    minRiskScore?: number;
    maxRiskScore?: number;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}
