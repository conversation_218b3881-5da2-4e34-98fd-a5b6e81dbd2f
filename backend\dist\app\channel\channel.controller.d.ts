import { ChannelService } from './channel.service';
import { CreateChannelDto, UpdateChannelDto, QueryChannelDto, CreateAdDto, UpdateAdDto, QueryAdDto } from './dto';
export declare class ChannelController {
    private readonly channelService;
    constructor(channelService: ChannelService);
    createChannel(createChannelDto: CreateChannelDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel;
    }>;
    findChannels(queryDto: QueryChannelDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").MarketingChannel[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    findSimpleChannels(): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel[];
    }>;
    createAd(createAdDto: CreateAdDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingAd;
    }>;
    findAds(queryDto: QueryAdDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").MarketingAd[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    findAdById(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingAd;
    }>;
    updateAd(id: number, updateAdDto: UpdateAdDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingAd;
    }>;
    toggleAdStatus(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingAd;
    }>;
    findChannelById(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel;
    }>;
    updateChannel(id: number, updateChannelDto: UpdateChannelDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel;
    }>;
    toggleChannelStatus(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel;
    }>;
}
