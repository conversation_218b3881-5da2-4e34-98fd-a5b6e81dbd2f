const http = require('http');

// HTTP请求辅助函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const response = {
            status: res.statusCode,
            data: JSON.parse(body)
          };
          resolve(response);
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试新的广告配置API结构
async function testAdConfigAPI() {
  const baseURL = 'localhost';
  const port = 3000;

  // 模拟登录获取token
  let token = '';

  try {
    console.log('🔐 正在登录获取token...');
    const loginOptions = {
      hostname: baseURL,
      port: port,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const loginResponse = await makeRequest(loginOptions, {
      username: 'admin',
      password: 'admin123'
    });

    if (loginResponse.status !== 200) {
      throw new Error(`登录失败: ${loginResponse.status} - ${JSON.stringify(loginResponse.data)}`);
    }

    token = loginResponse.data.result.token;
    console.log('✅ 登录成功，获取到token');
    
    // 设置请求头
    const authHeaders = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    console.log('\n📋 测试获取广告配置列表...');
    const listOptions = {
      hostname: baseURL,
      port: port,
      path: '/api/config/ads?page=1&pageSize=10',
      method: 'GET',
      headers: authHeaders
    };

    const listResponse = await makeRequest(listOptions);

    if (listResponse.status !== 200) {
      throw new Error(`获取列表失败: ${listResponse.status} - ${JSON.stringify(listResponse.data)}`);
    }

    console.log('✅ 获取广告配置列表成功');
    console.log('📋 完整响应数据:', JSON.stringify(listResponse.data, null, 2));

    const result = listResponse.data.result;
    const dataArray = result.data || result.list || result;

    console.log(`📊 总数: ${result.total || 'N/A'}`);
    console.log(`📄 当前页数据: ${Array.isArray(dataArray) ? dataArray.length : 'N/A'} 条`);

    // 显示第一条数据的新结构
    if (Array.isArray(dataArray) && dataArray.length > 0) {
      const firstAd = dataArray[0];
      console.log('\n🎯 第一条广告配置数据结构:');
      console.log(`- ID: ${firstAd.id}`);
      console.log(`- 广告类型: ${firstAd.adType}`);
      console.log(`- 标识符: ${firstAd.adIdentifier}`);
      console.log(`- 标题: ${firstAd.title}`);
      console.log(`- 状态: ${firstAd.status}`);
      
      if (firstAd.imageItems && Array.isArray(firstAd.imageItems)) {
        console.log(`- 图片项目数量: ${firstAd.imageItems.length}`);
        firstAd.imageItems.forEach((item, index) => {
          console.log(`  图片${index + 1}:`);
          console.log(`    - 图片URL: ${item.imageUrl}`);
          console.log(`    - 跳转类型: ${item.jumpType}`);
          console.log(`    - 跳转目标: ${item.jumpTarget}`);
          if (item.title) console.log(`    - 标题: ${item.title}`);
          if (item.description) console.log(`    - 描述: ${item.description}`);
        });
      } else {
        console.log('⚠️  imageItems字段不存在或不是数组');
      }
      
      // 测试创建新的广告配置
      console.log('\n🆕 测试创建新的广告配置...');
      const newAdConfig = {
        adType: 1,
        adIdentifier: 'test-carousel-' + Date.now(),
        title: '测试轮播广告',
        status: 1,
        sortOrder: 1,
        imageItems: [
          {
            imageUrl: 'https://example.com/image1.jpg',
            jumpType: 1,
            jumpTarget: '/games',
            title: '游戏页面',
            description: '跳转到游戏列表'
          },
          {
            imageUrl: 'https://example.com/image2.jpg',
            jumpType: 2,
            jumpTarget: 'https://example.com/promo',
            title: '促销活动',
            description: '查看最新促销'
          }
        ]
      };
      
      const createOptions = {
        hostname: baseURL,
        port: port,
        path: '/api/config/ads',
        method: 'POST',
        headers: authHeaders
      };

      const createResponse = await makeRequest(createOptions, newAdConfig);

      if (createResponse.status !== 201 && createResponse.status !== 200) {
        throw new Error(`创建失败: ${createResponse.status} - ${JSON.stringify(createResponse.data)}`);
      }

      console.log('✅ 创建广告配置成功');
      console.log(`🆔 新创建的广告ID: ${createResponse.data.result.id}`);

      // 测试获取单个广告配置
      const newAdId = createResponse.data.result.id;
      console.log(`\n🔍 测试获取单个广告配置 (ID: ${newAdId})...`);

      const getOptions = {
        hostname: baseURL,
        port: port,
        path: `/api/config/ads/${newAdId}`,
        method: 'GET',
        headers: authHeaders
      };

      const getResponse = await makeRequest(getOptions);
      
      console.log('✅ 获取单个广告配置成功');
      const retrievedAd = getResponse.data.result;
      console.log(`- 广告类型: ${retrievedAd.adType}`);
      console.log(`- 标识符: ${retrievedAd.adIdentifier}`);
      console.log(`- 图片项目数量: ${retrievedAd.imageItems?.length || 0}`);
      
      // 测试更新广告配置
      console.log(`\n✏️  测试更新广告配置 (ID: ${newAdId})...`);
      const updateData = {
        title: '更新后的测试轮播广告',
        imageItems: [
          {
            imageUrl: 'https://example.com/updated-image1.jpg',
            jumpType: 1,
            jumpTarget: '/updated-games',
            title: '更新的游戏页面',
            description: '跳转到更新的游戏列表'
          },
          {
            imageUrl: 'https://example.com/updated-image2.jpg',
            jumpType: 2,
            jumpTarget: 'https://example.com/updated-promo',
            title: '更新的促销活动',
            description: '查看更新的促销信息'
          }
        ]
      };
      
      const updateOptions = {
        hostname: baseURL,
        port: port,
        path: `/api/config/ads/${newAdId}`,
        method: 'PATCH',
        headers: authHeaders
      };

      const updateResponse = await makeRequest(updateOptions, updateData);

      if (updateResponse.status !== 200) {
        throw new Error(`更新失败: ${updateResponse.status} - ${JSON.stringify(updateResponse.data)}`);
      }

      console.log('✅ 更新广告配置成功');
      console.log(`- 新标题: ${updateResponse.data.result.title}`);
      console.log(`- 更新后图片项目数量: ${updateResponse.data.result.imageItems?.length || 0}`);

      // 清理：删除测试数据
      console.log(`\n🗑️  清理测试数据 (ID: ${newAdId})...`);

      const deleteOptions = {
        hostname: baseURL,
        port: port,
        path: `/api/config/ads/${newAdId}`,
        method: 'DELETE',
        headers: authHeaders
      };

      await makeRequest(deleteOptions);
      console.log('✅ 测试数据清理完成');
    }
    
    console.log('\n🎉 所有API测试完成！新的imageItems结构工作正常！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('💡 提示: 请确保使用正确的用户名和密码');
    }
  }
}

// 运行测试
testAdConfigAPI();
