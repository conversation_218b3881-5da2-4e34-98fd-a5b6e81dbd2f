"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemAuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
const sys_user_entity_1 = require("../entities/sys-user.entity");
let SystemAuthService = class SystemAuthService {
    userRepository;
    jwtService;
    configService;
    constructor(userRepository, jwtService, configService) {
        this.userRepository = userRepository;
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async validateUser(username, password) {
        const startTime = Date.now();
        console.log(`[AUTH_SERVICE] 开始验证用户 - 用户名: ${username}`);
        const user = await this.userRepository.findOne({
            where: { username },
            select: ['id', 'username', 'password', 'email', 'status', 'isSuperAdmin'],
            relations: ['roles'],
        });
        const queryTime = Date.now() - startTime;
        console.log(`[AUTH_SERVICE] 数据库查询完成 - 耗时: ${queryTime}ms, 找到用户: ${!!user}`);
        if (user) {
            console.log(`[AUTH_SERVICE] 用户状态: ${user.status}, 用户ID: ${user.id}`);
            console.log(`[AUTH_SERVICE] 超级管理员状态: ${user.isSuperAdmin}`);
            console.log(`[AUTH_SERVICE] 数据库密码哈希: ${user.password.substring(0, 20)}...`);
            console.log(`[AUTH_SERVICE] 输入的密码: ${password}`);
            if (user.status === 1) {
                const passwordCheckStart = Date.now();
                const isPasswordValid = await bcrypt.compare(password, user.password);
                const passwordCheckTime = Date.now() - passwordCheckStart;
                console.log(`[AUTH_SERVICE] 密码验证完成 - 耗时: ${passwordCheckTime}ms, 密码正确: ${isPasswordValid}`);
                if (isPasswordValid) {
                    await this.userRepository.update(user.id, {
                        lastLoginTime: new Date(),
                    });
                    const { password, ...result } = user;
                    const totalTime = Date.now() - startTime;
                    console.log(`[AUTH_SERVICE] 用户验证成功 - 总耗时: ${totalTime}ms`);
                    return result;
                }
                else {
                    console.log(`[AUTH_SERVICE] 密码验证失败 - 用户名: ${username}`);
                }
            }
            else {
                console.log(`[AUTH_SERVICE] 用户状态异常 - 用户名: ${username}, 状态: ${user.status}`);
            }
        }
        else {
            console.log(`[AUTH_SERVICE] 用户不存在 - 用户名: ${username}`);
        }
        const totalTime = Date.now() - startTime;
        console.log(`[AUTH_SERVICE] 用户验证失败 - 总耗时: ${totalTime}ms`);
        return null;
    }
    async login(user) {
        console.log(`[AUTH_SERVICE] 登录用户信息:`, {
            id: user.id,
            username: user.username,
            isSuperAdmin: user.isSuperAdmin,
            roles: user.roles?.map((role) => role.code) || []
        });
        const payload = {
            username: user.username,
            sub: user.id,
            roles: user.roles?.map((role) => role.code) || [],
            isSuperAdmin: user.isSuperAdmin || false,
            type: 'system',
        };
        console.log(`[AUTH_SERVICE] JWT payload:`, payload);
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = this.jwtService.sign(payload, {
            secret: this.configService.get('JWT_REFRESH_SECRET'),
            expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN'),
        });
        return {
            token: accessToken,
            refreshToken,
            userInfo: {
                id: user.id,
                username: user.username,
                email: user.email,
                roles: user.roles?.map((role) => role.code) || [],
                isSuperAdmin: user.isSuperAdmin || false,
            },
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
            });
            const user = await this.userRepository.findOne({
                where: { id: payload.sub },
                relations: ['roles'],
            });
            if (!user || user.status !== 1) {
                throw new common_1.UnauthorizedException('用户不存在或已被禁用');
            }
            const newPayload = {
                username: user.username,
                sub: user.id,
                roles: user.roles?.map((role) => role.code) || [],
                isSuperAdmin: user.isSuperAdmin || false,
                type: 'system',
            };
            const newAccessToken = this.jwtService.sign(newPayload);
            const newRefreshToken = this.jwtService.sign(newPayload, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
                expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN'),
            });
            return {
                token: newAccessToken,
                refreshToken: newRefreshToken,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('刷新令牌无效');
        }
    }
    async getUserInfo(userId, includeRoles = true) {
        const startTime = Date.now();
        console.log(`[AUTH_SERVICE] 开始获取用户信息 - 用户ID: ${userId}, 包含角色: ${includeRoles}`);
        const queryStart = Date.now();
        let user;
        if (includeRoles) {
            user = await this.userRepository
                .createQueryBuilder('user')
                .leftJoinAndSelect('user.roles', 'role')
                .where('user.id = :id', { id: userId })
                .select([
                'user.id',
                'user.username',
                'user.email',
                'user.phoneNumber',
                'user.avatar',
                'user.description',
                'user.isSuperAdmin',
                'role.code'
            ])
                .getOne();
        }
        else {
            user = await this.userRepository.findOne({
                where: { id: userId },
                select: [
                    'id',
                    'username',
                    'email',
                    'phoneNumber',
                    'avatar',
                    'description',
                    'isSuperAdmin',
                ],
            });
        }
        const queryTime = Date.now() - queryStart;
        console.log(`[AUTH_SERVICE] 数据库查询完成 - 耗时: ${queryTime}ms`);
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        const processStart = Date.now();
        const result = {
            ...user,
            roles: includeRoles ? (user.roles?.map((role) => role.code) || []) : [],
        };
        const processTime = Date.now() - processStart;
        console.log(`[AUTH_SERVICE] 处理用户信息完成 - 耗时: ${processTime}ms`);
        const totalTime = Date.now() - startTime;
        console.log(`[AUTH_SERVICE] 获取用户信息总耗时: ${totalTime}ms`);
        return result;
    }
    async updateUserInfo(userId, updateData) {
        const startTime = Date.now();
        console.log(`[AUTH_SERVICE] 开始更新用户信息 - 用户ID: ${userId}`, updateData);
        const processFieldsStart = Date.now();
        const allowedFields = ['email', 'phoneNumber', 'avatar', 'description'];
        const updateFields = {};
        for (const field of allowedFields) {
            if (updateData[field] !== undefined) {
                updateFields[field] = updateData[field];
            }
        }
        const processFieldsEnd = Date.now();
        console.log(`[AUTH_SERVICE] 处理更新字段完成 - 耗时: ${processFieldsEnd - processFieldsStart}ms, 更新字段:`, updateFields);
        if (Object.keys(updateFields).length === 0) {
            console.log(`[AUTH_SERVICE] 没有需要更新的字段，直接返回当前用户信息`);
            return this.getUserInfo(userId);
        }
        const updateUserStart = Date.now();
        const updateResult = await this.userRepository.update(userId, updateFields);
        const updateUserEnd = Date.now();
        console.log(`[AUTH_SERVICE] 更新用户完成 - 耗时: ${updateUserEnd - updateUserStart}ms, 影响行数: ${updateResult.affected}`);
        if (updateResult.affected === 0) {
            throw new common_1.UnauthorizedException('用户不存在或更新失败');
        }
        const getUserInfoStart = Date.now();
        const result = await this.getUserInfo(userId, true);
        const getUserInfoEnd = Date.now();
        console.log(`[AUTH_SERVICE] 获取用户信息完成 - 耗时: ${getUserInfoEnd - getUserInfoStart}ms`);
        const totalTime = Date.now() - startTime;
        console.log(`[AUTH_SERVICE] 更新用户信息总耗时: ${totalTime}ms`);
        return result;
    }
    async changePassword(userId, currentPassword, newPassword) {
        const startTime = Date.now();
        console.log(`[AUTH_SERVICE] 开始修改密码 - 用户ID: ${userId}`);
        const user = await this.userRepository.findOne({
            where: { id: userId },
            select: ['id', 'password'],
        });
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.UnauthorizedException('当前密码错误');
        }
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);
        const updateResult = await this.userRepository.update(userId, {
            password: hashedNewPassword,
        });
        if (updateResult.affected === 0) {
            throw new common_1.UnauthorizedException('密码更新失败');
        }
        const totalTime = Date.now() - startTime;
        console.log(`[AUTH_SERVICE] 修改密码成功 - 总耗时: ${totalTime}ms`);
        return { message: '密码修改成功' };
    }
    async resetAdminPassword(newPassword) {
        console.log(`[AUTH_SERVICE] 开始重置admin密码`);
        const adminUser = await this.userRepository.findOne({
            where: { username: 'admin' },
            select: ['id', 'username'],
        });
        if (!adminUser) {
            throw new common_1.UnauthorizedException('admin用户不存在');
        }
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);
        console.log(`[AUTH_SERVICE] 新密码哈希: ${hashedNewPassword.substring(0, 20)}...`);
        const updateResult = await this.userRepository.update(adminUser.id, {
            password: hashedNewPassword,
        });
        if (updateResult.affected === 0) {
            throw new common_1.UnauthorizedException('密码重置失败');
        }
        console.log(`[AUTH_SERVICE] admin密码重置成功`);
        return { message: 'admin密码重置成功' };
    }
};
exports.SystemAuthService = SystemAuthService;
exports.SystemAuthService = SystemAuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_user_entity_1.SysUser)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        jwt_1.JwtService,
        config_1.ConfigService])
], SystemAuthService);
//# sourceMappingURL=auth.service.js.map