"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysPermission = void 0;
const typeorm_1 = require("typeorm");
const sys_role_entity_1 = require("./sys-role.entity");
let SysPermission = class SysPermission {
    id;
    name;
    code;
    description;
    type;
    status;
    roles;
    createTime;
    updateTime;
};
exports.SysPermission = SysPermission;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SysPermission.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], SysPermission.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 100 }),
    __metadata("design:type", String)
], SysPermission.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 200 }),
    __metadata("design:type", String)
], SysPermission.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'button', length: 20 }),
    __metadata("design:type", String)
], SysPermission.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], SysPermission.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => sys_role_entity_1.SysRole, (role) => role.permissions),
    __metadata("design:type", Array)
], SysPermission.prototype, "roles", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], SysPermission.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], SysPermission.prototype, "updateTime", void 0);
exports.SysPermission = SysPermission = __decorate([
    (0, typeorm_1.Entity)('sys_permissions')
], SysPermission);
//# sourceMappingURL=sys-permission.entity.js.map