<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .preview {
            max-width: 300px;
            max-height: 300px;
            margin: 10px auto;
            display: block;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>前端图片上传测试</h1>
    <p>测试使用前端应用的上传功能</p>
    
    <div class="upload-area" id="uploadArea">
        <p>点击选择图片或拖拽图片到此处</p>
        <p>支持 JPG, PNG, GIF 格式，最大5MB</p>
        <input type="file" id="fileInput" accept="image/*" style="display: none;">
    </div>
    
    <div>
        <button onclick="testDirectUpload()">测试直接上传</button>
        <button onclick="testCompressedUpload()">测试压缩上传</button>
        <button onclick="testMultipleUpload()">测试批量上传</button>
    </div>
    
    <div id="result"></div>
    
    <script type="module">
        // 导入上传函数（模拟）
        // 在实际应用中，这些函数会从 utils/supabase-upload.ts 导入
        
        // 显示结果
        function showResult(type, message, imageUrl = null) {
            const result = document.getElementById('result');
            result.innerHTML = `
                <div class="result ${type}">
                    <p>${message}</p>
                    ${imageUrl ? `
                        <img src="${imageUrl}" alt="上传的图片" class="preview">
                        <p>图片URL: <a href="${imageUrl}" target="_blank">${imageUrl}</a></p>
                    ` : ''}
                </div>
            `;
        }
        
        // 模拟上传函数
        async function mockUploadImageToSupabase(file, folder = 'ads') {
            // 模拟上传延迟
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 模拟成功响应
            const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${file.name.split('.').pop()}`;
            return `https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/object/public/inda/${folder}/${fileName}`;
        }
        
        async function mockUploadCompressedImageToSupabase(file, folder = 'ads', options = {}) {
            // 模拟压缩和上传
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const fileName = `compressed-${Date.now()}-${Math.random().toString(36).substring(2)}.${file.name.split('.').pop()}`;
            return `https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/object/public/inda/${folder}/${fileName}`;
        }
        
        async function mockUploadMultipleImagesToSupabase(files, folder = 'ads', onProgress) {
            const results = [];
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                try {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    const url = await mockUploadImageToSupabase(file, folder);
                    results.push({ success: true, url, fileName: file.name });
                } catch (error) {
                    results.push({ 
                        success: false, 
                        error: error.message, 
                        fileName: file.name 
                    });
                }
                
                if (onProgress) {
                    const progress = ((i + 1) / files.length) * 100;
                    onProgress(progress, i + 1, files.length);
                }
            }
            
            return results;
        }
        
        // 测试函数
        window.testDirectUpload = async function() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('error', '请先选择一个图片文件');
                return;
            }
            
            showResult('loading', '正在上传图片（直接上传）...');
            
            try {
                const imageUrl = await mockUploadImageToSupabase(file, 'ads');
                showResult('success', '直接上传成功！', imageUrl);
            } catch (error) {
                showResult('error', `直接上传失败: ${error.message}`);
            }
        };
        
        window.testCompressedUpload = async function() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('error', '请先选择一个图片文件');
                return;
            }
            
            showResult('loading', '正在压缩并上传图片...');
            
            try {
                const imageUrl = await mockUploadCompressedImageToSupabase(file, 'ads', {
                    maxWidth: 1920,
                    maxHeight: 1080,
                    quality: 0.8
                });
                showResult('success', '压缩上传成功！', imageUrl);
            } catch (error) {
                showResult('error', `压缩上传失败: ${error.message}`);
            }
        };
        
        window.testMultipleUpload = async function() {
            const fileInput = document.getElementById('fileInput');
            fileInput.multiple = true;
            fileInput.click();
            
            fileInput.onchange = async function() {
                const files = Array.from(fileInput.files);
                
                if (files.length === 0) {
                    showResult('error', '请选择至少一个图片文件');
                    return;
                }
                
                showResult('loading', `正在批量上传 ${files.length} 个文件...`);
                
                try {
                    const results = await mockUploadMultipleImagesToSupabase(files, 'ads', (progress, current, total) => {
                        showResult('loading', `正在上传第 ${current}/${total} 个文件 (${Math.round(progress)}%)`);
                    });
                    
                    const successCount = results.filter(r => r.success).length;
                    const failCount = results.length - successCount;
                    
                    let message = `批量上传完成！成功: ${successCount}, 失败: ${failCount}`;
                    if (results.length > 0 && results[0].success) {
                        showResult('success', message, results[0].url);
                    } else {
                        showResult('error', message);
                    }
                } catch (error) {
                    showResult('error', `批量上传失败: ${error.message}`);
                }
                
                // 重置文件输入
                fileInput.multiple = false;
                fileInput.onchange = null;
            };
        };
        
        // DOM事件
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                showResult('success', `已选择文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showResult('success', `已选择文件: ${files[0].name} (${(files[0].size / 1024 / 1024).toFixed(2)} MB)`);
            }
        });
        
        console.log('前端上传测试页面已加载');
    </script>
</body>
</html>
