import { SystemAuthService } from './auth.service';
import { SystemLoginDto } from './dto/login.dto';
import { SystemRefreshTokenDto } from './dto/refresh-token.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
export declare class SystemAuthController {
    private authService;
    constructor(authService: SystemAuthService);
    login(req: any, loginDto: SystemLoginDto): Promise<{
        code: number;
        message: string;
        result: {
            token: string;
            refreshToken: string;
            userInfo: {
                id: any;
                username: any;
                email: any;
                roles: any;
                isSuperAdmin: any;
            };
        };
    }>;
    logout(): Promise<{
        code: number;
        message: string;
        result: {};
    }>;
    refreshToken(refreshTokenDto: SystemRefreshTokenDto): Promise<{
        code: number;
        message: string;
        result: {
            token: string;
            refreshToken: string;
        };
    }>;
    getProfile(req: any): Promise<{
        code: number;
        message: string;
        result: any;
    }>;
    updateProfile(req: any, updateData: any): Promise<{
        code: number;
        message: string;
        result: any;
    }>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<{
        code: number;
        message: string;
        result?: undefined;
    } | {
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
    resetAdminPassword(body: {
        newPassword: string;
    }): Promise<{
        code: number;
        message: string;
    }>;
}
