import { Repository } from 'typeorm';
import { MarketingChannel, MarketingAd } from '../entities';
import { CreateChannelDto, UpdateChannelDto, QueryChannelDto, CreateAdDto, UpdateAdDto, QueryAdDto } from './dto';
export declare class ChannelService {
    private channelRepository;
    private adRepository;
    constructor(channelRepository: Repository<MarketingChannel>, adRepository: Repository<MarketingAd>);
    createChannel(createChannelDto: CreateChannelDto): Promise<MarketingChannel>;
    findChannels(queryDto: QueryChannelDto): Promise<{
        list: MarketingChannel[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findChannelById(id: number): Promise<MarketingChannel>;
    updateChannel(id: number, updateChannelDto: UpdateChannelDto): Promise<MarketingChannel>;
    toggleChannelStatus(id: number): Promise<MarketingChannel>;
    findSimpleChannels(): Promise<MarketingChannel[]>;
    createAd(createAdDto: CreateAdDto): Promise<MarketingAd>;
    findAds(queryDto: QueryAdDto): Promise<{
        list: MarketingAd[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    findAdById(id: number): Promise<MarketingAd>;
    updateAd(id: number, updateAdDto: UpdateAdDto): Promise<MarketingAd>;
    toggleAdStatus(id: number): Promise<MarketingAd>;
}
