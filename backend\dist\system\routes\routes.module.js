"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoutesModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const routes_service_1 = require("./routes.service");
const routes_controller_1 = require("./routes.controller");
const menu_module_1 = require("../menu/menu.module");
const sys_permission_entity_1 = require("../entities/sys-permission.entity");
const sys_role_entity_1 = require("../entities/sys-role.entity");
let SystemRoutesModule = class SystemRoutesModule {
};
exports.SystemRoutesModule = SystemRoutesModule;
exports.SystemRoutesModule = SystemRoutesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            menu_module_1.SystemMenuModule,
            typeorm_1.TypeOrmModule.forFeature([sys_permission_entity_1.SysPermission, sys_role_entity_1.SysRole]),
        ],
        controllers: [routes_controller_1.SystemRoutesController],
        providers: [routes_service_1.SystemRoutesService],
        exports: [routes_service_1.SystemRoutesService],
    })
], SystemRoutesModule);
//# sourceMappingURL=routes.module.js.map