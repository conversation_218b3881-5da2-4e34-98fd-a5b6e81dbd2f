"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
let UserManagementService = class UserManagementService {
    userRepository;
    channelRepository;
    adRepository;
    pageRepository;
    riskEventRepository;
    constructor(userRepository, channelRepository, adRepository, pageRepository, riskEventRepository) {
        this.userRepository = userRepository;
        this.channelRepository = channelRepository;
        this.adRepository = adRepository;
        this.pageRepository = pageRepository;
        this.riskEventRepository = riskEventRepository;
    }
    async findUsers(queryDto) {
        const { page = 1, pageSize = 20, id, username, email, phone, nickname, status, accountType, kycStatus, channelId, adId, inviterId, minRiskScore, maxRiskScore, startDate, endDate, sortBy = 'createTime', sortOrder = 'DESC', } = queryDto;
        const queryBuilder = this.userRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.channel', 'channel')
            .leftJoinAndSelect('user.ad', 'ad')
            .leftJoinAndSelect('user.inviter', 'inviter')
            .select([
            'user.id',
            'user.uid',
            'user.username',
            'user.email',
            'user.phone',
            'user.nickname',
            'user.avatar',
            'user.gender',
            'user.rechargeBalance',
            'user.goldBalance',
            'user.withdrawableBalance',
            'user.vipLevel',
            'user.status',
            'user.accountType',
            'user.isVerified',
            'user.riskScore',
            'user.kycStatus',
            'user.inviterId',
            'user.acquisitionTag',
            'user.tags',
            'user.lastLoginTime',
            'user.registerIp',
            'user.createTime',
            'channel.id',
            'channel.name',
            'ad.id',
            'ad.name',
            'ad.identifier',
            'inviter.id',
            'inviter.username',
            'inviter.nickname',
        ]);
        this.addSearchConditions(queryBuilder, queryDto);
        queryBuilder.orderBy(`user.${sortBy}`, sortOrder);
        const skip = (page - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        const [users, total] = await queryBuilder.getManyAndCount();
        const list = users.map((user) => {
            let daysNotLoggedIn = 0;
            if (user.lastLoginTime) {
                const lastLogin = new Date(user.lastLoginTime);
                const now = new Date();
                const diffTime = now.getTime() - lastLogin.getTime();
                daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
            }
            else {
                const createTime = new Date(user.createTime);
                const now = new Date();
                const diffTime = now.getTime() - createTime.getTime();
                daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
            }
            return {
                id: user.id,
                uid: user.uid,
                username: user.username,
                email: user.email,
                phone: user.phone,
                nickname: user.nickname,
                avatar: user.avatar,
                gender: user.gender,
                rechargeBalance: user.rechargeBalance,
                goldBalance: user.goldBalance,
                withdrawableBalance: user.withdrawableBalance,
                vipLevel: user.vipLevel,
                status: user.status,
                accountType: user.accountType,
                isVerified: user.isVerified,
                riskScore: user.riskScore,
                kycStatus: user.kycStatus,
                inviterId: user.inviterId,
                channelName: user.channel?.name,
                adName: user.ad?.name,
                adIdentifier: user.ad?.identifier,
                acquisitionTag: user.acquisitionTag,
                tags: user.tags ? (Array.isArray(user.tags) ? user.tags : [user.tags]) : [],
                lastLoginTime: user.lastLoginTime,
                daysNotLoggedIn,
                registerIp: user.registerIp,
                createTime: user.createTime,
            };
        });
        return {
            list,
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    addSearchConditions(queryBuilder, queryDto) {
        const { id, username, email, phone, nickname, status, accountType, kycStatus, channelId, adId, inviterId, minRiskScore, maxRiskScore, startDate, endDate, } = queryDto;
        if (id) {
            queryBuilder.andWhere('user.id = :id', { id });
        }
        if (username) {
            queryBuilder.andWhere('user.username ILIKE :username', {
                username: `%${username}%`,
            });
        }
        if (email) {
            queryBuilder.andWhere('user.email ILIKE :email', {
                email: `%${email}%`,
            });
        }
        if (phone) {
            queryBuilder.andWhere('user.phone ILIKE :phone', {
                phone: `%${phone}%`,
            });
        }
        if (nickname) {
            queryBuilder.andWhere('user.nickname ILIKE :nickname', {
                nickname: `%${nickname}%`,
            });
        }
        if (status !== undefined && status !== null) {
            queryBuilder.andWhere('user.status = :status', { status });
        }
        if (accountType !== undefined) {
            queryBuilder.andWhere('user.accountType = :accountType', { accountType });
        }
        if (kycStatus !== undefined) {
            queryBuilder.andWhere('user.kycStatus = :kycStatus', { kycStatus });
        }
        if (channelId) {
            queryBuilder.andWhere('user.channelId = :channelId', { channelId });
        }
        if (adId) {
            queryBuilder.andWhere('user.adId = :adId', { adId });
        }
        if (inviterId) {
            queryBuilder.andWhere('user.inviterId = :inviterId', { inviterId });
        }
        if (minRiskScore !== undefined) {
            queryBuilder.andWhere('user.riskScore >= :minRiskScore', { minRiskScore });
        }
        if (maxRiskScore !== undefined) {
            queryBuilder.andWhere('user.riskScore <= :maxRiskScore', { maxRiskScore });
        }
        if (startDate) {
            queryBuilder.andWhere('user.createTime >= :startDate', { startDate });
        }
        if (endDate) {
            queryBuilder.andWhere('user.createTime <= :endDate', { endDate });
        }
    }
    async findUserById(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['inviter', 'channel', 'ad', 'promotionalPage'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        let daysNotLoggedIn = 0;
        if (user.lastLoginTime) {
            const lastLogin = new Date(user.lastLoginTime);
            const now = new Date();
            const diffTime = now.getTime() - lastLogin.getTime();
            daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        }
        else {
            const createTime = new Date(user.createTime);
            const now = new Date();
            const diffTime = now.getTime() - createTime.getTime();
            daysNotLoggedIn = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        }
        return {
            id: user.id,
            uid: user.uid,
            username: user.username,
            email: user.email,
            phone: user.phone,
            googleId: user.googleId,
            nickname: user.nickname,
            avatar: user.avatar,
            gender: user.gender,
            birthday: user.birthday,
            rechargeBalance: user.rechargeBalance,
            goldBalance: user.goldBalance,
            withdrawableBalance: user.withdrawableBalance,
            vipLevel: user.vipLevel,
            vipExp: user.vipExp,
            status: user.status,
            accountType: user.accountType,
            isVerified: user.isVerified,
            riskScore: user.riskScore,
            kycStatus: user.kycStatus,
            kycRejectReason: user.kycRejectReason,
            inviterId: user.inviterId,
            invitationPath: user.invitationPath,
            channelId: user.channelId,
            adId: user.adId,
            promotionalPageId: user.promotionalPageId,
            acquisitionTag: user.acquisitionTag,
            tags: user.tags,
            lastLoginTime: user.lastLoginTime,
            daysNotLoggedIn,
            lastLoginIp: user.lastLoginIp,
            registerIp: user.registerIp,
            createTime: user.createTime,
            updateTime: user.updateTime,
            inviter: user.inviter
                ? {
                    id: user.inviter.id,
                    username: user.inviter.username,
                    nickname: user.inviter.nickname,
                }
                : undefined,
            channel: user.channel
                ? {
                    id: user.channel.id,
                    name: user.channel.name,
                    identifier: user.channel.identifier,
                }
                : undefined,
            ad: user.ad
                ? {
                    id: user.ad.id,
                    name: user.ad.name,
                    identifier: user.ad.identifier,
                }
                : undefined,
            adIdentifier: user.ad?.identifier,
            promotionalPage: user.promotionalPage
                ? {
                    id: user.promotionalPage.id,
                    name: user.promotionalPage.name,
                    identifier: user.promotionalPage.identifier,
                }
                : undefined,
        };
    }
};
exports.UserManagementService = UserManagementService;
exports.UserManagementService = UserManagementService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.AppUser)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.MarketingChannel)),
    __param(2, (0, typeorm_1.InjectRepository)(entities_1.MarketingAd)),
    __param(3, (0, typeorm_1.InjectRepository)(entities_1.PromotionalPage)),
    __param(4, (0, typeorm_1.InjectRepository)(entities_1.RiskEvent)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UserManagementService);
//# sourceMappingURL=user-management.service.js.map