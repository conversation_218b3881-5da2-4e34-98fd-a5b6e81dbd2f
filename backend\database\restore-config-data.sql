-- 恢复配置管理数据
-- 此脚本用于恢复丢失的配置管理数据

SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;

-- ============================================================================
-- VIP配置数据 (VIP Configuration Data)
-- ============================================================================

-- 插入VIP配置
INSERT INTO vip_configs (id, vip_level, level_name, required_points, balance_ratio, gold_ratio, cash_ratio, daily_gold_reward, status, remark, created_by, create_time, update_time) VALUES
(1, 0, 'VIP0', 0, 1.0000, 1.0000, 1.0000, 0, 1, '普通用户', 2, NOW(), NOW()),
(2, 1, 'VIP1', 1000, 1.1000, 1.1000, 1.1000, 100, 1, 'VIP1用户', 2, NOW(), NOW()),
(3, 2, 'VIP2', 5000, 1.2000, 1.2000, 1.2000, 200, 1, 'VIP2用户', 2, NOW(), NOW()),
(4, 3, 'VIP3', 10000, 1.3000, 1.3000, 1.3000, 300, 1, 'VIP3用户', 2, NOW(), NOW())
ON CONFLICT (vip_level) DO UPDATE SET
  level_name = EXCLUDED.level_name,
  required_points = EXCLUDED.required_points,
  balance_ratio = EXCLUDED.balance_ratio,
  gold_ratio = EXCLUDED.gold_ratio,
  cash_ratio = EXCLUDED.cash_ratio,
  daily_gold_reward = EXCLUDED.daily_gold_reward,
  status = EXCLUDED.status,
  remark = EXCLUDED.remark,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('vip_configs_id_seq', (SELECT MAX(id) FROM vip_configs));

-- ============================================================================
-- 会员卡配置数据 (Membership Card Configuration Data)
-- ============================================================================

-- 插入会员卡配置
INSERT INTO membership_card_configs (id, card_type, card_name, price, description, daily_gold_base, daily_gold_activity_ratio, cash_discount_base, cash_discount_activity, status, created_by, create_time, update_time) VALUES
(1, 'silver', '银卡会员', 9.99, '银卡会员月卡，享受基础特权', 100, 1.20, 0.05, 0.10, 1, 2, NOW(), NOW()),
(2, 'gold', '金卡会员', 19.99, '金卡会员月卡，享受更多特权', 300, 1.50, 0.10, 0.15, 1, 2, NOW(), NOW()),
(3, 'diamond', '钻石会员', 49.99, '钻石会员月卡，享受高级特权', 800, 2.00, 0.15, 0.25, 1, 2, NOW(), NOW()),
(4, 'king', '王者会员', 99.99, '王者会员月卡，享受至尊特权', 2000, 3.00, 0.20, 0.35, 1, 2, NOW(), NOW())
ON CONFLICT (card_type) DO UPDATE SET
  card_name = EXCLUDED.card_name,
  price = EXCLUDED.price,
  description = EXCLUDED.description,
  daily_gold_base = EXCLUDED.daily_gold_base,
  daily_gold_activity_ratio = EXCLUDED.daily_gold_activity_ratio,
  cash_discount_base = EXCLUDED.cash_discount_base,
  cash_discount_activity = EXCLUDED.cash_discount_activity,
  status = EXCLUDED.status,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('membership_card_configs_id_seq', (SELECT MAX(id) FROM membership_card_configs));

-- ============================================================================
-- 金币充值配置数据 (Gold Recharge Configuration Data)
-- ============================================================================

-- 插入金币充值配置
INSERT INTO gold_recharge_configs (id, gold_amount, price, activity_bonus_gold, activity_start_time, activity_end_time, sort_order, status, created_by, create_time, update_time) VALUES
(1, 1000, 0.99, 100, NULL, NULL, 1, 1, 2, NOW(), NOW()),
(2, 5000, 4.99, 500, NULL, NULL, 2, 1, 2, NOW(), NOW()),
(3, 10000, 9.99, 1500, NULL, NULL, 3, 1, 2, NOW(), NOW()),
(4, 50000, 49.99, 10000, NULL, NULL, 4, 1, 2, NOW(), NOW()),
(5, 100000, 99.99, 25000, NULL, NULL, 5, 1, 2, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  gold_amount = EXCLUDED.gold_amount,
  price = EXCLUDED.price,
  activity_bonus_gold = EXCLUDED.activity_bonus_gold,
  activity_start_time = EXCLUDED.activity_start_time,
  activity_end_time = EXCLUDED.activity_end_time,
  sort_order = EXCLUDED.sort_order,
  status = EXCLUDED.status,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('gold_recharge_configs_id_seq', (SELECT MAX(id) FROM gold_recharge_configs));

-- ============================================================================
-- 余额充值配置数据 (Balance Recharge Configuration Data)
-- ============================================================================

-- 插入余额充值配置
INSERT INTO balance_recharge_configs (id, tier_name, recharge_amount, activity_bonus_amount, activity_start_time, activity_end_time, sort_order, status, created_by, create_time, update_time) VALUES
(1, '小额充值', 10.00, 1.00, NULL, NULL, 1, 1, 2, NOW(), NOW()),
(2, '标准充值', 50.00, 5.00, NULL, NULL, 2, 1, 2, NOW(), NOW()),
(3, '大额充值', 100.00, 15.00, NULL, NULL, 3, 1, 2, NOW(), NOW()),
(4, '超值充值', 500.00, 100.00, NULL, NULL, 4, 1, 2, NOW(), NOW()),
(5, '豪华充值', 1000.00, 250.00, NULL, NULL, 5, 1, 2, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  tier_name = EXCLUDED.tier_name,
  recharge_amount = EXCLUDED.recharge_amount,
  activity_bonus_amount = EXCLUDED.activity_bonus_amount,
  activity_start_time = EXCLUDED.activity_start_time,
  activity_end_time = EXCLUDED.activity_end_time,
  sort_order = EXCLUDED.sort_order,
  status = EXCLUDED.status,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('balance_recharge_configs_id_seq', (SELECT MAX(id) FROM balance_recharge_configs));

-- ============================================================================
-- 余额充值限制数据 (Balance Recharge Limits Data)
-- ============================================================================

-- 插入余额充值限制
INSERT INTO balance_recharge_limits (id, limit_name, min_amount, max_amount, status, created_by, create_time, update_time) VALUES
(1, '默认充值限制', 1.00, 10000.00, 1, 2, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  limit_name = EXCLUDED.limit_name,
  min_amount = EXCLUDED.min_amount,
  max_amount = EXCLUDED.max_amount,
  status = EXCLUDED.status,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('balance_recharge_limits_id_seq', (SELECT MAX(id) FROM balance_recharge_limits));

-- ============================================================================
-- 广告配置数据 (Ad Configuration Data)
-- ============================================================================

-- 插入广告配置示例数据
INSERT INTO ad_configs (id, ad_identifier, ad_type, title, images, jump_type, jump_target, sort_order, status, remark, created_by, create_time, update_time) VALUES
(1, 'home_carousel_1', 1, '首页轮播广告1', '["https://example.com/ad1.jpg"]', 1, '/games/popular', 1, 1, '首页轮播广告示例', 2, NOW(), NOW()),
(2, 'home_popup_1', 2, '首页弹窗广告', '["https://example.com/popup1.jpg"]', 2, 'https://example.com/promotion', 1, 1, '首页弹窗广告示例', 2, NOW(), NOW()),
(3, 'float_ad_1', 3, '浮动广告', '["https://example.com/float1.jpg"]', 1, '/vip', 1, 1, '浮动广告示例', 2, NOW(), NOW()),
(4, 'banner_ad_1', 5, '横幅广告', '["https://example.com/banner1.jpg"]', 1, '/recharge', 1, 1, '横幅广告示例', 2, NOW(), NOW()),
(5, 'home_grid_1', 7, '首页4宫格广告1', '["https://example.com/grid1.jpg", "https://example.com/grid2.jpg", "https://example.com/grid3.jpg", "https://example.com/grid4.jpg"]', 1, '/games', 1, 1, '首页4宫格广告示例', 2, NOW(), NOW())
ON CONFLICT (ad_identifier) DO UPDATE SET
  ad_type = EXCLUDED.ad_type,
  title = EXCLUDED.title,
  images = EXCLUDED.images,
  jump_type = EXCLUDED.jump_type,
  jump_target = EXCLUDED.jump_target,
  sort_order = EXCLUDED.sort_order,
  status = EXCLUDED.status,
  remark = EXCLUDED.remark,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('ad_configs_id_seq', (SELECT MAX(id) FROM ad_configs));

-- ============================================================================
-- APP首页配置数据 (App Home Configuration Data)
-- ============================================================================

-- 插入APP首页配置
INSERT INTO app_home_configs (id, config_name, description, top_float_ad_id, carousel_ad_id, home_grid_ad_id, splash_popup_ad_id, float_ad_id, status, sort_order, remark, created_by, create_time, update_time) VALUES
(1, '默认首页配置', '系统默认的APP首页配置', 3, 1, 5, 2, 3, 1, 1, '默认首页配置，包含轮播、弹窗、浮动等广告位', 2, NOW(), NOW()),
(2, '节日主题配置', '节日期间的特殊首页配置', 3, 1, 5, 2, 3, 0, 2, '节日主题配置，暂时禁用', 2, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  config_name = EXCLUDED.config_name,
  description = EXCLUDED.description,
  top_float_ad_id = EXCLUDED.top_float_ad_id,
  carousel_ad_id = EXCLUDED.carousel_ad_id,
  home_grid_ad_id = EXCLUDED.home_grid_ad_id,
  splash_popup_ad_id = EXCLUDED.splash_popup_ad_id,
  float_ad_id = EXCLUDED.float_ad_id,
  status = EXCLUDED.status,
  sort_order = EXCLUDED.sort_order,
  remark = EXCLUDED.remark,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('app_home_configs_id_seq', (SELECT MAX(id) FROM app_home_configs));

-- ============================================================================
-- APP首页推荐游戏数据 (App Home Recommended Games Data)
-- ============================================================================

-- 首先需要有应用数据，这里先跳过推荐游戏的插入
-- 因为 app_home_recommended_games 表需要 application_id 字段
-- 等应用管理模块有数据后再添加

-- 重置序列
SELECT setval('app_home_recommended_games_id_seq', GREATEST(COALESCE((SELECT MAX(id) FROM app_home_recommended_games), 0), 1));

-- ============================================================================
-- APP首页游戏分类数据 (App Home Game Categories Data)
-- ============================================================================

-- 插入APP首页游戏分类
INSERT INTO app_home_game_categories (id, home_config_id, category_title, sort_order, status, created_by, create_time, update_time) VALUES
(1, 1, '{"en": "Slots", "zh": "老虎机", "es": "Tragamonedas", "pt": "Caça-níqueis"}', 1, 1, 2, NOW(), NOW()),
(2, 1, '{"en": "Table Games", "zh": "桌面游戏", "es": "Juegos de Mesa", "pt": "Jogos de Mesa"}', 2, 1, 2, NOW(), NOW()),
(3, 1, '{"en": "Live Casino", "zh": "真人娱乐场", "es": "Casino en Vivo", "pt": "Cassino ao Vivo"}', 3, 1, 2, NOW(), NOW()),
(4, 1, '{"en": "Sports", "zh": "体育博彩", "es": "Deportes", "pt": "Esportes"}', 4, 1, 2, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  home_config_id = EXCLUDED.home_config_id,
  category_title = EXCLUDED.category_title,
  sort_order = EXCLUDED.sort_order,
  status = EXCLUDED.status,
  updated_by = EXCLUDED.created_by,
  update_time = EXCLUDED.update_time;

-- 重置序列
SELECT setval('app_home_game_categories_id_seq', (SELECT MAX(id) FROM app_home_game_categories));

-- ============================================================================
-- APP首页分类游戏关联数据 (App Home Category Games Data)
-- ============================================================================

-- 首先需要有应用数据，这里先跳过分类游戏的插入
-- 因为 app_home_category_games 表需要 application_id 字段
-- 等应用管理模块有数据后再添加

-- 重置序列
SELECT setval('app_home_category_games_id_seq', GREATEST(COALESCE((SELECT MAX(id) FROM app_home_category_games), 0), 1));

-- ============================================================================
-- 数据恢复完成
-- ============================================================================

SELECT 'Configuration management data restored successfully!' as status;
