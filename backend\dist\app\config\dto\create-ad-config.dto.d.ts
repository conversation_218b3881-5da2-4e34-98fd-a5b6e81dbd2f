import { AdType, JumpType, ImageItem } from '../entities/ad-config.entity';
export declare class CreateImageItemDto implements ImageItem {
    imageUrl: string;
    jumpType: JumpType;
    jumpTarget: string;
    title?: string;
    description?: string;
}
export declare class CreateAdConfigDto {
    adIdentifier: string;
    adType: AdType;
    title: string;
    imageItems: CreateImageItemDto[];
    sortOrder?: number;
    status?: number;
    remark?: string;
}
