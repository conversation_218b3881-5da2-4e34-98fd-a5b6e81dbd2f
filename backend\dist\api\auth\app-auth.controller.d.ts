import { AppAuthService } from './app-auth.service';
import { AppLoginDto, AppGoogleLoginDto, AppRegisterDto, AppLoginResponseDto } from './dto/app-login.dto';
export declare class AppAuthController {
    private readonly appAuthService;
    constructor(appAuthService: AppAuthService);
    login(loginDto: AppLoginDto, ip: string, userAgent: string): Promise<{
        code: number;
        message: string;
        result: AppLoginResponseDto;
    }>;
    register(registerDto: AppRegisterDto, ip: string, userAgent: string): Promise<{
        code: number;
        message: string;
        result: AppLoginResponseDto;
    }>;
    googleLogin(googleLoginDto: AppGoogleLoginDto, ip: string, userAgent: string): Promise<{
        code: number;
        message: string;
        result: {};
    }>;
    refreshToken(refreshToken: string): Promise<{
        code: number;
        message: string;
        result: {
            accessToken: string;
            refreshToken: string;
        };
    }>;
    logout(): Promise<{
        code: number;
        message: string;
        result: {};
    }>;
}
