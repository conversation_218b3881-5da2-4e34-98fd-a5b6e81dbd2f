import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsOptional, Min, Max, Length, IsIn, IsUrl, IsArray, ArrayMinSize, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AdType, JumpType, ImageItem } from '../entities/ad-config.entity';

export class CreateImageItemDto implements ImageItem {
  @ApiProperty({
    description: '图片URL',
    example: '/uploads/ads/banner1.jpg'
  })
  @IsNotEmpty({ message: '图片URL不能为空' })
  @IsString({ message: '图片URL必须是字符串' })
  @Length(1, 500, { message: '图片URL长度不能超过500个字符' })
  imageUrl: string;

  @ApiProperty({
    description: '跳转类型：1-内部路由，2-iframe页面',
    example: 1,
    enum: JumpType
  })
  @IsNotEmpty({ message: '跳转类型不能为空' })
  @IsNumber({}, { message: '跳转类型必须是数字' })
  @IsIn([1, 2], { message: '跳转类型必须是1或2' })
  jumpType: JumpType;

  @ApiProperty({
    description: '跳转目标（路由路径或URL）',
    example: '/games/hot'
  })
  @IsNotEmpty({ message: '跳转目标不能为空' })
  @IsString({ message: '跳转目标必须是字符串' })
  @Length(1, 500, { message: '跳转目标长度不能超过500个字符' })
  jumpTarget: string;

  @ApiProperty({
    description: '图片标题',
    example: '热门游戏',
    required: false
  })
  @IsOptional()
  @IsString({ message: '图片标题必须是字符串' })
  @Length(0, 100, { message: '图片标题长度不能超过100个字符' })
  title?: string;

  @ApiProperty({
    description: '图片描述',
    example: '查看最受欢迎的游戏',
    required: false
  })
  @IsOptional()
  @IsString({ message: '图片描述必须是字符串' })
  @Length(0, 200, { message: '图片描述长度不能超过200个字符' })
  description?: string;
}

export class CreateAdConfigDto {
  @ApiProperty({ 
    description: '唯一字符标识，前端根据此标识渲染广告', 
    example: 'banner_home_1' 
  })
  @IsNotEmpty({ message: '广告标识不能为空' })
  @IsString({ message: '广告标识必须是字符串' })
  @Length(1, 50, { message: '广告标识长度必须在1-50个字符之间' })
  adIdentifier: string;

  @ApiProperty({
    description: '广告类型：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏，7-首页4宫格',
    example: 1,
    enum: AdType
  })
  @IsNotEmpty({ message: '广告类型不能为空' })
  @IsNumber({}, { message: '广告类型必须是数字' })
  @IsIn([1, 2, 3, 4, 5, 6, 7], { message: '广告类型必须是1-7之间的数字' })
  adType: AdType;

  @ApiProperty({ 
    description: '广告标题', 
    example: '首页轮播广告' 
  })
  @IsNotEmpty({ message: '广告标题不能为空' })
  @IsString({ message: '广告标题必须是字符串' })
  @Length(1, 100, { message: '广告标题长度必须在1-100个字符之间' })
  title: string;

  @ApiProperty({
    description: '图片跳转项数组，每个元素包含图片URL和跳转信息',
    example: [
      {
        imageUrl: '/uploads/ads/banner1.jpg',
        jumpType: 1,
        jumpTarget: '/games/popular',
        title: '热门游戏',
        description: '查看最受欢迎的游戏'
      },
      {
        imageUrl: '/uploads/ads/banner2.jpg',
        jumpType: 2,
        jumpTarget: 'https://activity.example.com',
        title: '限时活动',
        description: '参与活动赢取丰厚奖励'
      }
    ],
    type: [CreateImageItemDto]
  })
  @IsNotEmpty({ message: '图片跳转项不能为空' })
  @IsArray({ message: '图片跳转项必须是数组格式' })
  @ArrayMinSize(1, { message: '至少需要配置一个图片跳转项' })
  @ValidateNested({ each: true })
  @Type(() => CreateImageItemDto)
  imageItems: CreateImageItemDto[];

  @ApiProperty({ 
    description: '排序，数字越小越靠前', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;

  @ApiProperty({ 
    description: '状态：1-启用，0-禁用', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @IsIn([0, 1], { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ 
    description: '备注说明', 
    example: '首页轮播广告示例', 
    required: false 
  })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  @Length(0, 500, { message: '备注长度不能超过500个字符' })
  remark?: string;
}
