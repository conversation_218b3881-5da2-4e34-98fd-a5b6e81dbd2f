import{v as _e,j as _}from"./index-CHjq8S-S.js";import{n as Zt,g as es,a as B}from"./react-BUTTOX-3.js";import{B as ts}from"./index-DDI4OfxQ.js";import{f as ss,a as rs,b as is,c as ns,d as as}from"./index-jASoOOIw.js";import{av as q,aq as os,az as ls,Q as Ie,d as le,u as cs,ak as hs,aP as us,M as ds,I as Ee,p as Be,aA as Ze,aE as et,ad as fs,aQ as gs,q as ps,s as I,an as tt,S as vs,n as st,aC as _s,aD as ws}from"./antd-CXPM1OiB.js";const ys=n=>{let e;return n?e=n:typeof fetch>"u"?e=(...t)=>_e(async()=>{const{default:s}=await Promise.resolve().then(()=>oe);return{default:s}},void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)};class Ge extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class ms extends Ge{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class bs extends Ge{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class ks extends Ge{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var qe;(function(n){n.Any="any",n.ApNortheast1="ap-northeast-1",n.ApNortheast2="ap-northeast-2",n.ApSouth1="ap-south-1",n.ApSoutheast1="ap-southeast-1",n.ApSoutheast2="ap-southeast-2",n.CaCentral1="ca-central-1",n.EuCentral1="eu-central-1",n.EuWest1="eu-west-1",n.EuWest2="eu-west-2",n.EuWest3="eu-west-3",n.SaEast1="sa-east-1",n.UsEast1="us-east-1",n.UsWest1="us-west-1",n.UsWest2="us-west-2"})(qe||(qe={}));var Ss=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};class js{constructor(e,{headers:t={},customFetch:s,region:r=qe.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=ys(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return Ss(this,void 0,void 0,function*(){try{const{headers:r,method:i,body:a}=t;let o={},{region:l}=t;l||(l=this.region),l&&l!=="any"&&(o["x-region"]=l);let c;a&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&(typeof Blob<"u"&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",c=a):typeof a=="string"?(o["Content-Type"]="text/plain",c=a):typeof FormData<"u"&&a instanceof FormData?c=a:(o["Content-Type"]="application/json",c=JSON.stringify(a)));const h=yield this.fetch(`${this.url}/${e}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),r),body:c}).catch(v=>{throw new ms(v)}),u=h.headers.get("x-relay-error");if(u&&u==="true")throw new bs(h);if(!h.ok)throw new ks(h);let d=((s=h.headers.get("Content-Type"))!==null&&s!==void 0?s:"text/plain").split(";")[0].trim(),f;return d==="application/json"?f=yield h.json():d==="application/octet-stream"?f=yield h.blob():d==="text/event-stream"?f=h:d==="multipart/form-data"?f=yield h.formData():f=yield h.text(),{data:f,error:null}}catch(r){return{data:null,error:r}}})}}var O={},Q={},Y={},X={},Z={},ee={},As=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},ae=As();const Es=ae.fetch,Ot=ae.fetch.bind(ae),Pt=ae.Headers,Ts=ae.Request,xs=ae.Response,oe=Object.freeze(Object.defineProperty({__proto__:null,Headers:Pt,Request:Ts,Response:xs,default:Ot,fetch:Es},Symbol.toStringTag,{value:"Module"})),Os=Zt(oe);var me={},rt;function It(){if(rt)return me;rt=1,Object.defineProperty(me,"__esModule",{value:!0});class n extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}}return me.default=n,me}var it;function $t(){if(it)return ee;it=1;var n=ee&&ee.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(ee,"__esModule",{value:!0});const e=n(Os),t=n(It());class s{constructor(i){this.shouldThrowOnError=!1,this.method=i.method,this.url=i.url,this.headers=i.headers,this.schema=i.schema,this.body=i.body,this.shouldThrowOnError=i.shouldThrowOnError,this.signal=i.signal,this.isMaybeSingle=i.isMaybeSingle,i.fetch?this.fetch=i.fetch:typeof fetch>"u"?this.fetch=e.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(i,a){return this.headers=Object.assign({},this.headers),this.headers[i]=a,this}then(i,a){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const o=this.fetch;let l=o(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async c=>{var h,u,d;let f=null,v=null,b=null,p=c.status,j=c.statusText;if(c.ok){if(this.method!=="HEAD"){const E=await c.text();E===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?v=E:v=JSON.parse(E))}const g=(h=this.headers.Prefer)===null||h===void 0?void 0:h.match(/count=(exact|planned|estimated)/),S=(u=c.headers.get("content-range"))===null||u===void 0?void 0:u.split("/");g&&S&&S.length>1&&(b=parseInt(S[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(v)&&(v.length>1?(f={code:"PGRST116",details:`Results contain ${v.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},v=null,b=null,p=406,j="Not Acceptable"):v.length===1?v=v[0]:v=null)}else{const g=await c.text();try{f=JSON.parse(g),Array.isArray(f)&&c.status===404&&(v=[],f=null,p=200,j="OK")}catch{c.status===404&&g===""?(p=204,j="No Content"):f={message:g}}if(f&&this.isMaybeSingle&&(!((d=f==null?void 0:f.details)===null||d===void 0)&&d.includes("0 rows"))&&(f=null,p=200,j="OK"),f&&this.shouldThrowOnError)throw new t.default(f)}return{error:f,data:v,count:b,status:p,statusText:j}});return this.shouldThrowOnError||(l=l.catch(c=>{var h,u,d;return{error:{message:`${(h=c==null?void 0:c.name)!==null&&h!==void 0?h:"FetchError"}: ${c==null?void 0:c.message}`,details:`${(u=c==null?void 0:c.stack)!==null&&u!==void 0?u:""}`,hint:"",code:`${(d=c==null?void 0:c.code)!==null&&d!==void 0?d:""}`},data:null,count:null,status:0,statusText:""}})),l.then(i,a)}returns(){return this}overrideTypes(){return this}}return ee.default=s,ee}var nt;function Ct(){if(nt)return Z;nt=1;var n=Z&&Z.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Z,"__esModule",{value:!0});const e=n($t());class t extends e.default{select(r){let i=!1;const a=(r??"*").split("").map(o=>/\s/.test(o)&&!i?"":(o==='"'&&(i=!i),o)).join("");return this.url.searchParams.set("select",a),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(r,{ascending:i=!0,nullsFirst:a,foreignTable:o,referencedTable:l=o}={}){const c=l?`${l}.order`:"order",h=this.url.searchParams.get(c);return this.url.searchParams.set(c,`${h?`${h},`:""}${r}.${i?"asc":"desc"}${a===void 0?"":a?".nullsfirst":".nullslast"}`),this}limit(r,{foreignTable:i,referencedTable:a=i}={}){const o=typeof a>"u"?"limit":`${a}.limit`;return this.url.searchParams.set(o,`${r}`),this}range(r,i,{foreignTable:a,referencedTable:o=a}={}){const l=typeof o>"u"?"offset":`${o}.offset`,c=typeof o>"u"?"limit":`${o}.limit`;return this.url.searchParams.set(l,`${r}`),this.url.searchParams.set(c,`${i-r+1}`),this}abortSignal(r){return this.signal=r,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:r=!1,verbose:i=!1,settings:a=!1,buffers:o=!1,wal:l=!1,format:c="text"}={}){var h;const u=[r?"analyze":null,i?"verbose":null,a?"settings":null,o?"buffers":null,l?"wal":null].filter(Boolean).join("|"),d=(h=this.headers.Accept)!==null&&h!==void 0?h:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${c}; for="${d}"; options=${u};`,c==="json"?this:this}rollback(){var r;return((r=this.headers.Prefer)!==null&&r!==void 0?r:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return Z.default=t,Z}var at;function Ke(){if(at)return X;at=1;var n=X&&X.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(X,"__esModule",{value:!0});const e=n(Ct());class t extends e.default{eq(r,i){return this.url.searchParams.append(r,`eq.${i}`),this}neq(r,i){return this.url.searchParams.append(r,`neq.${i}`),this}gt(r,i){return this.url.searchParams.append(r,`gt.${i}`),this}gte(r,i){return this.url.searchParams.append(r,`gte.${i}`),this}lt(r,i){return this.url.searchParams.append(r,`lt.${i}`),this}lte(r,i){return this.url.searchParams.append(r,`lte.${i}`),this}like(r,i){return this.url.searchParams.append(r,`like.${i}`),this}likeAllOf(r,i){return this.url.searchParams.append(r,`like(all).{${i.join(",")}}`),this}likeAnyOf(r,i){return this.url.searchParams.append(r,`like(any).{${i.join(",")}}`),this}ilike(r,i){return this.url.searchParams.append(r,`ilike.${i}`),this}ilikeAllOf(r,i){return this.url.searchParams.append(r,`ilike(all).{${i.join(",")}}`),this}ilikeAnyOf(r,i){return this.url.searchParams.append(r,`ilike(any).{${i.join(",")}}`),this}is(r,i){return this.url.searchParams.append(r,`is.${i}`),this}in(r,i){const a=Array.from(new Set(i)).map(o=>typeof o=="string"&&new RegExp("[,()]").test(o)?`"${o}"`:`${o}`).join(",");return this.url.searchParams.append(r,`in.(${a})`),this}contains(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cs.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cs.{${i.join(",")}}`):this.url.searchParams.append(r,`cs.${JSON.stringify(i)}`),this}containedBy(r,i){return typeof i=="string"?this.url.searchParams.append(r,`cd.${i}`):Array.isArray(i)?this.url.searchParams.append(r,`cd.{${i.join(",")}}`):this.url.searchParams.append(r,`cd.${JSON.stringify(i)}`),this}rangeGt(r,i){return this.url.searchParams.append(r,`sr.${i}`),this}rangeGte(r,i){return this.url.searchParams.append(r,`nxl.${i}`),this}rangeLt(r,i){return this.url.searchParams.append(r,`sl.${i}`),this}rangeLte(r,i){return this.url.searchParams.append(r,`nxr.${i}`),this}rangeAdjacent(r,i){return this.url.searchParams.append(r,`adj.${i}`),this}overlaps(r,i){return typeof i=="string"?this.url.searchParams.append(r,`ov.${i}`):this.url.searchParams.append(r,`ov.{${i.join(",")}}`),this}textSearch(r,i,{config:a,type:o}={}){let l="";o==="plain"?l="pl":o==="phrase"?l="ph":o==="websearch"&&(l="w");const c=a===void 0?"":`(${a})`;return this.url.searchParams.append(r,`${l}fts${c}.${i}`),this}match(r){return Object.entries(r).forEach(([i,a])=>{this.url.searchParams.append(i,`eq.${a}`)}),this}not(r,i,a){return this.url.searchParams.append(r,`not.${i}.${a}`),this}or(r,{foreignTable:i,referencedTable:a=i}={}){const o=a?`${a}.or`:"or";return this.url.searchParams.append(o,`(${r})`),this}filter(r,i,a){return this.url.searchParams.append(r,`${i}.${a}`),this}}return X.default=t,X}var ot;function Rt(){if(ot)return Y;ot=1;var n=Y&&Y.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(Y,"__esModule",{value:!0});const e=n(Ke());class t{constructor(r,{headers:i={},schema:a,fetch:o}){this.url=r,this.headers=i,this.schema=a,this.fetch=o}select(r,{head:i=!1,count:a}={}){const o=i?"HEAD":"GET";let l=!1;const c=(r??"*").split("").map(h=>/\s/.test(h)&&!l?"":(h==='"'&&(l=!l),h)).join("");return this.url.searchParams.set("select",c),a&&(this.headers.Prefer=`count=${a}`),new e.default({method:o,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(r,{count:i,defaultToNull:a=!0}={}){const o="POST",l=[];if(this.headers.Prefer&&l.push(this.headers.Prefer),i&&l.push(`count=${i}`),a||l.push("missing=default"),this.headers.Prefer=l.join(","),Array.isArray(r)){const c=r.reduce((h,u)=>h.concat(Object.keys(u)),[]);if(c.length>0){const h=[...new Set(c)].map(u=>`"${u}"`);this.url.searchParams.set("columns",h.join(","))}}return new e.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}upsert(r,{onConflict:i,ignoreDuplicates:a=!1,count:o,defaultToNull:l=!0}={}){const c="POST",h=[`resolution=${a?"ignore":"merge"}-duplicates`];if(i!==void 0&&this.url.searchParams.set("on_conflict",i),this.headers.Prefer&&h.push(this.headers.Prefer),o&&h.push(`count=${o}`),l||h.push("missing=default"),this.headers.Prefer=h.join(","),Array.isArray(r)){const u=r.reduce((d,f)=>d.concat(Object.keys(f)),[]);if(u.length>0){const d=[...new Set(u)].map(f=>`"${f}"`);this.url.searchParams.set("columns",d.join(","))}}return new e.default({method:c,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}update(r,{count:i}={}){const a="PATCH",o=[];return this.headers.Prefer&&o.push(this.headers.Prefer),i&&o.push(`count=${i}`),this.headers.Prefer=o.join(","),new e.default({method:a,url:this.url,headers:this.headers,schema:this.schema,body:r,fetch:this.fetch,allowEmpty:!1})}delete({count:r}={}){const i="DELETE",a=[];return r&&a.push(`count=${r}`),this.headers.Prefer&&a.unshift(this.headers.Prefer),this.headers.Prefer=a.join(","),new e.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return Y.default=t,Y}var ce={},he={},lt;function Ps(){return lt||(lt=1,Object.defineProperty(he,"__esModule",{value:!0}),he.version=void 0,he.version="0.0.0-automated"),he}var ct;function Is(){if(ct)return ce;ct=1,Object.defineProperty(ce,"__esModule",{value:!0}),ce.DEFAULT_HEADERS=void 0;const n=Ps();return ce.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${n.version}`},ce}var ht;function $s(){if(ht)return Q;ht=1;var n=Q&&Q.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Q,"__esModule",{value:!0});const e=n(Rt()),t=n(Ke()),s=Is();class r{constructor(a,{headers:o={},schema:l,fetch:c}={}){this.url=a,this.headers=Object.assign(Object.assign({},s.DEFAULT_HEADERS),o),this.schemaName=l,this.fetch=c}from(a){const o=new URL(`${this.url}/${a}`);return new e.default(o,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(a){return new r(this.url,{headers:this.headers,schema:a,fetch:this.fetch})}rpc(a,o={},{head:l=!1,get:c=!1,count:h}={}){let u;const d=new URL(`${this.url}/rpc/${a}`);let f;l||c?(u=l?"HEAD":"GET",Object.entries(o).filter(([b,p])=>p!==void 0).map(([b,p])=>[b,Array.isArray(p)?`{${p.join(",")}}`:`${p}`]).forEach(([b,p])=>{d.searchParams.append(b,p)})):(u="POST",f=o);const v=Object.assign({},this.headers);return h&&(v.Prefer=`count=${h}`),new t.default({method:u,url:d,headers:v,schema:this.schemaName,body:f,fetch:this.fetch,allowEmpty:!1})}}return Q.default=r,Q}var ut;function Cs(){if(ut)return O;ut=1;var n=O&&O.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(O,"__esModule",{value:!0}),O.PostgrestError=O.PostgrestBuilder=O.PostgrestTransformBuilder=O.PostgrestFilterBuilder=O.PostgrestQueryBuilder=O.PostgrestClient=void 0;const e=n($s());O.PostgrestClient=e.default;const t=n(Rt());O.PostgrestQueryBuilder=t.default;const s=n(Ke());O.PostgrestFilterBuilder=s.default;const r=n(Ct());O.PostgrestTransformBuilder=r.default;const i=n($t());O.PostgrestBuilder=i.default;const a=n(It());return O.PostgrestError=a.default,O.default={PostgrestClient:e.default,PostgrestQueryBuilder:t.default,PostgrestFilterBuilder:s.default,PostgrestTransformBuilder:r.default,PostgrestBuilder:i.default,PostgrestError:a.default},O}var Rs=Cs();const Us=es(Rs),{PostgrestClient:Ls,PostgrestQueryBuilder:Ri,PostgrestFilterBuilder:Ui,PostgrestTransformBuilder:Li,PostgrestBuilder:Di,PostgrestError:Bi}=Us;function Ds(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const Bs=Ds(),qs="2.11.13",Ms={"X-Client-Info":`realtime-js/${qs}`},Ns="1.0.0",Ut=1e4,Fs=1e3;var de;(function(n){n[n.connecting=0]="connecting",n[n.open=1]="open",n[n.closing=2]="closing",n[n.closed=3]="closed"})(de||(de={}));var $;(function(n){n.closed="closed",n.errored="errored",n.joined="joined",n.joining="joining",n.leaving="leaving"})($||($={}));var D;(function(n){n.close="phx_close",n.error="phx_error",n.join="phx_join",n.reply="phx_reply",n.leave="phx_leave",n.access_token="access_token"})(D||(D={}));var Me;(function(n){n.websocket="websocket"})(Me||(Me={}));var K;(function(n){n.Connecting="connecting",n.Open="open",n.Closing="closing",n.Closed="closed"})(K||(K={}));class zs{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),i=t.getUint8(2);let a=this.HEADER_LENGTH+2;const o=s.decode(e.slice(a,a+r));a=a+r;const l=s.decode(e.slice(a,a+i));a=a+i;const c=JSON.parse(s.decode(e.slice(a,e.byteLength)));return{ref:null,topic:o,event:l,payload:c}}}class Lt{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var A;(function(n){n.abstime="abstime",n.bool="bool",n.date="date",n.daterange="daterange",n.float4="float4",n.float8="float8",n.int2="int2",n.int4="int4",n.int4range="int4range",n.int8="int8",n.int8range="int8range",n.json="json",n.jsonb="jsonb",n.money="money",n.numeric="numeric",n.oid="oid",n.reltime="reltime",n.text="text",n.time="time",n.timestamp="timestamp",n.timestamptz="timestamptz",n.timetz="timetz",n.tsrange="tsrange",n.tstzrange="tstzrange"})(A||(A={}));const dt=(n,e,t={})=>{var s;const r=(s=t.skipTypes)!==null&&s!==void 0?s:[];return Object.keys(e).reduce((i,a)=>(i[a]=Ws(a,n,e,r),i),{})},Ws=(n,e,t,s)=>{const r=e.find(o=>o.name===n),i=r==null?void 0:r.type,a=t[n];return i&&!s.includes(i)?Dt(i,a):Ne(a)},Dt=(n,e)=>{if(n.charAt(0)==="_"){const t=n.slice(1,n.length);return Ks(e,t)}switch(n){case A.bool:return Js(e);case A.float4:case A.float8:case A.int2:case A.int4:case A.int8:case A.numeric:case A.oid:return Hs(e);case A.json:case A.jsonb:return Gs(e);case A.timestamp:return Vs(e);case A.abstime:case A.date:case A.daterange:case A.int4range:case A.int8range:case A.money:case A.reltime:case A.text:case A.time:case A.timestamptz:case A.timetz:case A.tsrange:case A.tstzrange:return Ne(e);default:return Ne(e)}},Ne=n=>n,Js=n=>{switch(n){case"t":return!0;case"f":return!1;default:return n}},Hs=n=>{if(typeof n=="string"){const e=parseFloat(n);if(!Number.isNaN(e))return e}return n},Gs=n=>{if(typeof n=="string")try{return JSON.parse(n)}catch(e){return console.log(`JSON parse error: ${e}`),n}return n},Ks=(n,e)=>{if(typeof n!="string")return n;const t=n.length-1,s=n[t];if(n[0]==="{"&&s==="}"){let i;const a=n.slice(1,t);try{i=JSON.parse("["+a+"]")}catch{i=a?a.split(","):[]}return i.map(o=>Dt(e,o))}return n},Vs=n=>typeof n=="string"?n.replace(" ","T"):n,Bt=n=>{let e=n;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class $e{constructor(e,t,s={},r=Ut){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t((s=this.receivedResp)===null||s===void 0?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var ft;(function(n){n.SYNC="sync",n.JOIN="join",n.LEAVE="leave"})(ft||(ft={}));class fe{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},r=>{const{onJoin:i,onLeave:a,onSync:o}=this.caller;this.joinRef=this.channel._joinRef(),this.state=fe.syncState(this.state,r,i,a),this.pendingDiffs.forEach(l=>{this.state=fe.syncDiff(this.state,l,i,a)}),this.pendingDiffs=[],o()}),this.channel._on(s.diff,{},r=>{const{onJoin:i,onLeave:a,onSync:o}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(r):(this.state=fe.syncDiff(this.state,r,i,a),o())}),this.onJoin((r,i,a)=>{this.channel._trigger("presence",{event:"join",key:r,currentPresences:i,newPresences:a})}),this.onLeave((r,i,a)=>{this.channel._trigger("presence",{event:"leave",key:r,currentPresences:i,leftPresences:a})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const i=this.cloneDeep(e),a=this.transformState(t),o={},l={};return this.map(i,(c,h)=>{a[c]||(l[c]=h)}),this.map(a,(c,h)=>{const u=i[c];if(u){const d=h.map(p=>p.presence_ref),f=u.map(p=>p.presence_ref),v=h.filter(p=>f.indexOf(p.presence_ref)<0),b=u.filter(p=>d.indexOf(p.presence_ref)<0);v.length>0&&(o[c]=v),b.length>0&&(l[c]=b)}else o[c]=h}),this.syncDiff(i,{joins:o,leaves:l},s,r)}static syncDiff(e,t,s,r){const{joins:i,leaves:a}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(o,l)=>{var c;const h=(c=e[o])!==null&&c!==void 0?c:[];if(e[o]=this.cloneDeep(l),h.length>0){const u=e[o].map(f=>f.presence_ref),d=h.filter(f=>u.indexOf(f.presence_ref)<0);e[o].unshift(...d)}s(o,h,l)}),this.map(a,(o,l)=>{let c=e[o];if(!c)return;const h=l.map(u=>u.presence_ref);c=c.filter(u=>h.indexOf(u.presence_ref)<0),e[o]=c,r(o,c,l),c.length===0&&delete e[o]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return"metas"in r?t[s]=r.metas.map(i=>(i.presence_ref=i.phx_ref,delete i.phx_ref,delete i.phx_ref_prev,i)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var gt;(function(n){n.ALL="*",n.INSERT="INSERT",n.UPDATE="UPDATE",n.DELETE="DELETE"})(gt||(gt={}));var pt;(function(n){n.BROADCAST="broadcast",n.PRESENCE="presence",n.POSTGRES_CHANGES="postgres_changes",n.SYSTEM="system"})(pt||(pt={}));var N;(function(n){n.SUBSCRIBED="SUBSCRIBED",n.TIMED_OUT="TIMED_OUT",n.CLOSED="CLOSED",n.CHANNEL_ERROR="CHANNEL_ERROR"})(N||(N={}));class Ve{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=$.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new $e(this,D.join,this.params,this.timeout),this.rejoinTimer=new Lt(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=$.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(r=>r.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=$.closed,this.socket._remove(this)}),this._onError(r=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,r),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=$.errored,this.rejoinTimer.scheduleTimeout())}),this._on(D.reply,{},(r,i)=>{this._trigger(this._replyEventName(i),r)}),this.presence=new fe(this),this.broadcastEndpointURL=Bt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.state==$.closed){const{config:{broadcast:i,presence:a,private:o}}=this.params;this._onError(h=>e==null?void 0:e(N.CHANNEL_ERROR,h)),this._onClose(()=>e==null?void 0:e(N.CLOSED));const l={},c={broadcast:i,presence:a,postgres_changes:(r=(s=this.bindings.postgres_changes)===null||s===void 0?void 0:s.map(h=>h.filter))!==null&&r!==void 0?r:[],private:o};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:c},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:h})=>{var u;if(this.socket.setAuth(),h===void 0){e==null||e(N.SUBSCRIBED);return}else{const d=this.bindings.postgres_changes,f=(u=d==null?void 0:d.length)!==null&&u!==void 0?u:0,v=[];for(let b=0;b<f;b++){const p=d[b],{filter:{event:j,schema:x,table:g,filter:S}}=p,E=h&&h[b];if(E&&E.event===j&&E.schema===x&&E.table===g&&E.filter===S)v.push(Object.assign(Object.assign({},p),{id:E.id}));else{this.unsubscribe(),this.state=$.errored,e==null||e(N.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=v,e&&e(N.SUBSCRIBED);return}}).receive("error",h=>{this.state=$.errored,e==null||e(N.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(h).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(N.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(!this._canPush()&&e.type==="broadcast"){const{event:i,payload:a}=e,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:a,private:this.private}]})};try{const c=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(s=t.timeout)!==null&&s!==void 0?s:this.timeout);return await((r=c.body)===null||r===void 0?void 0:r.cancel()),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(i=>{var a,o,l;const c=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((l=(o=(a=this.params)===null||a===void 0?void 0:a.config)===null||o===void 0?void 0:o.broadcast)===null||l===void 0)&&l.ack)&&i("ok"),c.receive("ok",()=>i("ok")),c.receive("error",()=>i("error")),c.receive("timeout",()=>i("timed out"))})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=$.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(D.close,"leave",this._joinRef())};this.joinPush.destroy();let s=null;return new Promise(r=>{s=new $e(this,D.leave,{},e),s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})}).finally(()=>{s==null||s.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){const r=new AbortController,i=setTimeout(()=>r.abort(),s),a=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),a}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new $e(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;const a=e.toLocaleLowerCase(),{close:o,error:l,leave:c,join:h}=D;if(s&&[o,l,c,h].indexOf(a)>=0&&s!==this._joinRef())return;let d=this._onMessage(a,t,s);if(t&&!d)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?(r=this.bindings.postgres_changes)===null||r===void 0||r.filter(f=>{var v,b,p;return((v=f.filter)===null||v===void 0?void 0:v.event)==="*"||((p=(b=f.filter)===null||b===void 0?void 0:b.event)===null||p===void 0?void 0:p.toLocaleLowerCase())===a}).map(f=>f.callback(d,s)):(i=this.bindings[a])===null||i===void 0||i.filter(f=>{var v,b,p,j,x,g;if(["broadcast","presence","postgres_changes"].includes(a))if("id"in f){const S=f.id,E=(v=f.filter)===null||v===void 0?void 0:v.event;return S&&((b=t.ids)===null||b===void 0?void 0:b.includes(S))&&(E==="*"||(E==null?void 0:E.toLocaleLowerCase())===((p=t.data)===null||p===void 0?void 0:p.type.toLocaleLowerCase()))}else{const S=(x=(j=f==null?void 0:f.filter)===null||j===void 0?void 0:j.event)===null||x===void 0?void 0:x.toLocaleLowerCase();return S==="*"||S===((g=t==null?void 0:t.event)===null||g===void 0?void 0:g.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===a}).map(f=>{if(typeof d=="object"&&"ids"in d){const v=d.data,{schema:b,table:p,commit_timestamp:j,type:x,errors:g}=v;d=Object.assign(Object.assign({},{schema:b,table:p,commit_timestamp:j,eventType:x,new:{},old:{},errors:g}),this._getPayloadRecords(v))}f.callback(d,s)})}_isClosed(){return this.state===$.closed}_isJoined(){return this.state===$.joined}_isJoining(){return this.state===$.joining}_isLeaving(){return this.state===$.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(r=>{var i;return!(((i=r.type)===null||i===void 0?void 0:i.toLocaleLowerCase())===s&&Ve.isEqual(r.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(D.close,{},e)}_onError(e){this._on(D.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=$.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=dt(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=dt(e.columns,e.old_record)),t}}const vt=()=>{},Qs=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Ys{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=Ms,this.params={},this.timeout=Ut,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=vt,this.ref=0,this.logger=vt,this.conn=null,this.sendBuffer=[],this.serializer=new zs,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=i=>{let a;return i?a=i:typeof fetch>"u"?a=(...o)=>_e(async()=>{const{default:l}=await Promise.resolve().then(()=>oe);return{default:l}},void 0).then(({default:l})=>l(...o)):a=fetch,(...o)=>a(...o)},this.endPoint=`${e}/${Me.websocket}`,this.httpEndpoint=Bt(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),(t!=null&&t.logLevel||t!=null&&t.log_level)&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=(s=t==null?void 0:t.params)===null||s===void 0?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:i=>[1e3,2e3,5e3,1e4][i-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(i,a)=>a(JSON.stringify(i)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Lt(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){this.conn||(this.transport||(this.transport=Bs),this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection())}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Ns}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(s=>s.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return this.channels.length===0&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(t=>t.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case de.connecting:return K.Connecting;case de.open:return K.Open;case de.closing:return K.Closing;default:return K.Closed}}isConnected(){return this.connectionState()===K.Open}channel(e,t={config:{}}){const s=`realtime:${e}`,r=this.getChannels().find(i=>i.topic===s);if(r)return r;{const i=new Ve(`realtime:${e}`,t,this);return this.channels.push(i),i}}push(e){const{topic:t,event:s,payload:r,ref:i}=e,a=()=>{this.encode(e,o=>{var l;(l=this.conn)===null||l===void 0||l.send(o)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?a():this.sendBuffer.push(a)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(s=>{t&&s.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),s.joinedOnce&&s._isJoined()&&s._push(D.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(e=this.conn)===null||e===void 0||e.close(Fs,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(s=>s.topic===e&&(s._isJoined()||s._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:s,event:r,payload:i,ref:a}=t;s==="phoenix"&&r==="phx_reply"&&this.heartbeatCallback(t.payload.status=="ok"?"ok":"error"),a&&a===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${s} ${r} ${a&&"("+a+")"||""}`,i),Array.from(this.channels).filter(o=>o._isMember(s)).forEach(o=>o._trigger(r,i,a)),this.stateChangeCallbacks.message.forEach(o=>o(t))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(D.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{const s=new Blob([Qs],{type:"application/javascript"});t=URL.createObjectURL(s)}return t}}class Qe extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function P(n){return typeof n=="object"&&n!==null&&"__isStorageError"in n}class Xs extends Qe{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Fe extends Qe{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Zs=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};const qt=n=>{let e;return n?e=n:typeof fetch>"u"?e=(...t)=>_e(async()=>{const{default:s}=await Promise.resolve().then(()=>oe);return{default:s}},void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},er=()=>Zs(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield _e(()=>Promise.resolve().then(()=>oe),void 0)).Response:Response}),ze=n=>{if(Array.isArray(n))return n.map(t=>ze(t));if(typeof n=="function"||n!==Object(n))return n;const e={};return Object.entries(n).forEach(([t,s])=>{const r=t.replace(/([-_][a-z])/gi,i=>i.toUpperCase().replace(/[-_]/g,""));e[r]=ze(s)}),e};var V=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};const Ce=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),tr=(n,e,t)=>V(void 0,void 0,void 0,function*(){const s=yield er();n instanceof s&&!(t!=null&&t.noResolveJson)?n.json().then(r=>{e(new Xs(Ce(r),n.status||500))}).catch(r=>{e(new Fe(Ce(r),r))}):e(new Fe(Ce(n),n))}),sr=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),s&&(r.body=JSON.stringify(s)),Object.assign(Object.assign({},r),t))};function we(n,e,t,s,r,i){return V(this,void 0,void 0,function*(){return new Promise((a,o)=>{n(t,sr(e,s,r,i)).then(l=>{if(!l.ok)throw l;return s!=null&&s.noResolveJson?l:l.json()}).then(l=>a(l)).catch(l=>tr(l,o,s))})})}function Te(n,e,t,s){return V(this,void 0,void 0,function*(){return we(n,"GET",e,t,s)})}function z(n,e,t,s,r){return V(this,void 0,void 0,function*(){return we(n,"POST",e,s,r,t)})}function rr(n,e,t,s,r){return V(this,void 0,void 0,function*(){return we(n,"PUT",e,s,r,t)})}function ir(n,e,t,s){return V(this,void 0,void 0,function*(){return we(n,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),s)})}function Mt(n,e,t,s,r){return V(this,void 0,void 0,function*(){return we(n,"DELETE",e,s,r,t)})}var R=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};const nr={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},_t={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ar{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=qt(r)}uploadOrUpdate(e,t,s,r){return R(this,void 0,void 0,function*(){try{let i;const a=Object.assign(Object.assign({},_t),r);let o=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(a.upsert)});const l=a.metadata;typeof Blob<"u"&&s instanceof Blob?(i=new FormData,i.append("cacheControl",a.cacheControl),l&&i.append("metadata",this.encodeMetadata(l)),i.append("",s)):typeof FormData<"u"&&s instanceof FormData?(i=s,i.append("cacheControl",a.cacheControl),l&&i.append("metadata",this.encodeMetadata(l))):(i=s,o["cache-control"]=`max-age=${a.cacheControl}`,o["content-type"]=a.contentType,l&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),r!=null&&r.headers&&(o=Object.assign(Object.assign({},o),r.headers));const c=this._removeEmptyFolders(t),h=this._getFinalPath(c),u=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:i,headers:o},a!=null&&a.duplex?{duplex:a.duplex}:{})),d=yield u.json();return u.ok?{data:{path:c,id:d.Id,fullPath:d.Key},error:null}:{data:null,error:d}}catch(i){if(P(i))return{data:null,error:i};throw i}})}upload(e,t,s){return R(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return R(this,void 0,void 0,function*(){const i=this._removeEmptyFolders(e),a=this._getFinalPath(i),o=new URL(this.url+`/object/upload/sign/${a}`);o.searchParams.set("token",t);try{let l;const c=Object.assign({upsert:_t.upsert},r),h=Object.assign(Object.assign({},this.headers),{"x-upsert":String(c.upsert)});typeof Blob<"u"&&s instanceof Blob?(l=new FormData,l.append("cacheControl",c.cacheControl),l.append("",s)):typeof FormData<"u"&&s instanceof FormData?(l=s,l.append("cacheControl",c.cacheControl)):(l=s,h["cache-control"]=`max-age=${c.cacheControl}`,h["content-type"]=c.contentType);const u=yield this.fetch(o.toString(),{method:"PUT",body:l,headers:h}),d=yield u.json();return u.ok?{data:{path:i,fullPath:d.Key},error:null}:{data:null,error:d}}catch(l){if(P(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return R(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);t!=null&&t.upsert&&(r["x-upsert"]="true");const i=yield z(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),a=new URL(this.url+i.url),o=a.searchParams.get("token");if(!o)throw new Qe("No token returned by API");return{data:{signedUrl:a.toString(),path:e,token:o},error:null}}catch(s){if(P(s))return{data:null,error:s};throw s}})}update(e,t,s){return R(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return R(this,void 0,void 0,function*(){try{return{data:yield z(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(P(r))return{data:null,error:r};throw r}})}copy(e,t,s){return R(this,void 0,void 0,function*(){try{return{data:{path:(yield z(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(P(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return R(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield z(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},s!=null&&s.transform?{transform:s.transform}:{}),{headers:this.headers});const a=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${a}`)},{data:i,error:null}}catch(r){if(P(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return R(this,void 0,void 0,function*(){try{const r=yield z(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return{data:r.map(a=>Object.assign(Object.assign({},a),{signedUrl:a.signedURL?encodeURI(`${this.url}${a.signedURL}${i}`):null})),error:null}}catch(r){if(P(r))return{data:null,error:r};throw r}})}download(e,t){return R(this,void 0,void 0,function*(){const r=typeof(t==null?void 0:t.transform)<"u"?"render/image/authenticated":"object",i=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),a=i?`?${i}`:"";try{const o=this._getFinalPath(e);return{data:yield(yield Te(this.fetch,`${this.url}/${r}/${o}${a}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(o){if(P(o))return{data:null,error:o};throw o}})}info(e){return R(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const s=yield Te(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:ze(s),error:null}}catch(s){if(P(s))return{data:null,error:s};throw s}})}exists(e){return R(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield ir(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(P(s)&&s instanceof Fe){const r=s.originalError;if([400,404].includes(r==null?void 0:r.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],i=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";i!==""&&r.push(i);const o=typeof(t==null?void 0:t.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&r.push(l);let c=r.join("&");return c!==""&&(c=`?${c}`),{data:{publicUrl:encodeURI(`${this.url}/${o}/public/${s}${c}`)}}}remove(e){return R(this,void 0,void 0,function*(){try{return{data:yield Mt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(P(t))return{data:null,error:t};throw t}})}list(e,t,s){return R(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},nr),t),{prefix:e||""});return{data:yield z(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(P(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer<"u"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const or="2.7.1",lr={"X-Client-Info":`storage-js/${or}`};var te=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};class cr{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},lr),t),this.fetch=qt(s)}listBuckets(){return te(this,void 0,void 0,function*(){try{return{data:yield Te(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(P(e))return{data:null,error:e};throw e}})}getBucket(e){return te(this,void 0,void 0,function*(){try{return{data:yield Te(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(P(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return te(this,void 0,void 0,function*(){try{return{data:yield z(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(P(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return te(this,void 0,void 0,function*(){try{return{data:yield rr(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(P(s))return{data:null,error:s};throw s}})}emptyBucket(e){return te(this,void 0,void 0,function*(){try{return{data:yield z(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(P(t))return{data:null,error:t};throw t}})}deleteBucket(e){return te(this,void 0,void 0,function*(){try{return{data:yield Mt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(P(t))return{data:null,error:t};throw t}})}}class hr extends cr{constructor(e,t={},s){super(e,t,s)}from(e){return new ar(this.url,this.headers,e,this.fetch)}}const ur="2.50.1";let ue="";typeof Deno<"u"?ue="deno":typeof document<"u"?ue="web":typeof navigator<"u"&&navigator.product==="ReactNative"?ue="react-native":ue="node";const dr={"X-Client-Info":`supabase-js-${ue}/${ur}`},fr={headers:dr},gr={schema:"public"},pr={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},vr={};var _r=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};const wr=n=>{let e;return n?e=n:typeof fetch>"u"?e=Ot:e=fetch,(...t)=>e(...t)},yr=()=>typeof Headers>"u"?Pt:Headers,mr=(n,e,t)=>{const s=wr(t),r=yr();return(i,a)=>_r(void 0,void 0,void 0,function*(){var o;const l=(o=yield e())!==null&&o!==void 0?o:n;let c=new r(a==null?void 0:a.headers);return c.has("apikey")||c.set("apikey",n),c.has("Authorization")||c.set("Authorization",`Bearer ${l}`),s(i,Object.assign(Object.assign({},a),{headers:c}))})};var br=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};function kr(n){return n.endsWith("/")?n:n+"/"}function Sr(n,e){var t,s;const{db:r,auth:i,realtime:a,global:o}=n,{db:l,auth:c,realtime:h,global:u}=e,d={db:Object.assign(Object.assign({},l),r),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},h),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},(t=u==null?void 0:u.headers)!==null&&t!==void 0?t:{}),(s=o==null?void 0:o.headers)!==null&&s!==void 0?s:{})}),accessToken:()=>br(this,void 0,void 0,function*(){return""})};return n.accessToken?d.accessToken=n.accessToken:delete d.accessToken,d}const Nt="2.70.0",ne=30*1e3,We=3,Re=We*ne,jr="http://localhost:9999",Ar="supabase.auth.token",Er={"X-Client-Info":`gotrue-js/${Nt}`},Je="X-Supabase-Api-Version",Ft={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},Tr=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,xr=6e5;class Ye extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function m(n){return typeof n=="object"&&n!==null&&"__isAuthError"in n}class Or extends Ye{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}function Pr(n){return m(n)&&n.name==="AuthApiError"}class zt extends Ye{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class J extends Ye{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class F extends J{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Ir(n){return m(n)&&n.name==="AuthSessionMissingError"}class be extends J{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class ke extends J{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Se extends J{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function $r(n){return m(n)&&n.name==="AuthImplicitGrantRedirectError"}class wt extends J{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class He extends J{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Ue(n){return m(n)&&n.name==="AuthRetryableFetchError"}class yt extends J{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class ge extends J{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const xe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),mt=` 	
\r=`.split(""),Cr=(()=>{const n=new Array(128);for(let e=0;e<n.length;e+=1)n[e]=-1;for(let e=0;e<mt.length;e+=1)n[mt[e].charCodeAt(0)]=-2;for(let e=0;e<xe.length;e+=1)n[xe[e].charCodeAt(0)]=e;return n})();function bt(n,e,t){if(n!==null)for(e.queue=e.queue<<8|n,e.queuedBits+=8;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(xe[s]),e.queuedBits-=6}else if(e.queuedBits>0)for(e.queue=e.queue<<6-e.queuedBits,e.queuedBits=6;e.queuedBits>=6;){const s=e.queue>>e.queuedBits-6&63;t(xe[s]),e.queuedBits-=6}}function Wt(n,e,t){const s=Cr[n];if(s>-1)for(e.queue=e.queue<<6|s,e.queuedBits+=6;e.queuedBits>=8;)t(e.queue>>e.queuedBits-8&255),e.queuedBits-=8;else{if(s===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(n)}"`)}}function kt(n){const e=[],t=a=>{e.push(String.fromCodePoint(a))},s={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},i=a=>{Lr(a,s,t)};for(let a=0;a<n.length;a+=1)Wt(n.charCodeAt(a),r,i);return e.join("")}function Rr(n,e){if(n<=127){e(n);return}else if(n<=2047){e(192|n>>6),e(128|n&63);return}else if(n<=65535){e(224|n>>12),e(128|n>>6&63),e(128|n&63);return}else if(n<=1114111){e(240|n>>18),e(128|n>>12&63),e(128|n>>6&63),e(128|n&63);return}throw new Error(`Unrecognized Unicode codepoint: ${n.toString(16)}`)}function Ur(n,e){for(let t=0;t<n.length;t+=1){let s=n.charCodeAt(t);if(s>55295&&s<=56319){const r=(s-55296)*1024&65535;s=(n.charCodeAt(t+1)-56320&65535|r)+65536,t+=1}Rr(s,e)}}function Lr(n,e,t){if(e.utf8seq===0){if(n<=127){t(n);return}for(let s=1;s<6;s+=1)if((n>>7-s&1)===0){e.utf8seq=s;break}if(e.utf8seq===2)e.codepoint=n&31;else if(e.utf8seq===3)e.codepoint=n&15;else if(e.utf8seq===4)e.codepoint=n&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(n<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|n&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}function Dr(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};for(let r=0;r<n.length;r+=1)Wt(n.charCodeAt(r),t,s);return new Uint8Array(e)}function Br(n){const e=[];return Ur(n,t=>e.push(t)),new Uint8Array(e)}function qr(n){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};return n.forEach(r=>bt(r,t,s)),bt(null,t,s),e.join("")}function Mr(n){return Math.round(Date.now()/1e3)+n}function Nr(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(n){const e=Math.random()*16|0;return(n=="x"?e:e&3|8).toString(16)})}const L=()=>typeof window<"u"&&typeof document<"u",H={tested:!1,writable:!1},pe=()=>{if(!L())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(H.tested)return H.writable;const n=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(n,n),globalThis.localStorage.removeItem(n),H.tested=!0,H.writable=!0}catch{H.tested=!0,H.writable=!1}return H.writable};function Fr(n){const e={},t=new URL(n);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((r,i)=>{e[i]=r})}catch{}return t.searchParams.forEach((s,r)=>{e[r]=s}),e}const Jt=n=>{let e;return n?e=n:typeof fetch>"u"?e=(...t)=>_e(async()=>{const{default:s}=await Promise.resolve().then(()=>oe);return{default:s}},void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},zr=n=>typeof n=="object"&&n!==null&&"status"in n&&"ok"in n&&"json"in n&&typeof n.json=="function",Ht=async(n,e,t)=>{await n.setItem(e,JSON.stringify(t))},je=async(n,e)=>{const t=await n.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch{return t}},Ae=async(n,e)=>{await n.removeItem(e)};class Oe{constructor(){this.promise=new Oe.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}Oe.promiseConstructor=Promise;function Le(n){const e=n.split(".");if(e.length!==3)throw new ge("Invalid JWT structure");for(let s=0;s<e.length;s++)if(!Tr.test(e[s]))throw new ge("JWT not in base64url format");return{header:JSON.parse(kt(e[0])),payload:JSON.parse(kt(e[1])),signature:Dr(e[2]),raw:{header:e[0],payload:e[1]}}}async function Wr(n){return await new Promise(e=>{setTimeout(()=>e(null),n)})}function Jr(n,e){return new Promise((s,r)=>{(async()=>{for(let i=0;i<1/0;i++)try{const a=await n(i);if(!e(i,null,a)){s(a);return}}catch(a){if(!e(i,a)){r(a);return}}})()})}function Hr(n){return("0"+n.toString(16)).substr(-2)}function Gr(){const e=new Uint32Array(56);if(typeof crypto>"u"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",s=t.length;let r="";for(let i=0;i<56;i++)r+=t.charAt(Math.floor(Math.random()*s));return r}return crypto.getRandomValues(e),Array.from(e,Hr).join("")}async function Kr(n){const t=new TextEncoder().encode(n),s=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(i=>String.fromCharCode(i)).join("")}async function Vr(n){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),n;const t=await Kr(n);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function se(n,e,t=!1){const s=Gr();let r=s;t&&(r+="/PASSWORD_RECOVERY"),await Ht(n,`${e}-code-verifier`,r);const i=await Vr(s);return[i,s===i?"plain":"s256"]}const Qr=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function Yr(n){const e=n.headers.get(Je);if(!e||!e.match(Qr))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch{return null}}function Xr(n){if(!n)throw new Error("Missing exp claim");const e=Math.floor(Date.now()/1e3);if(n<=e)throw new Error("JWT has expired")}function Zr(n){switch(n){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const ei=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function re(n){if(!ei.test(n))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var ti=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};const G=n=>n.msg||n.message||n.error_description||n.error||JSON.stringify(n),si=[502,503,504];async function St(n){var e;if(!zr(n))throw new He(G(n),0);if(si.includes(n.status))throw new He(G(n),n.status);let t;try{t=await n.json()}catch(i){throw new zt(G(i),i)}let s;const r=Yr(n);if(r&&r.getTime()>=Ft["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?s=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(s=t.error_code),s){if(s==="weak_password")throw new yt(G(t),n.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(s==="session_not_found")throw new F}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((i,a)=>i&&typeof a=="string",!0))throw new yt(G(t),n.status,t.weak_password.reasons);throw new Or(G(t),n.status||500,s)}const ri=(n,e,t,s)=>{const r={method:n,headers:(e==null?void 0:e.headers)||{}};return n==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),r.body=JSON.stringify(s),Object.assign(Object.assign({},r),t))};async function k(n,e,t,s){var r;const i=Object.assign({},s==null?void 0:s.headers);i[Je]||(i[Je]=Ft["2024-01-01"].name),s!=null&&s.jwt&&(i.Authorization=`Bearer ${s.jwt}`);const a=(r=s==null?void 0:s.query)!==null&&r!==void 0?r:{};s!=null&&s.redirectTo&&(a.redirect_to=s.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await ii(n,e,t+o,{headers:i,noResolveJson:s==null?void 0:s.noResolveJson},{},s==null?void 0:s.body);return s!=null&&s.xform?s==null?void 0:s.xform(l):{data:Object.assign({},l),error:null}}async function ii(n,e,t,s,r,i){const a=ri(e,s,r,i);let o;try{o=await n(t,Object.assign({},a))}catch(l){throw console.error(l),new He(G(l),0)}if(o.ok||await St(o),s!=null&&s.noResolveJson)return o;try{return await o.json()}catch(l){await St(l)}}function M(n){var e;let t=null;li(n)&&(t=Object.assign({},n),n.expires_at||(t.expires_at=Mr(n.expires_in)));const s=(e=n.user)!==null&&e!==void 0?e:n;return{data:{session:t,user:s},error:null}}function jt(n){const e=M(n);return!e.error&&n.weak_password&&typeof n.weak_password=="object"&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.message&&typeof n.weak_password.message=="string"&&n.weak_password.reasons.reduce((t,s)=>t&&typeof s=="string",!0)&&(e.data.weak_password=n.weak_password),e}function W(n){var e;return{data:{user:(e=n.user)!==null&&e!==void 0?e:n},error:null}}function ni(n){return{data:n,error:null}}function ai(n){const{action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i}=n,a=ti(n,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),o={action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:i},l=Object.assign({},a);return{data:{properties:o,user:l},error:null}}function oi(n){return n}function li(n){return n.access_token&&n.refresh_token&&n.expires_in}const De=["global","local","others"];var ci=function(n,e){var t={};for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&e.indexOf(s)<0&&(t[s]=n[s]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(n);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(n,s[r])&&(t[s[r]]=n[s[r]]);return t};class hi{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=Jt(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=De[0]){if(De.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${De.join(", ")}`);try{return await k(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(m(s))return{data:null,error:s};throw s}}async inviteUserByEmail(e,t={}){try{return await k(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:W})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}}async generateLink(e){try{const{options:t}=e,s=ci(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=s==null?void 0:s.newEmail,delete r.newEmail),await k(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:ai,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(m(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await k(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:W})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,s,r,i,a,o,l;try{const c={nextPage:null,lastPage:0,total:0},h=await k(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(s=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&s!==void 0?s:"",per_page:(i=(r=e==null?void 0:e.perPage)===null||r===void 0?void 0:r.toString())!==null&&i!==void 0?i:""},xform:oi});if(h.error)throw h.error;const u=await h.json(),d=(a=h.headers.get("x-total-count"))!==null&&a!==void 0?a:0,f=(l=(o=h.headers.get("link"))===null||o===void 0?void 0:o.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(v=>{const b=parseInt(v.split(";")[0].split("=")[1].substring(0,1)),p=JSON.parse(v.split(";")[1].split("=")[1]);c[`${p}Page`]=b}),c.total=parseInt(d)),{data:Object.assign(Object.assign({},u),c),error:null}}catch(c){if(m(c))return{data:{users:[]},error:c};throw c}}async getUserById(e){re(e);try{return await k(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:W})}catch(t){if(m(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){re(e);try{return await k(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:W})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}}async deleteUser(e,t=!1){re(e);try{return await k(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:W})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}}async _listFactors(e){re(e.userId);try{const{data:t,error:s}=await k(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:r=>({data:{factors:r},error:null})});return{data:t,error:s}}catch(t){if(m(t))return{data:null,error:t};throw t}}async _deleteFactor(e){re(e.userId),re(e.id);try{return{data:await k(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}}}const ui={getItem:n=>pe()?globalThis.localStorage.getItem(n):null,setItem:(n,e)=>{pe()&&globalThis.localStorage.setItem(n,e)},removeItem:n=>{pe()&&globalThis.localStorage.removeItem(n)}};function At(n={}){return{getItem:e=>n[e]||null,setItem:(e,t)=>{n[e]=t},removeItem:e=>{delete n[e]}}}function di(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const ie={debug:!!(globalThis&&pe()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Gt extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class fi extends Gt{}async function gi(n,e,t){ie.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",n,e);const s=new globalThis.AbortController;return e>0&&setTimeout(()=>{s.abort(),ie.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",n)},e),await Promise.resolve().then(()=>globalThis.navigator.locks.request(n,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},async r=>{if(r){ie.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",n,r.name);try{return await t()}finally{ie.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",n,r.name)}}else{if(e===0)throw ie.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",n),new fi(`Acquiring an exclusive Navigator LockManager lock "${n}" immediately failed`);if(ie.debug)try{const i=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(i,null,"  "))}catch(i){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",i)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await t()}}))}di();const pi={url:jr,storageKey:Ar,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Er,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Et(n,e,t){return await t()}class ve{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ve.nextInstanceID,ve.nextInstanceID+=1,this.instanceID>0&&L()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},pi),e);if(this.logDebugMessages=!!r.debug,typeof r.debug=="function"&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new hi({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Jt(r.fetch),this.lock=r.lock||Et,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:L()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=gi:this.lock=Et,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:pe()?this.storage=ui:(this.memoryStorage={},this.storage=At(this.memoryStorage)):(this.memoryStorage={},this.storage=At(this.memoryStorage)),L()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",i)}(s=this.broadcastChannel)===null||s===void 0||s.addEventListener("message",async i=>{this._debug("received broadcast notification from other tab or client",i),await this._notifyAllSubscribers(i.data.event,i.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Nt}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var e;try{const t=Fr(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),L()&&this.detectSessionInUrl&&s!=="none"){const{data:r,error:i}=await this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),$r(i)){const l=(e=i.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:i}}return await this._removeSession(),{error:i}}const{session:a,redirectType:o}=r;return this._debug("#_initialize()","detected session in URL",a,"redirect type",o),await this._saveSession(a),setTimeout(async()=>{o==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return m(t)?{error:t}:{error:new zt("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{const i=await k(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(s=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:(r=e==null?void 0:e.options)===null||r===void 0?void 0:r.captchaToken}},xform:M}),{data:a,error:o}=i;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,c=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(m(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(e){var t,s,r;try{let i;if("email"in e){const{email:h,password:u,options:d}=e;let f=null,v=null;this.flowType==="pkce"&&([f,v]=await se(this.storage,this.storageKey)),i=await k(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:d==null?void 0:d.emailRedirectTo,body:{email:h,password:u,data:(t=d==null?void 0:d.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken},code_challenge:f,code_challenge_method:v},xform:M})}else if("phone"in e){const{phone:h,password:u,options:d}=e;i=await k(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:h,password:u,data:(s=d==null?void 0:d.data)!==null&&s!==void 0?s:{},channel:(r=d==null?void 0:d.channel)!==null&&r!==void 0?r:"sms",gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}},xform:M})}else throw new ke("You must provide either an email or phone number and a password");const{data:a,error:o}=i;if(o||!a)return{data:{user:null,session:null},error:o};const l=a.session,c=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(i){if(m(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(e){try{let t;if("email"in e){const{email:i,password:a,options:o}=e;t=await k(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:i,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:jt})}else if("phone"in e){const{phone:i,password:a,options:o}=e;t=await k(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:i,password:a,gotrue_meta_security:{captcha_token:o==null?void 0:o.captchaToken}},xform:jt})}else throw new ke("You must provide either an email or phone number and a password");const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:!s||!s.session||!s.user?{data:{user:null,session:null},error:new be}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,s,r,i;return await this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(s=e.options)===null||s===void 0?void 0:s.scopes,queryParams:(r=e.options)===null||r===void 0?void 0:r.queryParams,skipBrowserRedirect:(i=e.options)===null||i===void 0?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if(t==="solana")return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,r,i,a,o,l,c,h,u,d,f;let v,b;if("message"in e)v=e.message,b=e.signature;else{const{chain:p,wallet:j,statement:x,options:g}=e;let S;if(L())if(typeof j=="object")S=j;else{const T=window;if("solana"in T&&typeof T.solana=="object"&&("signIn"in T.solana&&typeof T.solana.signIn=="function"||"signMessage"in T.solana&&typeof T.solana.signMessage=="function"))S=T.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof j!="object"||!(g!=null&&g.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");S=j}const E=new URL((t=g==null?void 0:g.url)!==null&&t!==void 0?t:window.location.href);if("signIn"in S&&S.signIn){const T=await S.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},g==null?void 0:g.signInWithSolana),{version:"1",domain:E.host,uri:E.href}),x?{statement:x}:null));let C;if(Array.isArray(T)&&T[0]&&typeof T[0]=="object")C=T[0];else if(T&&typeof T=="object"&&"signedMessage"in T&&"signature"in T)C=T;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in C&&"signature"in C&&(typeof C.signedMessage=="string"||C.signedMessage instanceof Uint8Array)&&C.signature instanceof Uint8Array)v=typeof C.signedMessage=="string"?C.signedMessage:new TextDecoder().decode(C.signedMessage),b=C.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in S)||typeof S.signMessage!="function"||!("publicKey"in S)||typeof S!="object"||!S.publicKey||!("toBase58"in S.publicKey)||typeof S.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");v=[`${E.host} wants you to sign in with your Solana account:`,S.publicKey.toBase58(),...x?["",x,""]:[""],"Version: 1",`URI: ${E.href}`,`Issued At: ${(r=(s=g==null?void 0:g.signInWithSolana)===null||s===void 0?void 0:s.issuedAt)!==null&&r!==void 0?r:new Date().toISOString()}`,...!((i=g==null?void 0:g.signInWithSolana)===null||i===void 0)&&i.notBefore?[`Not Before: ${g.signInWithSolana.notBefore}`]:[],...!((a=g==null?void 0:g.signInWithSolana)===null||a===void 0)&&a.expirationTime?[`Expiration Time: ${g.signInWithSolana.expirationTime}`]:[],...!((o=g==null?void 0:g.signInWithSolana)===null||o===void 0)&&o.chainId?[`Chain ID: ${g.signInWithSolana.chainId}`]:[],...!((l=g==null?void 0:g.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${g.signInWithSolana.nonce}`]:[],...!((c=g==null?void 0:g.signInWithSolana)===null||c===void 0)&&c.requestId?[`Request ID: ${g.signInWithSolana.requestId}`]:[],...!((u=(h=g==null?void 0:g.signInWithSolana)===null||h===void 0?void 0:h.resources)===null||u===void 0)&&u.length?["Resources",...g.signInWithSolana.resources.map(C=>`- ${C}`)]:[]].join(`
`);const T=await S.signMessage(new TextEncoder().encode(v),"utf8");if(!T||!(T instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");b=T}}try{const{data:p,error:j}=await k(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:v,signature:qr(b)},!((d=e.options)===null||d===void 0)&&d.captchaToken?{gotrue_meta_security:{captcha_token:(f=e.options)===null||f===void 0?void 0:f.captchaToken}}:null),xform:M});if(j)throw j;return!p||!p.session||!p.user?{data:{user:null,session:null},error:new be}:(p.session&&(await this._saveSession(p.session),await this._notifyAllSubscribers("SIGNED_IN",p.session)),{data:Object.assign({},p),error:j})}catch(p){if(m(p))return{data:{user:null,session:null},error:p};throw p}}async _exchangeCodeForSession(e){const t=await je(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(t??"").split("/");try{const{data:i,error:a}=await k(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:M});if(await Ae(this.storage,`${this.storageKey}-code-verifier`),a)throw a;return!i||!i.session||!i.user?{data:{user:null,session:null,redirectType:null},error:new be}:(i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",i.session)),{data:Object.assign(Object.assign({},i),{redirectType:r??null}),error:a})}catch(i){if(m(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(e){try{const{options:t,provider:s,token:r,access_token:i,nonce:a}=e,o=await k(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:a,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:M}),{data:l,error:c}=o;return c?{data:{user:null,session:null},error:c}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new be}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:c})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,s,r,i,a;try{if("email"in e){const{email:o,options:l}=e;let c=null,h=null;this.flowType==="pkce"&&([c,h]=await se(this.storage,this.storageKey));const{error:u}=await k(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:o,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:c,code_challenge_method:h},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:u}}if("phone"in e){const{phone:o,options:l}=e,{data:c,error:h}=await k(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:o,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},create_user:(i=l==null?void 0:l.shouldCreateUser)!==null&&i!==void 0?i:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(a=l==null?void 0:l.channel)!==null&&a!==void 0?a:"sms"}});return{data:{user:null,session:null,messageId:c==null?void 0:c.message_id},error:h}}throw new ke("You must provide either an email or phone number.")}catch(o){if(m(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(e){var t,s;try{let r,i;"options"in e&&(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo,i=(s=e.options)===null||s===void 0?void 0:s.captchaToken);const{data:a,error:o}=await k(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:M});if(o)throw o;if(!a)throw new Error("An error occurred on token verification.");const l=a.session,c=a.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:c,session:l},error:null}}catch(r){if(m(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,s,r;try{let i=null,a=null;return this.flowType==="pkce"&&([i,a]=await se(this.storage,this.storageKey)),await k(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&s!==void 0?s:void 0}),!((r=e==null?void 0:e.options)===null||r===void 0)&&r.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:a}),headers:this.headers,xform:ni})}catch(i){if(m(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new F;const{error:r}=await k(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(m(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:i}=e,{error:a}=await k(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},redirectTo:i==null?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}else if("phone"in e){const{phone:s,type:r,options:i}=e,{data:a,error:o}=await k(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:a==null?void 0:a.message_id},error:o}}throw new ke("You must provide either an email or phone number and a type")}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async t=>t))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await s,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch{}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const s=t();for(this.pendingInLock.push((async()=>{try{await s}catch{}})()),await s;this.pendingInLock.length;){const r=[...this.pendingInLock];await Promise.all(r),this.pendingInLock.splice(0,r.length)}return await s}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=await je(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const s=e.expires_at?e.expires_at*1e3-Date.now()<Re:!1;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let a=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,c,h)=>(!a&&c==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),a=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,c,h))})}return{data:{session:e},error:null}}const{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{return e?await k(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:W}):await this._useSession(async t=>{var s,r,i;const{data:a,error:o}=t;if(o)throw o;return!(!((s=a.session)===null||s===void 0)&&s.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new F}:await k(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(i=(r=a.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0,xform:W})})}catch(t){if(m(t))return Ir(t)&&(await this._removeSession(),await Ae(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{const{data:r,error:i}=s;if(i)throw i;if(!r.session)throw new F;const a=r.session;let o=null,l=null;this.flowType==="pkce"&&e.email!=null&&([o,l]=await se(this.storage,this.storageKey));const{data:c,error:h}=await k(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:l}),jwt:a.access_token,xform:W});if(h)throw h;return a.user=c.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(s){if(m(s))return{data:{user:null},error:s};throw s}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new F;const t=Date.now()/1e3;let s=t,r=!0,i=null;const{payload:a}=Le(e.access_token);if(a.exp&&(s=a.exp,r=s<=t),r){const{session:o,error:l}=await this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!o)return{data:{user:null,session:null},error:null};i=o}else{const{data:o,error:l}=await this._getUser(e.access_token);if(l)throw l;i={access_token:e.access_token,refresh_token:e.refresh_token,user:o.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(m(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){const{data:a,error:o}=t;if(o)throw o;e=(s=a.session)!==null&&s!==void 0?s:void 0}if(!(e!=null&&e.refresh_token))throw new F;const{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(m(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!L())throw new Se("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Se(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new wt("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Se("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new wt("No code detected.");const{data:x,error:g}=await this._exchangeCodeForSession(e.code);if(g)throw g;const S=new URL(window.location.href);return S.searchParams.delete("code"),window.history.replaceState(window.history.state,"",S.toString()),{data:{session:x.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:a,expires_in:o,expires_at:l,token_type:c}=e;if(!i||!o||!a||!c)throw new Se("No session defined in URL");const h=Math.round(Date.now()/1e3),u=parseInt(o);let d=h+u;l&&(d=parseInt(l));const f=d-h;f*1e3<=ne&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${u}s`);const v=d-u;h-v>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",v,d,h):h-v<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",v,d,h);const{data:b,error:p}=await this._getUser(i);if(p)throw p;const j={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:u,expires_at:d,refresh_token:a,token_type:c,user:b.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:j,redirectType:e.type},error:null}}catch(s){if(m(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await je(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;const{data:r,error:i}=t;if(i)return{error:i};const a=(s=r.session)===null||s===void 0?void 0:s.access_token;if(a){const{error:o}=await this.admin.signOut(a,e);if(o&&!(Pr(o)&&(o.status===404||o.status===401||o.status===403)))return{error:o}}return e!=="others"&&(await this._removeSession(),await Ae(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t=Nr(),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})))(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{const{data:{session:i},error:a}=t;if(a)throw a;await((s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(i){await((r=this.stateChangeEmitters.get(e))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",i),console.error(i)}})}async resetPasswordForEmail(e,t={}){let s=null,r=null;this.flowType==="pkce"&&([s,r]=await se(this.storage,this.storageKey,!0));try{return await k(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(i){if(m(i))return{data:null,error:i};throw i}}async getUserIdentities(){var e;try{const{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(m(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:s,error:r}=await this._useSession(async i=>{var a,o,l,c,h;const{data:u,error:d}=i;if(d)throw d;const f=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(a=e.options)===null||a===void 0?void 0:a.redirectTo,scopes:(o=e.options)===null||o===void 0?void 0:o.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await k(this.fetch,"GET",f,{headers:this.headers,jwt:(h=(c=u.session)===null||c===void 0?void 0:c.access_token)!==null&&h!==void 0?h:void 0})});if(r)throw r;return L()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(s==null?void 0:s.url),{data:{provider:e.provider,url:s==null?void 0:s.url},error:null}}catch(s){if(m(s))return{data:{provider:e.provider,url:null},error:s};throw s}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;const{data:i,error:a}=t;if(a)throw a;return await k(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(r=(s=i.session)===null||s===void 0?void 0:s.access_token)!==null&&r!==void 0?r:void 0})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const s=Date.now();return await Jr(async r=>(r>0&&await Wr(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await k(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:M})),(r,i)=>{const a=200*Math.pow(2,r);return i&&Ue(i)&&Date.now()+a-s<ne})}catch(s){if(this._debug(t,"error",s),m(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(t,"end")}}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),L()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=await je(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),s!==null&&await this._removeSession();return}const r=((e=s.expires_at)!==null&&e!==void 0?e:1/0)*1e3-Date.now()<Re;if(this._debug(t,`session has${r?"":" not"} expired with margin of ${Re}s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:i}=await this._callRefreshToken(s.refresh_token);i&&(console.error(i),Ue(i)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",i),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){this._debug(t,"error",s),console.error(s);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new F;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new Oe;const{data:i,error:a}=await this._refreshAccessToken(e);if(a)throw a;if(!i.session)throw new F;await this._saveSession(i.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",i.session);const o={session:i.session,error:null};return this.refreshingDeferred.resolve(o),o}catch(i){if(this._debug(r,"error",i),m(i)){const a={session:null,error:i};return Ue(i)||await this._removeSession(),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(a),a}throw(s=this.refreshingDeferred)===null||s===void 0||s.reject(i),i}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const i=[],a=Array.from(this.stateChangeEmitters.values()).map(async o=>{try{await o.callback(e,t)}catch(l){i.push(l)}});if(await Promise.all(a),i.length>0){for(let o=0;o<i.length;o+=1)console.error(i[o]);throw i[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Ht(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Ae(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&L()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),ne);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const e=Date.now();try{return await this._useSession(async t=>{const{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const r=Math.floor((s.expires_at*1e3-e)/ne);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts ${ne}ms, refresh threshold is ${We} ticks`),r<=We&&await this._callRefreshToken(s.refresh_token)})}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof Gt)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!L()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){const r=[`provider=${encodeURIComponent(t)}`];if(s!=null&&s.redirectTo&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),s!=null&&s.scopes&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),this.flowType==="pkce"){const[i,a]=await se(this.storage,this.storageKey),o=new URLSearchParams({code_challenge:`${encodeURIComponent(i)}`,code_challenge_method:`${encodeURIComponent(a)}`});r.push(o.toString())}if(s!=null&&s.queryParams){const i=new URLSearchParams(s.queryParams);r.push(i.toString())}return s!=null&&s.skipBrowserRedirect&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;const{data:r,error:i}=t;return i?{data:null,error:i}:await k(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;const{data:i,error:a}=t;if(a)return{data:null,error:a};const o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:c}=await k(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:(s=i==null?void 0:i.session)===null||s===void 0?void 0:s.access_token});return c?{data:null,error:c}:(e.factorType==="totp"&&(!((r=l==null?void 0:l.totp)===null||r===void 0)&&r.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(t){if(m(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:i}=t;if(i)return{data:null,error:i};const{data:a,error:o}=await k(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:o})})}catch(t){if(m(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:i}=t;return i?{data:null,error:i}:await k(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})})}catch(t){if(m(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const s=(e==null?void 0:e.factors)||[],r=s.filter(a=>a.factor_type==="totp"&&a.status==="verified"),i=s.filter(a=>a.factor_type==="phone"&&a.status==="verified");return{data:{all:s,totp:r,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;const{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:a}=Le(r.access_token);let o=null;a.aal&&(o=a.aal);let l=o;((s=(t=r.user.factors)===null||t===void 0?void 0:t.filter(u=>u.status==="verified"))!==null&&s!==void 0?s:[]).length>0&&(l="aal2");const h=a.amr||[];return{data:{currentLevel:o,nextLevel:l,currentAuthenticationMethods:h},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(a=>a.kid===e);if(s||(s=this.jwks.keys.find(a=>a.kid===e),s&&this.jwks_cached_at+xr>Date.now()))return s;const{data:r,error:i}=await k(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!r.keys||r.keys.length===0)throw new ge("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),s=r.keys.find(a=>a.kid===e),!s)throw new ge("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){const{data:f,error:v}=await this.getSession();if(v||!f.session)return{data:null,error:v};s=f.session.access_token}const{header:r,payload:i,signature:a,raw:{header:o,payload:l}}=Le(s);if(Xr(i.exp),!r.kid||r.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:f}=await this.getUser(s);if(f)throw f;return{data:{claims:i,header:r,signature:a},error:null}}const c=Zr(r.alg),h=await this.fetchJwk(r.kid,t),u=await crypto.subtle.importKey("jwk",h,c,!0,["verify"]);if(!await crypto.subtle.verify(c,u,a,Br(`${o}.${l}`)))throw new ge("Invalid JWT signature");return{data:{claims:i,header:r,signature:a},error:null}}catch(s){if(m(s))return{data:null,error:s};throw s}}}ve.nextInstanceID=0;const vi=ve;class _i extends vi{constructor(e){super(e)}}var wi=function(n,e,t,s){function r(i){return i instanceof t?i:new t(function(a){a(i)})}return new(t||(t=Promise))(function(i,a){function o(h){try{c(s.next(h))}catch(u){a(u)}}function l(h){try{c(s.throw(h))}catch(u){a(u)}}function c(h){h.done?i(h.value):r(h.value).then(o,l)}c((s=s.apply(n,e||[])).next())})};class yi{constructor(e,t,s){var r,i,a;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const o=kr(e),l=new URL(o);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,h={db:gr,realtime:vr,auth:Object.assign(Object.assign({},pr),{storageKey:c}),global:fr},u=Sr(s??{},h);this.storageKey=(r=u.auth.storageKey)!==null&&r!==void 0?r:"",this.headers=(i=u.global.headers)!==null&&i!==void 0?i:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(d,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((a=u.auth)!==null&&a!==void 0?a:{},this.headers,u.global.fetch),this.fetch=mr(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new Ls(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new js(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new hr(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return wi(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return(t=(e=s.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:a,lock:o,debug:l},c,h){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new _i({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),c),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:a,lock:o,debug:l,fetch:h,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Ys(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,s)=>{this._handleTokenChanged(t,"CLIENT",s==null?void 0:s.access_token)})}_handleTokenChanged(e,t,s){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==s?this.changedAccessToken=s:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const mi=(n,e,t)=>new yi(n,e,t),bi="https://ytrftwscazjboxbwnrxp.supabase.co",ki="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cmZ0d3NjYXpqYm94YnducnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NTU5NzQsImV4cCI6MjA1MDUzMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8",Tt=mi(bi,ki);async function Si(n,e="uploads"){try{if(!n.type.startsWith("image/"))throw new Error("只能上传图片文件");const t=5*1024*1024;if(n.size>t)throw new Error("图片大小不能超过5MB");const s=n.name.split(".").pop(),r=`${Date.now()}-${Math.random().toString(36).substring(2)}.${s}`,i=`${e}/${r}`,{data:a,error:o}=await Tt.storage.from("images").upload(i,n,{cacheControl:"3600",upsert:!1});if(o)throw console.error("Supabase上传错误:",o),new Error(`上传失败: ${o.message}`);const{data:l}=Tt.storage.from("images").getPublicUrl(i);if(!(l!=null&&l.publicUrl))throw new Error("获取图片URL失败");return l.publicUrl}catch(t){throw console.error("图片上传失败:",t),t}}function ji(n,e=1920,t=1080,s=.8){return new Promise((r,i)=>{const a=document.createElement("canvas"),o=a.getContext("2d"),l=new Image;l.onload=()=>{let{width:c,height:h}=l;c>e&&(h=h*e/c,c=e),h>t&&(c=c*t/h,h=t),a.width=c,a.height=h,o==null||o.drawImage(l,0,0,c,h),a.toBlob(u=>{if(u){const d=new File([u],n.name,{type:n.type,lastModified:Date.now()});r(d)}else i(new Error("图片压缩失败"))},n.type,s)},l.onerror=()=>i(new Error("图片加载失败")),l.src=URL.createObjectURL(n)})}async function Ai(n,e="uploads",t={}){const{maxWidth:s=1920,maxHeight:r=1080,quality:i=.8}=t;try{const a=await ji(n,s,r,i);return await Si(a,e)}catch(a){throw console.error("压缩上传失败:",a),a}}const{Option:xt}=Be,{TextArea:Ei}=Ee,Ti=[{value:1,label:"轮播"},{value:2,label:"弹窗广告"},{value:3,label:"浮点弹窗"},{value:4,label:"嵌入广告"},{value:5,label:"Banner"},{value:6,label:"插屏广告"},{value:7,label:"首页4宫格"}],xi=[{value:1,label:"内部路由"},{value:2,label:"iframe页面"}],qi=()=>{const[n,e]=B.useState([]),[t,s]=B.useState(!1),[r,i]=B.useState(!1),[a,o]=B.useState(null),[l]=q.useForm(),[c,h]=B.useState({page:1,pageSize:10}),[u,d]=B.useState(0),[f,v]=B.useState(!1),[b,p]=B.useState([]),[j,x]=B.useState(null),g=async y=>{s(!0);try{const w=await ss(y||c);w.code===200?(e(w.result.list||[]),d(w.result.total||0)):I.error(w.message||"获取广告配置失败")}catch(w){I.error("获取广告配置失败"),console.error("获取广告配置失败:",w)}finally{s(!1)}},S=async y=>{try{const w=await as(y);w.code===200?(I.success("创建成功"),i(!1),l.resetFields(),g()):I.error(w.message||"创建失败")}catch(w){I.error("创建失败"),console.error("创建广告配置失败:",w)}},E=async(y,w)=>{try{const U=await ns(y,w);U.code===200?(I.success("更新成功"),i(!1),g()):I.error(U.message||"更新失败")}catch(U){I.error("更新失败"),console.error("更新广告配置失败:",U)}},T=async y=>{try{const w=await is(y);w.code===200?(I.success("删除成功"),g()):I.error(w.message||"删除失败")}catch(w){I.error("删除失败"),console.error("删除广告配置失败:",w)}},C=async y=>{try{const w=await rs(y);w.code===200?(I.success(w.result.message),g()):I.error(w.message||"状态切换失败")}catch(w){I.error("状态切换失败"),console.error("状态切换失败:",w)}},Kt=async y=>{v(!0);try{if(!y.type.startsWith("image/"))throw new Error("只能上传图片文件");if(y.size>5*1024*1024)throw new Error("图片大小不能超过5MB");const w=await Ai(y,"ads",{maxWidth:1920,maxHeight:1080,quality:.8}),ye=[...l.getFieldValue("images")||[],w];if(l.getFieldValue("adType")===7&&ye.length>4)throw new Error("首页4宫格最多只能上传4张图片");return p(ye),l.setFieldsValue({images:ye}),I.success("图片上传成功"),w}catch(w){const U=w instanceof Error?w.message:"图片上传失败";throw I.error(U),console.error("图片上传失败:",w),w}finally{v(!1)}},Vt=y=>{const U=(l.getFieldValue("images")||[]).filter((ye,Xe)=>Xe!==y);p(U),l.setFieldsValue({images:U})},Pe=y=>{switch(y){case 1:return{min:2,max:10,text:"轮播广告至少需要2张图片"};case 7:return{min:4,max:4,text:"首页4宫格必须上传4张图片"};default:return{min:1,max:5,text:"至少需要1张图片"}}};B.useEffect(()=>{g()},[]);const Qt=[{title:"广告标识",dataIndex:"adIdentifier",key:"adIdentifier",width:150},{title:"广告类型",dataIndex:"adTypeName",key:"adTypeName",width:100,render:(y,w)=>_.jsx(tt,{color:"blue",children:y})},{title:"标题",dataIndex:"title",key:"title",width:200},{title:"图片",dataIndex:"images",key:"images",width:120,render:(y,w)=>_.jsx("div",{style:{display:"flex",gap:4,flexWrap:"wrap"},children:y&&y.length>0?_.jsxs(_.Fragment,{children:[_.jsx(Ze,{width:40,height:30,src:y[0],style:{objectFit:"cover",borderRadius:4},fallback:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"}),y.length>1&&_.jsxs("span",{style:{fontSize:"12px",color:"#666",alignSelf:"center",background:"#f0f0f0",padding:"2px 6px",borderRadius:"4px"},children:["+",y.length-1]})]}):_.jsx("span",{style:{color:"#ccc",fontSize:"12px"},children:"无图片"})})},{title:"跳转类型",dataIndex:"jumpTypeName",key:"jumpTypeName",width:100,render:y=>_.jsx(tt,{color:"green",children:y})},{title:"跳转目标",dataIndex:"jumpTarget",key:"jumpTarget",width:200,ellipsis:!0},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80,sorter:(y,w)=>y.sortOrder-w.sortOrder},{title:"状态",dataIndex:"status",key:"status",width:100,render:(y,w)=>_.jsx(vs,{checked:y===1,onChange:()=>C(w.id),checkedChildren:"启用",unCheckedChildren:"禁用"})},{title:"操作",key:"action",width:150,render:(y,w)=>_.jsxs(Ie,{children:[_.jsx(st,{title:"编辑",children:_.jsx(le,{type:"primary",size:"small",icon:_.jsx(_s,{}),onClick:()=>{o(w),p(w.images||[]),x(w.adType),l.setFieldsValue({...w,images:w.images||[]}),i(!0)}})}),_.jsx(ws,{title:"确定要删除这个广告配置吗？",onConfirm:()=>T(w.id),okText:"确定",cancelText:"取消",children:_.jsx(st,{title:"删除",children:_.jsx(le,{danger:!0,size:"small",icon:_.jsx(et,{})})})})]})}],Yt=async y=>{a?await E(a.id,y):await S(y)},Xt=()=>{i(!1),o(null),p([]),x(null),l.resetFields()};return _.jsxs(ts,{children:[_.jsx(os,{title:_.jsxs(Ie,{children:[_.jsx(us,{}),"广告配置管理"]}),extra:_.jsxs(Ie,{children:[_.jsx(le,{type:"primary",icon:_.jsx(cs,{}),onClick:()=>{o(null),p([]),x(null),l.resetFields(),i(!0)},children:"新增广告"}),_.jsx(le,{icon:_.jsx(hs,{}),onClick:()=>g(),children:"刷新"})]}),children:_.jsx(ls,{columns:Qt,dataSource:n,rowKey:"id",loading:t,pagination:{current:c.page,pageSize:c.pageSize,total:u,showSizeChanger:!0,showQuickJumper:!0,showTotal:(y,w)=>`第 ${w[0]}-${w[1]} 条/共 ${y} 条`,onChange:(y,w)=>{const U={...c,page:y,pageSize:w};h(U),g(U)}},scroll:{x:1200}})}),_.jsx(ds,{title:a?"编辑广告配置":"新增广告配置",open:r,onCancel:Xt,onOk:()=>l.submit(),width:800,confirmLoading:f,children:_.jsxs(q,{form:l,layout:"vertical",onFinish:Yt,children:[_.jsx(q.Item,{label:"广告标识",name:"adIdentifier",rules:[{required:!0,message:"请输入广告标识"},{pattern:/^[a-zA-Z0-9_]+$/,message:"广告标识只能包含字母、数字和下划线"}],children:_.jsx(Ee,{placeholder:"例如: banner_home_1"})}),_.jsx(q.Item,{label:"广告类型",name:"adType",rules:[{required:!0,message:"请选择广告类型"}],children:_.jsx(Be,{placeholder:"请选择广告类型",onChange:y=>{x(y),p([]),l.setFieldsValue({images:[]})},children:Ti.map(y=>_.jsx(xt,{value:y.value,children:y.label},y.value))})}),_.jsx(q.Item,{label:"广告标题",name:"title",rules:[{required:!0,message:"请输入广告标题"}],children:_.jsx(Ee,{placeholder:"请输入广告标题"})}),_.jsx(q.Item,{label:"广告图片",name:"images",rules:[{required:!0,message:"请上传广告图片"}],children:_.jsxs("div",{children:[j&&_.jsx("div",{style:{marginBottom:8,color:"#666",fontSize:"12px"},children:Pe(j).text}),_.jsxs("div",{style:{display:"flex",flexWrap:"wrap",gap:8},children:[b.map((y,w)=>_.jsxs("div",{style:{position:"relative"},children:[_.jsx(Ze,{width:100,height:100,src:y,style:{objectFit:"cover",borderRadius:4}}),_.jsx(le,{type:"text",danger:!0,size:"small",icon:_.jsx(et,{}),style:{position:"absolute",top:-8,right:-8,background:"#fff",border:"1px solid #d9d9d9",borderRadius:"50%",width:24,height:24,padding:0,display:"flex",alignItems:"center",justifyContent:"center"},onClick:()=>Vt(w)})]},w)),(!j||b.length<Pe(j).max)&&_.jsx(fs,{name:"image",listType:"picture-card",className:"avatar-uploader",showUploadList:!1,beforeUpload:y=>(Kt(y),!1),accept:"image/*",disabled:f,children:_.jsxs("div",{style:{width:100,height:100,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},children:[_.jsx(gs,{}),_.jsx("div",{style:{marginTop:8,fontSize:"12px"},children:f?"上传中...":"上传图片"})]})})]}),j&&_.jsxs("div",{style:{marginTop:8,fontSize:"12px",color:"#999"},children:["已上传 ",b.length," / ",Pe(j).max," 张图片"]})]})}),_.jsx(q.Item,{label:"跳转类型",name:"jumpType",rules:[{required:!0,message:"请选择跳转类型"}],children:_.jsx(Be,{placeholder:"请选择跳转类型",children:xi.map(y=>_.jsx(xt,{value:y.value,children:y.label},y.value))})}),_.jsx(q.Item,{label:"跳转目标",name:"jumpTarget",rules:[{required:!0,message:"请输入跳转目标"}],children:_.jsx(Ee,{placeholder:"内部路由: /games/hot 或 外部链接: https://example.com"})}),_.jsx(q.Item,{label:"排序",name:"sortOrder",initialValue:0,children:_.jsx(ps,{min:0,max:9999,placeholder:"数字越小越靠前",style:{width:"100%"}})}),_.jsx(q.Item,{label:"备注",name:"remark",children:_.jsx(Ei,{rows:3,placeholder:"请输入备注信息"})})]})})]})};export{qi as default};
