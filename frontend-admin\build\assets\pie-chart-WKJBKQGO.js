import{u as p,j as a}from"./index-CHjq8S-S.js";import{a as u}from"./home-BOsRgn0t.js";import{E as f}from"./index-Dq9acWLp.js";import{a as s}from"./react-BUTTOX-3.js";import{aq as d,aY as g}from"./antd-CXPM1OiB.js";function j(){const{t:e}=p(),[n,i]=s.useState([]),[t,m]=s.useState(e("home.allChannels")),c={electronics:e("home.electronics"),home_goods:e("home.homeGoods"),apparel_accessories:e("home.apparelAccessories"),food_beverages:e("home.foodBeverages"),beauty_skincare:e("home.beautySkincare")},h={title:{text:"",subtext:"",right:"10%"},tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:e("home.salesCategoryProportion"),type:"pie",radius:"55%",center:["50%","60%"],data:n}]};return s.useEffect(()=>{t&&u({by:t}).then(({result:o})=>{i(o.map(r=>{const l=r.code;return{...r,name:c[l]}}))})},[t]),a.jsx(d,{title:e("home.salesCategoryProportion"),extra:a.jsx(g,{options:[e("home.allChannels"),e("home.online"),e("home.site")],value:t,onChange:o=>m(o)}),children:a.jsx(f,{opts:{height:"auto",width:"auto"},option:h})})}export{j as default};
