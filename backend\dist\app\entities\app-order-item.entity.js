"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppOrderItem = void 0;
const typeorm_1 = require("typeorm");
const app_order_entity_1 = require("./app-order.entity");
const app_product_entity_1 = require("./app-product.entity");
let AppOrderItem = class AppOrderItem {
    id;
    orderId;
    productId;
    productName;
    productImage;
    price;
    quantity;
    totalPrice;
    specifications;
    order;
    product;
    createTime;
};
exports.AppOrderItem = AppOrderItem;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_id' }),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "orderId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_id', nullable: true }),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "productId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_name', length: 200 }),
    __metadata("design:type", String)
], AppOrderItem.prototype, "productName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'product_image', nullable: true, length: 500 }),
    __metadata("design:type", String)
], AppOrderItem.prototype, "productImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1 }),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "quantity", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_price', type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], AppOrderItem.prototype, "totalPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'jsonb', comment: '商品规格快照' }),
    __metadata("design:type", Object)
], AppOrderItem.prototype, "specifications", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_order_entity_1.AppOrder, (order) => order.items),
    (0, typeorm_1.JoinColumn)({ name: 'order_id' }),
    __metadata("design:type", app_order_entity_1.AppOrder)
], AppOrderItem.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_product_entity_1.AppProduct, (product) => product.orderItems),
    (0, typeorm_1.JoinColumn)({ name: 'product_id' }),
    __metadata("design:type", app_product_entity_1.AppProduct)
], AppOrderItem.prototype, "product", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppOrderItem.prototype, "createTime", void 0);
exports.AppOrderItem = AppOrderItem = __decorate([
    (0, typeorm_1.Entity)('app_order_items')
], AppOrderItem);
//# sourceMappingURL=app-order-item.entity.js.map