import { SystemWorkbookService } from './workbook.service';
import { CreateWorkbookDto } from './dto/create-workbook.dto';
import { UpdateWorkbookDto } from './dto/update-workbook.dto';
import { QueryWorkbookDto } from './dto/query-workbook.dto';
export declare class SystemWorkbookController {
    private readonly workbookService;
    constructor(workbookService: SystemWorkbookService);
    create(createWorkbookDto: CreateWorkbookDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysWorkbook;
    }>;
    findAll(queryWorkbookDto: QueryWorkbookDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").SysWorkbook[];
            total: number;
            current: number;
            pageSize: number;
        };
    }>;
    getTypes(): Promise<{
        code: number;
        message: string;
        result: any[];
    }>;
    findByType(type: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysWorkbook[];
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysWorkbook;
    }>;
    update(id: string, updateWorkbookDto: UpdateWorkbookDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysWorkbook;
    }>;
    remove(id: string): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
    updateOrder(items: {
        id: number;
        order: number;
    }[]): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
}
