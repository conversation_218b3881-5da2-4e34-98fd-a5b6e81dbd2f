import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, IsDateString, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateTransactionDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: '交易金额' })
  @IsNumber()
  amount: number;

  @ApiProperty({ description: '状态：1-收入，2-支出' })
  @IsNumber()
  @Min(1)
  @Max(2)
  status: number;

  @ApiProperty({ description: '交易类型' })
  @IsNumber()
  transactionType: number;

  @ApiPropertyOptional({ description: '订单号' })
  @IsOptional()
  @IsString()
  orderId?: string;

  @ApiPropertyOptional({ description: '交易描述' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '备注信息' })
  @IsOptional()
  @IsString()
  remark?: string;

  @ApiPropertyOptional({ description: '操作员ID' })
  @IsOptional()
  @IsNumber()
  operatorId?: number;
}

export class TransactionListQueryDto {
  @ApiProperty({ description: '用户ID' })
  @IsNumber()
  @Transform(({ value }) => parseInt(value))
  userId: number;

  @ApiPropertyOptional({ description: '状态：1-收入，2-支出' })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  status?: number;

  @ApiPropertyOptional({ description: '交易类型' })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : undefined)
  transactionType?: number;

  @ApiPropertyOptional({ description: '开始日期' })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({ description: '结束日期' })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiPropertyOptional({ description: '页码', default: 1 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : 1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', default: 20 })
  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => value ? parseInt(value) : 20)
  pageSize?: number;
}

export class TransactionStatisticsResponseDto {
  @ApiProperty({ description: '总收入' })
  totalIncome: number;

  @ApiProperty({ description: '总支出' })
  totalExpense: number;

  @ApiProperty({ description: '当前余额' })
  balance: number;

  @ApiProperty({ description: '交易笔数' })
  transactionCount: number;
}

export class UserAssetStatisticsResponseDto {
  @ApiProperty({ description: '现金统计', type: TransactionStatisticsResponseDto })
  cash: TransactionStatisticsResponseDto;

  @ApiProperty({ description: '金币统计', type: TransactionStatisticsResponseDto })
  gold: TransactionStatisticsResponseDto;

  @ApiProperty({ description: '充值统计', type: TransactionStatisticsResponseDto })
  recharge: TransactionStatisticsResponseDto;
}

export class TransactionItemDto {
  @ApiProperty({ description: '交易ID' })
  id: number;

  @ApiProperty({ description: '用户ID' })
  userId: number;

  @ApiProperty({ description: '用户UID' })
  uid: number;

  @ApiProperty({ description: '交易金额' })
  amount: number;

  @ApiProperty({ description: '交易前余额' })
  balanceBefore: number;

  @ApiProperty({ description: '交易后余额' })
  balanceAfter: number;

  @ApiProperty({ description: '状态：1-收入，2-支出' })
  status: number;

  @ApiProperty({ description: '交易类型' })
  transactionType: number;

  @ApiProperty({ description: '订单号' })
  orderId: string;

  @ApiProperty({ description: '交易描述' })
  description: string;

  @ApiProperty({ description: '备注信息' })
  remark: string;

  @ApiProperty({ description: '操作员ID' })
  operatorId: number;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;
}

export class TransactionListResponseDto {
  @ApiProperty({ description: '交易列表', type: [TransactionItemDto] })
  list: TransactionItemDto[];

  @ApiProperty({ description: '总数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  pageSize: number;
}
