"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemWorkbookController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const workbook_service_1 = require("./workbook.service");
const create_workbook_dto_1 = require("./dto/create-workbook.dto");
const update_workbook_dto_1 = require("./dto/update-workbook.dto");
const query_workbook_dto_1 = require("./dto/query-workbook.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let SystemWorkbookController = class SystemWorkbookController {
    workbookService;
    constructor(workbookService) {
        this.workbookService = workbookService;
    }
    async create(createWorkbookDto) {
        const result = await this.workbookService.create(createWorkbookDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(queryWorkbookDto) {
        const result = await this.workbookService.findAll(queryWorkbookDto);
        console.log('[WORKBOOK_CONTROLLER] 数据字典列表查询结果:', JSON.stringify(result, null, 2));
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getTypes() {
        const result = await this.workbookService.getTypes();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findByType(type) {
        const result = await this.workbookService.findByType(type);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.workbookService.findOne(+id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateWorkbookDto) {
        const result = await this.workbookService.update(+id, updateWorkbookDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.workbookService.remove(+id);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
    async updateOrder(items) {
        const result = await this.workbookService.updateOrder(items);
        return {
            code: 200,
            message: '排序更新成功',
            result,
        };
    }
};
exports.SystemWorkbookController = SystemWorkbookController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建数据字典' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_workbook_dto_1.CreateWorkbookDto]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取数据字典列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_workbook_dto_1.QueryWorkbookDto]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('types'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有字典类型' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "getTypes", null);
__decorate([
    (0, common_1.Get)('by-type/:type'),
    (0, swagger_1.ApiOperation)({ summary: '根据类型获取字典项' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取数据字典详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新数据字典' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_workbook_dto_1.UpdateWorkbookDto]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除数据字典' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('update-order'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新排序' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], SystemWorkbookController.prototype, "updateOrder", null);
exports.SystemWorkbookController = SystemWorkbookController = __decorate([
    (0, swagger_1.ApiTags)('系统数据字典管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('workbook'),
    __metadata("design:paramtypes", [workbook_service_1.SystemWorkbookService])
], SystemWorkbookController);
//# sourceMappingURL=workbook.controller.js.map