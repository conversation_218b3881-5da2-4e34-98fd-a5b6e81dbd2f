import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class VipConfig {
    id: number;
    vipLevel: number;
    levelName: string;
    requiredPoints: number;
    balanceRatio: number;
    cashRatio: number;
    goldRatio: number;
    dailyGoldReward: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    calculateUserPoints(balance: number, totalCashSpent: number, totalGoldSpent: number): number;
    isUserQualified(userPoints: number): boolean;
}
