"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfigPageResponseDto = exports.AppHomeConfigListResponseDto = exports.AppHomeConfigDetailResponseDto = exports.GameCategoryResponseDto = exports.CategoryGameResponseDto = exports.RecommendedGameResponseDto = exports.GameInfoResponseDto = exports.AdInfoResponseDto = exports.ImageItemResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ImageItemResponseDto {
    imageUrl;
    jumpType;
    jumpTarget;
    title;
    description;
}
exports.ImageItemResponseDto = ImageItemResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片URL', example: '/uploads/ads/banner1.jpg' }),
    __metadata("design:type", String)
], ImageItemResponseDto.prototype, "imageUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '跳转类型：1-内部路由，2-iframe页面', example: 1 }),
    __metadata("design:type", Number)
], ImageItemResponseDto.prototype, "jumpType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '跳转目标', example: '/games/hot' }),
    __metadata("design:type", String)
], ImageItemResponseDto.prototype, "jumpTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片标题', example: '热门游戏', required: false }),
    __metadata("design:type", String)
], ImageItemResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '图片描述', example: '查看最受欢迎的游戏', required: false }),
    __metadata("design:type", String)
], ImageItemResponseDto.prototype, "description", void 0);
class AdInfoResponseDto {
    id;
    adIdentifier;
    title;
    adType;
    imageItems;
}
exports.AdInfoResponseDto = AdInfoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告ID', example: 1 }),
    __metadata("design:type", Number)
], AdInfoResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识', example: 'banner_home_1' }),
    __metadata("design:type", String)
], AdInfoResponseDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标题', example: '首页轮播广告' }),
    __metadata("design:type", String)
], AdInfoResponseDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告类型', example: 1 }),
    __metadata("design:type", Number)
], AdInfoResponseDto.prototype, "adType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '图片跳转项数组',
        type: [ImageItemResponseDto],
        example: [
            {
                imageUrl: '/uploads/ads/banner1.jpg',
                jumpType: 1,
                jumpTarget: '/games/popular',
                title: '热门游戏',
                description: '查看最受欢迎的游戏'
            }
        ]
    }),
    __metadata("design:type", Array)
], AdInfoResponseDto.prototype, "imageItems", void 0);
class GameInfoResponseDto {
    id;
    name;
    iconUrl;
    posterUrl;
    categories;
    rtp;
    volatility;
    status;
}
exports.GameInfoResponseDto = GameInfoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏ID', example: 1 }),
    __metadata("design:type", Number)
], GameInfoResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏名称', example: '热门游戏1' }),
    __metadata("design:type", String)
], GameInfoResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏图标URL', example: '/uploads/games/icon1.jpg' }),
    __metadata("design:type", String)
], GameInfoResponseDto.prototype, "iconUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏海报URL', example: '/uploads/games/poster1.jpg' }),
    __metadata("design:type", String)
], GameInfoResponseDto.prototype, "posterUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏分类', example: ['slot', 'popular'] }),
    __metadata("design:type", Array)
], GameInfoResponseDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '返还率', example: 96.5 }),
    __metadata("design:type", Number)
], GameInfoResponseDto.prototype, "rtp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '波动性', example: 'medium' }),
    __metadata("design:type", String)
], GameInfoResponseDto.prototype, "volatility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏状态', example: 'active' }),
    __metadata("design:type", String)
], GameInfoResponseDto.prototype, "status", void 0);
class RecommendedGameResponseDto {
    id;
    sortOrder;
    game;
}
exports.RecommendedGameResponseDto = RecommendedGameResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联ID', example: 1 }),
    __metadata("design:type", Number)
], RecommendedGameResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    __metadata("design:type", Number)
], RecommendedGameResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏信息', type: GameInfoResponseDto }),
    __metadata("design:type", GameInfoResponseDto)
], RecommendedGameResponseDto.prototype, "game", void 0);
class CategoryGameResponseDto {
    id;
    sortOrder;
    game;
}
exports.CategoryGameResponseDto = CategoryGameResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '关联ID', example: 1 }),
    __metadata("design:type", Number)
], CategoryGameResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    __metadata("design:type", Number)
], CategoryGameResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏信息', type: GameInfoResponseDto }),
    __metadata("design:type", GameInfoResponseDto)
], CategoryGameResponseDto.prototype, "game", void 0);
class GameCategoryResponseDto {
    id;
    categoryTitle;
    sortOrder;
    status;
    games;
}
exports.GameCategoryResponseDto = GameCategoryResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类组ID', example: 1 }),
    __metadata("design:type", Number)
], GameCategoryResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '分类标题（多语言）',
        example: { "zh-CN": "热门游戏", "en-US": "Hot Games" }
    }),
    __metadata("design:type", Object)
], GameCategoryResponseDto.prototype, "categoryTitle", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    __metadata("design:type", Number)
], GameCategoryResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', example: 1 }),
    __metadata("design:type", Number)
], GameCategoryResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类下的游戏列表', type: [CategoryGameResponseDto] }),
    __metadata("design:type", Array)
], GameCategoryResponseDto.prototype, "games", void 0);
class AppHomeConfigDetailResponseDto {
    id;
    configName;
    description;
    status;
    sortOrder;
    remark;
    createTime;
    updateTime;
    topFloatAd;
    carouselAd;
    homeGridAd;
    splashPopupAd;
    floatAd;
    recommendedGames;
    gameCategories;
}
exports.AppHomeConfigDetailResponseDto = AppHomeConfigDetailResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置ID', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigDetailResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置名称', example: '默认首页配置' }),
    __metadata("design:type", String)
], AppHomeConfigDetailResponseDto.prototype, "configName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置描述', example: '系统默认的APP首页配置' }),
    __metadata("design:type", String)
], AppHomeConfigDetailResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigDetailResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigDetailResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注', example: '默认配置' }),
    __metadata("design:type", String)
], AppHomeConfigDetailResponseDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], AppHomeConfigDetailResponseDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], AppHomeConfigDetailResponseDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '顶部浮动广告', type: AdInfoResponseDto, required: false }),
    __metadata("design:type", AdInfoResponseDto)
], AppHomeConfigDetailResponseDto.prototype, "topFloatAd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '轮播广告', type: AdInfoResponseDto, required: false }),
    __metadata("design:type", AdInfoResponseDto)
], AppHomeConfigDetailResponseDto.prototype, "carouselAd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '首页九宫格', type: AdInfoResponseDto, required: false }),
    __metadata("design:type", AdInfoResponseDto)
], AppHomeConfigDetailResponseDto.prototype, "homeGridAd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '开屏弹窗广告', type: AdInfoResponseDto, required: false }),
    __metadata("design:type", AdInfoResponseDto)
], AppHomeConfigDetailResponseDto.prototype, "splashPopupAd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '浮点广告', type: AdInfoResponseDto, required: false }),
    __metadata("design:type", AdInfoResponseDto)
], AppHomeConfigDetailResponseDto.prototype, "floatAd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推荐游戏列表', type: [RecommendedGameResponseDto] }),
    __metadata("design:type", Array)
], AppHomeConfigDetailResponseDto.prototype, "recommendedGames", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏分类组列表', type: [GameCategoryResponseDto] }),
    __metadata("design:type", Array)
], AppHomeConfigDetailResponseDto.prototype, "gameCategories", void 0);
class AppHomeConfigListResponseDto {
    id;
    configName;
    description;
    status;
    sortOrder;
    recommendedGameCount;
    categoryCount;
    createTime;
    updateTime;
}
exports.AppHomeConfigListResponseDto = AppHomeConfigListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置ID', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigListResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置名称', example: '默认首页配置' }),
    __metadata("design:type", String)
], AppHomeConfigListResponseDto.prototype, "configName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置描述', example: '系统默认的APP首页配置' }),
    __metadata("design:type", String)
], AppHomeConfigListResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigListResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigListResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推荐游戏数量', example: 6 }),
    __metadata("design:type", Number)
], AppHomeConfigListResponseDto.prototype, "recommendedGameCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '分类组数量', example: 4 }),
    __metadata("design:type", Number)
], AppHomeConfigListResponseDto.prototype, "categoryCount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间', example: '2024-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], AppHomeConfigListResponseDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间', example: '2024-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], AppHomeConfigListResponseDto.prototype, "updateTime", void 0);
class AppHomeConfigPageResponseDto {
    list;
    total;
    page;
    pageSize;
    totalPages;
}
exports.AppHomeConfigPageResponseDto = AppHomeConfigPageResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '数据列表', type: [AppHomeConfigListResponseDto] }),
    __metadata("design:type", Array)
], AppHomeConfigPageResponseDto.prototype, "list", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总数', example: 100 }),
    __metadata("design:type", Number)
], AppHomeConfigPageResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前页', example: 1 }),
    __metadata("design:type", Number)
], AppHomeConfigPageResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10 }),
    __metadata("design:type", Number)
], AppHomeConfigPageResponseDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总页数', example: 10 }),
    __metadata("design:type", Number)
], AppHomeConfigPageResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=app-home-config-response.dto.js.map