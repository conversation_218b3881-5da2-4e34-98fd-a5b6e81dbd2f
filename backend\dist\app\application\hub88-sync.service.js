"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var Hub88SyncService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.Hub88SyncService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const crypto_1 = require("crypto");
const entities_1 = require("../entities");
let Hub88SyncService = Hub88SyncService_1 = class Hub88SyncService {
    applicationRepository;
    providerRepository;
    logger = new common_1.Logger(Hub88SyncService_1.name);
    constructor(applicationRepository, providerRepository) {
        this.applicationRepository = applicationRepository;
        this.providerRepository = providerRepository;
    }
    async syncGamesFromHub88() {
        const startTime = Date.now();
        const result = {
            total_games: 0,
            new_games: 0,
            updated_games: 0,
            errors: [],
            sync_duration: 0,
        };
        try {
            const hub88Provider = await this.providerRepository.findOne({
                where: { providerCode: 'HUB88' },
            });
            if (!hub88Provider) {
                throw new Error('Hub88供应商配置未找到');
            }
            const hub88Games = await this.fetchGamesFromHub88API();
            result.total_games = hub88Games.length;
            for (const hub88Game of hub88Games) {
                try {
                    await this.processHub88Game(hub88Game, hub88Provider.id, result);
                }
                catch (error) {
                    this.logger.error(`处理游戏 ${hub88Game.id} 时出错:`, error);
                    result.errors.push(`游戏 ${hub88Game.name} (${hub88Game.id}): ${error.message}`);
                }
            }
            result.sync_duration = Date.now() - startTime;
            this.logger.log(`Hub88游戏同步完成: ${result.new_games}个新游戏, ${result.updated_games}个更新游戏`);
            return result;
        }
        catch (error) {
            this.logger.error('Hub88游戏同步失败:', error);
            result.errors.push(`同步失败: ${error.message}`);
            result.sync_duration = Date.now() - startTime;
            return result;
        }
    }
    async processHub88Game(hub88Game, providerId, result) {
        const existingApp = await this.applicationRepository.findOne({
            where: { supplierIdentifier: hub88Game.id },
        });
        const gameData = this.mapHub88GameToApplication(hub88Game, providerId);
        if (existingApp) {
            Object.assign(existingApp, gameData);
            await this.applicationRepository.save(existingApp);
            result.updated_games++;
            this.logger.debug(`更新游戏: ${hub88Game.name}`);
        }
        else {
            const newApp = this.applicationRepository.create({
                ...gameData,
                appUuid: (0, crypto_1.randomUUID)(),
            });
            await this.applicationRepository.save(newApp);
            result.new_games++;
            this.logger.debug(`新增游戏: ${hub88Game.name}`);
        }
    }
    mapHub88GameToApplication(hub88Game, providerId) {
        return {
            appCode: `HUB88_${hub88Game.id}`,
            name: hub88Game.name,
            providerId,
            launchCode: hub88Game.id,
            supplierIdentifier: hub88Game.id,
            categories: [hub88Game.category, hub88Game.subcategory].filter(Boolean),
            platforms: hub88Game.platforms,
            orientation: hub88Game.mobile_optimized ? 'adaptive' : 'landscape',
            supportedVirtualCurrencies: ['gold_coin'],
            rtp: hub88Game.rtp,
            volatility: hub88Game.volatility.toLowerCase(),
            maxWinMultiplier: hub88Game.max_win,
            minBet: hub88Game.min_bet,
            maxBet: hub88Game.max_bet,
            iconUrl: hub88Game.icon_url,
            posterUrl: hub88Game.background_url,
            status: this.mapHub88Status(hub88Game.status),
            hasDemo: hub88Game.demo_available,
            hasMobile: hub88Game.mobile_optimized,
            hasDesktop: hub88Game.desktop_optimized,
            features: hub88Game.features,
            tags: this.generateTagsFromHub88Game(hub88Game),
            metadata: {
                hub88_id: hub88Game.id,
                launch_url: hub88Game.launch_url,
                supported_languages: hub88Game.languages,
                supported_currencies: hub88Game.currencies,
                last_sync: new Date().toISOString(),
                provider: hub88Game.provider,
            },
        };
    }
    mapHub88Status(hub88Status) {
        const statusMap = {
            'active': 'active',
            'inactive': 'inactive',
            'maintenance': 'maintenance',
            'coming_soon': 'testing',
            'retired': 'inactive',
        };
        return statusMap[hub88Status] || 'inactive';
    }
    generateTagsFromHub88Game(hub88Game) {
        const tags = [];
        if (hub88Game.demo_available)
            tags.push('free_spins');
        if (hub88Game.mobile_optimized)
            tags.push('mobile_optimized');
        if (hub88Game.rtp > 96)
            tags.push('high_rtp');
        if (hub88Game.max_win > 10000)
            tags.push('jackpot');
        if (hub88Game.features.includes('Free Spins'))
            tags.push('free_spins');
        if (hub88Game.features.includes('Wild Symbols'))
            tags.push('featured');
        return tags;
    }
    async fetchGamesFromHub88API() {
        await new Promise(resolve => setTimeout(resolve, 1000));
        return [
            {
                id: 'hub88_slot_001',
                name: 'Mystic Fortune',
                category: 'Slots',
                subcategory: 'Video Slots',
                provider: 'Hub88 Studios',
                rtp: 96.5,
                volatility: 'Medium',
                max_win: 5000,
                min_bet: 0.1,
                max_bet: 100,
                features: ['Free Spins', 'Wild Symbols', 'Multipliers'],
                platforms: ['desktop', 'mobile'],
                languages: ['en', 'zh-CN', 'ja'],
                currencies: ['USD', 'EUR', 'CNY'],
                demo_available: true,
                mobile_optimized: true,
                desktop_optimized: true,
                icon_url: 'https://cdn.hub88.io/games/mystic-fortune/icon.png',
                background_url: 'https://cdn.hub88.io/games/mystic-fortune/bg.jpg',
                launch_url: 'https://games.hub88.io/launch/hub88_slot_001',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-06-01T00:00:00Z',
            },
            {
                id: 'hub88_baccarat_001',
                name: 'Live Baccarat Pro',
                category: 'Live Casino',
                subcategory: 'Baccarat',
                provider: 'Hub88 Live',
                rtp: 98.9,
                volatility: 'Low',
                max_win: 500,
                min_bet: 1,
                max_bet: 10000,
                features: ['Live Dealer', 'Side Bets', 'Statistics'],
                platforms: ['desktop', 'mobile'],
                languages: ['en', 'zh-CN', 'ja', 'ko'],
                currencies: ['USD', 'EUR', 'CNY', 'JPY'],
                demo_available: false,
                mobile_optimized: true,
                desktop_optimized: true,
                icon_url: 'https://cdn.hub88.io/games/live-baccarat-pro/icon.png',
                background_url: 'https://cdn.hub88.io/games/live-baccarat-pro/bg.jpg',
                launch_url: 'https://games.hub88.io/launch/hub88_baccarat_001',
                status: 'active',
                created_at: '2024-02-01T00:00:00Z',
                updated_at: '2024-06-01T00:00:00Z',
            },
        ];
    }
};
exports.Hub88SyncService = Hub88SyncService;
exports.Hub88SyncService = Hub88SyncService = Hub88SyncService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.Application)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.ApplicationProvider)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], Hub88SyncService);
//# sourceMappingURL=hub88-sync.service.js.map