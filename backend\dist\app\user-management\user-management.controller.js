"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManagementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const user_management_service_1 = require("./user-management.service");
const user_operations_service_1 = require("./user-operations.service");
const dto_1 = require("./dto");
let UserManagementController = class UserManagementController {
    userManagementService;
    userOperationsService;
    constructor(userManagementService, userOperationsService) {
        this.userManagementService = userManagementService;
        this.userOperationsService = userOperationsService;
    }
    async findUsers(queryDto) {
        console.log('🔍 用户管理API被调用，查询参数:', queryDto);
        const result = await this.userManagementService.findUsers(queryDto);
        console.log('📊 查询结果:', { total: result.total, listLength: result.list.length });
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findUserById(id) {
        const result = await this.userManagementService.findUserById(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateUserStatus(id, updateDto, req) {
        await this.userOperationsService.updateUserStatus(id, updateDto, req.user.userId);
        return {
            code: 200,
            message: '用户状态更新成功',
            result: {},
        };
    }
    async updateUserTags(id, updateDto, req) {
        await this.userOperationsService.updateUserTags(id, updateDto, req.user.userId);
        return {
            code: 200,
            message: '用户标签更新成功',
            result: {},
        };
    }
    async getUserInvitationRelationship(id) {
        const result = await this.userOperationsService.getUserInvitationRelationship(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getUserRiskEvents(id, page = 1, pageSize = 20) {
        const result = await this.userOperationsService.getUserRiskEvents(id, page, pageSize);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getMarketingChannels() {
        const result = await this.userOperationsService.getMarketingChannels();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getMarketingAds(channelId) {
        const result = await this.userOperationsService.getMarketingAds(channelId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.UserManagementController = UserManagementController;
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取APP用户列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: dto_1.AppUserListResponseDto }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryAppUserDto]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "findUsers", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取APP用户详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: dto_1.AppUserDetailDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "findUserById", null);
__decorate([
    (0, common_1.Put)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户状态（封禁/解封）' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateUserStatusDto, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "updateUserStatus", null);
__decorate([
    (0, common_1.Put)(':id/tags'),
    (0, swagger_1.ApiOperation)({ summary: '更新用户标签' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateUserTagsDto, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "updateUserTags", null);
__decorate([
    (0, common_1.Get)(':id/invitation-relationship'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户邀请关系' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: dto_1.InvitationRelationshipDto }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getUserInvitationRelationship", null);
__decorate([
    (0, common_1.Get)(':id/risk-events'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户风险事件列表' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '用户ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('pageSize', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getUserRiskEvents", null);
__decorate([
    (0, common_1.Get)('marketing/channels'),
    (0, swagger_1.ApiOperation)({ summary: '获取营销渠道列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getMarketingChannels", null);
__decorate([
    (0, common_1.Get)('marketing/ads'),
    (0, swagger_1.ApiOperation)({ summary: '获取营销广告列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)('channelId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], UserManagementController.prototype, "getMarketingAds", null);
exports.UserManagementController = UserManagementController = __decorate([
    (0, swagger_1.ApiTags)('APP用户管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('app-users'),
    __metadata("design:paramtypes", [user_management_service_1.UserManagementService,
        user_operations_service_1.UserOperationsService])
], UserManagementController);
//# sourceMappingURL=user-management.controller.js.map