-- 修复金币充值配置表，添加缺失的 tier_name 字段
-- 这个字段在实体定义中存在，但在实际数据库表中缺失

-- 添加 tier_name 字段
ALTER TABLE gold_recharge_configs 
ADD COLUMN IF NOT EXISTS tier_name VARCHAR(100) NOT NULL DEFAULT '默认挡位';

-- 更新现有数据的 tier_name
UPDATE gold_recharge_configs 
SET tier_name = CASE 
    WHEN id = 1 THEN '小额充值'
    WHEN id = 2 THEN '推荐充值'
    WHEN id = 3 THEN '超值充值'
    WHEN id = 4 THEN '豪华充值'
    WHEN id = 5 THEN '至尊充值'
    ELSE '充值挡位' || id
END
WHERE tier_name = '默认挡位';

-- 移除默认值约束，因为现在所有行都有了正确的值
ALTER TABLE gold_recharge_configs 
ALTER COLUMN tier_name DROP DEFAULT;

-- 添加字段注释
COMMENT ON COLUMN gold_recharge_configs.tier_name IS '充值挡位名称';
