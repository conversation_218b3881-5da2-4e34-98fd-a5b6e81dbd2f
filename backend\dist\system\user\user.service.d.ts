import { Repository } from 'typeorm';
import { SysUser } from '../entities/sys-user.entity';
import { SysRole } from '../entities/sys-role.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { QueryUserDto } from './dto/query-user.dto';
export declare class SystemUserService {
    private userRepository;
    private roleRepository;
    constructor(userRepository: Repository<SysUser>, roleRepository: Repository<SysRole>);
    create(createUserDto: CreateUserDto, currentUserId?: number): Promise<{
        user: SysUser;
        generatedPassword: string | undefined;
    }>;
    findAll(queryUserDto: QueryUserDto): Promise<{
        list: SysUser[];
        total: number;
        current: number;
        pageSize: number;
    }>;
    findOne(id: number): Promise<SysUser>;
    update(id: number, updateUserDto: UpdateUserDto, currentUserId?: number): Promise<SysUser>;
    remove(id: number, currentUserId?: number): Promise<{
        message: string;
    }>;
    getUserInfo(userId: number): Promise<{
        roles: string[];
        id: number;
        username: string;
        email: string;
        password: string;
        phoneNumber: string;
        avatar: string;
        description: string;
        status: number;
        lastLoginTime: Date;
        isSuperAdmin: boolean;
        createTime: Date;
        updateTime: Date;
    }>;
    checkSuperAdminPermission(userId: number): Promise<void>;
    isSuperAdmin(userId: number): Promise<boolean>;
    resetPassword(userId: number, currentUserId: number): Promise<{
        newPassword: string;
    }>;
    private generateRandomPassword;
    getSuperAdmin(): Promise<SysUser | null>;
    setSuperAdmin(userId: number): Promise<void>;
}
