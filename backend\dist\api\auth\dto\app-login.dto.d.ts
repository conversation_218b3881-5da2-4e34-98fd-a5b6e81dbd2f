export declare class AppLoginDto {
    loginType: 'username' | 'email' | 'phone';
    identifier: string;
    password: string;
    deviceId?: string;
    deviceInfo?: string;
}
export declare class AppGoogleLoginDto {
    idToken: string;
    deviceId?: string;
    deviceInfo?: string;
}
export declare class AppRegisterDto {
    username: string;
    email: string;
    phone?: string;
    password: string;
    nickname?: string;
    inviteCode?: string;
    channelIdentifier?: string;
    adIdentifier?: string;
    pageIdentifier?: string;
    deviceId?: string;
    deviceInfo?: string;
}
export declare class AppLoginResponseDto {
    accessToken: string;
    refreshToken: string;
    tokenType: string;
    expiresIn: number;
    user: {
        id: number;
        uid: number;
        username: string;
        email: string;
        nickname: string;
        avatar: string;
        vipLevel: number;
        rechargeBalance: number;
        goldBalance: number;
    };
}
