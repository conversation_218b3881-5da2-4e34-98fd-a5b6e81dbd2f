# 环境配置示例
# Environment Configuration Example

# 后端 API 前缀
VITE_API_BASE_URL=http://localhost:3000/api

# 登录之后默认调转的路由
VITE_BASE_HOME_PATH=/home

# 网站标题
VITE_GLOB_APP_TITLE=InApp2 管理后台

# 环境类型 (local | supabase | production)
VITE_ENVIRONMENT=supabase

# Supabase配置
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase S3存储配置
VITE_SUPABASE_S3_ACCESS_KEY_ID=your_s3_access_key_id
VITE_SUPABASE_S3_SECRET_ACCESS_KEY=your_s3_secret_access_key
VITE_SUPABASE_S3_ENDPOINT=your_s3_endpoint
VITE_SUPABASE_S3_REGION=your_s3_region
VITE_SUPABASE_S3_BUCKET=your_s3_bucket
VITE_SUPABASE_S3_FOLDER=your_s3_folder

# 文件上传配置
VITE_MAX_FILE_SIZE=5242880
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/svg+xml,image/webp

# 图片压缩配置
VITE_COMPRESSION_MAX_WIDTH=1920
VITE_COMPRESSION_MAX_HEIGHT=1080
VITE_COMPRESSION_QUALITY=0.8
