"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigListDto = exports.VipConfigQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class VipConfigQueryDto {
    page = 1;
    pageSize = 10;
    vipLevel;
    status;
    levelName;
}
exports.VipConfigQueryDto = VipConfigQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '页码必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '页码不能小于1' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 10, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '每页数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '每页数量不能小于1' }),
    (0, class_validator_1.Max)(100, { message: '每页数量不能大于100' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级筛选', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'VIP等级必须是数字' }),
    (0, class_validator_1.Min)(0, { message: 'VIP等级不能小于0' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], VipConfigQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称搜索', example: '青铜', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '等级名称必须是字符串' }),
    __metadata("design:type", String)
], VipConfigQueryDto.prototype, "levelName", void 0);
class VipConfigListDto {
    id;
    vipLevel;
    levelName;
    requiredPoints;
    balanceRatio;
    cashRatio;
    goldRatio;
    dailyGoldReward;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
}
exports.VipConfigListDto = VipConfigListDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP配置ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称' }),
    __metadata("design:type", String)
], VipConfigListDto.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '所需积分' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '余额积分比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "balanceRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '现金消耗积分比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "cashRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币消耗积分比例' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "goldRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日可领取金币数量' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注说明' }),
    __metadata("design:type", String)
], VipConfigListDto.prototype, "remark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人ID' }),
    __metadata("design:type", Number)
], VipConfigListDto.prototype, "updatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], VipConfigListDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], VipConfigListDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建人信息', required: false }),
    __metadata("design:type", Object)
], VipConfigListDto.prototype, "creator", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新人信息', required: false }),
    __metadata("design:type", Object)
], VipConfigListDto.prototype, "updater", void 0);
//# sourceMappingURL=vip-config-query.dto.js.map