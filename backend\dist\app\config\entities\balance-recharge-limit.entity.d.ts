import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class BalanceRechargeLimit {
    id: number;
    minAmount: number;
    maxAmount: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    isAmountValid(amount: number): boolean;
    getLimitDescription(): string;
    getAmountValidationError(amount: number): string | null;
}
