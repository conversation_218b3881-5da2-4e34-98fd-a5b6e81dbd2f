{"pagination": {"DescribeCustomKeyStores": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "CustomKeyStores"}, "ListAliases": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Aliases"}, "ListGrants": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "<PERSON>s"}, "ListKeyPolicies": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "PolicyNames"}, "ListKeyRotations": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Rotations"}, "ListKeys": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Keys"}, "ListResourceTags": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "Tags"}, "ListRetirableGrants": {"input_token": "<PERSON><PERSON>", "limit_key": "Limit", "more_results": "Truncated", "output_token": "NextMarker", "result_key": "<PERSON>s"}}}