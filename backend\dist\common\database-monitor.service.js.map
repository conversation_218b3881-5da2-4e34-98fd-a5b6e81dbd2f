{"version": 3, "file": "database-monitor.service.js", "sourceRoot": "", "sources": ["../../src/common/database-monitor.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,6CAAmD;AACnD,qCAAqC;AAG9B,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAKvB;IAJO,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAElE,YAEU,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAC7B,CAAC;IAKJ,KAAK,CAAC,yBAAyB;QAC7B,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC9B,OAAO;YACT,CAAC;YAGD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAa,CAAC;YAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBAEhC,MAAM,QAAQ,GAAG;oBACf,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;oBAChC,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC;oBAC9B,YAAY,EAAE,IAAI,CAAC,YAAY,IAAI,CAAC;oBACpC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,SAAS;oBAC9C,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,SAAS;iBAC/C,CAAC;gBAGF,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;oBACzC,CAAC,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEvE,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;oBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;gBAC7E,CAAC;qBAAM,IAAI,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,YAAY,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAClE,CAAC;gBAGD,IAAI,IAAI,IAAI,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;oBACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;gBAC9G,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB;QAMtB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAGvC,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAa,CAAC;YAC7C,IAAI,eAAe,GAAG,CAAC,CAAC;YACxB,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClD,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;YACvD,CAAC;YAED,OAAO;gBACL,SAAS,EAAE,IAAI;gBACf,OAAO;gBACP,eAAe;aAChB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAEvC,OAAO;gBACL,SAAS,EAAE,KAAK;gBAChB,OAAO;gBACP,eAAe,EAAE,CAAC;gBAClB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,gBAAgB;QACpB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG;gBAEd;;wEAEgE;gBAGhE,8EAA8E;gBAG9E;;;;iBAIS;aACV,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CACtC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CACnD,CAAC;YAEF,OAAO;gBACL,iBAAiB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;gBACjF,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC5E,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;aACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAa,CAAC;YAC7C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBAClD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;gBAGhC,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE/B,CAAC;YACH,CAAC;YAGD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEhC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAKD,uBAAuB;QAErB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,yBAAyB,EAAE,CAAC;QACnC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;QAGd,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC7B,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAGnB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC/B,IAAI,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBACnC,IAAI,KAAK,EAAE,CAAC;oBACV,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1B,CAAC;CACF,CAAA;AApLY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,0BAAgB,GAAE,CAAA;qCACC,oBAAU;GALrB,sBAAsB,CAoLlC"}