import { AppUser } from './app-user.entity';
import { AppOrderItem } from './app-order-item.entity';
export declare class AppOrder {
    id: number;
    orderNo: string;
    userId: number;
    totalAmount: number;
    discountAmount: number;
    shippingFee: number;
    actualAmount: number;
    status: number;
    paymentMethod: string;
    paymentTime: Date;
    shippingTime: Date;
    deliveryTime: Date;
    receiverName: string;
    receiverPhone: string;
    receiverAddress: string;
    remark: string;
    user: AppUser;
    items: AppOrderItem[];
    createTime: Date;
    updateTime: Date;
}
