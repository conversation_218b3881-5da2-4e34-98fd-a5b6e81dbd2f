"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppBusinessModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const config_1 = require("@nestjs/config");
const entities_1 = require("./entities");
const supplier_module_1 = require("./supplier/supplier.module");
const application_module_1 = require("./application/application.module");
const channel_module_1 = require("./channel/channel.module");
const transaction_module_1 = require("./transaction/transaction.module");
const config_module_1 = require("./config/config.module");
let AppBusinessModule = class AppBusinessModule {
};
exports.AppBusinessModule = AppBusinessModule;
exports.AppBusinessModule = AppBusinessModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.AppUser,
                entities_1.AppCategory,
                entities_1.AppProduct,
                entities_1.AppOrder,
                entities_1.AppOrderItem,
                entities_1.AppUserAddress,
                entities_1.ApplicationProvider,
                entities_1.ProviderEnvironment,
                entities_1.Application,
                entities_1.MarketingChannel,
                entities_1.MarketingAd,
                entities_1.PromotionalPage,
                entities_1.RiskEvent,
                entities_1.DeviceLogRealtime,
                entities_1.DeviceLogHistory,
                entities_1.CashTransaction,
                entities_1.GoldTransaction,
                entities_1.RechargeTransaction,
            ]),
            passport_1.PassportModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_EXPIRES_IN'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            supplier_module_1.SupplierModule,
            application_module_1.ApplicationModule,
            channel_module_1.ChannelModule,
            transaction_module_1.TransactionModule,
            config_module_1.ConfigModule,
        ],
        controllers: [],
        providers: [],
        exports: [
            typeorm_1.TypeOrmModule,
        ],
    })
], AppBusinessModule);
//# sourceMappingURL=app.module.js.map