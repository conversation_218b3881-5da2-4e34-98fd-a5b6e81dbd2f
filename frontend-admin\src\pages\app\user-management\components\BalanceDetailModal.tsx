import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Card,
  Statistic,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Tag,
  Row,
  Col,
  Form,
  message,
  Spin,
} from 'antd';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';

import { transactionApi } from '#src/api/transaction';
import type {
  UserAssetStatistics,
  TransactionItem,
  TransactionListQuery,
  CashTransactionType,
  GoldTransactionType,
  RechargeTransactionType,
} from '#src/api/transaction/types';
import {
  TransactionStatus,
  CashTransactionTypeLabels,
  GoldTransactionTypeLabels,
  RechargeTransactionTypeLabels,
  TransactionStatusLabels,
} from '#src/api/transaction/types';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface BalanceDetailModalProps {
  visible: boolean;
  onCancel: () => void;
  userId: number;
  userName: string;
}

const BalanceDetailModal: React.FC<BalanceDetailModalProps> = ({
  visible,
  onCancel,
  userId,
  userName,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('cash');
  const [searchParams, setSearchParams] = useState<Omit<TransactionListQuery, 'userId'>>({
    page: 1,
    pageSize: 20,
  });

  // 获取用户资产统计
  const {
    data: assetStatistics,
    loading: statisticsLoading,
    run: fetchStatistics,
  } = useRequest(
    () => transactionApi.getUserAssetStatistics(userId),
    {
      manual: true,
    },
  );

  // 获取交易明细列表
  const {
    data: transactionList,
    loading: listLoading,
    run: fetchTransactionList,
  } = useRequest(
    () => {
      if (activeTab === 'cash') {
        return transactionApi.getCashTransactionList(userId, searchParams);
      } else if (activeTab === 'gold') {
        return transactionApi.getGoldTransactionList(userId, searchParams);
      } else {
        return transactionApi.getRechargeTransactionList(userId, searchParams);
      }
    },
    {
      manual: true,
      refreshDeps: [activeTab, searchParams],
    },
  );

  useEffect(() => {
    if (visible && userId) {
      fetchStatistics();
      fetchTransactionList();
    }
  }, [visible, userId, fetchStatistics, fetchTransactionList]);

  // 处理搜索
  const handleSearch = (values: any) => {
    const { dateRange, ...rest } = values;
    const newParams = {
      ...rest,
      startDate: dateRange?.[0]?.format('YYYY-MM-DD'),
      endDate: dateRange?.[1]?.format('YYYY-MM-DD'),
      page: 1,
      pageSize: 20,
    };
    setSearchParams(newParams);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    setSearchParams({ page: 1, pageSize: 20 });
  };

  // 获取当前标签页的交易类型选项
  const getTransactionTypeOptions = () => {
    if (activeTab === 'cash') {
      return Object.entries(CashTransactionTypeLabels).map(([value, label]) => (
        <Option key={value} value={Number(value)}>{label}</Option>
      ));
    } else if (activeTab === 'gold') {
      return Object.entries(GoldTransactionTypeLabels).map(([value, label]) => (
        <Option key={value} value={Number(value)}>{label}</Option>
      ));
    } else {
      return Object.entries(RechargeTransactionTypeLabels).map(([value, label]) => (
        <Option key={value} value={Number(value)}>{label}</Option>
      ));
    }
  };

  // 获取交易类型标签
  const getTransactionTypeLabel = (type: number) => {
    if (activeTab === 'cash') {
      return CashTransactionTypeLabels[type as CashTransactionType] || '未知';
    } else if (activeTab === 'gold') {
      return GoldTransactionTypeLabels[type as GoldTransactionType] || '未知';
    } else {
      return RechargeTransactionTypeLabels[type as RechargeTransactionType] || '未知';
    }
  };

  // 表格列定义
  const columns: ColumnsType<TransactionItem> = [
    {
      title: '交易时间',
      dataIndex: 'createTime',
      width: 160,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '交易类型',
      dataIndex: 'transactionType',
      width: 120,
      render: (type: number) => (
        <Tag color="blue">{getTransactionTypeLabel(type)}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 80,
      render: (status: TransactionStatus) => (
        <Tag color={status === TransactionStatus.INCOME ? 'green' : 'red'}>
          {TransactionStatusLabels[status]}
        </Tag>
      ),
    },
    {
      title: '交易金额',
      dataIndex: 'amount',
      width: 120,
      render: (amount: number, record: TransactionItem) => (
        <span style={{ color: record.status === TransactionStatus.INCOME ? '#52c41a' : '#ff4d4f' }}>
          {record.status === TransactionStatus.INCOME ? '+' : '-'}
          {activeTab === 'gold' ? amount.toLocaleString() : Number(amount).toFixed(4)}
        </span>
      ),
    },
    {
      title: '交易前余额',
      dataIndex: 'balanceBefore',
      width: 120,
      render: (balance: number) => 
        activeTab === 'gold' ? balance.toLocaleString() : Number(balance).toFixed(4),
    },
    {
      title: '交易后余额',
      dataIndex: 'balanceAfter',
      width: 120,
      render: (balance: number) => 
        activeTab === 'gold' ? balance.toLocaleString() : Number(balance).toFixed(4),
    },
    {
      title: '订单号',
      dataIndex: 'orderId',
      width: 150,
      render: (orderId: string) => orderId || '-',
    },
    {
      title: '描述',
      dataIndex: 'description',
      width: 200,
      render: (description: string) => description || '-',
    },
  ];

  // 获取当前资产统计
  const getCurrentStatistics = () => {
    if (!assetStatistics) return null;
    return assetStatistics[activeTab as keyof UserAssetStatistics];
  };

  const currentStats = getCurrentStatistics();

  return (
    <Modal
      title={`${userName} - 资产详情`}
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      destroyOnClose
    >
      <Spin spinning={statisticsLoading}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'cash',
              label: '现金资产',
              children: assetStatistics && (
                <div>
                  {/* 现金资产统计 */}
                  <Card title="现金资产统计" className="mb-4">
                    <Row gutter={16} className="mb-4">
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="资产总额"
                            value={(assetStatistics.cash.balance + assetStatistics.recharge.balance)}
                            precision={4}
                            valueStyle={{ color: '#1890ff' }}
                            suffix="(充值+可提现)"
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="可提现总额"
                            value={assetStatistics.cash.balance}
                            precision={4}
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="充值总额"
                            value={assetStatistics.recharge.balance}
                            precision={4}
                            valueStyle={{ color: '#fa8c16' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="邀请/活动获取"
                            value={assetStatistics.cash.inviteActivityAmount || 0}
                            precision={4}
                            valueStyle={{ color: '#13c2c2' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="累计下注"
                            value={assetStatistics.cash.betAmount || 0}
                            precision={4}
                            valueStyle={{ color: '#ff4d4f' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="累计赢金"
                            value={assetStatistics.cash.winAmount || 0}
                            precision={4}
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </Card>
                      </Col>
                      <Col span={6}>
                        <Card>
                          <Statistic
                            title="下注/赢金占比"
                            value={assetStatistics.cash.betWinRatio || 0}
                            precision={2}
                            suffix="%"
                            valueStyle={{ color: '#722ed1' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </div>
              )
            },
            {
              key: 'gold',
              label: '金币资产',
              children: assetStatistics && (
                <div>
                  {/* 金币资产统计 */}
                  <Card title="金币资产统计" className="mb-4">
                    <Row gutter={16} className="mb-4">
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="充值总额"
                            value={assetStatistics.gold.depositAmount || 0}
                            valueStyle={{ color: '#fa8c16' }}
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="VIP卡领取总额"
                            value={assetStatistics.gold.vipCardAmount || 0}
                            valueStyle={{ color: '#eb2f96' }}
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="推广活动总额"
                            value={assetStatistics.gold.activityAmount || 0}
                            valueStyle={{ color: '#13c2c2' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="累计下注"
                            value={assetStatistics.gold.betAmount || 0}
                            valueStyle={{ color: '#ff4d4f' }}
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="累计赢取"
                            value={assetStatistics.gold.winAmount || 0}
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </Card>
                      </Col>
                      <Col span={8}>
                        <Card>
                          <Statistic
                            title="下注/赢金占比"
                            value={assetStatistics.gold.betWinRatio || 0}
                            precision={2}
                            suffix="%"
                            valueStyle={{ color: '#722ed1' }}
                          />
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </div>
              )
            }
          ]}
        />
      </Spin>

      {/* 搜索表单 */}
      <Card title="交易明细" className="mt-4">
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
          className="mb-4"
        >
          <Form.Item name="status">
            <Select placeholder="交易状态" allowClear style={{ width: 120 }}>
              <Option value={TransactionStatus.INCOME}>收入</Option>
              <Option value={TransactionStatus.EXPENSE}>支出</Option>
            </Select>
          </Form.Item>

          <Form.Item name="transactionType">
            <Select placeholder="交易类型" allowClear style={{ width: 150 }}>
              {getTransactionTypeOptions()}
            </Select>
          </Form.Item>

          <Form.Item name="dateRange">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset}>重置</Button>
              <Button icon={<ReloadOutlined />} onClick={fetchTransactionList}>
                刷新
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <Table
          columns={columns}
          dataSource={transactionList?.list || []}
          rowKey="id"
          loading={listLoading}
          pagination={{
            current: transactionList?.page || 1,
            pageSize: transactionList?.pageSize || 20,
            total: transactionList?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setSearchParams(prev => ({ ...prev, page, pageSize }));
            },
          }}
          scroll={{ x: 1000 }}
        />
      </Card>
    </Modal>
  );
};

export default BalanceDetailModal;
