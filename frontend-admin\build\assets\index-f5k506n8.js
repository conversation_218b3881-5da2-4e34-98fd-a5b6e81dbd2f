import{u as T,a as b,j as s,F as l}from"./index-CHjq8S-S.js";import{f as k,D as B,a as D,b as R}from"./detail-D6pQ_BQH.js";import{a as i}from"./react-BUTTOX-3.js";import{B as x}from"./index-DDI4OfxQ.js";import{B as U}from"./index-S5qMhsyX.js";import{u as y}from"./useMutation-BkoFKhTK.js";import{getConstantColumns as F}from"./constants-C9COdJJq.js";import{aD as j,bO as M,d as z,bB as v,s as m,M as A}from"./antd-CXPM1OiB.js";import"./useQuery-C2zD5aNt.js";import"./index-CG1eZs-g.js";import"./Table-DTyH0rpt.js";import"./index-CZVy594C.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";import"./index-CnuJ2TqD.js";import"./index-DVrd-Evt.js";function ee(){const{t:e}=T(),d=i.useRef(),[w,a]=i.useState(!1),[S,u]=i.useState(""),[g,c]=i.useState(null),r=b(t=>t.isSuperAdmin),p=y({mutationFn:D,onSuccess:()=>{m.success(e("common.deleteSuccess")),h()},onError:t=>{m.error(t.message||e("common.deleteFailed"))}}),f=y({mutationFn:R,onSuccess:t=>{const o=t.result.newPassword;A.success({title:e("system.user.resetPasswordSuccess"),content:s.jsxs("div",{children:[s.jsxs("p",{children:[e("system.user.newPassword"),":"]}),s.jsx("p",{style:{fontFamily:"monospace",fontSize:"16px",fontWeight:"bold",color:"#1890ff",backgroundColor:"#f0f0f0",padding:"8px",borderRadius:"4px"},children:o}),s.jsx("p",{style:{color:"#ff4d4f",fontSize:"12px"},children:e("system.user.passwordWarning")})]}),width:500})},onError:t=>{m.error(t.message||e("system.user.resetPasswordFailed"))}}),h=()=>{var t;(t=d.current)==null||t.reload()},P=t=>{a(t),t||c(null)},C=[...F(e),{title:e("common.action"),valueType:"option",key:"option",width:200,fixed:"right",render:(t,o,E,I)=>{const n=[];return r&&n.push(s.jsx(l,{type:"link",size:"small",onClick:()=>{c(o),a(!0),u(e("system.user.editUser"))},children:e("common.edit")},"edit")),r&&n.push(s.jsx(j,{title:e("system.user.resetPasswordConfirm"),onConfirm:()=>{f.mutate({userId:o.id})},okText:e("common.confirm"),cancelText:e("common.cancel"),children:s.jsx(l,{type:"link",size:"small",icon:s.jsx(M,{}),loading:f.isPending,children:e("system.user.resetPassword")})},"reset-password")),r&&!o.isSuperAdmin&&n.push(s.jsx(j,{title:e("common.deleteConfirm"),onConfirm:()=>{p.mutate(o.id)},okText:e("common.confirm"),cancelText:e("common.cancel"),children:s.jsx(l,{type:"link",size:"small",danger:!0,loading:p.isPending,children:e("common.delete")})},"delete")),n}}];return r?s.jsxs(x,{className:"h-full",children:[s.jsx(U,{columns:C,actionRef:d,request:async t=>{const o=await k(t);return{...o,data:o.result.list,total:o.result.total}},headerTitle:e("system.user.title"),toolBarRender:()=>[s.jsx(z,{icon:s.jsx(v,{}),type:"primary",onClick:()=>{c(null),a(!0),u(e("system.user.addUser"))},children:e("common.add")},"add-user")],rowKey:"id",search:{labelWidth:"auto"}}),s.jsx(B,{title:S,open:w,onCloseChange:P,detailData:g,refreshTable:h})]}):s.jsx(x,{className:"h-full",children:s.jsx("div",{className:"flex items-center justify-center h-64",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h3",{children:e("system.user.noPermission")}),s.jsx("p",{children:e("system.user.superAdminOnly")})]})})})}export{ee as default};
