const { createConnection } = require('typeorm');
const path = require('path');

async function debugTypeORMEntity() {
  try {
    console.log('🔍 调试TypeORM实体映射...\n');

    // 创建数据库连接
    const connection = await createConnection({
      type: 'postgres',
      host: '**************',
      port: 5435,
      username: 'user_jJSpPW',
      password: 'password_DmrhYX',
      database: 'inapp2',
      entities: [path.join(__dirname, 'src/app/config/entities/*.entity.{ts,js}')],
      synchronize: false,
      logging: true,
    });

    console.log('✅ 数据库连接成功');

    // 获取 BalanceRechargeLimit 实体的 repository
    const BalanceRechargeLimit = require('./src/app/config/entities/balance-recharge-limit.entity').BalanceRechargeLimit;
    const repository = connection.getRepository(BalanceRechargeLimit);

    console.log('📊 查询余额充值限制数据...');
    
    const limit = await repository.findOne({
      where: { status: 1 },
    });

    if (limit) {
      console.log('\n🔍 TypeORM查询结果:');
      console.log('原始对象:', limit);
      
      console.log('\n📋 字段类型分析:');
      console.log('id:', typeof limit.id, '值:', limit.id);
      console.log('limitName:', typeof limit.limitName, '值:', limit.limitName);
      console.log('minAmount:', typeof limit.minAmount, '值:', limit.minAmount);
      console.log('maxAmount:', typeof limit.maxAmount, '值:', limit.maxAmount);
      console.log('status:', typeof limit.status, '值:', limit.status);
      console.log('remark:', typeof limit.remark, '值:', limit.remark);
      
      console.log('\n🔄 方法调用测试:');
      try {
        const description = limit.getLimitDescription();
        console.log('getLimitDescription():', description);
      } catch (error) {
        console.error('getLimitDescription() 错误:', error.message);
      }
      
      try {
        const isValid = limit.isAmountValid(100);
        console.log('isAmountValid(100):', isValid);
      } catch (error) {
        console.error('isAmountValid() 错误:', error.message);
      }
      
    } else {
      console.log('❌ 没有找到状态为1的余额充值限制数据');
    }

    await connection.close();
    console.log('\n✅ 数据库连接已关闭');

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
  }
}

debugTypeORMEntity();
