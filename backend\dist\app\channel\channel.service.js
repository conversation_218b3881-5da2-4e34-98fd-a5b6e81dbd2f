"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChannelService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
let ChannelService = class ChannelService {
    channelRepository;
    adRepository;
    constructor(channelRepository, adRepository) {
        this.channelRepository = channelRepository;
        this.adRepository = adRepository;
    }
    async createChannel(createChannelDto) {
        const existingByName = await this.channelRepository.findOne({
            where: { name: createChannelDto.name },
        });
        if (existingByName) {
            throw new common_1.ConflictException(`渠道名称 ${createChannelDto.name} 已存在`);
        }
        const existingByIdentifier = await this.channelRepository.findOne({
            where: { identifier: createChannelDto.identifier },
        });
        if (existingByIdentifier) {
            throw new common_1.ConflictException(`渠道标识 ${createChannelDto.identifier} 已存在`);
        }
        const channel = this.channelRepository.create(createChannelDto);
        return await this.channelRepository.save(channel);
    }
    async findChannels(queryDto) {
        const { page, pageSize, search, status, sortBy, sortOrder } = queryDto;
        const queryBuilder = this.channelRepository.createQueryBuilder('channel');
        if (search) {
            queryBuilder.andWhere('channel.name ILIKE :search', { search: `%${search}%` });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('channel.status = :status', { status });
        }
        queryBuilder.orderBy(`channel.${sortBy}`, sortOrder);
        const currentPage = page || 1;
        const currentPageSize = pageSize || 10;
        const skip = (currentPage - 1) * currentPageSize;
        queryBuilder.skip(skip).take(currentPageSize);
        const [channels, total] = await queryBuilder.getManyAndCount();
        return {
            list: channels,
            total,
            page: currentPage,
            pageSize: currentPageSize,
            totalPages: Math.ceil(total / currentPageSize),
        };
    }
    async findChannelById(id) {
        const channel = await this.channelRepository.findOne({
            where: { id },
            relations: ['ads'],
        });
        if (!channel) {
            throw new common_1.NotFoundException(`ID为 ${id} 的渠道不存在`);
        }
        return channel;
    }
    async updateChannel(id, updateChannelDto) {
        const channel = await this.findChannelById(id);
        if (updateChannelDto.name && updateChannelDto.name !== channel.name) {
            const existingChannel = await this.channelRepository.findOne({
                where: { name: updateChannelDto.name },
            });
            if (existingChannel) {
                throw new common_1.ConflictException(`渠道名称 ${updateChannelDto.name} 已存在`);
            }
        }
        if (updateChannelDto.identifier && updateChannelDto.identifier !== channel.identifier) {
            const existingChannel = await this.channelRepository.findOne({
                where: { identifier: updateChannelDto.identifier },
            });
            if (existingChannel) {
                throw new common_1.ConflictException(`渠道标识 ${updateChannelDto.identifier} 已存在`);
            }
        }
        Object.assign(channel, updateChannelDto);
        return await this.channelRepository.save(channel);
    }
    async toggleChannelStatus(id) {
        const channel = await this.findChannelById(id);
        channel.status = channel.status === 1 ? 0 : 1;
        return await this.channelRepository.save(channel);
    }
    async findSimpleChannels() {
        return await this.channelRepository
            .createQueryBuilder('channel')
            .select(['channel.id', 'channel.name', 'channel.identifier'])
            .where('channel.status = :status', { status: 1 })
            .orderBy('channel.name', 'ASC')
            .getMany();
    }
    async createAd(createAdDto) {
        const channel = await this.findChannelById(createAdDto.channelId);
        const existingAd = await this.adRepository.findOne({
            where: { identifier: createAdDto.identifier },
        });
        if (existingAd) {
            throw new common_1.ConflictException(`广告标识 ${createAdDto.identifier} 已存在`);
        }
        if (createAdDto.adType === 2 && !createAdDto.referrerId) {
            throw new common_1.BadRequestException('推荐系统引流类型的广告必须指定推荐人ID');
        }
        if (createAdDto.referrerId) {
        }
        const ad = this.adRepository.create(createAdDto);
        return await this.adRepository.save(ad);
    }
    async findAds(queryDto) {
        console.log('[CHANNEL_SERVICE] 广告查询DTO:', JSON.stringify(queryDto, null, 2));
        const { page, pageSize, search, channelId, status, adType, referrerId, sortBy, sortOrder } = queryDto;
        console.log('[CHANNEL_SERVICE] 解构后的参数:', { page, pageSize, search, channelId, status, adType, referrerId, sortBy, sortOrder });
        const queryBuilder = this.adRepository
            .createQueryBuilder('ad')
            .leftJoinAndSelect('ad.channel', 'channel');
        if (search) {
            queryBuilder.andWhere('ad.name ILIKE :search', { search: `%${search}%` });
        }
        if (channelId) {
            queryBuilder.andWhere('ad.channelId = :channelId', { channelId });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('ad.status = :status', { status });
        }
        if (adType !== undefined) {
            queryBuilder.andWhere('ad.adType = :adType', { adType });
        }
        if (referrerId) {
            queryBuilder.andWhere('ad.referrerId = :referrerId', { referrerId });
        }
        queryBuilder.orderBy(`ad.${sortBy}`, sortOrder);
        const currentPage = page || 1;
        const currentPageSize = pageSize || 10;
        const skip = (currentPage - 1) * currentPageSize;
        queryBuilder.skip(skip).take(currentPageSize);
        const [ads, total] = await queryBuilder.getManyAndCount();
        return {
            list: ads,
            total,
            page: currentPage,
            pageSize: currentPageSize,
            totalPages: Math.ceil(total / currentPageSize),
        };
    }
    async findAdById(id) {
        const ad = await this.adRepository.findOne({
            where: { id },
            relations: ['channel'],
        });
        if (!ad) {
            throw new common_1.NotFoundException(`ID为 ${id} 的广告不存在`);
        }
        return ad;
    }
    async updateAd(id, updateAdDto) {
        const ad = await this.findAdById(id);
        if (updateAdDto.channelId && updateAdDto.channelId !== ad.channelId) {
            await this.findChannelById(updateAdDto.channelId);
        }
        if (updateAdDto.identifier && updateAdDto.identifier !== ad.identifier) {
            const existingAd = await this.adRepository.findOne({
                where: { identifier: updateAdDto.identifier },
            });
            if (existingAd) {
                throw new common_1.ConflictException(`广告标识 ${updateAdDto.identifier} 已存在`);
            }
        }
        Object.assign(ad, updateAdDto);
        return await this.adRepository.save(ad);
    }
    async toggleAdStatus(id) {
        const ad = await this.findAdById(id);
        ad.status = ad.status === 1 ? 0 : 1;
        return await this.adRepository.save(ad);
    }
};
exports.ChannelService = ChannelService;
exports.ChannelService = ChannelService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.MarketingChannel)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.MarketingAd)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], ChannelService);
//# sourceMappingURL=channel.service.js.map