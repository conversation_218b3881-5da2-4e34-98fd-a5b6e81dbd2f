import{r as i,u as b,W as P,j as r}from"./index-CHjq8S-S.js";import{u as w,g as F}from"./useQuery-C2zD5aNt.js";import{a as I}from"./react-BUTTOX-3.js";import{B as S}from"./index-CG1eZs-g.js";import{u as f}from"./useMutation-BkoFKhTK.js";import{av as n,s as u,I as c,p as M,S as U}from"./antd-CXPM1OiB.js";function _(t){return i.get("users/list",{searchParams:t}).json()}function q(t){return i.post("users",{json:t}).json()}function v(t,l){return i.patch(`users/${t}`,{json:l}).json()}function $(t){return i.delete(`users/${t}`).json()}function A(t){return i.post("users/reset-password",{json:t}).json()}function E({title:t,open:l,onCloseChange:d,detailData:o,refreshTable:h}){const{t:e}=b(),[a]=n.useForm();P();const{data:j}=w({queryKey:["role-list-all"],queryFn:async()=>(await F()).result,initialData:[]}),p=f({mutationFn:q,onSuccess:s=>{u.success(e("common.createSuccess")),s.result.generatedPassword&&u.info(`${e("system.user.generatedPassword")}: ${s.result.generatedPassword}`,10),d(!1),h(),a.resetFields()},onError:s=>{u.error(s.message||e("common.createFailed"))}}),y=f({mutationFn:({id:s,data:m})=>v(s,m),onSuccess:()=>{u.success(e("common.updateSuccess")),d(!1),h(),a.resetFields()},onError:s=>{u.error(s.message||e("common.updateFailed"))}}),x=async s=>{try{const m={...s,roleIds:s.roleIds||[]};o!=null&&o.id?await y.mutateAsync({id:o.id,data:m}):await p.mutateAsync(m)}catch(m){console.error("Form submission error:",m)}},g=()=>{a.resetFields(),d(!1)};return I.useEffect(()=>{var s;l&&o?a.setFieldsValue({...o,roleIds:((s=o.roles)==null?void 0:s.map(m=>m.id))||[]}):l&&!o&&a.resetFields()},[l,o,a]),r.jsx(S,{title:t,open:l,onCancel:g,onOk:()=>a.submit(),confirmLoading:p.isPending||y.isPending,width:600,children:r.jsxs(n,{form:a,layout:"vertical",onFinish:x,autoComplete:"off",children:[r.jsx(n.Item,{label:e("system.user.username"),name:"username",rules:[{required:!0,message:e("system.user.usernameRequired")},{min:3,message:e("system.user.usernameMinLength")},{max:50,message:e("system.user.usernameMaxLength")}],children:r.jsx(c,{placeholder:e("system.user.usernamePlaceholder")})}),r.jsx(n.Item,{label:e("system.user.email"),name:"email",rules:[{required:!0,message:e("system.user.emailRequired")},{type:"email",message:e("system.user.emailInvalid")}],children:r.jsx(c,{placeholder:e("system.user.emailPlaceholder")})}),!o&&r.jsx(n.Item,{label:e("system.user.password"),name:"password",extra:e("system.user.passwordOptional"),children:r.jsx(c.Password,{placeholder:e("system.user.passwordPlaceholder")})}),r.jsx(n.Item,{label:e("system.user.phoneNumber"),name:"phoneNumber",children:r.jsx(c,{placeholder:e("system.user.phoneNumberPlaceholder")})}),r.jsx(n.Item,{label:e("system.user.description"),name:"description",children:r.jsx(c.TextArea,{placeholder:e("system.user.descriptionPlaceholder"),rows:3})}),r.jsx(n.Item,{label:e("system.user.roles"),name:"roleIds",children:r.jsx(M,{mode:"multiple",placeholder:e("system.user.rolesPlaceholder"),options:j.map(s=>({label:s.name,value:s.id}))})}),r.jsx(n.Item,{label:e("common.status"),name:"status",valuePropName:"checked",getValueFromEvent:s=>s?1:0,getValueProps:s=>({checked:s===1}),children:r.jsx(U,{checkedChildren:e("common.enabled"),unCheckedChildren:e("common.disabled")})})]})})}const V=Object.freeze(Object.defineProperty({__proto__:null,Detail:E},Symbol.toStringTag,{value:"Module"}));export{E as D,$ as a,A as b,V as d,_ as f};
