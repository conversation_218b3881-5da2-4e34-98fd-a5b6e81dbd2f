"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../entities");
let SupplierService = class SupplierService {
    providerRepository;
    environmentRepository;
    constructor(providerRepository, environmentRepository) {
        this.providerRepository = providerRepository;
        this.environmentRepository = environmentRepository;
    }
    async createProvider(createProviderDto) {
        const existingProvider = await this.providerRepository.findOne({
            where: { providerCode: createProviderDto.providerCode },
        });
        if (existingProvider) {
            throw new common_1.ConflictException(`供应商代码 ${createProviderDto.providerCode} 已存在`);
        }
        const provider = this.providerRepository.create(createProviderDto);
        return await this.providerRepository.save(provider);
    }
    async findSimpleProviders() {
        return await this.providerRepository
            .createQueryBuilder('provider')
            .select(['provider.id', 'provider.name', 'provider.providerCode', 'provider.status'])
            .where('provider.status IN (:...statuses)', { statuses: ['active', 'testing'] })
            .orderBy('provider.name', 'ASC')
            .getMany();
    }
    async findProviders(queryDto) {
        const { page, pageSize, name, providerCode, status, integrationType, commercialModelType, sortBy, sortOrder } = queryDto;
        const queryBuilder = this.providerRepository.createQueryBuilder('provider');
        if (name) {
            queryBuilder.andWhere('provider.name ILIKE :name', { name: `%${name}%` });
        }
        if (providerCode) {
            queryBuilder.andWhere('provider.providerCode ILIKE :providerCode', { providerCode: `%${providerCode}%` });
        }
        if (status) {
            queryBuilder.andWhere('provider.status = :status', { status });
        }
        if (integrationType) {
            queryBuilder.andWhere('provider.integrationType = :integrationType', { integrationType });
        }
        if (commercialModelType) {
            queryBuilder.andWhere('provider.commercialModelType = :commercialModelType', { commercialModelType });
        }
        queryBuilder.orderBy(`provider.${sortBy}`, sortOrder);
        const currentPage = page || 1;
        const currentPageSize = pageSize || 10;
        const skip = (currentPage - 1) * currentPageSize;
        queryBuilder.skip(skip).take(currentPageSize);
        const [providers, total] = await queryBuilder.getManyAndCount();
        return {
            list: providers,
            total,
            page: currentPage,
            pageSize: currentPageSize,
            totalPages: Math.ceil(total / currentPageSize),
        };
    }
    async findProviderById(id) {
        const provider = await this.providerRepository.findOne({
            where: { id },
            relations: ['environments'],
        });
        if (!provider) {
            throw new common_1.NotFoundException(`ID为 ${id} 的供应商不存在`);
        }
        return provider;
    }
    async updateProvider(id, updateProviderDto) {
        const provider = await this.findProviderById(id);
        if (updateProviderDto.providerCode && updateProviderDto.providerCode !== provider.providerCode) {
            const existingProvider = await this.providerRepository.findOne({
                where: { providerCode: updateProviderDto.providerCode },
            });
            if (existingProvider) {
                throw new common_1.ConflictException(`供应商代码 ${updateProviderDto.providerCode} 已存在`);
            }
        }
        Object.assign(provider, updateProviderDto);
        return await this.providerRepository.save(provider);
    }
    async removeProvider(id) {
        const provider = await this.findProviderById(id);
        await this.providerRepository.remove(provider);
    }
    async createEnvironment(providerId, createEnvironmentDto) {
        const provider = await this.findProviderById(providerId);
        const existingEnvironment = await this.environmentRepository.findOne({
            where: {
                providerId,
                environmentType: createEnvironmentDto.environmentType,
            },
        });
        if (existingEnvironment) {
            throw new common_1.ConflictException(`供应商 ${provider.name} 的 ${createEnvironmentDto.environmentType} 环境配置已存在`);
        }
        const environment = this.environmentRepository.create({
            ...createEnvironmentDto,
            providerId,
        });
        return await this.environmentRepository.save(environment);
    }
    async findEnvironmentsByProviderId(providerId) {
        await this.findProviderById(providerId);
        return await this.environmentRepository.find({
            where: { providerId },
            order: { environmentType: 'ASC' },
        });
    }
    async findEnvironmentById(id) {
        const environment = await this.environmentRepository.findOne({
            where: { id },
            relations: ['provider'],
        });
        if (!environment) {
            throw new common_1.NotFoundException(`ID为 ${id} 的环境配置不存在`);
        }
        return environment;
    }
    async updateEnvironment(id, updateEnvironmentDto) {
        const environment = await this.findEnvironmentById(id);
        Object.assign(environment, updateEnvironmentDto);
        return await this.environmentRepository.save(environment);
    }
    async removeEnvironment(id) {
        const environment = await this.findEnvironmentById(id);
        await this.environmentRepository.remove(environment);
    }
    async toggleEnvironmentStatus(id) {
        const environment = await this.findEnvironmentById(id);
        environment.isActive = !environment.isActive;
        return await this.environmentRepository.save(environment);
    }
};
exports.SupplierService = SupplierService;
exports.SupplierService = SupplierService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.ApplicationProvider)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.ProviderEnvironment)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SupplierService);
//# sourceMappingURL=supplier.service.js.map