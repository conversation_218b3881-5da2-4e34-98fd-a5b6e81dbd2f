import{j as a}from"./index-CHjq8S-S.js";import{an as d}from"./antd-CXPM1OiB.js";import"./react-BUTTOX-3.js";function u(e){return[{title:"ID",dataIndex:"id",key:"id",width:80,search:!1},{title:e("system.user.username"),dataIndex:"username",key:"username",width:120,ellipsis:!0},{title:e("system.user.email"),dataIndex:"email",key:"email",width:200,ellipsis:!0},{title:e("system.user.phoneNumber"),dataIndex:"phoneNumber",key:"phoneNumber",width:120,search:!1,render:s=>s||"-"},{title:e("system.user.roles"),dataIndex:"roles",key:"roles",width:150,search:!1,render:(s,t)=>{var i;return a.jsx("div",{children:(i=t.roles)==null?void 0:i.map(r=>a.jsx(d,{color:"blue",children:r.name},r.id))})}},{title:e("system.user.isSuperAdmin"),dataIndex:"isSuperAdmin",key:"isSuperAdmin",width:100,search:!1,render:(s,t)=>a.jsx(d,{color:t.isSuperAdmin?"red":"default",children:t.isSuperAdmin?e("system.user.superAdmin"):e("system.user.normalUser")})},{title:e("common.status"),dataIndex:"status",key:"status",width:100,valueType:"select",valueEnum:{1:{text:e("common.enabled"),status:"Success"},0:{text:e("common.disabled"),status:"Error"}}},{title:e("system.user.lastLoginTime"),dataIndex:"lastLoginTime",key:"lastLoginTime",width:160,search:!1,valueType:"dateTime",render:s=>s||"-"},{title:e("common.createTime"),dataIndex:"createTime",key:"createTime",width:160,search:!1,valueType:"dateTime"}]}export{u as getConstantColumns};
