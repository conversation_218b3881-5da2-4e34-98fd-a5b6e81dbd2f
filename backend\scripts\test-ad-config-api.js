const axios = require('axios');

// 测试广告配置API的新数据结构
async function testAdConfigAPI() {
  const baseURL = 'http://localhost:3000';
  
  try {
    console.log('🧪 测试广告配置API...');
    
    // 1. 获取广告配置列表
    console.log('\n1. 获取广告配置列表...');
    const listResponse = await axios.get(`${baseURL}/config/ads`, {
      params: { page: 1, pageSize: 10 }
    });
    
    console.log('✅ 列表获取成功');
    console.log(`📊 总数: ${listResponse.data.result.total}`);
    
    if (listResponse.data.result.list.length > 0) {
      const firstAd = listResponse.data.result.list[0];
      console.log(`📋 第一个广告: ${firstAd.title}`);
      console.log(`🖼️  图片项数量: ${firstAd.imageItems?.length || 0}`);
      
      if (firstAd.imageItems && firstAd.imageItems.length > 0) {
        console.log('🔗 图片项详情:');
        firstAd.imageItems.forEach((item, index) => {
          console.log(`  ${index + 1}. ${item.title || '无标题'} -> ${item.jumpTarget} (类型: ${item.jumpType})`);
        });
      }
      
      // 2. 根据ID获取单个广告配置
      console.log(`\n2. 获取广告配置详情 (ID: ${firstAd.id})...`);
      const detailResponse = await axios.get(`${baseURL}/config/ads/${firstAd.id}`);
      console.log('✅ 详情获取成功');
      console.log(`📝 标题: ${detailResponse.data.result.title}`);
      console.log(`🖼️  图片项: ${detailResponse.data.result.imageItems?.length || 0} 个`);
    }
    
    // 3. 根据广告标识获取配置（供APP使用）
    console.log('\n3. 根据标识获取广告配置...');
    try {
      const identifierResponse = await axios.get(`${baseURL}/config/ads/by-identifier/home_carousel_multi`);
      console.log('✅ 标识查询成功');
      console.log(`📝 标题: ${identifierResponse.data.result.title}`);
      console.log(`🖼️  图片项: ${identifierResponse.data.result.imageItems?.length || 0} 个`);
    } catch (error) {
      console.log('⚠️  标识查询失败 (可能不存在该标识)');
    }
    
    // 4. 根据广告类型获取配置列表
    console.log('\n4. 根据类型获取广告配置...');
    try {
      const typeResponse = await axios.get(`${baseURL}/config/ads/by-type/1`); // 轮播类型
      console.log('✅ 类型查询成功');
      console.log(`📊 轮播广告数量: ${typeResponse.data.result?.length || 0}`);
    } catch (error) {
      console.log('⚠️  类型查询失败');
    }
    
    console.log('\n🎉 API测试完成！新的 imageItems 数据结构工作正常。');
    
  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
  }
}

// 执行测试
testAdConfigAPI();
