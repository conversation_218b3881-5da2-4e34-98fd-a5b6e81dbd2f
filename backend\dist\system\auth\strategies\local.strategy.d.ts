import { Strategy } from 'passport-local';
import { SystemAuthService } from '../auth.service';
declare const SystemLocalStrategy_base: new (...args: [] | [options: import("passport-local").IStrategyOptionsWithRequest] | [options: import("passport-local").IStrategyOptions]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class SystemLocalStrategy extends SystemLocalStrategy_base {
    private authService;
    constructor(authService: SystemAuthService);
    validate(username: string, password: string): Promise<any>;
}
export {};
