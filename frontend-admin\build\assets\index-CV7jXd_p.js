import{r as s}from"./index-CHjq8S-S.js";const n="app-users",o={getUserList:t=>{const e=t?Object.fromEntries(Object.entries(t).map(([r,a])=>[r,String(a)])):void 0;return s.get(`${n}/list`,{searchParams:e}).json().then(r=>r.result)},getUserDetail:t=>s.get(`${n}/${t}`).json().then(e=>e.result),updateUserStatus:(t,e)=>s.put(`${n}/${t}/status`,{json:e}).json().then(()=>{}),updateUserTags:(t,e)=>s.put(`${n}/${t}/tags`,{json:e}).json().then(()=>{}),getInvitationRelationship:t=>s.get(`${n}/${t}/invitation-relationship`).json().then(e=>e.result),getUserRiskEvents:(t,e=1,r=20)=>{const a={page:String(e),pageSize:String(r)};return s.get(`${n}/${t}/risk-events`,{searchParams:a}).json().then(i=>i.result)},getMarketingChannels:()=>s.get(`${n}/marketing/channels`).json().then(t=>t.result),getMarketingAds:t=>{const e=t?{channelId:String(t)}:void 0;return s.get(`${n}/marketing/ads`,{searchParams:e}).json().then(r=>r.result)}};export{o as u};
