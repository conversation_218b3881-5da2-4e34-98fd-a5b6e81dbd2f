"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateApplicationDto {
    appCode;
    name;
    providerId;
    launchCode;
    categories;
    platforms;
    orientation;
    supportedVirtualCurrencies;
    rtp;
    volatility;
    maxWinMultiplier;
    iconUrl;
    posterUrl;
    status;
    tags;
    metadata;
    supplierIdentifier;
    features;
    minBet;
    maxBet;
    hasDemo;
    hasMobile;
    hasDesktop;
}
exports.CreateApplicationDto = CreateApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用简称代码', example: 'PG_FORTUNE_TIGER' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "appCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用显示名称', example: '招财进宝' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商ID', example: 1 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "providerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '启动代码', example: 'fortune-tiger' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "launchCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏分类', example: ['slot_games', 'video_slots'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateApplicationDto.prototype, "categories", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支持平台', example: ['ios', 'android', 'h5'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateApplicationDto.prototype, "platforms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '显示方向', example: 'adaptive', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "orientation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '支持虚拟货币', example: ['gold_coin', 'diamond'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateApplicationDto.prototype, "supportedVirtualCurrencies", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '返还率(%)', example: 96.81, required: false }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 2 }),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "rtp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '波动性', example: 'medium', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "volatility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最大倍数', example: 5000, required: false }),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "maxWinMultiplier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用图标URL', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "iconUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用海报URL', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "posterUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '应用状态', example: 'active', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '标签值数组', example: ['hot', 'featured'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateApplicationDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '技术扩展元数据', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateApplicationDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '供应商特定标识符', example: 'pg_68', required: false }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateApplicationDto.prototype, "supplierIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '游戏特性', example: ['free_spins', 'wild_symbols'], required: false }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateApplicationDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最小投注', example: 0.08, required: false }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 4 }),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "minBet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最大投注', example: 40, required: false }),
    (0, class_validator_1.IsNumber)({ maxDecimalPlaces: 4 }),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateApplicationDto.prototype, "maxBet", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持演示模式', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateApplicationDto.prototype, "hasDemo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持移动设备', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateApplicationDto.prototype, "hasMobile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否支持桌面设备', example: true, required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateApplicationDto.prototype, "hasDesktop", void 0);
//# sourceMappingURL=create-application.dto.js.map