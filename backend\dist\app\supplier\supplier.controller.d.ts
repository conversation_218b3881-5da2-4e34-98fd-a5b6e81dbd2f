import { SupplierService } from './supplier.service';
import { CreateProviderDto, UpdateProviderDto, CreateEnvironmentDto, UpdateEnvironmentDto, QueryProviderDto } from './dto';
export declare class SupplierController {
    private readonly supplierService;
    constructor(supplierService: SupplierService);
    createProvider(createProviderDto: CreateProviderDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").ApplicationProvider;
    }>;
    findProviders(queryDto: QueryProviderDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").ApplicationProvider[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    getSimpleProviders(): Promise<{
        code: number;
        message: string;
        result: import("../entities").ApplicationProvider[];
    }>;
    findProviderById(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").ApplicationProvider;
    }>;
    updateProvider(id: number, updateProviderDto: UpdateProviderDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").ApplicationProvider;
    }>;
    removeProvider(id: number): Promise<{
        code: number;
        message: string;
    }>;
    createEnvironment(providerId: number, createEnvironmentDto: CreateEnvironmentDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").ProviderEnvironment;
    }>;
    findEnvironmentsByProviderId(providerId: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").ProviderEnvironment[];
    }>;
    findEnvironmentById(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").ProviderEnvironment;
    }>;
    updateEnvironment(id: number, updateEnvironmentDto: UpdateEnvironmentDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").ProviderEnvironment;
    }>;
    removeEnvironment(id: number): Promise<{
        code: number;
        message: string;
    }>;
    toggleEnvironmentStatus(id: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").ProviderEnvironment;
    }>;
}
