#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/bin.mjs" "$@"
else
  exec node  "$basedir/../.pnpm/@eslint+config-inspector@1.0.2_eslint@9.23.0_jiti@2.4.2_/node_modules/@eslint/config-inspector/bin.mjs" "$@"
fi
