{"name": "react-antd-admin", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@10.6.4", "description": "React Hooks + TypeScript + Ant Design = react-antd-admin", "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/condorheroblog/react-antd-admin/blob/main/README.md", "repository": {"type": "git", "url": "git+https://github.com/condorheroblog/react-antd-admin.git"}, "bugs": {"url": "https://github.com/condorheroblog/react-antd-admin/issues"}, "imports": {"#*": "./*"}, "scripts": {"dev": "vite --host", "build": "NODE_OPTIONS=--max-old-space-size=8192 vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "analyzer": "vite-bundle-visualizer --output analyzer/stats.html", "eslint-config-inspector": "eslint-config-inspector", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepare": "simple-git-hooks", "pnpm:install": "pnpm install", "npm-check": "taze major -lrw", "test": "vitest"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@supabase/supabase-js": "^2.50.1", "@tanstack/react-query": "^5.69.0", "ahooks": "^3.8.4", "antd": "^5.24.4", "antd-img-crop": "^4.24.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.3", "i18next": "^24.2.3", "keepalive-for-react": "^4.0.2", "ky": "^1.7.5", "motion": "^12.5.0", "nprogress": "^0.2.0", "pinyin-pro": "^3.26.0", "react": "18", "react-countup": "^6.5.3", "react-dom": "18", "react-error-boundary": "^5.0.0", "react-i18next": "^15.4.1", "react-jss": "^10.10.0", "react-router": "^7.4.0", "simplebar-react": "^3.3.0", "spin-delay": "^2.0.1", "tailwind-merge": "^2.6.0", "zustand": "^5.0.3"}, "devDependencies": {"@antfu/eslint-config": "^4.10.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint-react/eslint-plugin": "^1.36.1", "@eslint/config-inspector": "^1.0.2", "@faker-js/faker": "^9.6.0", "@svgr/plugin-jsx": "^8.1.0", "@svgr/plugin-svgo": "^8.1.0", "@tanstack/react-query-devtools": "^5.69.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "code-inspector-plugin": "^0.20.6", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "happy-dom": "^17.4.4", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "simple-git-hooks": "^2.11.1", "tailwindcss": "^3.4.17", "taze": "^19.0.2", "typescript": "^5.8.2", "vite": "^6.2.3", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-checker": "^0.9.1", "vite-plugin-fake-server": "^2.2.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.0.9"}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "npx commitlint --edit"}, "lint-staged": {"*": ["eslint --fix"]}}