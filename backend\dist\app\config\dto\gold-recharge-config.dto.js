"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EffectiveGoldRechargeConfigDto = exports.GoldRechargeConfigQueryDto = exports.UpdateGoldRechargeConfigDto = exports.CreateGoldRechargeConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateGoldRechargeConfigDto {
    tierName;
    goldAmount;
    price;
    activityBonusGold;
    activityStartTime;
    activityEndTime;
    sortOrder;
    status;
}
exports.CreateGoldRechargeConfigDto = CreateGoldRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值挡位名称', example: '小额充值' }),
    (0, class_validator_1.IsNotEmpty)({ message: '挡位名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '挡位名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '挡位名称长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], CreateGoldRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币数量', example: 1000 }),
    (0, class_validator_1.IsNotEmpty)({ message: '金币数量不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '金币数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '金币数量必须大于0' }),
    __metadata("design:type", Number)
], CreateGoldRechargeConfigDto.prototype, "goldAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（元）', example: 6.00 }),
    (0, class_validator_1.IsNotEmpty)({ message: '价格不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '价格必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '价格必须大于0' }),
    __metadata("design:type", Number)
], CreateGoldRechargeConfigDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金币数量', example: 200, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活动赠送金币数量必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活动赠送金币数量不能小于0' }),
    __metadata("design:type", Number)
], CreateGoldRechargeConfigDto.prototype, "activityBonusGold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动开始时间格式不正确' }),
    __metadata("design:type", String)
], CreateGoldRechargeConfigDto.prototype, "activityStartTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动结束时间格式不正确' }),
    __metadata("design:type", String)
], CreateGoldRechargeConfigDto.prototype, "activityEndTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序顺序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序顺序不能小于0' }),
    __metadata("design:type", Number)
], CreateGoldRechargeConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateGoldRechargeConfigDto.prototype, "status", void 0);
class UpdateGoldRechargeConfigDto {
    tierName;
    goldAmount;
    price;
    activityBonusGold;
    activityStartTime;
    activityEndTime;
    sortOrder;
    status;
}
exports.UpdateGoldRechargeConfigDto = UpdateGoldRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值挡位名称', example: '小额充值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '挡位名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '挡位名称长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], UpdateGoldRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币数量', example: 1000, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '金币数量必须是数字' }),
    (0, class_validator_1.Min)(1, { message: '金币数量必须大于0' }),
    __metadata("design:type", Number)
], UpdateGoldRechargeConfigDto.prototype, "goldAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格（元）', example: 6.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '价格必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '价格必须大于0' }),
    __metadata("design:type", Number)
], UpdateGoldRechargeConfigDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金币数量', example: 200, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活动赠送金币数量必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活动赠送金币数量不能小于0' }),
    __metadata("design:type", Number)
], UpdateGoldRechargeConfigDto.prototype, "activityBonusGold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动开始时间格式不正确' }),
    __metadata("design:type", String)
], UpdateGoldRechargeConfigDto.prototype, "activityStartTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动结束时间格式不正确' }),
    __metadata("design:type", String)
], UpdateGoldRechargeConfigDto.prototype, "activityEndTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序顺序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序顺序不能小于0' }),
    __metadata("design:type", Number)
], UpdateGoldRechargeConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], UpdateGoldRechargeConfigDto.prototype, "status", void 0);
class GoldRechargeConfigQueryDto {
    status;
}
exports.GoldRechargeConfigQueryDto = GoldRechargeConfigQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], GoldRechargeConfigQueryDto.prototype, "status", void 0);
class EffectiveGoldRechargeConfigDto {
    id;
    tierName;
    goldAmount;
    price;
    effectiveGoldAmount;
    activityBonusGold;
    isActivityActive;
    activityStatusDescription;
    sortOrder;
}
exports.EffectiveGoldRechargeConfigDto = EffectiveGoldRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置ID' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '挡位名称' }),
    __metadata("design:type", String)
], EffectiveGoldRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '基础金币数量' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "goldAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '价格' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前生效的金币数量（基础+活动赠送）' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "effectiveGoldAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金币数量（仅活动期间有效）' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "activityBonusGold", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否在活动期间' }),
    __metadata("design:type", Boolean)
], EffectiveGoldRechargeConfigDto.prototype, "isActivityActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动状态描述' }),
    __metadata("design:type", String)
], EffectiveGoldRechargeConfigDto.prototype, "activityStatusDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序' }),
    __metadata("design:type", Number)
], EffectiveGoldRechargeConfigDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=gold-recharge-config.dto.js.map