const n=/^[\w-]{4,16}$/,a=/^(?:(?:\+|00)86)?1\d{10}$/;function o(r){return[{required:!0,message:r("form.username.required")},{pattern:n,message:r("form.username.invalid")}]}function u(r){return[{required:!0,message:r("form.password.required")},{pattern:/^(?=.*\d)(?=.*[a-z])[\w~!@#$%^&*+.\-]{8,16}$/i,message:r("form.password.invalid")}]}function d(r){return[{required:!0,message:r("form.mobile.required")},{pattern:a,message:r("form.mobile.invalid")}]}function m(r){return[{required:!0,message:r("form.currentPassword.required")}]}function t(r){return[{required:!0,message:r("form.newPassword.required")},{pattern:/^(?=.*\d)(?=.*[a-z])[\w~!@#$%^&*+.\-]{8,16}$/i,message:r("form.newPassword.invalid")}]}function E(r){return[{required:!0,message:r("form.confirmPassword.required")},({getFieldValue:s})=>({validator(i,e){return!e||s("newPassword")===e?Promise.resolve():Promise.reject(new Error(r("form.confirmPassword.invalid")))}})]}export{m as C,d as M,t as N,u as P,o as U,E as a};
