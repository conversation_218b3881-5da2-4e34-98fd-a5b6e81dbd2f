import { Repository } from 'typeorm';
import { Application, ApplicationProvider } from '../entities';
export interface Hub88Game {
    id: string;
    name: string;
    category: string;
    subcategory?: string;
    provider: string;
    rtp: number;
    volatility: string;
    max_win: number;
    min_bet: number;
    max_bet: number;
    features: string[];
    platforms: string[];
    languages: string[];
    currencies: string[];
    demo_available: boolean;
    mobile_optimized: boolean;
    desktop_optimized: boolean;
    icon_url?: string;
    background_url?: string;
    launch_url: string;
    status: string;
    created_at: string;
    updated_at: string;
}
export interface Hub88SyncResult {
    total_games: number;
    new_games: number;
    updated_games: number;
    errors: string[];
    sync_duration: number;
}
export declare class Hub88SyncService {
    private applicationRepository;
    private providerRepository;
    private readonly logger;
    constructor(applicationRepository: Repository<Application>, providerRepository: Repository<ApplicationProvider>);
    syncGamesFromHub88(): Promise<Hub88SyncResult>;
    private processHub88Game;
    private mapHub88GameToApplication;
    private mapHub88Status;
    private generateTagsFromHub88Game;
    private fetchGamesFromHub88API;
}
