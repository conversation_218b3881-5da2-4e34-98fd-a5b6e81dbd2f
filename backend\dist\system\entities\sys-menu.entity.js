"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysMenu = void 0;
const typeorm_1 = require("typeorm");
let SysMenu = class SysMenu {
    id;
    title;
    path;
    icon;
    parentId;
    order;
    menuType;
    status;
    component;
    meta;
    permissionCode;
    buttonPermissions;
    parent;
    children;
    createTime;
    updateTime;
};
exports.SysMenu = SysMenu;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SysMenu.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], SysMenu.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], SysMenu.prototype, "path", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 100 }),
    __metadata("design:type", String)
], SysMenu.prototype, "icon", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parent_id', nullable: true }),
    __metadata("design:type", Number)
], SysMenu.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], SysMenu.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'menu_type', default: 0, comment: '菜单类型：0-父菜单，1-子菜单' }),
    __metadata("design:type", Number)
], SysMenu.prototype, "menuType", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], SysMenu.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 200 }),
    __metadata("design:type", String)
], SysMenu.prototype, "component", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'jsonb' }),
    __metadata("design:type", Object)
], SysMenu.prototype, "meta", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'permission_code', nullable: true, length: 100 }),
    __metadata("design:type", String)
], SysMenu.prototype, "permissionCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'button_permissions', nullable: true, type: 'jsonb' }),
    __metadata("design:type", Array)
], SysMenu.prototype, "buttonPermissions", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => SysMenu, (menu) => menu.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", SysMenu)
], SysMenu.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => SysMenu, (menu) => menu.parent),
    __metadata("design:type", Array)
], SysMenu.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], SysMenu.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], SysMenu.prototype, "updateTime", void 0);
exports.SysMenu = SysMenu = __decorate([
    (0, typeorm_1.Entity)('sys_menus')
], SysMenu);
//# sourceMappingURL=sys-menu.entity.js.map