import { Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
declare const SystemJwtStrategy_base: new (...args: [opt: import("passport-jwt").StrategyOptionsWithRequest] | [opt: import("passport-jwt").StrategyOptionsWithoutRequest]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class SystemJwtStrategy extends SystemJwtStrategy_base {
    private configService;
    constructor(configService: ConfigService);
    validate(payload: any): Promise<{
        userId: any;
        username: any;
        roles: any;
        isSuperAdmin: any;
        type: string;
    }>;
}
export {};
