"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromotionalPage = void 0;
const typeorm_1 = require("typeorm");
const marketing_ad_entity_1 = require("./marketing-ad.entity");
const app_user_entity_1 = require("./app-user.entity");
let PromotionalPage = class PromotionalPage {
    id;
    adId;
    templateId;
    name;
    identifier;
    contentData;
    publicUrl;
    status;
    createdBy;
    ad;
    users;
    createdAt;
    updatedAt;
};
exports.PromotionalPage = PromotionalPage;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], PromotionalPage.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ad_id' }),
    __metadata("design:type", Number)
], PromotionalPage.prototype, "adId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'template_id', nullable: true }),
    __metadata("design:type", Number)
], PromotionalPage.prototype, "templateId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], PromotionalPage.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], PromotionalPage.prototype, "identifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'content_data', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], PromotionalPage.prototype, "contentData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'public_url', length: 255, unique: true, nullable: true }),
    __metadata("design:type", String)
], PromotionalPage.prototype, "publicUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '状态：0-草稿，1-已发布，2-已归档' }),
    __metadata("design:type", Number)
], PromotionalPage.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true }),
    __metadata("design:type", Number)
], PromotionalPage.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => marketing_ad_entity_1.MarketingAd, (ad) => ad.promotionalPages),
    (0, typeorm_1.JoinColumn)({ name: 'ad_id' }),
    __metadata("design:type", marketing_ad_entity_1.MarketingAd)
], PromotionalPage.prototype, "ad", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_user_entity_1.AppUser, (user) => user.promotionalPage),
    __metadata("design:type", Array)
], PromotionalPage.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], PromotionalPage.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], PromotionalPage.prototype, "updatedAt", void 0);
exports.PromotionalPage = PromotionalPage = __decorate([
    (0, typeorm_1.Entity)('promotional_pages')
], PromotionalPage);
//# sourceMappingURL=promotional-page.entity.js.map