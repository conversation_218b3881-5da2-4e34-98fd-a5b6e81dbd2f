import{a_ as s,a$ as o}from"./antd-CXPM1OiB.js";import{R as d}from"./react-BUTTOX-3.js";import{P}from"./BaseForm-DrXILIwp.js";import{j as l}from"./index-CHjq8S-S.js";var f=["fieldProps","min","proFieldProps","max"],F=function(r,i){var e=r.fieldProps,a=r.min,p=r.proFieldProps,t=r.max,m=s(r,f);return l.jsx(P,o({valueType:"digit",fieldProps:o({min:a,max:t},e),ref:i,filedConfig:{defaultProps:{width:"100%"}},proFieldProps:p},m))},j=d.forwardRef(F);export{j as F};
