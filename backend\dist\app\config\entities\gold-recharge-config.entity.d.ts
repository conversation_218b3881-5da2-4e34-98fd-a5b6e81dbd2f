import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class GoldRechargeConfig {
    id: number;
    tierName: string;
    goldAmount: number;
    price: number;
    activityBonusGold: number;
    activityStartTime: Date;
    activityEndTime: Date;
    sortOrder: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    isActivityActive(): boolean;
    getEffectiveGoldAmount(): number;
    getActivityStatus(): {
        isActive: boolean;
        description: string;
    };
    getActivityBonusGold(): number;
}
