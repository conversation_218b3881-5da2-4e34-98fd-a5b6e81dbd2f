import { DatabaseHealthService } from './database-health.service';
export declare class DatabaseHealthController {
    private readonly databaseHealthService;
    constructor(databaseHealthService: DatabaseHealthService);
    checkHealth(): Promise<{
        status: "healthy" | "unhealthy";
        latency: number;
        connectionCount: number;
        details: any;
    }>;
    getPerformanceStats(): Promise<any>;
    testLatency(iterations?: number): Promise<{
        min: number;
        max: number;
        avg: number;
        results: number[];
    }>;
}
