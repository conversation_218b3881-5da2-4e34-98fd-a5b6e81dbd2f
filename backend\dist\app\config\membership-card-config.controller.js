"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MembershipCardConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const membership_card_config_service_1 = require("./membership-card-config.service");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
let MembershipCardConfigController = class MembershipCardConfigController {
    membershipCardConfigService;
    constructor(membershipCardConfigService) {
        this.membershipCardConfigService = membershipCardConfigService;
    }
    async create(createDto, req) {
        const result = await this.membershipCardConfigService.create(createDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll() {
        const result = await this.membershipCardConfigService.findAll();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findAllEffective() {
        const result = await this.membershipCardConfigService.findAllEffective();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getActiveActivityConfigs() {
        const result = await this.membershipCardConfigService.getActiveActivityConfigs();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.membershipCardConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateDto, req) {
        const result = await this.membershipCardConfigService.update(id, updateDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.membershipCardConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async updateActivityTime(body, req) {
        const startTime = new Date(body.startTime);
        const endTime = new Date(body.endTime);
        const result = await this.membershipCardConfigService.updateActivityTime(startTime, endTime, req.user.id);
        return {
            code: 200,
            message: result.message,
        };
    }
};
exports.MembershipCardConfigController = MembershipCardConfigController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建会员卡配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取会员卡配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('effective'),
    (0, swagger_1.ApiOperation)({ summary: '获取生效的会员卡配置（考虑活动时间）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "findAllEffective", null);
__decorate([
    (0, common_1.Get)('active-activities'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前活动中的会员卡配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "getActiveActivityConfigs", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取会员卡配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新会员卡配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除会员卡配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)('batch/activity-time'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新会员卡配置的活动时间' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], MembershipCardConfigController.prototype, "updateActivityTime", null);
exports.MembershipCardConfigController = MembershipCardConfigController = __decorate([
    (0, swagger_1.ApiTags)('会员月卡配置管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/membership-cards'),
    __metadata("design:paramtypes", [membership_card_config_service_1.MembershipCardConfigService])
], MembershipCardConfigController);
//# sourceMappingURL=membership-card-config.controller.js.map