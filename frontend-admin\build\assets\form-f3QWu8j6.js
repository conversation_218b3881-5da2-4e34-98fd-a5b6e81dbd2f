import{j as e}from"./index-CHjq8S-S.js";import{l as V,h as Y,a as b}from"./react-BUTTOX-3.js";import{g as q,u as z,c as B}from"./index-C13Ae5CQ.js";import{g as $}from"./index-CQY4kBt5.js";import{u as Q,D as i}from"./useDictionary-BdQSQyn4.js";import{av as r,ah as H,aq as v,Q as W,d as f,aj as J,ao as n,ap as l,I as m,p as j,ae as x,aw as N,q as A,u as K,ax as X,ay as Z,s as y}from"./antd-CXPM1OiB.js";import"./index-DosLe4-o.js";const{TextArea:ce}=m,{Option:o}=j;function de(){const{id:u}=V(),P=Y(),[C]=r.useForm(),[w,T]=b.useState(!1),[_,E]=b.useState(!1),[ee,R]=b.useState(null),[L,M]=b.useState([]),[p,I]=b.useState([]),d=!!u,{dictionaries:h,loading:g}=Q([i.APP_STATUS,i.GAME_STATUS,i.GAME_CATEGORY,i.GAME_TYPE,i.APP_TAG,i.GAME_FEATURE,i.APP_PLATFORM,i.DEVICE_SUPPORT,i.APP_ORIENTATION,i.VIRTUAL_CURRENCY,i.APP_VOLATILITY,i.LAUNCH_MODE,i.GAME_QUALITY]),O=async()=>{try{const s=await $();s.code===200&&M(s.result)}catch(s){console.error("获取供应商列表失败:",s)}},k=async()=>{if(u){T(!0);try{const s=await q(Number(u));if(s.code===200){const a=s.result;if(R(a),C.setFieldsValue(a),a.metadata){const c=Object.entries(a.metadata).map(([t,D])=>({key:t,value:D}));I(c)}}}catch{y.error("获取应用信息失败")}finally{T(!1)}}};b.useEffect(()=>{O(),d&&k()},[u]);const U=async s=>{console.log("表单提交的数据:",s);const a={};p.forEach(t=>{t.key&&t.value!==void 0&&(a[t.key]=t.value)});const c={...s,metadata:Object.keys(a).length>0?a:void 0};E(!0);try{let t;d&&u?(console.log("更新应用，ID:",u),t=await z(Number(u),c)):(console.log("创建新应用"),t=await B(c)),console.log("API响应:",t),t.code===200?(y.success(d?"更新成功":"创建成功"),P("/app/application")):y.error(t.message||(d?"更新失败":"创建失败"))}catch(t){console.error("保存失败:",t),y.error(d?"更新失败":"创建失败")}finally{E(!1)}},F=()=>{I([...p,{key:"",value:""}])},G=s=>{const a=p.filter((c,t)=>t!==s);I(a)},S=(s,a,c)=>{const t=[...p];t[s][a]=c,I(t)};return w?e.jsx("div",{className:"flex justify-center items-center h-96",children:e.jsx(H,{size:"large"})}):e.jsx("div",{className:"p-6",children:e.jsxs(v,{children:[e.jsx("div",{className:"mb-4",children:e.jsxs(W,{children:[e.jsx(f,{icon:e.jsx(J,{}),onClick:()=>P("/app/application"),children:"返回"}),e.jsx("span",{className:"text-lg font-medium",children:d?"编辑应用":"新增应用"})]})}),e.jsxs(r,{form:C,layout:"vertical",onFinish:U,initialValues:{status:"active",platforms:[],supportedVirtualCurrencies:[],tags:[],categories:[],features:[],hasDemo:!1,hasMobile:!0,hasDesktop:!0},children:[e.jsxs(v,{title:"核心身份",size:"small",className:"mb-4",children:[e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"应用名称",name:"name",rules:[{required:!0,message:"请输入应用名称"}],children:e.jsx(m,{placeholder:"请输入应用名称"})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"简称代码",name:"appCode",rules:[{required:!0,message:"请输入简称代码"}],children:e.jsx(m,{placeholder:"请输入简称代码",disabled:d,style:{backgroundColor:d?"#f5f5f5":void 0}})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"应用供应商",name:"providerId",rules:[{required:!0,message:"请选择应用供应商"}],children:e.jsx(j,{placeholder:"请选择应用供应商",children:L.map(s=>e.jsx(o,{value:s.id,children:s.name},s.id))})})})]}),e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"启动代码",name:"launchCode",rules:[{required:!0,message:"请输入启动代码"}],children:e.jsx(m,{placeholder:"客户端用于启动应用的唯一代码"})})}),e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"应用状态",name:"status",rules:[{required:!0,message:"请选择应用状态"}],children:e.jsx(j,{placeholder:"请选择应用状态",loading:g,children:(h[i.APP_STATUS]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value))})})})]})]}),e.jsxs(v,{title:"分类与展示",size:"small",className:"mb-4",children:[e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"游戏分类",name:"categories",tooltip:"支持多选，可选择多个分类标签",children:e.jsxs(j,{mode:"multiple",placeholder:"请选择游戏分类",allowClear:!0,loading:g,showSearch:!0,filterOption:(s,a)=>String((a==null?void 0:a.children)||"").toLowerCase().includes(s.toLowerCase()),children:[(h[i.GAME_CATEGORY]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value)),(h[i.GAME_TYPE]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value))]})})}),e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"应用标签",name:"tags",tooltip:"营销和展示相关的标签",children:e.jsx(j,{mode:"multiple",placeholder:"请选择应用标签",allowClear:!0,loading:g,showSearch:!0,filterOption:(s,a)=>String((a==null?void 0:a.children)||"").toLowerCase().includes(s.toLowerCase()),children:(h[i.APP_TAG]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value))})})})]}),e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"游戏特性",name:"features",tooltip:"游戏的技术特性和玩法特点",children:e.jsx(j,{mode:"multiple",placeholder:"请选择游戏特性",allowClear:!0,loading:g,showSearch:!0,filterOption:(s,a)=>String((a==null?void 0:a.children)||"").toLowerCase().includes(s.toLowerCase()),children:(h[i.GAME_FEATURE]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value))})})}),e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"支持平台",name:"platforms",children:e.jsx(x.Group,{children:e.jsx(n,{children:(h[i.DEVICE_SUPPORT]||[]).map(s=>e.jsx(l,{span:8,children:e.jsx(x,{value:s.value,disabled:s.disabled,children:s.label})},s.value))})})})})]}),e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"显示方向",name:"orientation",children:e.jsx(N.Group,{children:(h[i.APP_ORIENTATION]||[]).map(s=>e.jsx(N,{value:s.value,disabled:s.disabled,children:s.label},s.value))})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"支持虚拟货币",name:"supportedVirtualCurrencies",children:e.jsx(x.Group,{children:e.jsx(n,{children:(h[i.VIRTUAL_CURRENCY]||[]).map(s=>e.jsx(l,{span:12,children:e.jsx(x,{value:s.value,disabled:s.disabled,children:s.label})},s.value))})})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"设备支持",children:e.jsxs(n,{gutter:8,children:[e.jsx(l,{span:8,children:e.jsx(r.Item,{name:"hasDemo",valuePropName:"checked",noStyle:!0,children:e.jsx(x,{children:"演示模式"})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{name:"hasMobile",valuePropName:"checked",noStyle:!0,children:e.jsx(x,{children:"移动设备"})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{name:"hasDesktop",valuePropName:"checked",noStyle:!0,children:e.jsx(x,{children:"桌面设备"})})})]})})})]}),e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"应用图标",name:"iconUrl",children:e.jsx(m,{placeholder:"图标URL地址"})})}),e.jsx(l,{span:12,children:e.jsx(r.Item,{label:"应用海报",name:"posterUrl",children:e.jsx(m,{placeholder:"海报URL地址"})})})]})]}),e.jsxs(v,{title:"数值与经济模型",size:"small",className:"mb-4",children:[e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"返还率 (RTP %)",name:"rtp",rules:[{type:"number",min:0,max:100,message:"返还率必须在0-100之间"}],children:e.jsx(A,{placeholder:"请输入返还率",min:0,max:100,precision:2,style:{width:"100%"},addonAfter:"%"})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"波动性",name:"volatility",children:e.jsx(j,{placeholder:"请选择波动性",allowClear:!0,loading:g,children:(h[i.APP_VOLATILITY]||[]).map(s=>e.jsx(o,{value:s.value,disabled:s.disabled,children:s.label},s.value))})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"最大倍数",name:"maxWinMultiplier",rules:[{type:"number",min:1,message:"最大倍数必须大于0"}],children:e.jsx(A,{placeholder:"请输入最大倍数",min:1,style:{width:"100%"}})})})]}),e.jsxs(n,{gutter:24,children:[e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"最小投注",name:"minBet",rules:[{type:"number",min:0,message:"最小投注不能为负数"}],children:e.jsx(A,{placeholder:"请输入最小投注",min:0,precision:4,style:{width:"100%"}})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"最大投注",name:"maxBet",rules:[{type:"number",min:0,message:"最大投注不能为负数"}],children:e.jsx(A,{placeholder:"请输入最大投注",min:0,precision:4,style:{width:"100%"}})})}),e.jsx(l,{span:8,children:e.jsx(r.Item,{label:"供应商标识符",name:"supplierIdentifier",tooltip:"供应商特定的游戏标识符，用于API调用",children:e.jsx(m,{placeholder:"如：pg_68, hub88_123"})})})]})]}),e.jsx(v,{title:"技术扩展信息",size:"small",className:"mb-4",children:e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"技术人员可以添加自定义参数，用于存储供应商特定的技术配置"}),e.jsx(f,{type:"dashed",icon:e.jsx(K,{}),onClick:F,size:"small",children:"添加参数"})]}),p.map((s,a)=>e.jsxs(n,{gutter:16,className:"mb-2",children:[e.jsx(l,{span:8,children:e.jsx(m,{placeholder:"参数名",value:s.key,onChange:c=>S(a,"key",c.target.value)})}),e.jsx(l,{span:14,children:e.jsx(m,{placeholder:"参数值",value:s.value,onChange:c=>S(a,"value",c.target.value)})}),e.jsx(l,{span:2,children:e.jsx(f,{type:"text",danger:!0,icon:e.jsx(X,{}),onClick:()=>G(a)})})]},a)),p.length===0&&e.jsx("div",{className:"text-center text-gray-400 py-4",children:"暂无扩展参数，点击上方按钮添加"})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(f,{onClick:()=>P("/app/application"),children:"取消"}),e.jsx(f,{type:"primary",htmlType:"submit",loading:_,icon:e.jsx(Z,{}),children:d?"更新应用":"创建应用"})]})]})]})})}export{de as default};
