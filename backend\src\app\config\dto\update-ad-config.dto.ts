import { PartialType } from '@nestjs/swagger';
import { CreateAdConfigDto, CreateImageItemDto } from './create-ad-config.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsString, Min, Max, Length, IsIn, IsArray, ArrayMinSize, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AdType, JumpType } from '../entities/ad-config.entity';

export class UpdateAdConfigDto extends PartialType(CreateAdConfigDto) {
  @ApiProperty({ 
    description: '唯一字符标识，前端根据此标识渲染广告', 
    example: 'banner_home_1',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '广告标识必须是字符串' })
  @Length(1, 50, { message: '广告标识长度必须在1-50个字符之间' })
  adIdentifier?: string;

  @ApiProperty({
    description: '广告类型：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏，7-首页4宫格',
    example: 1,
    enum: AdType,
    required: false
  })
  @IsOptional()
  @IsNumber({}, { message: '广告类型必须是数字' })
  @IsIn([1, 2, 3, 4, 5, 6, 7], { message: '广告类型必须是1-7之间的数字' })
  adType?: AdType;

  @ApiProperty({ 
    description: '广告标题', 
    example: '首页轮播广告',
    required: false 
  })
  @IsOptional()
  @IsString({ message: '广告标题必须是字符串' })
  @Length(1, 100, { message: '广告标题长度必须在1-100个字符之间' })
  title?: string;

  @ApiProperty({
    description: '图片跳转项数组，每个元素包含图片URL和跳转信息',
    example: [
      {
        imageUrl: '/uploads/ads/banner1.jpg',
        jumpType: 1,
        jumpTarget: '/games/popular',
        title: '热门游戏',
        description: '查看最受欢迎的游戏'
      }
    ],
    type: [CreateImageItemDto],
    required: false
  })
  @IsOptional()
  @IsArray({ message: '图片跳转项必须是数组格式' })
  @ArrayMinSize(1, { message: '至少需要配置一个图片跳转项' })
  @ValidateNested({ each: true })
  @Type(() => CreateImageItemDto)
  imageItems?: CreateImageItemDto[];

  @ApiProperty({ 
    description: '排序，数字越小越靠前', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '排序必须是数字' })
  @Min(0, { message: '排序不能小于0' })
  @Max(9999, { message: '排序不能大于9999' })
  sortOrder?: number;

  @ApiProperty({ 
    description: '状态：1-启用，0-禁用', 
    example: 1, 
    required: false 
  })
  @IsOptional()
  @IsNumber({}, { message: '状态必须是数字' })
  @IsIn([0, 1], { message: '状态值必须是0或1' })
  status?: number;

  @ApiProperty({ 
    description: '备注说明', 
    example: '首页轮播广告示例', 
    required: false 
  })
  @IsOptional()
  @IsString({ message: '备注必须是字符串' })
  @Length(0, 500, { message: '备注长度不能超过500个字符' })
  remark?: string;
}
