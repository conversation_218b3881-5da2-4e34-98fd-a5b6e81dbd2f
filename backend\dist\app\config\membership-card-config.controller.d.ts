import { MembershipCardConfigService, CreateMembershipCardConfigDto, UpdateMembershipCardConfigDto } from './membership-card-config.service';
export declare class MembershipCardConfigController {
    private readonly membershipCardConfigService;
    constructor(membershipCardConfigService: MembershipCardConfigService);
    create(createDto: CreateMembershipCardConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").MembershipCardConfig;
    }>;
    findAll(): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            cardType: string;
            cardName: string;
            price: number;
            description: string;
            dailyGoldBase: number;
            dailyGoldActivityRatio: number;
            cashDiscountBase: number;
            cashDiscountActivity: number;
            activityStartTime: Date;
            activityEndTime: Date;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        }[];
    }>;
    findAllEffective(): Promise<{
        code: number;
        message: string;
        result: {
            id: number;
            cardType: string;
            cardName: string;
            price: number;
            description: string;
            dailyGoldBase: number;
            effectiveDailyGold: number;
            cashDiscountBase: number;
            effectiveCashDiscount: number;
            isActivityActive: boolean;
            activityStatusDescription: string;
        }[];
    }>;
    getActiveActivityConfigs(): Promise<{
        code: number;
        message: string;
        result: import("./entities").MembershipCardConfig[];
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            cardType: string;
            cardName: string;
            price: number;
            description: string;
            dailyGoldBase: number;
            dailyGoldActivityRatio: number;
            cashDiscountBase: number;
            cashDiscountActivity: number;
            activityStartTime: Date;
            activityEndTime: Date;
            status: number;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    update(id: number, updateDto: UpdateMembershipCardConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").MembershipCardConfig;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
    }>;
    updateActivityTime(body: {
        startTime: string;
        endTime: string;
    }, req: any): Promise<{
        code: number;
        message: string;
    }>;
}
