"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfig = exports.JumpType = exports.AdType = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
var AdType;
(function (AdType) {
    AdType[AdType["CAROUSEL"] = 1] = "CAROUSEL";
    AdType[AdType["POPUP"] = 2] = "POPUP";
    AdType[AdType["FLOAT"] = 3] = "FLOAT";
    AdType[AdType["EMBED"] = 4] = "EMBED";
    AdType[AdType["BANNER"] = 5] = "BANNER";
    AdType[AdType["INTERSTITIAL"] = 6] = "INTERSTITIAL";
    AdType[AdType["HOME_GRID_4"] = 7] = "HOME_GRID_4";
})(AdType || (exports.AdType = AdType = {}));
var JumpType;
(function (JumpType) {
    JumpType[JumpType["INTERNAL_ROUTE"] = 1] = "INTERNAL_ROUTE";
    JumpType[JumpType["IFRAME_PAGE"] = 2] = "IFRAME_PAGE";
})(JumpType || (exports.JumpType = JumpType = {}));
let AdConfig = class AdConfig {
    id;
    adIdentifier;
    adType;
    title;
    images;
    jumpType;
    jumpTarget;
    sortOrder;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    getAdTypeName() {
        const typeNames = {
            [AdType.CAROUSEL]: '轮播',
            [AdType.POPUP]: '弹窗广告',
            [AdType.FLOAT]: '浮点弹窗',
            [AdType.EMBED]: '嵌入广告',
            [AdType.BANNER]: 'Banner',
            [AdType.INTERSTITIAL]: '插屏广告',
            [AdType.HOME_GRID_4]: '首页4宫格'
        };
        return typeNames[this.adType] || '未知类型';
    }
    getJumpTypeName() {
        const jumpTypeNames = {
            [JumpType.INTERNAL_ROUTE]: '内部路由',
            [JumpType.IFRAME_PAGE]: 'iframe页面'
        };
        return jumpTypeNames[this.jumpType] || '未知类型';
    }
    isEnabled() {
        return this.status === 1;
    }
    isCarouselType() {
        return this.adType === AdType.CAROUSEL;
    }
    isHomeGrid4Type() {
        return this.adType === AdType.HOME_GRID_4;
    }
    getImageCount() {
        return Array.isArray(this.images) ? this.images.length : 0;
    }
    getFirstImage() {
        return Array.isArray(this.images) && this.images.length > 0 ? this.images[0] : null;
    }
    validateImageCount() {
        const imageCount = this.getImageCount();
        switch (this.adType) {
            case AdType.CAROUSEL:
                if (imageCount < 2) {
                    return { valid: false, message: `轮播广告至少需要2张图片，当前只有${imageCount}张` };
                }
                break;
            case AdType.HOME_GRID_4:
                if (imageCount !== 4) {
                    return { valid: false, message: `首页4宫格广告必须上传4张图片，当前有${imageCount}张` };
                }
                break;
            default:
                if (imageCount === 0) {
                    return { valid: false, message: '至少需要上传一张图片' };
                }
                break;
        }
        return { valid: true };
    }
};
exports.AdConfig = AdConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AdConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ad_identifier',
        length: 50,
        unique: true,
        comment: '唯一字符标识，前端根据此标识渲染广告'
    }),
    __metadata("design:type", String)
], AdConfig.prototype, "adIdentifier", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ad_type',
        type: 'int',
        comment: '广告类型：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏'
    }),
    __metadata("design:type", Number)
], AdConfig.prototype, "adType", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100, comment: '广告标题' }),
    __metadata("design:type", String)
], AdConfig.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'images',
        type: 'jsonb',
        comment: '图片URL数组，JSON格式存储多张图片'
    }),
    __metadata("design:type", Array)
], AdConfig.prototype, "images", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'jump_type',
        type: 'int',
        comment: '跳转类型：1-内部路由，2-iframe页面'
    }),
    __metadata("design:type", Number)
], AdConfig.prototype, "jumpType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'jump_target',
        length: 500,
        comment: '跳转目标（路由路径或URL）'
    }),
    __metadata("design:type", String)
], AdConfig.prototype, "jumpTarget", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'sort_order',
        default: 0,
        comment: '排序，数字越小越靠前'
    }),
    __metadata("design:type", Number)
], AdConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AdConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '备注说明' }),
    __metadata("design:type", String)
], AdConfig.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], AdConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], AdConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AdConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AdConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AdConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AdConfig.prototype, "updater", void 0);
exports.AdConfig = AdConfig = __decorate([
    (0, typeorm_1.Entity)('ad_configs')
], AdConfig);
//# sourceMappingURL=ad-config.entity.js.map