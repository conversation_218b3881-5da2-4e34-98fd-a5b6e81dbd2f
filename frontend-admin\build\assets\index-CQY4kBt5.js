import{r}from"./index-CHjq8S-S.js";function o(e){return r.post("suppliers/providers",{json:e}).json()}function p(){return r.get("suppliers/providers/simple").json()}function a(e){const n=e?Object.fromEntries(Object.entries(e).map(([t,s])=>[t,String(s)])):void 0;return r.get("suppliers/providers",{searchParams:n}).json()}function u(e){return r.get(`suppliers/providers/${e}`).json()}function c(e,n){return r.patch(`suppliers/providers/${e}`,{json:n}).json()}function d(e){return r.delete(`suppliers/providers/${e}`).json()}function v(e,n){return r.post(`suppliers/providers/${e}/environments`,{json:n}).json()}function g(e){return r.get(`suppliers/providers/${e}/environments`).json()}function l(e,n){return r.patch(`suppliers/environments/${e}`,{json:n}).json()}function m(e){return r.delete(`suppliers/environments/${e}`).json()}function f(e){return r.patch(`suppliers/environments/${e}/toggle-status`).json()}function j(e){return{active:"激活",inactive:"停用",testing:"测试中",suspended:"暂停"}[e]||e}function P(e){return{api_integration:"API集成",iframe_integration:"iframe集成",redirect_integration:"重定向集成"}[e]||e}function M(e){return{ggr_share:"GGR分成",revenue_share:"收入分成",fixed_fee:"固定费用",cpa:"CPA模式"}[e]||e}function $(e){return{staging:"测试环境",production:"生产环境"}[e]||e}export{u as a,g as b,l as c,v as d,$ as e,m as f,p as g,o as h,P as i,M as j,a as k,j as l,d as m,f as t,c as u};
