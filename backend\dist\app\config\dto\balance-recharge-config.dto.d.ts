export declare class CreateBalanceRechargeConfigDto {
    tierName: string;
    rechargeAmount: number;
    activityBonusAmount?: number;
    activityStartTime?: string;
    activityEndTime?: string;
    sortOrder?: number;
    status?: number;
}
export declare class UpdateBalanceRechargeConfigDto {
    tierName?: string;
    rechargeAmount?: number;
    activityBonusAmount?: number;
    activityStartTime?: string;
    activityEndTime?: string;
    sortOrder?: number;
    status?: number;
}
export declare class BalanceRechargeConfigQueryDto {
    status?: number;
}
export declare class UpdateBalanceRechargeLimitDto {
    limitName?: string;
    minAmount: number;
    maxAmount: number;
    remark?: string;
}
export declare class EffectiveBalanceRechargeConfigDto {
    id: number;
    tierName: string;
    rechargeAmount: number;
    effectiveRechargeAmount: number;
    activityBonusAmount: number;
    isActivityActive: boolean;
    activityStatusDescription: string;
    sortOrder: number;
}
