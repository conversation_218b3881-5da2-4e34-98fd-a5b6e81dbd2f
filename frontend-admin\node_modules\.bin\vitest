#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" "$@"
fi
