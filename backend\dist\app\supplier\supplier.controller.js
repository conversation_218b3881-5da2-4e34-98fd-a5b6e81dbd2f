"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplierController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const supplier_service_1 = require("./supplier.service");
const dto_1 = require("./dto");
let SupplierController = class SupplierController {
    supplierService;
    constructor(supplierService) {
        this.supplierService = supplierService;
    }
    async createProvider(createProviderDto) {
        const result = await this.supplierService.createProvider(createProviderDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findProviders(queryDto) {
        const result = await this.supplierService.findProviders(queryDto);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getSimpleProviders() {
        const providers = await this.supplierService.findSimpleProviders();
        return {
            code: 200,
            message: '获取成功',
            result: providers,
        };
    }
    async findProviderById(id) {
        const result = await this.supplierService.findProviderById(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateProvider(id, updateProviderDto) {
        const result = await this.supplierService.updateProvider(id, updateProviderDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async removeProvider(id) {
        await this.supplierService.removeProvider(id);
        return {
            code: 200,
            message: '删除成功',
        };
    }
    async createEnvironment(providerId, createEnvironmentDto) {
        const result = await this.supplierService.createEnvironment(providerId, createEnvironmentDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findEnvironmentsByProviderId(providerId) {
        const result = await this.supplierService.findEnvironmentsByProviderId(providerId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findEnvironmentById(id) {
        const result = await this.supplierService.findEnvironmentById(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateEnvironment(id, updateEnvironmentDto) {
        const result = await this.supplierService.updateEnvironment(id, updateEnvironmentDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async removeEnvironment(id) {
        await this.supplierService.removeEnvironment(id);
        return {
            code: 200,
            message: '删除成功',
        };
    }
    async toggleEnvironmentStatus(id) {
        const result = await this.supplierService.toggleEnvironmentStatus(id);
        return {
            code: 200,
            message: '切换成功',
            result,
        };
    }
};
exports.SupplierController = SupplierController;
__decorate([
    (0, common_1.Post)('providers'),
    (0, swagger_1.ApiOperation)({ summary: '创建合作商' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '供应商代码已存在' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateProviderDto]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "createProvider", null);
__decorate([
    (0, common_1.Get)('providers'),
    (0, swagger_1.ApiOperation)({ summary: '获取合作商列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryProviderDto]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "findProviders", null);
__decorate([
    (0, common_1.Get)('providers/simple'),
    (0, swagger_1.ApiOperation)({ summary: '获取供应商简化列表（用于下拉选择）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '获取成功' },
                result: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            name: { type: 'string', example: 'PG Soft' },
                            providerCode: { type: 'string', example: 'PG_SOFT' },
                            status: { type: 'string', example: 'active' },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "getSimpleProviders", null);
__decorate([
    (0, common_1.Get)('providers/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取合作商详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '合作商ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '合作商不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "findProviderById", null);
__decorate([
    (0, common_1.Patch)('providers/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新合作商信息' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '合作商ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '合作商不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '供应商代码已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateProviderDto]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "updateProvider", null);
__decorate([
    (0, common_1.Delete)('providers/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除合作商' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '合作商ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '合作商不存在' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "removeProvider", null);
__decorate([
    (0, common_1.Post)('providers/:providerId/environments'),
    (0, swagger_1.ApiOperation)({ summary: '为合作商创建环境配置' }),
    (0, swagger_1.ApiParam)({ name: 'providerId', description: '合作商ID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '合作商不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '环境配置已存在' }),
    __param(0, (0, common_1.Param)('providerId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.CreateEnvironmentDto]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "createEnvironment", null);
__decorate([
    (0, common_1.Get)('providers/:providerId/environments'),
    (0, swagger_1.ApiOperation)({ summary: '获取合作商的环境配置列表' }),
    (0, swagger_1.ApiParam)({ name: 'providerId', description: '合作商ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '合作商不存在' }),
    __param(0, (0, common_1.Param)('providerId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "findEnvironmentsByProviderId", null);
__decorate([
    (0, common_1.Get)('environments/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取环境配置详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '环境配置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '环境配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "findEnvironmentById", null);
__decorate([
    (0, common_1.Patch)('environments/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新环境配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '环境配置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '环境配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateEnvironmentDto]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "updateEnvironment", null);
__decorate([
    (0, common_1.Delete)('environments/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除环境配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '环境配置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '环境配置不存在' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "removeEnvironment", null);
__decorate([
    (0, common_1.Patch)('environments/:id/toggle-status'),
    (0, swagger_1.ApiOperation)({ summary: '切换环境激活状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '环境配置ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '环境配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], SupplierController.prototype, "toggleEnvironmentStatus", null);
exports.SupplierController = SupplierController = __decorate([
    (0, swagger_1.ApiTags)('合作商管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('suppliers'),
    __metadata("design:paramtypes", [supplier_service_1.SupplierService])
], SupplierController);
//# sourceMappingURL=supplier.controller.js.map