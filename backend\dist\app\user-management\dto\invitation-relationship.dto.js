"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvitationRelationshipDto = exports.InvitationUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class InvitationUserDto {
    id;
    uid;
    username;
    nickname;
    avatar;
    status;
    createTime;
    lastLoginTime;
}
exports.InvitationUserDto = InvitationUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], InvitationUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '对外展示的用户ID' }),
    __metadata("design:type", Number)
], InvitationUserDto.prototype, "uid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名' }),
    __metadata("design:type", String)
], InvitationUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '昵称' }),
    __metadata("design:type", String)
], InvitationUserDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像URL' }),
    __metadata("design:type", String)
], InvitationUserDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户状态：0-正常，1-已封禁，2-已注销' }),
    __metadata("design:type", Number)
], InvitationUserDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册时间' }),
    __metadata("design:type", Date)
], InvitationUserDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    __metadata("design:type", Date)
], InvitationUserDto.prototype, "lastLoginTime", void 0);
class InvitationRelationshipDto {
    inviter;
    invitees;
    invitationPath;
    invitationLevel;
    totalInvitees;
    directInvitees;
    indirectInvitees;
}
exports.InvitationRelationshipDto = InvitationRelationshipDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请人信息', required: false }),
    __metadata("design:type", InvitationUserDto)
], InvitationRelationshipDto.prototype, "inviter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '被邀请人列表', type: [InvitationUserDto] }),
    __metadata("design:type", Array)
], InvitationRelationshipDto.prototype, "invitees", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请路径' }),
    __metadata("design:type", String)
], InvitationRelationshipDto.prototype, "invitationPath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请层级' }),
    __metadata("design:type", Number)
], InvitationRelationshipDto.prototype, "invitationLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '总邀请人数' }),
    __metadata("design:type", Number)
], InvitationRelationshipDto.prototype, "totalInvitees", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '直接邀请人数' }),
    __metadata("design:type", Number)
], InvitationRelationshipDto.prototype, "directInvitees", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '间接邀请人数' }),
    __metadata("design:type", Number)
], InvitationRelationshipDto.prototype, "indirectInvitees", void 0);
//# sourceMappingURL=invitation-relationship.dto.js.map