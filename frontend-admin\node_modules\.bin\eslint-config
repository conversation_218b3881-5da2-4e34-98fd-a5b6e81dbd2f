#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/eslint-config/bin/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/eslint-config/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/eslint-config/bin/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/eslint-config/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules/@antfu/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/@antfu+eslint-config@4.11.0_2ddbbe799ae15071723d8f769d1cb77e/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@antfu/eslint-config/bin/index.js" "$@"
else
  exec node  "$basedir/../@antfu/eslint-config/bin/index.js" "$@"
fi
