const { Client } = require('pg');

// 数据库连接配置
const dbConfig = {
  host: '**************',
  port: 5435,
  database: 'inapp2',
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
};

// 检查数据库表结构
async function checkDatabaseSchema() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('🔗 数据库连接成功');
    
    // 检查balance_recharge_limits表结构
    console.log('\n📋 检查balance_recharge_limits表结构:');
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'balance_recharge_limits' 
      ORDER BY ordinal_position;
    `);
    
    console.log('表字段:');
    tableStructure.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // 检查表中的数据
    console.log('\n📊 检查balance_recharge_limits表数据:');
    const tableData = await client.query('SELECT * FROM balance_recharge_limits;');
    console.log(`数据行数: ${tableData.rows.length}`);
    tableData.rows.forEach((row, index) => {
      console.log(`行 ${index + 1}:`, row);
    });
    
    // 检查是否有limit_name字段但实体中没有定义
    const hasLimitName = tableStructure.rows.some(row => row.column_name === 'limit_name');
    if (hasLimitName) {
      console.log('\n⚠️  发现问题: 数据库表中有limit_name字段，但实体定义中可能没有');
    }
    
  } catch (error) {
    console.error('❌ 数据库操作失败:', error.message);
  } finally {
    await client.end();
  }
}

// 运行检查
checkDatabaseSchema().catch(console.error);
