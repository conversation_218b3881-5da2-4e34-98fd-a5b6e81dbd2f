import { AppUser } from './app-user.entity';
export declare enum CashTransactionStatus {
    INCOME = 1,
    EXPENSE = 2
}
export declare enum CashTransactionType {
    BET = 1,
    WIN = 2,
    DEPOSIT = 3,
    WITHDRAW = 4,
    GOLD_EXCHANGE = 5,
    RECHARGE_EXCHANGE = 6
}
export declare class CashTransaction {
    id: number;
    userId: number;
    uid: number;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    status: CashTransactionStatus;
    transactionType: CashTransactionType;
    orderId: string;
    description: string;
    remark: string;
    operatorId: number;
    createTime: Date;
    updateTime: Date;
    user: AppUser;
}
export declare const CashTransactionTypeLabels: {
    1: string;
    2: string;
    3: string;
    4: string;
    5: string;
    6: string;
};
export declare const CashTransactionStatusLabels: {
    1: string;
    2: string;
};
