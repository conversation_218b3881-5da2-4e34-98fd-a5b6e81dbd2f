"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoldRechargeConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
let GoldRechargeConfig = class GoldRechargeConfig {
    id;
    tierName;
    goldAmount;
    price;
    activityBonusGold;
    activityStartTime;
    activityEndTime;
    sortOrder;
    status;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    isActivityActive() {
        const now = new Date();
        if (!this.activityStartTime || !this.activityEndTime) {
            return false;
        }
        return now >= this.activityStartTime && now <= this.activityEndTime;
    }
    getEffectiveGoldAmount() {
        if (this.isActivityActive()) {
            return this.goldAmount + this.activityBonusGold;
        }
        return this.goldAmount;
    }
    getActivityStatus() {
        const now = new Date();
        if (!this.activityStartTime || !this.activityEndTime) {
            return { isActive: false, description: '暂无活动' };
        }
        if (now < this.activityStartTime) {
            return {
                isActive: false,
                description: `活动未开始，将于 ${this.activityStartTime.toLocaleString()} 开始`
            };
        }
        if (now > this.activityEndTime) {
            return {
                isActive: false,
                description: `活动已结束，于 ${this.activityEndTime.toLocaleString()} 结束`
            };
        }
        return {
            isActive: true,
            description: `活动进行中，将于 ${this.activityEndTime.toLocaleString()} 结束`
        };
    }
    getActivityBonusGold() {
        return this.isActivityActive() ? this.activityBonusGold : 0;
    }
};
exports.GoldRechargeConfig = GoldRechargeConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'tier_name', length: 100, comment: '充值挡位名称' }),
    __metadata("design:type", String)
], GoldRechargeConfig.prototype, "tierName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'gold_amount',
        type: 'bigint',
        comment: '金币数量'
    }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "goldAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'decimal',
        precision: 10,
        scale: 2,
        comment: '价格（元）'
    }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activity_bonus_gold',
        type: 'bigint',
        default: 0,
        comment: '活动期间赠送的金币数量'
    }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "activityBonusGold", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activity_start_time',
        type: 'timestamp with time zone',
        nullable: true,
        comment: '活动开始时间'
    }),
    __metadata("design:type", Date)
], GoldRechargeConfig.prototype, "activityStartTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'activity_end_time',
        type: 'timestamp with time zone',
        nullable: true,
        comment: '活动结束时间'
    }),
    __metadata("design:type", Date)
], GoldRechargeConfig.prototype, "activityEndTime", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'sort_order',
        default: 0,
        comment: '排序顺序，数字越小越靠前'
    }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], GoldRechargeConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], GoldRechargeConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], GoldRechargeConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], GoldRechargeConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], GoldRechargeConfig.prototype, "updater", void 0);
exports.GoldRechargeConfig = GoldRechargeConfig = __decorate([
    (0, typeorm_1.Entity)('gold_recharge_configs')
], GoldRechargeConfig);
//# sourceMappingURL=gold-recharge-config.entity.js.map