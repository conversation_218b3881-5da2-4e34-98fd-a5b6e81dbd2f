"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ad_config_service_1 = require("./ad-config.service");
const create_ad_config_dto_1 = require("./dto/create-ad-config.dto");
const update_ad_config_dto_1 = require("./dto/update-ad-config.dto");
const ad_config_query_dto_1 = require("./dto/ad-config-query.dto");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const ad_config_entity_1 = require("./entities/ad-config.entity");
let AdConfigController = class AdConfigController {
    adConfigService;
    constructor(adConfigService) {
        this.adConfigService = adConfigService;
    }
    async create(createAdConfigDto, req) {
        const result = await this.adConfigService.create(createAdConfigDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(query) {
        const result = await this.adConfigService.findAll(query);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getAdTypeStats() {
        const result = await this.adConfigService.getAdTypeStats();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findByType(adType) {
        const result = await this.adConfigService.findByType(adType);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findByIdentifier(adIdentifier) {
        const result = await this.adConfigService.findByIdentifier(adIdentifier);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.adConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateAdConfigDto, req) {
        const result = await this.adConfigService.update(id, updateAdConfigDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.adConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async toggleStatus(id, req) {
        const result = await this.adConfigService.toggleStatus(id, req.user.id);
        return {
            code: 200,
            message: result.message,
            result,
        };
    }
    async updateSortOrder(updates, req) {
        const result = await this.adConfigService.updateSortOrder(updates, req.user.id);
        return {
            code: 200,
            message: result.message,
        };
    }
};
exports.AdConfigController = AdConfigController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建广告配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '广告标识已存在' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_ad_config_dto_1.CreateAdConfigDto, Object]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取广告配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [ad_config_query_dto_1.AdConfigQueryDto]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取广告类型统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "getAdTypeStats", null);
__decorate([
    (0, common_1.Get)('by-type/:adType'),
    (0, swagger_1.ApiOperation)({ summary: '根据广告类型获取广告列表（供前端APP使用）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('adType', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)('by-identifier/:adIdentifier'),
    (0, swagger_1.ApiOperation)({ summary: '根据广告标识获取广告配置（供前端APP使用）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告配置不存在或已禁用' }),
    __param(0, (0, common_1.Param)('adIdentifier')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "findByIdentifier", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取广告配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新广告配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '广告标识已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_ad_config_dto_1.UpdateAdConfigDto, Object]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除广告配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/toggle-status'),
    (0, swagger_1.ApiOperation)({ summary: '切换广告配置状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '广告配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "toggleStatus", null);
__decorate([
    (0, common_1.Post)('batch-sort'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新排序' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '排序更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, Object]),
    __metadata("design:returntype", Promise)
], AdConfigController.prototype, "updateSortOrder", null);
exports.AdConfigController = AdConfigController = __decorate([
    (0, swagger_1.ApiTags)('广告配置管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/ads'),
    __metadata("design:paramtypes", [ad_config_service_1.AdConfigService])
], AdConfigController);
//# sourceMappingURL=ad-config.controller.js.map