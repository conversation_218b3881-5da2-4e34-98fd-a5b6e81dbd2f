import { Repository } from 'typeorm';
import { SysMenu } from '../entities/sys-menu.entity';
import { CreateMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
export declare class SystemMenuService {
    private menuRepository;
    constructor(menuRepository: Repository<SysMenu>);
    create(createMenuDto: CreateMenuDto): Promise<SysMenu>;
    findAll(): Promise<any[]>;
    findAllFlat(): Promise<{
        name: string;
        currentActiveMenu: string;
        iframeLink: string;
        keepAlive: number;
        externalLink: string;
        hideInMenu: number;
        ignoreAccess: number;
        id: number;
        title: string;
        path: string;
        icon: string;
        parentId: number;
        order: number;
        menuType: number;
        status: number;
        component: string;
        meta: any;
        permissionCode: string;
        buttonPermissions: string[];
        parent: SysMenu;
        children: SysMenu[];
        createTime: Date;
        updateTime: Date;
    }[]>;
    findOne(id: number): Promise<SysMenu>;
    update(id: number, updateMenuDto: UpdateMenuDto): Promise<SysMenu>;
    remove(id: number): Promise<{
        message: string;
    }>;
    private buildMenuTree;
    getMenusByRoles(roleCodes: string[], isSuperAdmin?: boolean, userPermissions?: string[]): Promise<any[]>;
    getUserMenus(roleCodes: string[], isSuperAdmin?: boolean, userPermissions?: string[]): Promise<any[]>;
    private filterMenuPermissions;
    private convertToRoutes;
}
