"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vip_config_entity_1 = require("./entities/vip-config.entity");
const app_user_entity_1 = require("../entities/app-user.entity");
const cash_transaction_entity_1 = require("../entities/cash-transaction.entity");
const gold_transaction_entity_1 = require("../entities/gold-transaction.entity");
let VipConfigService = class VipConfigService {
    vipConfigRepository;
    appUserRepository;
    cashTransactionRepository;
    goldTransactionRepository;
    dataSource;
    constructor(vipConfigRepository, appUserRepository, cashTransactionRepository, goldTransactionRepository, dataSource) {
        this.vipConfigRepository = vipConfigRepository;
        this.appUserRepository = appUserRepository;
        this.cashTransactionRepository = cashTransactionRepository;
        this.goldTransactionRepository = goldTransactionRepository;
        this.dataSource = dataSource;
    }
    async create(createVipConfigDto, userId) {
        const existingConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: createVipConfigDto.vipLevel },
        });
        if (existingConfig) {
            throw new common_1.ConflictException(`VIP等级 ${createVipConfigDto.vipLevel} 已存在`);
        }
        await this.validatePointsIncrement(createVipConfigDto.vipLevel, createVipConfigDto.requiredPoints);
        const vipConfig = this.vipConfigRepository.create({
            ...createVipConfigDto,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.vipConfigRepository.save(vipConfig);
    }
    async findAll(query) {
        const { page = 1, pageSize = 10, vipLevel, status, levelName } = query;
        const queryBuilder = this.vipConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater');
        if (vipLevel !== undefined) {
            queryBuilder.andWhere('config.vipLevel = :vipLevel', { vipLevel });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('config.status = :status', { status });
        }
        if (levelName) {
            queryBuilder.andWhere('config.levelName LIKE :levelName', { levelName: `%${levelName}%` });
        }
        queryBuilder.orderBy('config.vipLevel', 'ASC');
        const skip = (page - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        const [list, total] = await queryBuilder.getManyAndCount();
        return {
            list: list.map(config => ({
                ...config,
                creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
                updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
            })),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    async findOne(id) {
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        return {
            ...vipConfig,
            creator: vipConfig.creator ? { id: vipConfig.creator.id, username: vipConfig.creator.username } : null,
            updater: vipConfig.updater ? { id: vipConfig.updater.id, username: vipConfig.updater.username } : null,
        };
    }
    async update(id, updateVipConfigDto, userId) {
        const vipConfig = await this.vipConfigRepository.findOne({ where: { id } });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        if (updateVipConfigDto.vipLevel !== undefined && updateVipConfigDto.vipLevel !== vipConfig.vipLevel) {
            const existingConfig = await this.vipConfigRepository.findOne({
                where: { vipLevel: updateVipConfigDto.vipLevel },
            });
            if (existingConfig) {
                throw new common_1.ConflictException(`VIP等级 ${updateVipConfigDto.vipLevel} 已存在`);
            }
        }
        if (updateVipConfigDto.requiredPoints !== undefined || updateVipConfigDto.vipLevel !== undefined) {
            const newLevel = updateVipConfigDto.vipLevel ?? vipConfig.vipLevel;
            const newPoints = updateVipConfigDto.requiredPoints ?? vipConfig.requiredPoints;
            await this.validatePointsIncrement(newLevel, newPoints, id);
        }
        Object.assign(vipConfig, updateVipConfigDto, { updatedBy: userId });
        const savedConfig = await this.vipConfigRepository.save(vipConfig);
        return savedConfig;
    }
    async remove(id) {
        const vipConfig = await this.vipConfigRepository.findOne({ where: { id } });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP配置不存在');
        }
        const userCount = await this.appUserRepository.count({
            where: { vipLevel: vipConfig.vipLevel },
        });
        if (userCount > 0) {
            throw new common_1.ConflictException(`该VIP等级已被 ${userCount} 个用户使用，无法删除`);
        }
        await this.vipConfigRepository.remove(vipConfig);
        return { message: '删除成功' };
    }
    async validatePointsIncrement(vipLevel, requiredPoints, excludeId) {
        if (vipLevel === 0)
            return;
        const queryBuilder = this.vipConfigRepository.createQueryBuilder('config');
        if (excludeId) {
            queryBuilder.where('config.id != :excludeId', { excludeId });
        }
        const prevConfig = await queryBuilder
            .andWhere('config.vipLevel = :prevLevel', { prevLevel: vipLevel - 1 })
            .andWhere('config.status = 1')
            .getOne();
        if (prevConfig && requiredPoints <= prevConfig.requiredPoints) {
            throw new common_1.BadRequestException(`VIP等级${vipLevel}的所需积分(${requiredPoints})必须大于前一等级的积分(${prevConfig.requiredPoints})`);
        }
        const nextConfig = await queryBuilder
            .andWhere('config.vipLevel = :nextLevel', { nextLevel: vipLevel + 1 })
            .andWhere('config.status = 1')
            .getOne();
        if (nextConfig && requiredPoints >= nextConfig.requiredPoints) {
            throw new common_1.BadRequestException(`VIP等级${vipLevel}的所需积分(${requiredPoints})必须小于下一等级的积分(${nextConfig.requiredPoints})`);
        }
    }
    async calculateUserPoints(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('用户VIP等级配置不存在');
        }
        const totalCashSpent = await this.cashTransactionRepository
            .createQueryBuilder('transaction')
            .select('COALESCE(SUM(transaction.amount), 0)', 'total')
            .where('transaction.userId = :userId', { userId })
            .andWhere('transaction.status = 2')
            .getRawOne();
        const totalGoldSpent = await this.goldTransactionRepository
            .createQueryBuilder('transaction')
            .select('COALESCE(SUM(transaction.amount), 0)', 'total')
            .where('transaction.userId = :userId', { userId })
            .andWhere('transaction.status = 2')
            .getRawOne();
        const balancePoints = user.withdrawableBalance * vipConfig.balanceRatio;
        const cashPoints = parseFloat(totalCashSpent.total) * vipConfig.cashRatio;
        const goldPoints = parseFloat(totalGoldSpent.total) * vipConfig.goldRatio;
        const totalPoints = Math.floor(balancePoints + cashPoints + goldPoints);
        return {
            totalPoints,
            breakdown: {
                withdrawableBalance: user.withdrawableBalance,
                balanceRatio: vipConfig.balanceRatio,
                balancePoints: Math.floor(balancePoints),
                totalCashSpent: parseFloat(totalCashSpent.total),
                cashRatio: vipConfig.cashRatio,
                cashPoints: Math.floor(cashPoints),
                totalGoldSpent: parseFloat(totalGoldSpent.total),
                goldRatio: vipConfig.goldRatio,
                goldPoints: Math.floor(goldPoints),
                dailyGoldReward: vipConfig.dailyGoldReward,
            },
        };
    }
    async updateUserVipLevel(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const { totalPoints } = await this.calculateUserPoints(userId);
        const oldLevel = user.vipLevel;
        const vipConfigs = await this.vipConfigRepository.find({
            where: { status: 1 },
            order: { vipLevel: 'DESC' },
        });
        let newLevel = 0;
        for (const config of vipConfigs) {
            if (totalPoints >= config.requiredPoints) {
                newLevel = config.vipLevel;
                break;
            }
        }
        if (newLevel !== oldLevel) {
            await this.appUserRepository.update(userId, {
                vipLevel: newLevel,
                vipExp: totalPoints,
            });
        }
        else {
            await this.appUserRepository.update(userId, {
                vipExp: totalPoints,
            });
        }
        return { oldLevel, newLevel, points: totalPoints };
    }
    async recalculateAllUserVipLevels() {
        const users = await this.appUserRepository.find();
        let processed = 0;
        let upgraded = 0;
        for (const user of users) {
            try {
                const result = await this.updateUserVipLevel(user.id);
                processed++;
                if (result.oldLevel !== result.newLevel) {
                    upgraded++;
                }
            }
            catch (error) {
                console.error(`更新用户 ${user.id} VIP等级失败:`, error.message);
            }
        }
        return { processed, upgraded };
    }
    async getActiveVipLevels() {
        return await this.vipConfigRepository.find({
            where: { status: 1 },
            order: { vipLevel: 'ASC' },
            select: ['id', 'vipLevel', 'levelName', 'requiredPoints'],
        });
    }
};
exports.VipConfigService = VipConfigService;
exports.VipConfigService = VipConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vip_config_entity_1.VipConfig)),
    __param(1, (0, typeorm_1.InjectRepository)(app_user_entity_1.AppUser)),
    __param(2, (0, typeorm_1.InjectRepository)(cash_transaction_entity_1.CashTransaction)),
    __param(3, (0, typeorm_1.InjectRepository)(gold_transaction_entity_1.GoldTransaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], VipConfigService);
//# sourceMappingURL=vip-config.service.js.map