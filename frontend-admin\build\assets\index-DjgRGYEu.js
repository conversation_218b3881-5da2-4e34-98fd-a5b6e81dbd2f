import{j as t}from"./index-CHjq8S-S.js";import{a as n}from"./react-BUTTOX-3.js";import{B as A}from"./index-DDI4OfxQ.js";import{l as B,m as D}from"./index-jASoOOIw.js";import{av as s,aq as M,az as R,al as r,Q as y,d as j,ak as N,aR as p,M as q,I as g,q as l,aF as v,S as Y,s as c,an as x,aS as $,aC as G}from"./antd-CXPM1OiB.js";const L=()=>{const[T,d]=n.useState([]),[C,h]=n.useState(!1),[b,o]=n.useState(!1),[f,I]=n.useState(null),[m]=s.useForm(),u=async()=>{h(!0);try{const e=await B();e.code===200?e.result&&e.result.list?d(Array.isArray(e.result.list)?e.result.list:[]):Array.isArray(e.result)?d(e.result):d([]):c.error(e.message||"获取会员卡配置失败")}catch(e){c.error("获取会员卡配置失败"),console.error("获取会员卡配置失败:",e)}finally{h(!1)}},S=async(e,i)=>{try{const a=await D(e,i);a.code===200?(c.success("更新成功"),o(!1),u()):c.error(a.message||"更新失败")}catch(a){c.error("更新失败"),console.error("更新会员卡配置失败:",a)}};n.useEffect(()=>{u()},[]);const k=e=>({silver:"#C0C0C0",gold:"#FFD700",diamond:"#B9F2FF",king:"#FF6B6B"})[e]||"#1890ff",w=(e,i)=>{if(!e||!i)return!1;const a=r();return a.isAfter(r(e))&&a.isBefore(r(i))},F=[{title:"会员卡类型",dataIndex:"cardType",key:"cardType",render:(e,i)=>t.jsx(x,{color:k(e),icon:t.jsx(p,{}),children:i.cardName})},{title:"价格",dataIndex:"price",key:"price",render:e=>`¥${Number(e||0).toFixed(2)}`},{title:"每日基础金币",dataIndex:"dailyGoldBase",key:"dailyGoldBase",render:e=>`${Number(e||0).toLocaleString()} 金币`},{title:"活动金币比例",dataIndex:"dailyGoldActivityRatio",key:"dailyGoldActivityRatio",render:e=>`${(Number(e||0)*100).toFixed(1)}%`},{title:"基础充值优惠",dataIndex:"cashDiscountBase",key:"cashDiscountBase",render:e=>`${(Number(e||0)*100).toFixed(1)}%`},{title:"活动充值优惠",dataIndex:"cashDiscountActivity",key:"cashDiscountActivity",render:e=>`${(Number(e||0)*100).toFixed(1)}%`},{title:"活动状态",key:"activityStatus",render:(e,i)=>{const a=w(i.activityStartTime,i.activityEndTime);return t.jsx(x,{color:a?"green":"default",icon:t.jsx($,{}),children:a?"活动中":"非活动期"})}},{title:"状态",dataIndex:"status",key:"status",render:e=>t.jsx(x,{color:e===1?"green":"red",children:e===1?"启用":"禁用"})},{title:"操作",key:"action",render:(e,i)=>t.jsx(y,{children:t.jsx(j,{type:"primary",size:"small",icon:t.jsx(G,{}),onClick:()=>{I(i),m.setFieldsValue({...i,activityStartTime:i.activityStartTime?r(i.activityStartTime):null,activityEndTime:i.activityEndTime?r(i.activityEndTime):null}),o(!0)},children:"编辑"})})}],E=async e=>{if(f){const i={...e,activityStartTime:e.activityStartTime?e.activityStartTime.toISOString():null,activityEndTime:e.activityEndTime?e.activityEndTime.toISOString():null};await S(f.id,i)}};return t.jsxs(A,{children:[t.jsx(M,{title:t.jsxs(y,{children:[t.jsx(p,{}),"会员月卡配置管理"]}),extra:t.jsx(y,{children:t.jsx(j,{icon:t.jsx(N,{}),onClick:u,children:"刷新"})}),children:t.jsx(R,{columns:F,dataSource:T,rowKey:"id",loading:C,pagination:!1,size:"middle",expandable:{expandedRowRender:e=>t.jsxs("div",{style:{padding:"16px",backgroundColor:"#fafafa"},children:[t.jsx("h4",{children:"功能介绍："}),t.jsx("p",{children:e.description}),t.jsxs("div",{style:{marginTop:16},children:[t.jsx("strong",{children:"活动时间："}),e.activityStartTime&&e.activityEndTime?t.jsxs("span",{children:[r(e.activityStartTime).format("YYYY-MM-DD HH:mm")," 至"," ",r(e.activityEndTime).format("YYYY-MM-DD HH:mm")]}):t.jsx("span",{style:{color:"#999"},children:"暂无活动"})]})]})}})}),t.jsx(q,{title:"编辑会员卡配置",open:b,onCancel:()=>o(!1),onOk:()=>m.submit(),width:800,children:t.jsxs(s,{form:m,layout:"vertical",onFinish:E,children:[t.jsx(s.Item,{label:"会员卡名称",name:"cardName",rules:[{required:!0,message:"请输入会员卡名称"}],children:t.jsx(g,{})}),t.jsx(s.Item,{label:"价格（元）",name:"price",rules:[{required:!0,message:"请输入价格"}],children:t.jsx(l,{min:0,step:.01,style:{width:"100%"}})}),t.jsx(s.Item,{label:"功能介绍",name:"description",children:t.jsx(g.TextArea,{rows:3})}),t.jsx(s.Item,{label:"每日基础金币数量",name:"dailyGoldBase",rules:[{required:!0,message:"请输入每日基础金币数量"}],children:t.jsx(l,{min:0,style:{width:"100%"}})}),t.jsx(s.Item,{label:"活动期间金币赠送比例",name:"dailyGoldActivityRatio",rules:[{required:!0,message:"请输入活动金币赠送比例"}],children:t.jsx(l,{min:0,max:10,step:.01,style:{width:"100%"}})}),t.jsx(s.Item,{label:"充值现金基础优惠比例",name:"cashDiscountBase",rules:[{required:!0,message:"请输入基础优惠比例"}],children:t.jsx(l,{min:0,max:1,step:.01,style:{width:"100%"}})}),t.jsx(s.Item,{label:"活动期间额外优惠比例",name:"cashDiscountActivity",rules:[{required:!0,message:"请输入活动优惠比例"}],children:t.jsx(l,{min:0,max:1,step:.01,style:{width:"100%"}})}),t.jsx(s.Item,{label:"活动开始时间",name:"activityStartTime",children:t.jsx(v,{showTime:!0,style:{width:"100%"}})}),t.jsx(s.Item,{label:"活动结束时间",name:"activityEndTime",children:t.jsx(v,{showTime:!0,style:{width:"100%"}})}),t.jsx(s.Item,{label:"状态",name:"status",valuePropName:"checked",getValueFromEvent:e=>e?1:0,getValueProps:e=>({checked:e===1}),children:t.jsx(Y,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})]})};export{L as default};
