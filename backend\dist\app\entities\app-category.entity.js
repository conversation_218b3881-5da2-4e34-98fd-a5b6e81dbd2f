"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppCategory = void 0;
const typeorm_1 = require("typeorm");
const app_product_entity_1 = require("./app-product.entity");
let AppCategory = class AppCategory {
    id;
    name;
    description;
    parentId;
    icon;
    image;
    order;
    status;
    parent;
    children;
    products;
    createTime;
    updateTime;
};
exports.AppCategory = AppCategory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppCategory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], AppCategory.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'text' }),
    __metadata("design:type", String)
], AppCategory.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'parent_id', nullable: true }),
    __metadata("design:type", Number)
], AppCategory.prototype, "parentId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], AppCategory.prototype, "icon", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], AppCategory.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], AppCategory.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AppCategory.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AppCategory, (category) => category.children),
    (0, typeorm_1.JoinColumn)({ name: 'parent_id' }),
    __metadata("design:type", AppCategory)
], AppCategory.prototype, "parent", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => AppCategory, (category) => category.parent),
    __metadata("design:type", Array)
], AppCategory.prototype, "children", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_product_entity_1.AppProduct, (product) => product.category),
    __metadata("design:type", Array)
], AppCategory.prototype, "products", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppCategory.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppCategory.prototype, "updateTime", void 0);
exports.AppCategory = AppCategory = __decorate([
    (0, typeorm_1.Entity)('app_categories')
], AppCategory);
//# sourceMappingURL=app-category.entity.js.map