import { Repository } from 'typeorm';
import { Application } from '../entities';
import { CreateApplicationDto } from './dto/create-application.dto';
import { UpdateApplicationDto } from './dto/update-application.dto';
import { QueryApplicationDto, ApplicationListResponse } from './dto/query-application.dto';
export declare class ApplicationService {
    private applicationRepository;
    constructor(applicationRepository: Repository<Application>);
    create(createApplicationDto: CreateApplicationDto): Promise<Application>;
    findAll(queryDto: QueryApplicationDto): Promise<ApplicationListResponse>;
    findOne(id: number): Promise<Application>;
    findByUuid(appUuid: string): Promise<Application>;
    update(id: number, updateApplicationDto: UpdateApplicationDto): Promise<Application>;
    remove(id: number): Promise<{
        message: string;
    }>;
    toggleStatus(id: number, status: string): Promise<Application>;
}
