import {
  require_react
} from "./chunk-THYVJR3I.js";
import {
  __commonJS
} from "./chunk-4B2QHNJT.js";

// node_modules/.pnpm/spin-delay@2.0.1_react@18.3.1/node_modules/spin-delay/dist/index.js
var require_dist = __commonJS({
  "node_modules/.pnpm/spin-delay@2.0.1_react@18.3.1/node_modules/spin-delay/dist/index.js"(exports, module) {
    (function(global, factory) {
      typeof exports === "object" && typeof module !== "undefined" ? factory(exports, require_react()) : typeof define === "function" && define.amd ? define(["exports", "react"], factory) : (global = global || self, factory(global.spinDelay = {}, global.react));
    })(exports, function(exports2, react) {
      const defaultOptions = {
        delay: 500,
        minDuration: 200,
        ssr: true
      };
      function useIsSSR() {
        const [isSSR, setIsSSR] = react.useState(true);
        react.useEffect(() => {
          setIsSSR(false);
        }, []);
        return isSSR;
      }
      function useSpinDelay(loading, options) {
        options = Object.assign({}, defaultOptions, options);
        const isSSR = useIsSSR() && options.ssr;
        const initialState = isSSR && loading ? "DISPLAY" : "IDLE";
        const [state, setState] = react.useState(initialState);
        const timeout = react.useRef(null);
        react.useEffect(() => {
          if (loading && (state === "IDLE" || isSSR)) {
            clearTimeout(timeout.current);
            const delay = isSSR ? 0 : options.delay;
            timeout.current = setTimeout(() => {
              if (!loading) {
                return setState("IDLE");
              }
              timeout.current = setTimeout(() => {
                setState("EXPIRE");
              }, options.minDuration);
              setState("DISPLAY");
            }, delay);
            if (!isSSR) {
              setState("DELAY");
            }
          }
          if (!loading && state !== "DISPLAY") {
            clearTimeout(timeout.current);
            setState("IDLE");
          }
        }, [loading, state, options.delay, options.minDuration, isSSR]);
        react.useEffect(() => {
          return () => clearTimeout(timeout.current);
        }, []);
        return state === "DISPLAY" || state === "EXPIRE";
      }
      exports2.defaultOptions = defaultOptions;
      exports2.useSpinDelay = useSpinDelay;
    });
  }
});
export default require_dist();
//# sourceMappingURL=spin-delay.js.map
