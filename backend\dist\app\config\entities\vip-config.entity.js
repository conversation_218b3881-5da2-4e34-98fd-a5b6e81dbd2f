"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
let VipConfig = class VipConfig {
    id;
    vipLevel;
    levelName;
    requiredPoints;
    balanceRatio;
    cashRatio;
    goldRatio;
    dailyGoldReward;
    status;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    calculateUserPoints(balance, totalCashSpent, totalGoldSpent) {
        const balancePoints = balance * this.balanceRatio;
        const cashPoints = totalCashSpent * this.cashRatio;
        const goldPoints = totalGoldSpent * this.goldRatio;
        return Math.floor(balancePoints + cashPoints + goldPoints);
    }
    isUserQualified(userPoints) {
        return userPoints >= this.requiredPoints;
    }
};
exports.VipConfig = VipConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], VipConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vip_level', unique: true, comment: 'VIP等级，从0开始，唯一' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "vipLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'level_name', length: 50, comment: '等级名称显示' }),
    __metadata("design:type", String)
], VipConfig.prototype, "levelName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'required_points', type: 'bigint', comment: '达到该等级所需的积分' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "requiredPoints", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'balance_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 0.0000,
        comment: '余额积分比例，当前余额*此比例=积分'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "balanceRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cash_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 0.0000,
        comment: '现金消耗积分比例，累计消耗现金*此比例=积分'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "cashRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'gold_ratio',
        type: 'decimal',
        precision: 10,
        scale: 4,
        default: 0.0000,
        comment: '金币消耗积分比例，累计消耗金币*此比例=积分'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "goldRatio", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'daily_gold_reward',
        type: 'bigint',
        default: 0,
        comment: '每日可领取金币数量'
    }),
    __metadata("design:type", Number)
], VipConfig.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '备注说明' }),
    __metadata("design:type", String)
], VipConfig.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], VipConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], VipConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], VipConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], VipConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], VipConfig.prototype, "updater", void 0);
exports.VipConfig = VipConfig = __decorate([
    (0, typeorm_1.Entity)('vip_configs')
], VipConfig);
//# sourceMappingURL=vip-config.entity.js.map