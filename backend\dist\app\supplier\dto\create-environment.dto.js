"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEnvironmentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateEnvironmentDto {
    environmentType;
    isActive;
    apiBaseUrl;
    apiKey;
    secretKey;
    operatorToken;
    serverIpWhitelist;
    extraConfig;
}
exports.CreateEnvironmentDto = CreateEnvironmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '环境类型',
        example: 'staging',
        enum: ['staging', 'production']
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsIn)(['staging', 'production']),
    __metadata("design:type", String)
], CreateEnvironmentDto.prototype, "environmentType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '此环境是否激活可用',
        example: true,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateEnvironmentDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '该环境的API根地址',
        example: 'https://api.staging.pgsoft.com'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUrl)(),
    (0, class_validator_1.Length)(1, 512),
    __metadata("design:type", String)
], CreateEnvironmentDto.prototype, "apiBaseUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'API Key 或类似凭证',
        example: 'pg_staging_key_123',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateEnvironmentDto.prototype, "apiKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Secret Key（应加密存储）',
        example: 'pg_staging_secret_456',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 512),
    __metadata("design:type", String)
], CreateEnvironmentDto.prototype, "secretKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '某些供应商需要的运营商令牌或特定ID',
        example: 'operator_pg_staging',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateEnvironmentDto.prototype, "operatorToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '供应商服务器出口IP白名单',
        example: ['*************', '*************'],
        type: [String],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateEnvironmentDto.prototype, "serverIpWhitelist", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '存储其他非通用技术参数的备用字段',
        example: { timeout: 30, retry_count: 3, currency: 'USD' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateEnvironmentDto.prototype, "extraConfig", void 0);
//# sourceMappingURL=create-environment.dto.js.map