# InApp2 本地开发环境配置
# Local Development Environment Configuration

# 应用配置
NODE_ENV=development
APP_PORT=3000
APP_PREFIX=api

# 本地PostgreSQL数据库配置
DB_HOST=**************
DB_PORT=5435
DB_USERNAME=user_jJSpPW
DB_PASSWORD=password_DmrhYX
DB_DATABASE=inapp2
DB_POOL_SIZE=10
DB_CONNECTION_TIMEOUT=30000
DB_QUERY_TIMEOUT=30000

# JWT配置
JWT_SECRET=your-local-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-local-refresh-secret-key-change-in-production
JWT_REFRESH_EXPIRES_IN=30d

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://localhost:3333

# 本地开发标识
ENVIRONMENT=local

# 可选：Redis配置（如果使用）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=

# 可选：文件上传配置
# UPLOAD_PATH=./uploads
# MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=debug
LOG_FILE=./logs/app.log
