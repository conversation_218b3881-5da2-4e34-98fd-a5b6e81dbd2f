"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoleController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const role_service_1 = require("./role.service");
const create_role_dto_1 = require("./dto/create-role.dto");
const update_role_dto_1 = require("./dto/update-role.dto");
const query_role_dto_1 = require("./dto/query-role.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let SystemRoleController = class SystemRoleController {
    roleService;
    constructor(roleService) {
        this.roleService = roleService;
    }
    checkSuperAdminPermission(user, operation) {
        if (!user.isSuperAdmin) {
            throw new common_1.ForbiddenException(`只有超级管理员才能${operation}角色`);
        }
    }
    async create(createRoleDto, req) {
        this.checkSuperAdminPermission(req.user, '创建');
        const result = await this.roleService.create(createRoleDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async getSimpleRoles() {
        const roles = await this.roleService.findSimpleRoles();
        return {
            code: 200,
            message: '获取成功',
            result: roles,
        };
    }
    async findAll(queryRoleDto) {
        const result = await this.roleService.findAll(queryRoleDto);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findAllSimple() {
        const result = await this.roleService.findAllSimple();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.roleService.findOne(+id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateRoleDto, req) {
        this.checkSuperAdminPermission(req.user, '更新');
        const result = await this.roleService.update(+id, updateRoleDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id, req) {
        this.checkSuperAdminPermission(req.user, '删除');
        const result = await this.roleService.remove(+id);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
};
exports.SystemRoleController = SystemRoleController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建角色' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_role_dto_1.CreateRoleDto, Object]),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色简化列表（用于下拉选择）' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        schema: {
            type: 'object',
            properties: {
                code: { type: 'number', example: 200 },
                message: { type: 'string', example: '获取成功' },
                result: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'number', example: 1 },
                            name: { type: 'string', example: '管理员' },
                            code: { type: 'string', example: 'admin' },
                            status: { type: 'number', example: 1 },
                        },
                    },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "getSimpleRoles", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_role_dto_1.QueryRoleDto]),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '获取简单角色列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "findAllSimple", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取角色详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_role_dto_1.UpdateRoleDto, Object]),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除角色' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SystemRoleController.prototype, "remove", null);
exports.SystemRoleController = SystemRoleController = __decorate([
    (0, swagger_1.ApiTags)('系统角色管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('roles'),
    __metadata("design:paramtypes", [role_service_1.SystemRoleService])
], SystemRoleController);
//# sourceMappingURL=role.controller.js.map