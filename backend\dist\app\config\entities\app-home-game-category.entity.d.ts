import { SysUser } from '../../../system/entities/sys-user.entity';
import { AppHomeConfig } from './app-home-config.entity';
import { AppHomeCategoryGame } from './app-home-category-game.entity';
export interface CategoryTitle {
    'zh-CN': string;
    'en-US': string;
    [key: string]: string;
}
export declare class AppHomeGameCategory {
    id: number;
    homeConfigId: number;
    categoryTitle: CategoryTitle;
    sortOrder: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    homeConfig: AppHomeConfig;
    categoryGames: AppHomeCategoryGame[];
    creator: SysUser;
    updater: SysUser;
    isEnabled(): boolean;
    getTitle(language?: string): string;
    getAllTitles(): CategoryTitle;
    setTitle(language: string, title: string): void;
    validateTitles(): {
        valid: boolean;
        errors: string[];
    };
    getGameCount(): number;
    hasMinimumGames(): boolean;
}
