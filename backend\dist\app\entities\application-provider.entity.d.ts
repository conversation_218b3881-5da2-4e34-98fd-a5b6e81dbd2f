import { ProviderEnvironment } from './provider-environment.entity';
export declare class ApplicationProvider {
    id: number;
    providerCode: string;
    name: string;
    status: string;
    integrationType: string;
    commercialModelType: string;
    rateValue: number;
    billingCycle: string;
    billingCurrency: string;
    minimumGuarantee: number;
    amName: string;
    amEmail: string;
    techSupportEmail: string;
    financeEmail: string;
    notes: string;
    environments: ProviderEnvironment[];
    createdAt: Date;
    updatedAt: Date;
    apiBaseUrl: string;
    apiKey: string;
    apiSecret: string;
    lobbyUrl: string;
    depositUrl: string;
    webhookUrl: string;
    supportedCurrencies: string[];
    supportedLanguages: string[];
    integrationConfig: Record<string, any>;
}
