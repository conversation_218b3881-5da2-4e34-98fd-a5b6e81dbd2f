"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemJwtStrategy = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const passport_jwt_1 = require("passport-jwt");
const config_1 = require("@nestjs/config");
let SystemJwtStrategy = class SystemJwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'system-jwt') {
    configService;
    constructor(configService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET') || 'default-secret',
        });
        this.configService = configService;
    }
    async validate(payload) {
        console.log(`[JWT_STRATEGY] 解析JWT payload:`, payload);
        const result = {
            userId: payload.sub,
            username: payload.username,
            roles: payload.roles,
            isSuperAdmin: payload.isSuperAdmin || false,
            type: 'system',
        };
        console.log(`[JWT_STRATEGY] 返回用户信息:`, result);
        return result;
    }
};
exports.SystemJwtStrategy = SystemJwtStrategy;
exports.SystemJwtStrategy = SystemJwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SystemJwtStrategy);
//# sourceMappingURL=jwt.strategy.js.map