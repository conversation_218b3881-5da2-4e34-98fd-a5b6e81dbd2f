"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysRole = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("./sys-user.entity");
const sys_permission_entity_1 = require("./sys-permission.entity");
let SysRole = class SysRole {
    id;
    name;
    code;
    remark;
    status;
    users;
    permissions;
    createTime;
    updateTime;
};
exports.SysRole = SysRole;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SysRole.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], SysRole.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, length: 50 }),
    __metadata("design:type", String)
], SysRole.prototype, "code", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 200 }),
    __metadata("design:type", String)
], SysRole.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], SysRole.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => sys_user_entity_1.SysUser, (user) => user.roles),
    __metadata("design:type", Array)
], SysRole.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => sys_permission_entity_1.SysPermission, (permission) => permission.roles),
    (0, typeorm_1.JoinTable)({
        name: 'sys_role_permissions',
        joinColumn: { name: 'role_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'permission_id', referencedColumnName: 'id' },
    }),
    __metadata("design:type", Array)
], SysRole.prototype, "permissions", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], SysRole.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], SysRole.prototype, "updateTime", void 0);
exports.SysRole = SysRole = __decorate([
    (0, typeorm_1.Entity)('sys_roles')
], SysRole);
//# sourceMappingURL=sys-role.entity.js.map