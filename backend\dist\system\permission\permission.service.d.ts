import { Repository } from 'typeorm';
import { SysPermission } from '../entities/sys-permission.entity';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { QueryPermissionDto } from './dto/query-permission.dto';
export declare class SystemPermissionService {
    private permissionRepository;
    constructor(permissionRepository: Repository<SysPermission>);
    create(createPermissionDto: CreatePermissionDto): Promise<SysPermission>;
    findAll(queryPermissionDto: QueryPermissionDto): Promise<{
        list: SysPermission[];
        total: number;
        current: number;
        pageSize: number;
    }>;
    findOne(id: number): Promise<SysPermission>;
    update(id: number, updatePermissionDto: UpdatePermissionDto): Promise<SysPermission>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAllSimple(): Promise<SysPermission[]>;
    findByType(type: string): Promise<SysPermission[]>;
}
