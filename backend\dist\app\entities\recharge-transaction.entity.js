"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RechargeTransactionStatusLabels = exports.RechargeTransactionTypeLabels = exports.RechargeTransaction = exports.RechargeTransactionType = exports.RechargeTransactionStatus = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
var RechargeTransactionStatus;
(function (RechargeTransactionStatus) {
    RechargeTransactionStatus[RechargeTransactionStatus["INCOME"] = 1] = "INCOME";
    RechargeTransactionStatus[RechargeTransactionStatus["EXPENSE"] = 2] = "EXPENSE";
})(RechargeTransactionStatus || (exports.RechargeTransactionStatus = RechargeTransactionStatus = {}));
var RechargeTransactionType;
(function (RechargeTransactionType) {
    RechargeTransactionType[RechargeTransactionType["BET"] = 1] = "BET";
    RechargeTransactionType[RechargeTransactionType["WIN"] = 2] = "WIN";
    RechargeTransactionType[RechargeTransactionType["DEPOSIT"] = 3] = "DEPOSIT";
    RechargeTransactionType[RechargeTransactionType["BALANCE_EXCHANGE"] = 4] = "BALANCE_EXCHANGE";
})(RechargeTransactionType || (exports.RechargeTransactionType = RechargeTransactionType = {}));
let RechargeTransaction = class RechargeTransaction {
    id;
    userId;
    uid;
    amount;
    balanceBefore;
    balanceAfter;
    status;
    transactionType;
    orderId;
    description;
    remark;
    operatorId;
    createTime;
    updateTime;
    user;
};
exports.RechargeTransaction = RechargeTransaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "uid", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_before', type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "balanceBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_after', type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "balanceAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', comment: '状态：1-收入，2-支出' }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-充值，4-余额兑换' }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "transactionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_id', nullable: true, length: 100 }),
    __metadata("design:type", String)
], RechargeTransaction.prototype, "orderId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 255 }),
    __metadata("design:type", String)
], RechargeTransaction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], RechargeTransaction.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operator_id', nullable: true }),
    __metadata("design:type", Number)
], RechargeTransaction.prototype, "operatorId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], RechargeTransaction.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], RechargeTransaction.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], RechargeTransaction.prototype, "user", void 0);
exports.RechargeTransaction = RechargeTransaction = __decorate([
    (0, typeorm_1.Entity)('recharge_transactions')
], RechargeTransaction);
exports.RechargeTransactionTypeLabels = {
    [RechargeTransactionType.BET]: '下注',
    [RechargeTransactionType.WIN]: '赢金',
    [RechargeTransactionType.DEPOSIT]: '充值',
    [RechargeTransactionType.BALANCE_EXCHANGE]: '余额兑换',
};
exports.RechargeTransactionStatusLabels = {
    [RechargeTransactionStatus.INCOME]: '收入',
    [RechargeTransactionStatus.EXPENSE]: '支出',
};
//# sourceMappingURL=recharge-transaction.entity.js.map