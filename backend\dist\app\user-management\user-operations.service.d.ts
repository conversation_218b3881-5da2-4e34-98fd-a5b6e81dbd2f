import { Repository } from 'typeorm';
import { AppUser, MarketingChannel, MarketingAd, RiskEvent } from '../entities';
import { UpdateUserStatusDto, UpdateUserTagsDto, InvitationRelationshipDto } from './dto';
export declare class UserOperationsService {
    private readonly userRepository;
    private readonly channelRepository;
    private readonly adRepository;
    private readonly riskEventRepository;
    constructor(userRepository: Repository<AppUser>, channelRepository: Repository<MarketingChannel>, adRepository: Repository<MarketingAd>, riskEventRepository: Repository<RiskEvent>);
    updateUserStatus(id: number, updateDto: UpdateUserStatusDto, operatorId: number): Promise<void>;
    updateUserTags(id: number, updateDto: UpdateUserTagsDto, operatorId: number): Promise<void>;
    getUserInvitationRelationship(id: number): Promise<InvitationRelationshipDto>;
    getUserRiskEvents(userId: number, page?: number, pageSize?: number): Promise<{
        list: RiskEvent[];
        total: number;
        page: number;
        pageSize: number;
        totalPages: number;
    }>;
    getMarketingChannels(): Promise<MarketingChannel[]>;
    getMarketingAds(channelId?: number): Promise<MarketingAd[]>;
}
