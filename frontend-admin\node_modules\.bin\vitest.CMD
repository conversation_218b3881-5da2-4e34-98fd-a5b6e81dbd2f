@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules\vitest\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules\vitest\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vitest\vitest.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vitest\vitest.mjs" %*
)
