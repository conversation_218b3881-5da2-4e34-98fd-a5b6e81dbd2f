"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Application = void 0;
const typeorm_1 = require("typeorm");
const application_provider_entity_1 = require("./application-provider.entity");
let Application = class Application {
    id;
    appUuid;
    appCode;
    name;
    providerId;
    provider;
    launchCode;
    categories;
    platforms;
    orientation;
    supportedVirtualCurrencies;
    rtp;
    volatility;
    maxWinMultiplier;
    iconUrl;
    posterUrl;
    status;
    metadata;
    tags;
    supplierIdentifier;
    features;
    minBet;
    maxBet;
    hasDemo;
    hasMobile;
    hasDesktop;
    createdAt;
    updatedAt;
};
exports.Application = Application;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Application.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'app_uuid', length: 36, unique: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], Application.prototype, "appUuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'app_code', length: 100, unique: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], Application.prototype, "appCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 255 }),
    __metadata("design:type", String)
], Application.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider_id' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Number)
], Application.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => application_provider_entity_1.ApplicationProvider, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'provider_id' }),
    __metadata("design:type", application_provider_entity_1.ApplicationProvider)
], Application.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'launch_code', length: 255 }),
    __metadata("design:type", String)
], Application.prototype, "launchCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Array)
], Application.prototype, "categories", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Application.prototype, "platforms", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Application.prototype, "orientation", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'supported_virtual_currencies', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Application.prototype, "supportedVirtualCurrencies", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], Application.prototype, "rtp", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, nullable: true }),
    __metadata("design:type", String)
], Application.prototype, "volatility", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'max_win_multiplier', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], Application.prototype, "maxWinMultiplier", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'icon_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], Application.prototype, "iconUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'poster_url', length: 512, nullable: true }),
    __metadata("design:type", String)
], Application.prototype, "posterUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, default: 'active' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], Application.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], Application.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Application.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'supplier_identifier', length: 255, nullable: true }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", String)
], Application.prototype, "supplierIdentifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], Application.prototype, "features", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'min_bet', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], Application.prototype, "minBet", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'max_bet', type: 'decimal', precision: 10, scale: 4, nullable: true }),
    __metadata("design:type", Number)
], Application.prototype, "maxBet", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'has_demo', type: 'boolean', default: false }),
    __metadata("design:type", Boolean)
], Application.prototype, "hasDemo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'has_mobile', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], Application.prototype, "hasMobile", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'has_desktop', type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], Application.prototype, "hasDesktop", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    (0, typeorm_1.Index)(),
    __metadata("design:type", Date)
], Application.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], Application.prototype, "updatedAt", void 0);
exports.Application = Application = __decorate([
    (0, typeorm_1.Entity)('applications')
], Application);
//# sourceMappingURL=application.entity.js.map