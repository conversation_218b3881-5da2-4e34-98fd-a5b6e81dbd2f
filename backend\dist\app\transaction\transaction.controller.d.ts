import { TransactionService } from './transaction.service';
import { CreateTransactionDto, TransactionListQueryDto } from './dto/transaction.dto';
export declare class TransactionController {
    private readonly transactionService;
    constructor(transactionService: TransactionService);
    createCashTransaction(createTransactionDto: CreateTransactionDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").CashTransaction;
    }>;
    createGoldTransaction(createTransactionDto: CreateTransactionDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").GoldTransaction;
    }>;
    createRechargeTransaction(createTransactionDto: CreateTransactionDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").RechargeTransaction;
    }>;
    getUserAssetStatistics(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            cash: import("./transaction.service").TransactionStatistics;
            gold: import("./transaction.service").TransactionStatistics;
            recharge: import("./transaction.service").TransactionStatistics;
        };
    }>;
    getCashStatistics(userId: number): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionStatistics;
    }>;
    getGoldStatistics(userId: number): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionStatistics;
    }>;
    getRechargeStatistics(userId: number): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionStatistics;
    }>;
    getCashTransactionList(userId: number, query: Omit<TransactionListQueryDto, 'userId'>): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionListResponse;
    }>;
    getGoldTransactionList(userId: number, query: Omit<TransactionListQueryDto, 'userId'>): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionListResponse;
    }>;
    getRechargeTransactionList(userId: number, query: Omit<TransactionListQueryDto, 'userId'>): Promise<{
        code: number;
        message: string;
        result: import("./transaction.service").TransactionListResponse;
    }>;
}
