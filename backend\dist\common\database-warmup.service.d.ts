import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { DatabaseMonitorService } from './database-monitor.service';
export declare class DatabaseWarmupService implements OnModuleInit {
    private dataSource;
    private databaseMonitorService;
    private readonly logger;
    constructor(dataSource: DataSource, databaseMonitorService: DatabaseMonitorService);
    onModuleInit(): Promise<void>;
    private warmupConnections;
    private startHealthCheck;
    refreshConnectionPool(): Promise<{
        success: boolean;
        before: any;
        after: any;
        error?: undefined;
    } | {
        success: boolean;
        error: any;
        before?: undefined;
        after?: undefined;
    }>;
    private getConnectionPoolStats;
    detectSlowQueries(): Promise<any>;
}
