/**
 * 测试上传功能页面
 */

import React, { useState } from 'react';
import { Button, Upload, message, Card, Space, Image, Progress } from 'antd';
import { UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import { 
  uploadImageToSupabase, 
  uploadCompressedImageToSupabase, 
  uploadMultipleImagesToSupabase,
  deleteImageFromSupabase 
} from '@/utils/supabase-upload';

const TestUploadPage: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState(0);

  // 单个图片上传
  const handleSingleUpload = async (file: File) => {
    setUploading(true);
    try {
      const imageUrl = await uploadImageToSupabase(file);
      setUploadedImages(prev => [...prev, imageUrl]);
      message.success('图片上传成功！');
      console.log('上传成功:', imageUrl);
    } catch (error) {
      message.error(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('上传失败:', error);
    } finally {
      setUploading(false);
    }
  };

  // 压缩上传
  const handleCompressedUpload = async (file: File) => {
    setUploading(true);
    try {
      const imageUrl = await uploadCompressedImageToSupabase(file, 'ads', {
        maxWidth: 1280,
        maxHeight: 720,
        quality: 0.8
      });
      setUploadedImages(prev => [...prev, imageUrl]);
      message.success('压缩图片上传成功！');
      console.log('压缩上传成功:', imageUrl);
    } catch (error) {
      message.error(`压缩上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('压缩上传失败:', error);
    } finally {
      setUploading(false);
    }
  };

  // 批量上传
  const handleMultipleUpload = async (files: File[]) => {
    setUploading(true);
    setUploadProgress(0);
    
    try {
      const results = await uploadMultipleImagesToSupabase(
        files, 
        'ads',
        (progress, current, total) => {
          setUploadProgress(progress);
          console.log(`上传进度: ${progress.toFixed(1)}% (${current}/${total})`);
        }
      );

      const successUrls = results
        .filter(result => result.success)
        .map(result => result.url!)
        .filter(Boolean);

      const failedCount = results.filter(result => !result.success).length;

      setUploadedImages(prev => [...prev, ...successUrls]);
      
      if (failedCount > 0) {
        message.warning(`批量上传完成，${successUrls.length}个成功，${failedCount}个失败`);
      } else {
        message.success(`批量上传成功，共${successUrls.length}个文件`);
      }

      console.log('批量上传结果:', results);
    } catch (error) {
      message.error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('批量上传失败:', error);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // 删除图片
  const handleDeleteImage = async (imageUrl: string) => {
    try {
      const success = await deleteImageFromSupabase(imageUrl);
      if (success) {
        setUploadedImages(prev => prev.filter(url => url !== imageUrl));
        message.success('图片删除成功！');
      } else {
        message.error('图片删除失败');
      }
    } catch (error) {
      message.error(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('删除失败:', error);
    }
  };

  const uploadProps = {
    beforeUpload: (file: File) => {
      // 阻止默认上传行为
      return false;
    },
    showUploadList: false,
  };

  return (
    <div style={{ padding: '24px' }}>
      <h1>Supabase 上传功能测试</h1>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 单个上传测试 */}
        <Card title="单个图片上传测试" size="small">
          <Upload
            {...uploadProps}
            onChange={(info) => {
              const file = info.fileList[info.fileList.length - 1]?.originFileObj;
              if (file) {
                handleSingleUpload(file);
              }
            }}
          >
            <Button icon={<UploadOutlined />} loading={uploading}>
              选择图片上传
            </Button>
          </Upload>
        </Card>

        {/* 压缩上传测试 */}
        <Card title="压缩图片上传测试" size="small">
          <Upload
            {...uploadProps}
            onChange={(info) => {
              const file = info.fileList[info.fileList.length - 1]?.originFileObj;
              if (file) {
                handleCompressedUpload(file);
              }
            }}
          >
            <Button icon={<UploadOutlined />} loading={uploading}>
              选择图片压缩上传 (1280x720, 80%质量)
            </Button>
          </Upload>
        </Card>

        {/* 批量上传测试 */}
        <Card title="批量图片上传测试" size="small">
          <Upload
            {...uploadProps}
            multiple
            onChange={(info) => {
              const files = info.fileList
                .map(item => item.originFileObj)
                .filter(Boolean) as File[];
              if (files.length > 0) {
                handleMultipleUpload(files);
              }
            }}
          >
            <Button icon={<UploadOutlined />} loading={uploading}>
              选择多个图片批量上传
            </Button>
          </Upload>
          
          {uploadProgress > 0 && (
            <div style={{ marginTop: '12px' }}>
              <Progress percent={Math.round(uploadProgress)} />
            </div>
          )}
        </Card>

        {/* 已上传图片展示 */}
        {uploadedImages.length > 0 && (
          <Card title={`已上传图片 (${uploadedImages.length})`} size="small">
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: '16px' }}>
              {uploadedImages.map((imageUrl, index) => (
                <div key={index} style={{ position: 'relative' }}>
                  <Image
                    src={imageUrl}
                    alt={`上传图片 ${index + 1}`}
                    style={{ width: '100%', height: '150px', objectFit: 'cover' }}
                    preview={{
                      mask: '预览'
                    }}
                  />
                  <Button
                    type="primary"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    style={{ 
                      position: 'absolute', 
                      top: '8px', 
                      right: '8px',
                      opacity: 0.8
                    }}
                    onClick={() => handleDeleteImage(imageUrl)}
                  >
                    删除
                  </Button>
                  <div style={{ 
                    position: 'absolute', 
                    bottom: '0', 
                    left: '0', 
                    right: '0',
                    background: 'rgba(0,0,0,0.7)',
                    color: 'white',
                    padding: '4px 8px',
                    fontSize: '12px',
                    wordBreak: 'break-all'
                  }}>
                    {imageUrl.split('/').pop()}
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* 配置信息 */}
        <Card title="当前配置信息" size="small">
          <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px', fontSize: '12px' }}>
            {JSON.stringify({
              environment: import.meta.env.VITE_ENVIRONMENT || 'supabase',
              supabaseUrl: import.meta.env.VITE_SUPABASE_URL,
              bucket: import.meta.env.VITE_SUPABASE_S3_BUCKET,
              folder: import.meta.env.VITE_SUPABASE_S3_FOLDER,
              maxFileSize: import.meta.env.VITE_MAX_FILE_SIZE,
              allowedTypes: import.meta.env.VITE_ALLOWED_IMAGE_TYPES
            }, null, 2)}
          </pre>
        </Card>
      </Space>
    </div>
  );
};

export default TestUploadPage;
