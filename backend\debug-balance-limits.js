const { Pool } = require('pg');

// 数据库连接配置
const pool = new Pool({
  host: '**************',
  port: 5435,
  database: 'inapp2',
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
});

async function debugBalanceLimits() {
  try {
    console.log('🔍 调试余额充值限制数据...\n');

    // 查询原始数据
    const result = await pool.query('SELECT * FROM balance_recharge_limits WHERE status = 1');
    
    console.log('📊 数据库查询结果:');
    console.log('数据条数:', result.rows.length);
    
    if (result.rows.length > 0) {
      const row = result.rows[0];
      console.log('\n🔍 第一条数据详情:');
      console.log('原始数据:', row);
      
      console.log('\n📋 字段类型分析:');
      console.log('id:', typeof row.id, '值:', row.id);
      console.log('limit_name:', typeof row.limit_name, '值:', row.limit_name);
      console.log('min_amount:', typeof row.min_amount, '值:', row.min_amount);
      console.log('max_amount:', typeof row.max_amount, '值:', row.max_amount);
      console.log('status:', typeof row.status, '值:', row.status);
      console.log('remark:', typeof row.remark, '值:', row.remark);
      
      console.log('\n🔄 数据转换测试:');
      console.log('Number(min_amount):', Number(row.min_amount));
      console.log('Number(max_amount):', Number(row.max_amount));
      console.log('parseFloat(min_amount):', parseFloat(row.min_amount));
      console.log('parseFloat(max_amount):', parseFloat(row.max_amount));
      
      // 测试比较操作
      console.log('\n⚖️ 比较操作测试:');
      console.log('max_amount > 0:', row.max_amount > 0);
      console.log('Number(max_amount) > 0:', Number(row.max_amount) > 0);
      console.log('parseFloat(max_amount) > 0:', parseFloat(row.max_amount) > 0);
    } else {
      console.log('❌ 没有找到状态为1的余额充值限制数据');
      
      // 查询所有数据
      const allResult = await pool.query('SELECT * FROM balance_recharge_limits');
      console.log('所有数据条数:', allResult.rows.length);
      if (allResult.rows.length > 0) {
        console.log('所有数据:', allResult.rows);
      }
    }

  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
  } finally {
    await pool.end();
  }
}

debugBalanceLimits();
