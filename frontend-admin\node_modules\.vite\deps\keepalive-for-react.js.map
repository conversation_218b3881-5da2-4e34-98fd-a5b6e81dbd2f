{"version": 3, "sources": ["../../.pnpm/keepalive-for-react@4.0.2_r_5c6f15039a6f4305f3f6aafcbf1c9ec2/node_modules/keepalive-for-react/dist/esm/index.mjs"], "sourcesContent": ["import { jsx, jsxs } from 'react/jsx-runtime';\nimport { createContext, memo, useMemo, useRef, useEffect, Fragment, startTransition, useState, useLayoutEffect, useCallback, useImperativeHandle, useContext } from 'react';\nimport { createPortal } from 'react-dom';\n\nfunction isNil(value) {\n    return value === null || value === undefined;\n}\nfunction isRegExp(value) {\n    return Object.prototype.toString.call(value) === \"[object RegExp]\";\n}\nfunction isArr(value) {\n    return Array.isArray(value);\n}\nfunction isFn(value) {\n    return typeof value === \"function\";\n}\nfunction domAttrSet(dom) {\n    return {\n        set: (key, value) => {\n            dom.setAttribute(key, value);\n            return domAttrSet(dom);\n        },\n    };\n}\nfunction delayAsync(milliseconds = 100) {\n    let _timeID;\n    return new Promise((resolve, _reject) => {\n        _timeID = setTimeout(() => {\n            resolve();\n            if (!isNil(_timeID)) {\n                clearTimeout(_timeID);\n            }\n        }, milliseconds);\n    });\n}\nfunction isInclude(include, val) {\n    const includes = isArr(include) ? include : isNil(include) ? [] : [include];\n    return includes.some(include => {\n        if (isRegExp(include)) {\n            return include.test(val);\n        }\n        else {\n            return val === include;\n        }\n    });\n}\nfunction macroTask(fn) {\n    setTimeout(fn, 0);\n}\n\nconst CacheComponentContext = createContext({\n    active: false,\n    refresh: () => { },\n    destroy: () => Promise.resolve(),\n    destroyAll: () => Promise.resolve(),\n    destroyOther: () => Promise.resolve(),\n    getCacheNodes: () => [],\n});\n\nconst CacheComponentProvider = memo(function (props) {\n    const { children, active, refresh, destroy, destroyAll, destroyOther, getCacheNodes } = props;\n    const value = useMemo(() => {\n        return { active, refresh, destroy, destroyAll, destroyOther, getCacheNodes };\n    }, [active, refresh, destroy, destroyAll, destroyOther, getCacheNodes]);\n    return jsx(CacheComponentContext.Provider, { value: value, children: children });\n});\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol */\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nconst cacheDivMarkedClassName = \"keepalive-cache-div\";\nfunction getChildNodes(dom) {\n    return dom ? Array.from(dom.children) : [];\n}\nfunction removeDivNodes(nodes) {\n    nodes.forEach(node => {\n        if (node.classList.contains(cacheDivMarkedClassName)) {\n            node.remove();\n        }\n    });\n}\nfunction renderCacheDiv(containerDiv, cacheDiv) {\n    const removeNodes = getChildNodes(containerDiv);\n    removeDivNodes(removeNodes);\n    containerDiv.appendChild(cacheDiv);\n    cacheDiv.classList.remove(\"inactive\");\n    cacheDiv.classList.add(\"active\");\n}\nfunction switchActiveNodesToInactive(containerDiv, cacheKey) {\n    const nodes = getChildNodes(containerDiv);\n    const activeNodes = nodes.filter(node => node.classList.contains(\"active\") && node.getAttribute(\"data-cache-key\") !== cacheKey);\n    activeNodes.forEach(node => {\n        node.classList.remove(\"active\");\n        node.classList.add(\"inactive\");\n    });\n    return activeNodes;\n}\nfunction isCached(cacheKey, exclude, include) {\n    if (include) {\n        return isInclude(include, cacheKey);\n    }\n    else {\n        if (exclude) {\n            return !isInclude(exclude, cacheKey);\n        }\n        return true;\n    }\n}\nconst CacheComponent = memo(function (props) {\n    const { errorElement: ErrorBoundary = Fragment, cacheNodeClassName, children, cacheKey, exclude, include } = props;\n    const { active, renderCount, destroy, transition, viewTransition, duration, containerDivRef } = props;\n    const activatedRef = useRef(false);\n    activatedRef.current = activatedRef.current || active;\n    const cacheDiv = useMemo(() => {\n        const cacheDiv = document.createElement(\"div\");\n        domAttrSet(cacheDiv)\n            .set(\"data-cache-key\", cacheKey)\n            .set(\"style\", \"height: 100%\")\n            .set(\"data-render-count\", renderCount.toString());\n        cacheDiv.className = cacheDivMarkedClassName + (cacheNodeClassName ? ` ${cacheNodeClassName}` : \"\");\n        return cacheDiv;\n    }, [renderCount, cacheNodeClassName]);\n    useEffect(() => {\n        const cached = isCached(cacheKey, exclude, include);\n        const containerDiv = containerDivRef.current;\n        if (!containerDiv) {\n            console.warn(`keepalive: cache container not found`);\n            return;\n        }\n        if (transition) {\n            (() => __awaiter(this, void 0, void 0, function* () {\n                if (active) {\n                    const inactiveNodes = switchActiveNodesToInactive(containerDiv, cacheKey);\n                    // duration - 40ms is to avoid the animation effect ending too early\n                    yield delayAsync(duration - 40);\n                    removeDivNodes(inactiveNodes);\n                    if (containerDiv.contains(cacheDiv)) {\n                        return;\n                    }\n                    renderCacheDiv(containerDiv, cacheDiv);\n                }\n                else {\n                    if (!cached) {\n                        yield delayAsync(duration);\n                        destroy(cacheKey);\n                    }\n                }\n            }))();\n        }\n        else {\n            if (active) {\n                const makeChange = () => {\n                    const inactiveNodes = switchActiveNodesToInactive(containerDiv, cacheKey);\n                    removeDivNodes(inactiveNodes);\n                    if (containerDiv.contains(cacheDiv)) {\n                        return;\n                    }\n                    renderCacheDiv(containerDiv, cacheDiv);\n                };\n                if (viewTransition && document.startViewTransition) {\n                    document.startViewTransition(makeChange);\n                }\n                else {\n                    makeChange();\n                }\n            }\n            else {\n                if (!cached) {\n                    destroy(cacheKey);\n                }\n            }\n        }\n    }, [active, containerDivRef, cacheKey, exclude, include]);\n    return activatedRef.current ? createPortal(jsx(ErrorBoundary, { children: children }), cacheDiv, cacheKey) : null;\n}, (prevProps, nextProps) => {\n    return (prevProps.active === nextProps.active &&\n        prevProps.renderCount === nextProps.renderCount &&\n        prevProps.children === nextProps.children &&\n        prevProps.exclude === nextProps.exclude &&\n        prevProps.include === nextProps.include);\n});\n\n/**\n * Compatible with React versions < 18 startTransition\n * @param cb Callback function to be executed in transition\n */\nconst safeStartTransition = (cb) => {\n    if (typeof startTransition !== \"undefined\" && isFn(startTransition)) {\n        startTransition(cb);\n    }\n    else {\n        cb();\n    }\n};\n\nfunction useKeepAliveRef() {\n    return useRef();\n}\nfunction KeepAlive(props) {\n    const { activeCacheKey, max = 10, exclude, include, onBeforeActive, customContainerRef, cacheNodeClassName = `cache-component`, containerClassName = \"keep-alive-render\", errorElement, transition = false, viewTransition = false, duration = 200, children, aliveRef, maxAliveTime = 0, } = props;\n    const containerDivRef = customContainerRef || useRef(null);\n    const [cacheNodes, setCacheNodes] = useState([]);\n    useLayoutEffect(() => {\n        if (isNil(activeCacheKey))\n            return;\n        safeStartTransition(() => {\n            setCacheNodes(prevCacheNodes => {\n                const lastActiveTime = Date.now();\n                const cacheNode = prevCacheNodes.find(item => item.cacheKey === activeCacheKey);\n                if (cacheNode) {\n                    return prevCacheNodes.map(item => {\n                        if (item.cacheKey === activeCacheKey) {\n                            let needUpdate = false;\n                            if (isFn(onBeforeActive))\n                                onBeforeActive(activeCacheKey);\n                            if (maxAliveTime) {\n                                const prev = item.lastActiveTime;\n                                if (isArr(maxAliveTime)) {\n                                    const config = maxAliveTime.find(item => {\n                                        return isRegExp(item.match) ? item.match.test(activeCacheKey) : item.match === activeCacheKey;\n                                    });\n                                    if (config) {\n                                        needUpdate = config && prev + config.expire * 1000 < lastActiveTime;\n                                    }\n                                }\n                                else {\n                                    needUpdate = prev + maxAliveTime * 1000 < lastActiveTime;\n                                }\n                            }\n                            return Object.assign(Object.assign({}, item), { ele: children, lastActiveTime, renderCount: needUpdate ? item.renderCount + 1 : item.renderCount });\n                        }\n                        return item;\n                    });\n                }\n                else {\n                    if (isFn(onBeforeActive))\n                        onBeforeActive(activeCacheKey);\n                    if (prevCacheNodes.length > max) {\n                        const node = prevCacheNodes.reduce((prev, cur) => {\n                            return prev.lastActiveTime < cur.lastActiveTime ? prev : cur;\n                        });\n                        prevCacheNodes.splice(prevCacheNodes.indexOf(node), 1);\n                    }\n                    return [...prevCacheNodes, { cacheKey: activeCacheKey, lastActiveTime, ele: children, renderCount: 0 }];\n                }\n            });\n        });\n    }, [activeCacheKey, children]);\n    const refresh = useCallback((cacheKey) => {\n        setCacheNodes(cacheNodes => {\n            const targetCacheKey = cacheKey || activeCacheKey;\n            return cacheNodes.map(item => {\n                if (item.cacheKey === targetCacheKey) {\n                    return Object.assign(Object.assign({}, item), { renderCount: item.renderCount + 1 });\n                }\n                return item;\n            });\n        });\n    }, [setCacheNodes, activeCacheKey]);\n    const destroy = useCallback((cacheKey) => {\n        const targetCacheKey = cacheKey || activeCacheKey;\n        const cacheKeys = isArr(targetCacheKey) ? targetCacheKey : [targetCacheKey];\n        return new Promise(resolve => {\n            macroTask(() => {\n                setCacheNodes(cacheNodes => {\n                    return [...cacheNodes.filter(item => !cacheKeys.includes(item.cacheKey))];\n                });\n                resolve();\n            });\n        });\n    }, [setCacheNodes, activeCacheKey]);\n    const destroyAll = useCallback(() => {\n        return new Promise(resolve => {\n            macroTask(() => {\n                setCacheNodes([]);\n                resolve();\n            });\n        });\n    }, [setCacheNodes]);\n    const destroyOther = useCallback((cacheKey) => {\n        const targetCacheKey = cacheKey || activeCacheKey;\n        return new Promise(resolve => {\n            macroTask(() => {\n                setCacheNodes(cacheNodes => {\n                    return [...cacheNodes.filter(item => item.cacheKey === targetCacheKey)];\n                });\n                resolve();\n            });\n        });\n    }, [activeCacheKey, setCacheNodes]);\n    const getCacheNodes = useCallback(() => {\n        return cacheNodes;\n    }, [cacheNodes]);\n    useImperativeHandle(aliveRef, () => ({\n        refresh,\n        destroy,\n        destroyAll,\n        destroyOther,\n        getCacheNodes,\n    }));\n    return (jsxs(Fragment, { children: [jsx(\"div\", { ref: containerDivRef, className: containerClassName, style: { height: \"100%\" } }), cacheNodes.map(item => {\n                const { cacheKey, ele, renderCount } = item;\n                return (jsx(CacheComponentProvider, { active: activeCacheKey === cacheKey, refresh: refresh, destroy: destroy, destroyAll: destroyAll, destroyOther: destroyOther, getCacheNodes: getCacheNodes, children: jsx(CacheComponent, { destroy: destroy, include: include, exclude: exclude, transition: transition, viewTransition: viewTransition, duration: duration, renderCount: renderCount, containerDivRef: containerDivRef, errorElement: errorElement, active: activeCacheKey === cacheKey, cacheNodeClassName: cacheNodeClassName, cacheKey: cacheKey, children: ele }) }, `${cacheKey}-${renderCount}`));\n            })] }));\n}\n\nconst useKeepAliveContext = () => {\n    return useContext(CacheComponentContext);\n};\n\nfunction useOnActive(cb, deps, skipMount = false, effect) {\n    const { active } = useKeepAliveContext();\n    const isMount = useRef(false);\n    effect(() => {\n        if (!active)\n            return;\n        if (skipMount && !isMount.current) {\n            isMount.current = true;\n            return;\n        }\n        const destroyCb = cb();\n        return () => {\n            if (isFn(destroyCb)) {\n                destroyCb();\n            }\n        };\n    }, [active, ...deps]);\n}\n\nconst useEffectOnActive = (cb, deps, skipMount = false) => {\n    useOnActive(cb, deps, skipMount, useEffect);\n};\n\nconst useLayoutEffectOnActive = (cb, deps, skipMount = false) => {\n    useOnActive(cb, deps, skipMount, useLayoutEffect);\n};\n\n/**\n * @deprecated since version 3.0.2. Use `useKeepAliveRef` instead.\n */\nconst useKeepaliveRef = useKeepAliveRef;\n\nexport { KeepAlive, KeepAlive as default, useEffectOnActive, useKeepAliveContext, useKeepAliveRef, useKeepaliveRef, useLayoutEffectOnActive };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,yBAA0B;AAC1B,mBAAoK;AACpK,uBAA6B;AAE7B,SAAS,MAAM,OAAO;AAClB,SAAO,UAAU,QAAQ,UAAU;AACvC;AACA,SAAS,SAAS,OAAO;AACrB,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AACA,SAAS,MAAM,OAAO;AAClB,SAAO,MAAM,QAAQ,KAAK;AAC9B;AACA,SAAS,KAAK,OAAO;AACjB,SAAO,OAAO,UAAU;AAC5B;AACA,SAAS,WAAW,KAAK;AACrB,SAAO;AAAA,IACH,KAAK,CAAC,KAAK,UAAU;AACjB,UAAI,aAAa,KAAK,KAAK;AAC3B,aAAO,WAAW,GAAG;AAAA,IACzB;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,eAAe,KAAK;AACpC,MAAI;AACJ,SAAO,IAAI,QAAQ,CAAC,SAAS,YAAY;AACrC,cAAU,WAAW,MAAM;AACvB,cAAQ;AACR,UAAI,CAAC,MAAM,OAAO,GAAG;AACjB,qBAAa,OAAO;AAAA,MACxB;AAAA,IACJ,GAAG,YAAY;AAAA,EACnB,CAAC;AACL;AACA,SAAS,UAAU,SAAS,KAAK;AAC7B,QAAM,WAAW,MAAM,OAAO,IAAI,UAAU,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;AAC1E,SAAO,SAAS,KAAK,CAAAA,aAAW;AAC5B,QAAI,SAASA,QAAO,GAAG;AACnB,aAAOA,SAAQ,KAAK,GAAG;AAAA,IAC3B,OACK;AACD,aAAO,QAAQA;AAAA,IACnB;AAAA,EACJ,CAAC;AACL;AACA,SAAS,UAAU,IAAI;AACnB,aAAW,IAAI,CAAC;AACpB;AAEA,IAAM,4BAAwB,4BAAc;AAAA,EACxC,QAAQ;AAAA,EACR,SAAS,MAAM;AAAA,EAAE;AAAA,EACjB,SAAS,MAAM,QAAQ,QAAQ;AAAA,EAC/B,YAAY,MAAM,QAAQ,QAAQ;AAAA,EAClC,cAAc,MAAM,QAAQ,QAAQ;AAAA,EACpC,eAAe,MAAM,CAAC;AAC1B,CAAC;AAED,IAAM,6BAAyB,mBAAK,SAAU,OAAO;AACjD,QAAM,EAAE,UAAU,QAAQ,SAAS,SAAS,YAAY,cAAc,cAAc,IAAI;AACxF,QAAM,YAAQ,sBAAQ,MAAM;AACxB,WAAO,EAAE,QAAQ,SAAS,SAAS,YAAY,cAAc,cAAc;AAAA,EAC/E,GAAG,CAAC,QAAQ,SAAS,SAAS,YAAY,cAAc,aAAa,CAAC;AACtE,aAAO,wBAAI,sBAAsB,UAAU,EAAE,OAAc,SAAmB,CAAC;AACnF,CAAC;AAkBD,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AACpD,WAAS,MAAM,OAAO;AACpB,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAC3D,cAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACzD,aAAS,UAAU,OAAO;AACxB,UAAI;AACF,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAC5B,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,SAAS,OAAO;AACvB,UAAI;AACF,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAChC,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AACA,aAAS,KAAK,QAAQ;AACpB,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IACpF;AACA,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACtE,CAAC;AACH;AAMA,IAAM,0BAA0B;AAChC,SAAS,cAAc,KAAK;AACxB,SAAO,MAAM,MAAM,KAAK,IAAI,QAAQ,IAAI,CAAC;AAC7C;AACA,SAAS,eAAe,OAAO;AAC3B,QAAM,QAAQ,UAAQ;AAClB,QAAI,KAAK,UAAU,SAAS,uBAAuB,GAAG;AAClD,WAAK,OAAO;AAAA,IAChB;AAAA,EACJ,CAAC;AACL;AACA,SAAS,eAAe,cAAc,UAAU;AAC5C,QAAM,cAAc,cAAc,YAAY;AAC9C,iBAAe,WAAW;AAC1B,eAAa,YAAY,QAAQ;AACjC,WAAS,UAAU,OAAO,UAAU;AACpC,WAAS,UAAU,IAAI,QAAQ;AACnC;AACA,SAAS,4BAA4B,cAAc,UAAU;AACzD,QAAM,QAAQ,cAAc,YAAY;AACxC,QAAM,cAAc,MAAM,OAAO,UAAQ,KAAK,UAAU,SAAS,QAAQ,KAAK,KAAK,aAAa,gBAAgB,MAAM,QAAQ;AAC9H,cAAY,QAAQ,UAAQ;AACxB,SAAK,UAAU,OAAO,QAAQ;AAC9B,SAAK,UAAU,IAAI,UAAU;AAAA,EACjC,CAAC;AACD,SAAO;AACX;AACA,SAAS,SAAS,UAAU,SAAS,SAAS;AAC1C,MAAI,SAAS;AACT,WAAO,UAAU,SAAS,QAAQ;AAAA,EACtC,OACK;AACD,QAAI,SAAS;AACT,aAAO,CAAC,UAAU,SAAS,QAAQ;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,qBAAiB,mBAAK,SAAU,OAAO;AACzC,QAAM,EAAE,cAAc,gBAAgB,uBAAU,oBAAoB,UAAU,UAAU,SAAS,QAAQ,IAAI;AAC7G,QAAM,EAAE,QAAQ,aAAa,SAAS,YAAY,gBAAgB,UAAU,gBAAgB,IAAI;AAChG,QAAM,mBAAe,qBAAO,KAAK;AACjC,eAAa,UAAU,aAAa,WAAW;AAC/C,QAAM,eAAW,sBAAQ,MAAM;AAC3B,UAAMC,YAAW,SAAS,cAAc,KAAK;AAC7C,eAAWA,SAAQ,EACd,IAAI,kBAAkB,QAAQ,EAC9B,IAAI,SAAS,cAAc,EAC3B,IAAI,qBAAqB,YAAY,SAAS,CAAC;AACpD,IAAAA,UAAS,YAAY,2BAA2B,qBAAqB,IAAI,kBAAkB,KAAK;AAChG,WAAOA;AAAA,EACX,GAAG,CAAC,aAAa,kBAAkB,CAAC;AACpC,8BAAU,MAAM;AACZ,UAAM,SAAS,SAAS,UAAU,SAAS,OAAO;AAClD,UAAM,eAAe,gBAAgB;AACrC,QAAI,CAAC,cAAc;AACf,cAAQ,KAAK,sCAAsC;AACnD;AAAA,IACJ;AACA,QAAI,YAAY;AACZ,OAAC,MAAM,UAAU,MAAM,QAAQ,QAAQ,aAAa;AAChD,YAAI,QAAQ;AACR,gBAAM,gBAAgB,4BAA4B,cAAc,QAAQ;AAExE,gBAAM,WAAW,WAAW,EAAE;AAC9B,yBAAe,aAAa;AAC5B,cAAI,aAAa,SAAS,QAAQ,GAAG;AACjC;AAAA,UACJ;AACA,yBAAe,cAAc,QAAQ;AAAA,QACzC,OACK;AACD,cAAI,CAAC,QAAQ;AACT,kBAAM,WAAW,QAAQ;AACzB,oBAAQ,QAAQ;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ,CAAC,GAAG;AAAA,IACR,OACK;AACD,UAAI,QAAQ;AACR,cAAM,aAAa,MAAM;AACrB,gBAAM,gBAAgB,4BAA4B,cAAc,QAAQ;AACxE,yBAAe,aAAa;AAC5B,cAAI,aAAa,SAAS,QAAQ,GAAG;AACjC;AAAA,UACJ;AACA,yBAAe,cAAc,QAAQ;AAAA,QACzC;AACA,YAAI,kBAAkB,SAAS,qBAAqB;AAChD,mBAAS,oBAAoB,UAAU;AAAA,QAC3C,OACK;AACD,qBAAW;AAAA,QACf;AAAA,MACJ,OACK;AACD,YAAI,CAAC,QAAQ;AACT,kBAAQ,QAAQ;AAAA,QACpB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,QAAQ,iBAAiB,UAAU,SAAS,OAAO,CAAC;AACxD,SAAO,aAAa,cAAU,mCAAa,wBAAI,eAAe,EAAE,SAAmB,CAAC,GAAG,UAAU,QAAQ,IAAI;AACjH,GAAG,CAAC,WAAW,cAAc;AACzB,SAAQ,UAAU,WAAW,UAAU,UACnC,UAAU,gBAAgB,UAAU,eACpC,UAAU,aAAa,UAAU,YACjC,UAAU,YAAY,UAAU,WAChC,UAAU,YAAY,UAAU;AACxC,CAAC;AAMD,IAAM,sBAAsB,CAAC,OAAO;AAChC,MAAI,OAAO,iCAAoB,eAAe,KAAK,4BAAe,GAAG;AACjE,sCAAgB,EAAE;AAAA,EACtB,OACK;AACD,OAAG;AAAA,EACP;AACJ;AAEA,SAAS,kBAAkB;AACvB,aAAO,qBAAO;AAClB;AACA,SAAS,UAAU,OAAO;AACtB,QAAM,EAAE,gBAAgB,MAAM,IAAI,SAAS,SAAS,gBAAgB,oBAAoB,qBAAqB,mBAAmB,qBAAqB,qBAAqB,cAAc,aAAa,OAAO,iBAAiB,OAAO,WAAW,KAAK,UAAU,UAAU,eAAe,EAAG,IAAI;AAC9R,QAAM,kBAAkB,0BAAsB,qBAAO,IAAI;AACzD,QAAM,CAAC,YAAY,aAAa,QAAI,uBAAS,CAAC,CAAC;AAC/C,oCAAgB,MAAM;AAClB,QAAI,MAAM,cAAc;AACpB;AACJ,wBAAoB,MAAM;AACtB,oBAAc,oBAAkB;AAC5B,cAAM,iBAAiB,KAAK,IAAI;AAChC,cAAM,YAAY,eAAe,KAAK,UAAQ,KAAK,aAAa,cAAc;AAC9E,YAAI,WAAW;AACX,iBAAO,eAAe,IAAI,UAAQ;AAC9B,gBAAI,KAAK,aAAa,gBAAgB;AAClC,kBAAI,aAAa;AACjB,kBAAI,KAAK,cAAc;AACnB,+BAAe,cAAc;AACjC,kBAAI,cAAc;AACd,sBAAM,OAAO,KAAK;AAClB,oBAAI,MAAM,YAAY,GAAG;AACrB,wBAAM,SAAS,aAAa,KAAK,CAAAC,UAAQ;AACrC,2BAAO,SAASA,MAAK,KAAK,IAAIA,MAAK,MAAM,KAAK,cAAc,IAAIA,MAAK,UAAU;AAAA,kBACnF,CAAC;AACD,sBAAI,QAAQ;AACR,iCAAa,UAAU,OAAO,OAAO,SAAS,MAAO;AAAA,kBACzD;AAAA,gBACJ,OACK;AACD,+BAAa,OAAO,eAAe,MAAO;AAAA,gBAC9C;AAAA,cACJ;AACA,qBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,KAAK,UAAU,gBAAgB,aAAa,aAAa,KAAK,cAAc,IAAI,KAAK,YAAY,CAAC;AAAA,YACtJ;AACA,mBAAO;AAAA,UACX,CAAC;AAAA,QACL,OACK;AACD,cAAI,KAAK,cAAc;AACnB,2BAAe,cAAc;AACjC,cAAI,eAAe,SAAS,KAAK;AAC7B,kBAAM,OAAO,eAAe,OAAO,CAAC,MAAM,QAAQ;AAC9C,qBAAO,KAAK,iBAAiB,IAAI,iBAAiB,OAAO;AAAA,YAC7D,CAAC;AACD,2BAAe,OAAO,eAAe,QAAQ,IAAI,GAAG,CAAC;AAAA,UACzD;AACA,iBAAO,CAAC,GAAG,gBAAgB,EAAE,UAAU,gBAAgB,gBAAgB,KAAK,UAAU,aAAa,EAAE,CAAC;AAAA,QAC1G;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,CAAC,gBAAgB,QAAQ,CAAC;AAC7B,QAAM,cAAU,0BAAY,CAAC,aAAa;AACtC,kBAAc,CAAAC,gBAAc;AACxB,YAAM,iBAAiB,YAAY;AACnC,aAAOA,YAAW,IAAI,UAAQ;AAC1B,YAAI,KAAK,aAAa,gBAAgB;AAClC,iBAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,GAAG,EAAE,aAAa,KAAK,cAAc,EAAE,CAAC;AAAA,QACvF;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,CAAC,eAAe,cAAc,CAAC;AAClC,QAAM,cAAU,0BAAY,CAAC,aAAa;AACtC,UAAM,iBAAiB,YAAY;AACnC,UAAM,YAAY,MAAM,cAAc,IAAI,iBAAiB,CAAC,cAAc;AAC1E,WAAO,IAAI,QAAQ,aAAW;AAC1B,gBAAU,MAAM;AACZ,sBAAc,CAAAA,gBAAc;AACxB,iBAAO,CAAC,GAAGA,YAAW,OAAO,UAAQ,CAAC,UAAU,SAAS,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC5E,CAAC;AACD,gBAAQ;AAAA,MACZ,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,CAAC,eAAe,cAAc,CAAC;AAClC,QAAM,iBAAa,0BAAY,MAAM;AACjC,WAAO,IAAI,QAAQ,aAAW;AAC1B,gBAAU,MAAM;AACZ,sBAAc,CAAC,CAAC;AAChB,gBAAQ;AAAA,MACZ,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,CAAC,aAAa,CAAC;AAClB,QAAM,mBAAe,0BAAY,CAAC,aAAa;AAC3C,UAAM,iBAAiB,YAAY;AACnC,WAAO,IAAI,QAAQ,aAAW;AAC1B,gBAAU,MAAM;AACZ,sBAAc,CAAAA,gBAAc;AACxB,iBAAO,CAAC,GAAGA,YAAW,OAAO,UAAQ,KAAK,aAAa,cAAc,CAAC;AAAA,QAC1E,CAAC;AACD,gBAAQ;AAAA,MACZ,CAAC;AAAA,IACL,CAAC;AAAA,EACL,GAAG,CAAC,gBAAgB,aAAa,CAAC;AAClC,QAAM,oBAAgB,0BAAY,MAAM;AACpC,WAAO;AAAA,EACX,GAAG,CAAC,UAAU,CAAC;AACf,wCAAoB,UAAU,OAAO;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,EAAE;AACF,aAAQ,yBAAK,uBAAU,EAAE,UAAU,KAAC,wBAAI,OAAO,EAAE,KAAK,iBAAiB,WAAW,oBAAoB,OAAO,EAAE,QAAQ,OAAO,EAAE,CAAC,GAAG,WAAW,IAAI,UAAQ;AAC/I,UAAM,EAAE,UAAU,KAAK,YAAY,IAAI;AACvC,eAAQ,wBAAI,wBAAwB,EAAE,QAAQ,mBAAmB,UAAU,SAAkB,SAAkB,YAAwB,cAA4B,eAA8B,cAAU,wBAAI,gBAAgB,EAAE,SAAkB,SAAkB,SAAkB,YAAwB,gBAAgC,UAAoB,aAA0B,iBAAkC,cAA4B,QAAQ,mBAAmB,UAAU,oBAAwC,UAAoB,UAAU,IAAI,CAAC,EAAE,GAAG,GAAG,QAAQ,IAAI,WAAW,EAAE;AAAA,EAChlB,CAAC,CAAC,EAAE,CAAC;AACjB;AAEA,IAAM,sBAAsB,MAAM;AAC9B,aAAO,yBAAW,qBAAqB;AAC3C;AAEA,SAAS,YAAY,IAAI,MAAM,YAAY,OAAO,QAAQ;AACtD,QAAM,EAAE,OAAO,IAAI,oBAAoB;AACvC,QAAM,cAAU,qBAAO,KAAK;AAC5B,SAAO,MAAM;AACT,QAAI,CAAC;AACD;AACJ,QAAI,aAAa,CAAC,QAAQ,SAAS;AAC/B,cAAQ,UAAU;AAClB;AAAA,IACJ;AACA,UAAM,YAAY,GAAG;AACrB,WAAO,MAAM;AACT,UAAI,KAAK,SAAS,GAAG;AACjB,kBAAU;AAAA,MACd;AAAA,IACJ;AAAA,EACJ,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC;AACxB;AAEA,IAAM,oBAAoB,CAAC,IAAI,MAAM,YAAY,UAAU;AACvD,cAAY,IAAI,MAAM,WAAW,sBAAS;AAC9C;AAEA,IAAM,0BAA0B,CAAC,IAAI,MAAM,YAAY,UAAU;AAC7D,cAAY,IAAI,MAAM,WAAW,4BAAe;AACpD;AAKA,IAAM,kBAAkB;", "names": ["include", "cacheDiv", "item", "cacheNodes"]}