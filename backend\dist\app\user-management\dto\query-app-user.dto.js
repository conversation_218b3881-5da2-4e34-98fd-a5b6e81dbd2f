"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryAppUserDto = exports.KycStatus = exports.AccountType = exports.UserStatus = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
var UserStatus;
(function (UserStatus) {
    UserStatus[UserStatus["NORMAL"] = 0] = "NORMAL";
    UserStatus[UserStatus["BANNED"] = 1] = "BANNED";
    UserStatus[UserStatus["DEACTIVATED"] = 2] = "DEACTIVATED";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var AccountType;
(function (AccountType) {
    AccountType[AccountType["NORMAL_USER"] = 0] = "NORMAL_USER";
    AccountType[AccountType["INTERNAL_EMPLOYEE"] = 1] = "INTERNAL_EMPLOYEE";
})(AccountType || (exports.AccountType = AccountType = {}));
var KycStatus;
(function (KycStatus) {
    KycStatus[KycStatus["NOT_VERIFIED"] = 0] = "NOT_VERIFIED";
    KycStatus[KycStatus["UNDER_REVIEW"] = 1] = "UNDER_REVIEW";
    KycStatus[KycStatus["APPROVED"] = 2] = "APPROVED";
    KycStatus[KycStatus["REJECTED"] = 3] = "REJECTED";
})(KycStatus || (exports.KycStatus = KycStatus = {}));
class QueryAppUserDto {
    page = 1;
    pageSize = 20;
    id;
    username;
    email;
    phone;
    nickname;
    status;
    accountType;
    kycStatus;
    channelId;
    adId;
    inviterId;
    minRiskScore;
    maxRiskScore;
    startDate;
    endDate;
    sortBy = 'createTime';
    sortOrder = 'DESC';
}
exports.QueryAppUserDto = QueryAppUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '页码', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每页数量', example: 20, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "pageSize", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '昵称搜索', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户状态', enum: UserStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsEnum)(UserStatus),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '账户类型', enum: AccountType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsEnum)(AccountType),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "accountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KYC状态', enum: KycStatus, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsEnum)(KycStatus),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "kycStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "channelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "adId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请人ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "inviterId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '风险分值最小值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "minRiskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '风险分值最大值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], QueryAppUserDto.prototype, "maxRiskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册开始时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册结束时间', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序字段', required: false, example: 'createTime' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "sortBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序方向', required: false, example: 'DESC' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryAppUserDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=query-app-user.dto.js.map