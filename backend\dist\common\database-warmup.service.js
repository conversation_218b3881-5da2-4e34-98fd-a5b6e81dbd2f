"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseWarmupService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseWarmupService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const database_monitor_service_1 = require("./database-monitor.service");
let DatabaseWarmupService = DatabaseWarmupService_1 = class DatabaseWarmupService {
    dataSource;
    databaseMonitorService;
    logger = new common_1.Logger(DatabaseWarmupService_1.name);
    constructor(dataSource, databaseMonitorService) {
        this.dataSource = dataSource;
        this.databaseMonitorService = databaseMonitorService;
    }
    async onModuleInit() {
        this.logger.log('🔗 开始初始化数据库连接');
        await this.warmupConnections();
        this.startHealthCheck();
        this.databaseMonitorService.startPeriodicMonitoring();
    }
    async warmupConnections() {
        this.logger.log('开始预热数据库连接池...');
        try {
            const warmupPromises = Array.from({ length: 8 }, async (_, index) => {
                try {
                    const startTime = Date.now();
                    await this.dataSource.query('SELECT 1 as warmup_test');
                    const duration = Date.now() - startTime;
                    this.logger.log(`连接 ${index + 1} 预热完成，耗时: ${duration}ms`);
                    return duration;
                }
                catch (error) {
                    this.logger.warn(`连接 ${index + 1} 预热失败: ${error.message}`);
                    return -1;
                }
            });
            const results = await Promise.all(warmupPromises);
            const successCount = results.filter(r => r > 0).length;
            const avgTime = results.filter(r => r > 0).reduce((a, b) => a + b, 0) / successCount;
            this.logger.log(`连接池预热完成: ${successCount}/8 个连接成功，平均耗时: ${avgTime.toFixed(2)}ms`);
        }
        catch (error) {
            this.logger.error(`连接池预热失败: ${error.message}`);
        }
    }
    startHealthCheck() {
        setInterval(async () => {
            try {
                const startTime = Date.now();
                await this.dataSource.query('SELECT 1 as health_check');
                const duration = Date.now() - startTime;
                if (duration > 5000) {
                    this.logger.warn(`数据库响应较慢: ${duration}ms`);
                }
                else {
                    this.logger.debug(`数据库健康检查通过: ${duration}ms`);
                }
            }
            catch (error) {
                this.logger.error(`数据库健康检查失败: ${error.message}`);
            }
        }, 5 * 60 * 1000);
    }
    async refreshConnectionPool() {
        this.logger.log('刷新数据库连接池...');
        try {
            const beforeStats = await this.getConnectionPoolStats();
            this.logger.log(`刷新前连接池状态: ${JSON.stringify(beforeStats)}`);
            await Promise.all([
                this.dataSource.query('SELECT pg_stat_reset()'),
                this.dataSource.query('SELECT 1'),
                this.dataSource.query('SELECT now()'),
            ]);
            const afterStats = await this.getConnectionPoolStats();
            this.logger.log(`刷新后连接池状态: ${JSON.stringify(afterStats)}`);
            return {
                success: true,
                before: beforeStats,
                after: afterStats,
            };
        }
        catch (error) {
            this.logger.error(`连接池刷新失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
            };
        }
    }
    async getConnectionPoolStats() {
        try {
            const result = await this.dataSource.query(`
        SELECT 
          count(*) as total_connections,
          count(*) FILTER (WHERE state = 'active') as active_connections,
          count(*) FILTER (WHERE state = 'idle') as idle_connections,
          count(*) FILTER (WHERE application_name LIKE '%inapp2-backend%') as app_connections
        FROM pg_stat_activity
      `);
            return result[0];
        }
        catch (error) {
            this.logger.warn(`获取连接池统计失败: ${error.message}`);
            return null;
        }
    }
    async detectSlowQueries() {
        try {
            const slowQueries = await this.dataSource.query(`
        SELECT 
          pid,
          now() - pg_stat_activity.query_start AS duration,
          query,
          state,
          application_name
        FROM pg_stat_activity 
        WHERE (now() - pg_stat_activity.query_start) > interval '30 seconds'
        AND state != 'idle'
        AND application_name LIKE '%inapp2-backend%'
        ORDER BY duration DESC
      `);
            if (slowQueries.length > 0) {
                this.logger.warn(`发现 ${slowQueries.length} 个慢查询:`);
                slowQueries.forEach((query, index) => {
                    this.logger.warn(`  ${index + 1}. PID: ${query.pid}, 耗时: ${query.duration}, 状态: ${query.state}`);
                    this.logger.warn(`     查询: ${query.query.substring(0, 100)}...`);
                });
            }
            return slowQueries;
        }
        catch (error) {
            this.logger.error(`检测慢查询失败: ${error.message}`);
            return [];
        }
    }
};
exports.DatabaseWarmupService = DatabaseWarmupService;
exports.DatabaseWarmupService = DatabaseWarmupService = DatabaseWarmupService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource,
        database_monitor_service_1.DatabaseMonitorService])
], DatabaseWarmupService);
//# sourceMappingURL=database-warmup.service.js.map