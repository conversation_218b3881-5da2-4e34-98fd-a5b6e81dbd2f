import{j as e}from"./index-CHjq8S-S.js";import{a as p}from"./react-BUTTOX-3.js";import{a as j,b as f}from"./index-DMJlwb4Y.js";import{av as r,M as b,I as c,p as l,S as I,s as d}from"./antd-CXPM1OiB.js";const w=({open:o,onOpenChange:i,initialValues:s,onFinish:u})=>{const[t]=r.useForm(),m=!!s;p.useEffect(()=>{o&&(s?t.setFieldsValue({...s,status:s.status===1}):(t.resetFields(),t.setFieldsValue({status:!0,type:"button"})))},[o,s,t]);const h=async()=>{try{const a=await t.validateFields(),n={...a,status:a.status?1:0};m&&s?(await j({...n,id:s.id}),d.success("更新成功")):(await f(n),d.success("创建成功")),u()}catch(a){console.error("提交失败:",a),d.error("操作失败")}},x=()=>{t.resetFields(),i(!1)};return e.jsx(b,{title:m?"编辑权限":"新建权限",open:o,onOk:h,onCancel:x,destroyOnClose:!0,width:600,children:e.jsxs(r,{form:t,layout:"vertical",preserve:!1,children:[e.jsx(r.Item,{name:"name",label:"权限名称",rules:[{required:!0,message:"请输入权限名称"},{max:50,message:"权限名称不能超过50个字符"}],children:e.jsx(c,{placeholder:"请输入权限名称，如：用户新增"})}),e.jsx(r.Item,{name:"code",label:"权限代码",rules:[{required:!0,message:"请输入权限代码"},{max:100,message:"权限代码不能超过100个字符"},{pattern:/^[a-zA-Z0-9:_-]+$/,message:"权限代码只能包含字母、数字、冒号、下划线和横线"}],children:e.jsx(c,{placeholder:"请输入权限代码，如：system:user:add"})}),e.jsx(r.Item,{name:"type",label:"权限类型",rules:[{required:!0,message:"请选择权限类型"}],children:e.jsxs(l,{placeholder:"请选择权限类型",children:[e.jsx(l.Option,{value:"menu",children:"菜单权限"}),e.jsx(l.Option,{value:"button",children:"按钮权限"}),e.jsx(l.Option,{value:"api",children:"API权限"})]})}),e.jsx(r.Item,{name:"description",label:"权限描述",rules:[{max:200,message:"权限描述不能超过200个字符"}],children:e.jsx(c.TextArea,{placeholder:"请输入权限描述，如：允许用户创建新的用户账号",rows:3})}),e.jsx(r.Item,{name:"status",label:"状态",valuePropName:"checked",children:e.jsx(I,{checkedChildren:"启用",unCheckedChildren:"禁用"})})]})})};export{w as default};
