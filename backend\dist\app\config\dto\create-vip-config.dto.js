"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVipConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateVipConfigDto {
    vipLevel;
    levelName;
    requiredPoints;
    balanceRatio;
    cashRatio;
    goldRatio;
    dailyGoldReward;
    status;
    remark;
}
exports.CreateVipConfigDto = CreateVipConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级，从0开始', example: 1 }),
    (0, class_validator_1.IsNotEmpty)({ message: 'VIP等级不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: 'VIP等级必须是数字' }),
    (0, class_validator_1.Min)(0, { message: 'VIP等级不能小于0' }),
    (0, class_validator_1.Max)(99, { message: 'VIP等级不能大于99' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '等级名称', example: '青铜会员' }),
    (0, class_validator_1.IsNotEmpty)({ message: '等级名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '等级名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '等级名称长度必须在1-50个字符之间' }),
    __metadata("design:type", String)
], CreateVipConfigDto.prototype, "levelName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '达到该等级所需的积分', example: 1000 }),
    (0, class_validator_1.IsNotEmpty)({ message: '所需积分不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '所需积分必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '所需积分不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "requiredPoints", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '余额积分比例', example: 1.2, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '余额积分比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '余额积分比例不能小于0' }),
    (0, class_validator_1.Max)(999.9999, { message: '余额积分比例不能大于999.9999' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "balanceRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '现金消耗积分比例', example: 0.0002, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '现金消耗积分比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '现金消耗积分比例不能小于0' }),
    (0, class_validator_1.Max)(999.9999, { message: '现金消耗积分比例不能大于999.9999' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "cashRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币消耗积分比例', example: 0.0002, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '金币消耗积分比例必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '金币消耗积分比例不能小于0' }),
    (0, class_validator_1.Max)(999.9999, { message: '金币消耗积分比例不能大于999.9999' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "goldRatio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '每日可领取金币数量', example: 100, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '每日可领取金币数量必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '每日可领取金币数量不能小于0' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "dailyGoldReward", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateVipConfigDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注说明', example: '青铜等级会员', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '备注长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateVipConfigDto.prototype, "remark", void 0);
//# sourceMappingURL=create-vip-config.dto.js.map