"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemLocalAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
let SystemLocalAuthGuard = class SystemLocalAuthGuard extends (0, passport_1.AuthGuard)('system-local') {
};
exports.SystemLocalAuthGuard = SystemLocalAuthGuard;
exports.SystemLocalAuthGuard = SystemLocalAuthGuard = __decorate([
    (0, common_1.Injectable)()
], SystemLocalAuthGuard);
//# sourceMappingURL=local-auth.guard.js.map