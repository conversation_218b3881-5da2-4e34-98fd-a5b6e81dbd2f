"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAdConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const ad_config_entity_1 = require("../entities/ad-config.entity");
class CreateAdConfigDto {
    adIdentifier;
    adType;
    title;
    images;
    jumpType;
    jumpTarget;
    sortOrder;
    status;
    remark;
}
exports.CreateAdConfigDto = CreateAdConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '唯一字符标识，前端根据此标识渲染广告',
        example: 'banner_home_1'
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '广告标识不能为空' }),
    (0, class_validator_1.IsString)({ message: '广告标识必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '广告标识长度必须在1-50个字符之间' }),
    __metadata("design:type", String)
], CreateAdConfigDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '广告类型：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏，7-首页4宫格',
        example: 1,
        enum: ad_config_entity_1.AdType
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '广告类型不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '广告类型必须是数字' }),
    (0, class_validator_1.IsIn)([1, 2, 3, 4, 5, 6, 7], { message: '广告类型必须是1-7之间的数字' }),
    __metadata("design:type", Number)
], CreateAdConfigDto.prototype, "adType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '广告标题',
        example: '首页轮播广告'
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '广告标题不能为空' }),
    (0, class_validator_1.IsString)({ message: '广告标题必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '广告标题长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], CreateAdConfigDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '图片URL数组',
        example: ['/uploads/ads/banner1.jpg', '/uploads/ads/banner2.jpg'],
        type: [String]
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '图片不能为空' }),
    (0, class_validator_1.IsArray)({ message: '图片必须是数组格式' }),
    (0, class_validator_1.ArrayMinSize)(1, { message: '至少需要上传一张图片' }),
    (0, class_validator_1.IsString)({ each: true, message: '每个图片URL必须是字符串' }),
    (0, class_validator_1.Length)(1, 500, { each: true, message: '图片URL长度不能超过500个字符' }),
    __metadata("design:type", Array)
], CreateAdConfigDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '跳转类型：1-内部路由，2-iframe页面',
        example: 1,
        enum: ad_config_entity_1.JumpType
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '跳转类型不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '跳转类型必须是数字' }),
    (0, class_validator_1.IsIn)([1, 2], { message: '跳转类型必须是1或2' }),
    __metadata("design:type", Number)
], CreateAdConfigDto.prototype, "jumpType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '跳转目标（路由路径或URL）',
        example: '/games/hot'
    }),
    (0, class_validator_1.IsNotEmpty)({ message: '跳转目标不能为空' }),
    (0, class_validator_1.IsString)({ message: '跳转目标必须是字符串' }),
    (0, class_validator_1.Length)(1, 500, { message: '跳转目标长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateAdConfigDto.prototype, "jumpTarget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '排序，数字越小越靠前',
        example: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序不能小于0' }),
    (0, class_validator_1.Max)(9999, { message: '排序不能大于9999' }),
    __metadata("design:type", Number)
], CreateAdConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '状态：1-启用，0-禁用',
        example: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.IsIn)([0, 1], { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateAdConfigDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '备注说明',
        example: '首页轮播广告示例',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '备注长度不能超过500个字符' }),
    __metadata("design:type", String)
], CreateAdConfigDto.prototype, "remark", void 0);
//# sourceMappingURL=create-ad-config.dto.js.map