"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EffectiveBalanceRechargeConfigDto = exports.UpdateBalanceRechargeLimitDto = exports.BalanceRechargeConfigQueryDto = exports.UpdateBalanceRechargeConfigDto = exports.CreateBalanceRechargeConfigDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateBalanceRechargeConfigDto {
    tierName;
    rechargeAmount;
    activityBonusAmount;
    activityStartTime;
    activityEndTime;
    sortOrder;
    status;
}
exports.CreateBalanceRechargeConfigDto = CreateBalanceRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值挡位名称', example: '小额充值' }),
    (0, class_validator_1.IsNotEmpty)({ message: '挡位名称不能为空' }),
    (0, class_validator_1.IsString)({ message: '挡位名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '挡位名称长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], CreateBalanceRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 10.00 }),
    (0, class_validator_1.IsNotEmpty)({ message: '充值金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '充值金额必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '充值金额必须大于0' }),
    __metadata("design:type", Number)
], CreateBalanceRechargeConfigDto.prototype, "rechargeAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金额', example: 2.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活动赠送金额必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活动赠送金额不能小于0' }),
    __metadata("design:type", Number)
], CreateBalanceRechargeConfigDto.prototype, "activityBonusAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动开始时间格式不正确' }),
    __metadata("design:type", String)
], CreateBalanceRechargeConfigDto.prototype, "activityStartTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动结束时间格式不正确' }),
    __metadata("design:type", String)
], CreateBalanceRechargeConfigDto.prototype, "activityEndTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序顺序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序顺序不能小于0' }),
    __metadata("design:type", Number)
], CreateBalanceRechargeConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], CreateBalanceRechargeConfigDto.prototype, "status", void 0);
class UpdateBalanceRechargeConfigDto {
    tierName;
    rechargeAmount;
    activityBonusAmount;
    activityStartTime;
    activityEndTime;
    sortOrder;
    status;
}
exports.UpdateBalanceRechargeConfigDto = UpdateBalanceRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值挡位名称', example: '小额充值', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '挡位名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 100, { message: '挡位名称长度必须在1-100个字符之间' }),
    __metadata("design:type", String)
], UpdateBalanceRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值金额', example: 10.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '充值金额必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '充值金额必须大于0' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeConfigDto.prototype, "rechargeAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金额', example: 2.00, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '活动赠送金额必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '活动赠送金额不能小于0' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeConfigDto.prototype, "activityBonusAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动开始时间', example: '2025-08-01T00:00:00.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动开始时间格式不正确' }),
    __metadata("design:type", String)
], UpdateBalanceRechargeConfigDto.prototype, "activityStartTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动结束时间', example: '2025-08-31T23:59:59.000Z', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: '活动结束时间格式不正确' }),
    __metadata("design:type", String)
], UpdateBalanceRechargeConfigDto.prototype, "activityEndTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '排序顺序必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '排序顺序不能小于0' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeConfigDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeConfigDto.prototype, "status", void 0);
class BalanceRechargeConfigQueryDto {
    status;
}
exports.BalanceRechargeConfigQueryDto = BalanceRechargeConfigQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '状态筛选：1-启用，0-禁用', example: 1, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: '状态必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '状态值必须是0或1' }),
    (0, class_validator_1.Max)(1, { message: '状态值必须是0或1' }),
    __metadata("design:type", Number)
], BalanceRechargeConfigQueryDto.prototype, "status", void 0);
class UpdateBalanceRechargeLimitDto {
    limitName;
    minAmount;
    maxAmount;
    remark;
}
exports.UpdateBalanceRechargeLimitDto = UpdateBalanceRechargeLimitDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '限制配置名称', example: '默认充值限制' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '限制配置名称必须是字符串' }),
    (0, class_validator_1.Length)(1, 50, { message: '限制配置名称长度必须在1-50字符之间' }),
    __metadata("design:type", String)
], UpdateBalanceRechargeLimitDto.prototype, "limitName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最低充值金额', example: 1.00 }),
    (0, class_validator_1.IsNotEmpty)({ message: '最低充值金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '最低充值金额必须是数字' }),
    (0, class_validator_1.Min)(0.01, { message: '最低充值金额必须大于0' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeLimitDto.prototype, "minAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最高充值金额，0表示不限制', example: 10000.00 }),
    (0, class_validator_1.IsNotEmpty)({ message: '最高充值金额不能为空' }),
    (0, class_validator_1.IsNumber)({}, { message: '最高充值金额必须是数字' }),
    (0, class_validator_1.Min)(0, { message: '最高充值金额不能小于0' }),
    __metadata("design:type", Number)
], UpdateBalanceRechargeLimitDto.prototype, "maxAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '备注信息', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: '备注信息必须是字符串' }),
    (0, class_validator_1.Length)(0, 500, { message: '备注信息长度不能超过500字符' }),
    __metadata("design:type", String)
], UpdateBalanceRechargeLimitDto.prototype, "remark", void 0);
class EffectiveBalanceRechargeConfigDto {
    id;
    tierName;
    rechargeAmount;
    effectiveRechargeAmount;
    activityBonusAmount;
    isActivityActive;
    activityStatusDescription;
    sortOrder;
}
exports.EffectiveBalanceRechargeConfigDto = EffectiveBalanceRechargeConfigDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '配置ID' }),
    __metadata("design:type", Number)
], EffectiveBalanceRechargeConfigDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '挡位名称' }),
    __metadata("design:type", String)
], EffectiveBalanceRechargeConfigDto.prototype, "tierName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '基础充值金额' }),
    __metadata("design:type", Number)
], EffectiveBalanceRechargeConfigDto.prototype, "rechargeAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '当前生效的充值金额（基础+活动赠送）' }),
    __metadata("design:type", Number)
], EffectiveBalanceRechargeConfigDto.prototype, "effectiveRechargeAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动赠送金额（仅活动期间有效）' }),
    __metadata("design:type", Number)
], EffectiveBalanceRechargeConfigDto.prototype, "activityBonusAmount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否在活动期间' }),
    __metadata("design:type", Boolean)
], EffectiveBalanceRechargeConfigDto.prototype, "isActivityActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '活动状态描述' }),
    __metadata("design:type", String)
], EffectiveBalanceRechargeConfigDto.prototype, "activityStatusDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '排序顺序' }),
    __metadata("design:type", Number)
], EffectiveBalanceRechargeConfigDto.prototype, "sortOrder", void 0);
//# sourceMappingURL=balance-recharge-config.dto.js.map