export declare class CreateTransactionDto {
    userId: number;
    amount: number;
    status: number;
    transactionType: number;
    orderId?: string;
    description?: string;
    remark?: string;
    operatorId?: number;
}
export declare class TransactionListQueryDto {
    userId: number;
    status?: number;
    transactionType?: number;
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
}
export declare class TransactionStatisticsResponseDto {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
}
export declare class UserAssetStatisticsResponseDto {
    cash: TransactionStatisticsResponseDto;
    gold: TransactionStatisticsResponseDto;
    recharge: TransactionStatisticsResponseDto;
}
export declare class TransactionItemDto {
    id: number;
    userId: number;
    uid: number;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    status: number;
    transactionType: number;
    orderId: string;
    description: string;
    remark: string;
    operatorId: number;
    createTime: Date;
    updateTime: Date;
}
export declare class TransactionListResponseDto {
    list: TransactionItemDto[];
    total: number;
    page: number;
    pageSize: number;
}
