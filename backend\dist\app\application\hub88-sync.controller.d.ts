import { Hub88SyncService, Hub88SyncResult } from './hub88-sync.service';
export declare class Hub88SyncController {
    private readonly hub88SyncService;
    constructor(hub88SyncService: Hub88SyncService);
    syncGames(): Promise<{
        code: number;
        message: string;
        result: Hub88SyncResult;
    }>;
    getSyncStatus(): Promise<{
        code: number;
        message: string;
        result: {
            provider_configured: boolean;
            last_sync: string;
            total_hub88_games: number;
            active_games: number;
            inactive_games: number;
        };
    }>;
}
