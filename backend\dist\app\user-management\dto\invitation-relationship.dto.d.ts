export declare class InvitationUserDto {
    id: number;
    uid: number;
    username: string;
    nickname: string;
    avatar: string;
    status: number;
    createTime: Date;
    lastLoginTime: Date;
}
export declare class InvitationRelationshipDto {
    inviter?: InvitationUserDto;
    invitees: InvitationUserDto[];
    invitationPath: string;
    invitationLevel: number;
    totalInvitees: number;
    directInvitees: number;
    indirectInvitees: number;
}
