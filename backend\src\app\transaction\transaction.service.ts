import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction, CashTransactionStatus, CashTransactionType } from '../entities/cash-transaction.entity';
import { GoldTransaction, GoldTransactionStatus, GoldTransactionType } from '../entities/gold-transaction.entity';
import { RechargeTransaction, RechargeTransactionStatus, RechargeTransactionType } from '../entities/recharge-transaction.entity';

// DTO接口定义
export interface CreateTransactionDto {
  userId: number;
  amount: number;
  status: number;
  transactionType: number;
  orderId?: string;
  description?: string;
  remark?: string;
  operatorId?: number;
}

export interface TransactionStatistics {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
  // 新增详细统计
  betAmount?: number;        // 累计下注
  winAmount?: number;        // 累计赢金
  betWinRatio?: number;      // 下注/赢金占比
  depositAmount?: number;    // 充值总额
  vipCardAmount?: number;    // VIP卡领取总额
  activityAmount?: number;   // 推广活动总额
  inviteActivityAmount?: number; // 邀请/活动获取
}

export interface TransactionListQuery {
  userId: number;
  status?: number;
  transactionType?: number;
  startDate?: string | Date;
  endDate?: string | Date;
  page?: number;
  pageSize?: number;
}

export interface TransactionListResponse {
  list: any[];
  total: number;
  page: number;
  pageSize: number;
}

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(AppUser)
    private readonly userRepository: Repository<AppUser>,
    @InjectRepository(CashTransaction)
    private readonly cashTransactionRepository: Repository<CashTransaction>,
    @InjectRepository(GoldTransaction)
    private readonly goldTransactionRepository: Repository<GoldTransaction>,
    @InjectRepository(RechargeTransaction)
    private readonly rechargeTransactionRepository: Repository<RechargeTransaction>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 记录现金交易
   */
  async createCashTransaction(dto: CreateTransactionDto): Promise<CashTransaction> {
    return this.dataSource.transaction(async (manager) => {
      // 获取用户当前余额
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.withdrawableBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === CashTransactionStatus.INCOME 
        ? balanceBefore + amount 
        : balanceBefore - amount;

      // 创建交易记录
      const transaction = manager.create(CashTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      // 更新用户可提现余额
      await manager.update(AppUser, dto.userId, { withdrawableBalance: balanceAfter });

      return manager.save(CashTransaction, transaction);
    });
  }

  /**
   * 记录金币交易
   */
  async createGoldTransaction(dto: CreateTransactionDto): Promise<GoldTransaction> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.goldBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === GoldTransactionStatus.INCOME 
        ? balanceBefore + amount 
        : balanceBefore - amount;

      const transaction = manager.create(GoldTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      await manager.update(AppUser, dto.userId, { goldBalance: balanceAfter });

      return manager.save(GoldTransaction, transaction);
    });
  }

  /**
   * 记录充值余额交易
   */
  async createRechargeTransaction(dto: CreateTransactionDto): Promise<RechargeTransaction> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.rechargeBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === RechargeTransactionStatus.INCOME
        ? balanceBefore + amount
        : balanceBefore - amount;

      const transaction = manager.create(RechargeTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      await manager.update(AppUser, dto.userId, { rechargeBalance: balanceAfter });

      return manager.save(RechargeTransaction, transaction);
    });
  }

  /**
   * 获取现金交易统计
   */
  async getCashStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.cashTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount',
        // 下注金额 (支出状态的下注类型)
        'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
        // 赢金金额 (收入状态的赢金类型)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
        // 邀请/活动获取 (收入状态的充值、金币兑换、充值余额兑换)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType IN (3, 5, 6) THEN t.amount ELSE 0 END) as inviteActivityAmount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });

    const betAmount = Number(result.betAmount) || 0;
    const winAmount = Number(result.winAmount) || 0;
    const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;

    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.withdrawableBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
      betAmount,
      winAmount,
      betWinRatio: Number(betWinRatio.toFixed(2)),
      inviteActivityAmount: Number(result.inviteActivityAmount) || 0,
    };
  }

  /**
   * 获取金币交易统计
   */
  async getGoldStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.goldTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount',
        // 下注金额 (支出状态的下注类型)
        'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
        // 赢金金额 (收入状态的赢金类型)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
        // VIP卡领取总额 (收入状态的VIP卡领取类型)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType = 3 THEN t.amount ELSE 0 END) as vipCardAmount',
        // 推广活动总额 (收入状态的赠送、交易获取、活动领取)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType IN (4, 5, 6) THEN t.amount ELSE 0 END) as activityAmount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });

    const betAmount = Number(result.betAmount) || 0;
    const winAmount = Number(result.winAmount) || 0;
    const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;

    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.goldBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
      betAmount,
      winAmount,
      betWinRatio: Number(betWinRatio.toFixed(2)),
      vipCardAmount: Number(result.vipCardAmount) || 0,
      activityAmount: Number(result.activityAmount) || 0,
    };
  }

  /**
   * 获取充值余额交易统计
   */
  async getRechargeStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.rechargeTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount',
        // 下注金额 (支出状态的下注类型)
        'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
        // 赢金金额 (收入状态的赢金类型)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
        // 充值总额 (收入状态的充值类型)
        'SUM(CASE WHEN t.status = 1 AND t.transactionType = 3 THEN t.amount ELSE 0 END) as depositAmount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });

    const betAmount = Number(result.betAmount) || 0;
    const winAmount = Number(result.winAmount) || 0;
    const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;

    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.rechargeBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
      betAmount,
      winAmount,
      betWinRatio: Number(betWinRatio.toFixed(2)),
      depositAmount: Number(result.depositAmount) || 0,
    };
  }

  /**
   * 获取现金交易明细列表
   */
  async getCashTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.cashTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取金币交易明细列表
   */
  async getGoldTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.goldTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取充值余额交易明细列表
   */
  async getRechargeTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.rechargeTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取用户所有资产统计
   */
  async getUserAssetStatistics(userId: number) {
    const [cashStats, goldStats, rechargeStats] = await Promise.all([
      this.getCashStatistics(userId),
      this.getGoldStatistics(userId),
      this.getRechargeStatistics(userId),
    ]);

    return {
      cash: cashStats,
      gold: goldStats,
      recharge: rechargeStats,
    };
  }
}
