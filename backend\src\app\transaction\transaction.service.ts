import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction, CashTransactionStatus, CashTransactionType } from '../entities/cash-transaction.entity';
import { GoldTransaction, GoldTransactionStatus, GoldTransactionType } from '../entities/gold-transaction.entity';
import { RechargeTransaction, RechargeTransactionStatus, RechargeTransactionType } from '../entities/recharge-transaction.entity';

// DTO接口定义
export interface CreateTransactionDto {
  userId: number;
  amount: number;
  status: number;
  transactionType: number;
  orderId?: string;
  description?: string;
  remark?: string;
  operatorId?: number;
}

export interface TransactionStatistics {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
}

export interface TransactionListQuery {
  userId: number;
  status?: number;
  transactionType?: number;
  startDate?: string | Date;
  endDate?: string | Date;
  page?: number;
  pageSize?: number;
}

export interface TransactionListResponse {
  list: any[];
  total: number;
  page: number;
  pageSize: number;
}

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(AppUser)
    private readonly userRepository: Repository<AppUser>,
    @InjectRepository(CashTransaction)
    private readonly cashTransactionRepository: Repository<CashTransaction>,
    @InjectRepository(GoldTransaction)
    private readonly goldTransactionRepository: Repository<GoldTransaction>,
    @InjectRepository(RechargeTransaction)
    private readonly rechargeTransactionRepository: Repository<RechargeTransaction>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 记录现金交易
   */
  async createCashTransaction(dto: CreateTransactionDto): Promise<CashTransaction> {
    return this.dataSource.transaction(async (manager) => {
      // 获取用户当前余额
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.withdrawableBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === CashTransactionStatus.INCOME 
        ? balanceBefore + amount 
        : balanceBefore - amount;

      // 创建交易记录
      const transaction = manager.create(CashTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      // 更新用户可提现余额
      await manager.update(AppUser, dto.userId, { withdrawableBalance: balanceAfter });

      return manager.save(CashTransaction, transaction);
    });
  }

  /**
   * 记录金币交易
   */
  async createGoldTransaction(dto: CreateTransactionDto): Promise<GoldTransaction> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.goldBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === GoldTransactionStatus.INCOME 
        ? balanceBefore + amount 
        : balanceBefore - amount;

      const transaction = manager.create(GoldTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      await manager.update(AppUser, dto.userId, { goldBalance: balanceAfter });

      return manager.save(GoldTransaction, transaction);
    });
  }

  /**
   * 记录充值余额交易
   */
  async createRechargeTransaction(dto: CreateTransactionDto): Promise<RechargeTransaction> {
    return this.dataSource.transaction(async (manager) => {
      const user = await manager.findOne(AppUser, { where: { id: dto.userId } });
      if (!user) {
        throw new NotFoundException('用户不存在');
      }

      const balanceBefore = Number(user.rechargeBalance) || 0;
      const amount = Number(dto.amount);
      const balanceAfter = dto.status === RechargeTransactionStatus.INCOME
        ? balanceBefore + amount
        : balanceBefore - amount;

      const transaction = manager.create(RechargeTransaction, {
        userId: dto.userId,
        uid: user.uid,
        amount,
        balanceBefore,
        balanceAfter,
        status: dto.status,
        transactionType: dto.transactionType,
        orderId: dto.orderId,
        description: dto.description,
        remark: dto.remark,
        operatorId: dto.operatorId,
      });

      await manager.update(AppUser, dto.userId, { rechargeBalance: balanceAfter });

      return manager.save(RechargeTransaction, transaction);
    });
  }

  /**
   * 获取现金交易统计
   */
  async getCashStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.cashTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.withdrawableBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
    };
  }

  /**
   * 获取金币交易统计
   */
  async getGoldStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.goldTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });
    
    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.goldBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
    };
  }

  /**
   * 获取充值余额交易统计
   */
  async getRechargeStatistics(userId: number): Promise<TransactionStatistics> {
    const result = await this.rechargeTransactionRepository
      .createQueryBuilder('t')
      .select([
        'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
        'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
        'COUNT(*) as transactionCount'
      ])
      .where('t.userId = :userId', { userId })
      .getRawOne();

    const user = await this.userRepository.findOne({ where: { id: userId } });

    return {
      totalIncome: Number(result.totalIncome) || 0,
      totalExpense: Number(result.totalExpense) || 0,
      balance: Number(user?.rechargeBalance) || 0,
      transactionCount: Number(result.transactionCount) || 0,
    };
  }

  /**
   * 获取现金交易明细列表
   */
  async getCashTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.cashTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createTime', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createTime >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createTime <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取金币交易明细列表
   */
  async getGoldTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.goldTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createTime', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createTime >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createTime <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取钻石交易明细列表
   */
  async getDiamondTransactionList(query: TransactionListQuery): Promise<TransactionListResponse> {
    const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;

    const queryBuilder = this.diamondTransactionRepository
      .createQueryBuilder('t')
      .where('t.userId = :userId', { userId })
      .orderBy('t.createTime', 'DESC');

    if (status) {
      queryBuilder.andWhere('t.status = :status', { status });
    }

    if (transactionType) {
      queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
    }

    if (startDate) {
      const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
      queryBuilder.andWhere('t.createTime >= :startDate', { startDate: startDateObj });
    }

    if (endDate) {
      const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
      queryBuilder.andWhere('t.createTime <= :endDate', { endDate: endDateObj });
    }

    const [list, total] = await queryBuilder
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .getManyAndCount();

    return {
      list,
      total,
      page,
      pageSize,
    };
  }

  /**
   * 获取用户所有资产统计
   */
  async getUserAssetStatistics(userId: number) {
    const [cashStats, goldStats, diamondStats] = await Promise.all([
      this.getCashStatistics(userId),
      this.getGoldStatistics(userId),
      this.getDiamondStatistics(userId),
    ]);

    return {
      cash: cashStats,
      gold: goldStats,
      diamond: diamondStats,
    };
  }
}
