import { VipConfigService } from './vip-config.service';
import { DailyRewardService } from './daily-reward.service';
import { CreateVipConfigDto } from './dto/create-vip-config.dto';
import { UpdateVipConfigDto } from './dto/update-vip-config.dto';
import { VipConfigQueryDto } from './dto/vip-config-query.dto';
export declare class VipConfigController {
    private readonly vipConfigService;
    private readonly dailyRewardService;
    constructor(vipConfigService: VipConfigService, dailyRewardService: DailyRewardService);
    create(createVipConfigDto: CreateVipConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").VipConfig;
    }>;
    findAll(query: VipConfigQueryDto): Promise<{
        code: number;
        message: string;
        result: {
            list: {
                creator: {
                    id: number;
                    username: string;
                } | null;
                updater: {
                    id: number;
                    username: string;
                } | null;
                id: number;
                vipLevel: number;
                levelName: string;
                requiredPoints: number;
                balanceRatio: number;
                cashRatio: number;
                goldRatio: number;
                dailyGoldReward: number;
                status: number;
                remark: string;
                createdBy: number;
                updatedBy: number;
                createTime: Date;
                updateTime: Date;
            }[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    getActiveLevels(): Promise<{
        code: number;
        message: string;
        result: import("./entities").VipConfig[];
    }>;
    findOne(id: number): Promise<{
        code: number;
        message: string;
        result: {
            creator: {
                id: number;
                username: string;
            } | null;
            updater: {
                id: number;
                username: string;
            } | null;
            id: number;
            vipLevel: number;
            levelName: string;
            requiredPoints: number;
            balanceRatio: number;
            cashRatio: number;
            goldRatio: number;
            dailyGoldReward: number;
            status: number;
            remark: string;
            createdBy: number;
            updatedBy: number;
            createTime: Date;
            updateTime: Date;
        };
    }>;
    update(id: number, updateVipConfigDto: UpdateVipConfigDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("./entities").VipConfig;
    }>;
    remove(id: number): Promise<{
        code: number;
        message: string;
    }>;
    calculateUserPoints(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            totalPoints: number;
            breakdown: any;
        };
    }>;
    updateUserVipLevel(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            oldLevel: number;
            newLevel: number;
            points: number;
        };
    }>;
    recalculateAllUserVipLevels(): Promise<{
        code: number;
        message: string;
        result: {
            processed: number;
            upgraded: number;
        };
    }>;
    claimDailyGoldReward(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            success: boolean;
            goldAmount: number;
            message: string;
        };
    }>;
    getDailyRewardStatus(userId: number): Promise<{
        code: number;
        message: string;
        result: {
            canClaim: boolean;
            goldAmount: number;
            vipLevel: number;
            levelName: string;
            alreadyClaimed: boolean;
        };
    }>;
    getDailyRewardHistory(userId: number, page?: number, pageSize?: number): Promise<{
        code: number;
        message: string;
        result: {
            list: {
                id: number;
                amount: number;
                vipLevel: string;
                claimTime: Date;
                remark: string;
            }[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
}
