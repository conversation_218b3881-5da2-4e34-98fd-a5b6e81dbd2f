import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AppUser } from './app-user.entity';

// 金币交易状态枚举
export enum GoldTransactionStatus {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
}

// 金币交易类型枚举
export enum GoldTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  VIP_CARD_CLAIM = 3, // 会员卡领取
  GIFT = 4, // 赠送
  TRADE_ACQUIRE = 5, // 交易获取
  ACTIVITY_CLAIM = 6, // 活动领取
}

@Entity('gold_transactions')
export class GoldTransaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'transaction_id' })
  transactionId: string;

  @Column({ type: 'bigint' })
  amount: number;

  @Column({ name: 'balance_before', type: 'bigint' })
  balanceBefore: number;

  @Column({ name: 'balance_after', type: 'bigint' })
  balanceAfter: number;

  @Column({ type: 'int', comment: '状态：1-收入，2-支出' })
  status: GoldTransactionStatus;

  @Column({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-会员卡领取，4-赠送，5-交易获取，6-活动领取' })
  transactionType: GoldTransactionType;

  @Column({ nullable: true, length: 255 })
  description: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => AppUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: AppUser;
}

// 交易类型标签映射
export const GoldTransactionTypeLabels = {
  [GoldTransactionType.BET]: '下注',
  [GoldTransactionType.WIN]: '赢金',
  [GoldTransactionType.VIP_CARD_CLAIM]: '会员卡领取',
  [GoldTransactionType.GIFT]: '赠送',
  [GoldTransactionType.TRADE_ACQUIRE]: '交易获取',
  [GoldTransactionType.ACTIVITY_CLAIM]: '活动领取',
};

// 交易状态标签映射
export const GoldTransactionStatusLabels = {
  [GoldTransactionStatus.INCOME]: '收入',
  [GoldTransactionStatus.EXPENSE]: '支出',
};
