"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MarketingAd = void 0;
const typeorm_1 = require("typeorm");
const marketing_channel_entity_1 = require("./marketing-channel.entity");
const promotional_page_entity_1 = require("./promotional-page.entity");
const app_user_entity_1 = require("./app-user.entity");
let MarketingAd = class MarketingAd {
    id;
    channelId;
    name;
    identifier;
    status;
    description;
    budget;
    startDate;
    endDate;
    targetAudience;
    adType;
    referrerId;
    channel;
    promotionalPages;
    users;
    createdAt;
    updatedAt;
};
exports.MarketingAd = MarketingAd;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], MarketingAd.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'channel_id' }),
    __metadata("design:type", Number)
], MarketingAd.prototype, "channelId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], MarketingAd.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], MarketingAd.prototype, "identifier", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], MarketingAd.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], MarketingAd.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], MarketingAd.prototype, "budget", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true, name: 'start_date' }),
    __metadata("design:type", Date)
], MarketingAd.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true, name: 'end_date' }),
    __metadata("design:type", Date)
], MarketingAd.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true, name: 'target_audience' }),
    __metadata("design:type", Object)
], MarketingAd.prototype, "targetAudience", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'smallint',
        default: 1,
        name: 'ad_type',
        comment: '广告类型: 1=媒体买量, 2=推荐系统引流'
    }),
    __metadata("design:type", Number)
], MarketingAd.prototype, "adType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'bigint',
        nullable: true,
        name: 'referrer_id',
        comment: '推荐人ID，当ad_type=2时必填，关联users表的id'
    }),
    __metadata("design:type", Number)
], MarketingAd.prototype, "referrerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => marketing_channel_entity_1.MarketingChannel, (channel) => channel.ads),
    (0, typeorm_1.JoinColumn)({ name: 'channel_id' }),
    __metadata("design:type", marketing_channel_entity_1.MarketingChannel)
], MarketingAd.prototype, "channel", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => promotional_page_entity_1.PromotionalPage, (page) => page.ad),
    __metadata("design:type", Array)
], MarketingAd.prototype, "promotionalPages", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_user_entity_1.AppUser, (user) => user.ad),
    __metadata("design:type", Array)
], MarketingAd.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], MarketingAd.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], MarketingAd.prototype, "updatedAt", void 0);
exports.MarketingAd = MarketingAd = __decorate([
    (0, typeorm_1.Entity)('marketing_ads')
], MarketingAd);
//# sourceMappingURL=marketing-ad.entity.js.map