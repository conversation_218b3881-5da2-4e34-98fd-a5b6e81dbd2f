"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUserAddress = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
let AppUserAddress = class AppUserAddress {
    id;
    userId;
    name;
    phone;
    province;
    city;
    district;
    address;
    postalCode;
    isDefault;
    user;
    createTime;
    updateTime;
};
exports.AppUserAddress = AppUserAddress;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppUserAddress.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], AppUserAddress.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "province", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "city", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "district", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "address", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'postal_code', nullable: true, length: 10 }),
    __metadata("design:type", String)
], AppUserAddress.prototype, "postalCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_default', default: 0, comment: '是否默认地址：1-是，0-否' }),
    __metadata("design:type", Number)
], AppUserAddress.prototype, "isDefault", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser, (user) => user.addresses),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], AppUserAddress.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppUserAddress.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppUserAddress.prototype, "updateTime", void 0);
exports.AppUserAddress = AppUserAddress = __decorate([
    (0, typeorm_1.Entity)('app_user_addresses')
], AppUserAddress);
//# sourceMappingURL=app-user-address.entity.js.map