import{a$ as u,b3 as Yn,b4 as Kn,b5 as He,b6 as Gn,t as Ie,y as fe,b7 as qn,b8 as we,a_ as Pe,l as ot,b9 as Xn,aZ as Ee,al as ze,ba as B,d as Tt,bb as xe,bc as Zn,a5 as Jn,bd as Kt,P as Qn,b0 as Ae,b1 as pe,be as ft,ap as er,ao as tr,bf as wt,bg as nr,n as Gt,bh as qt,H as Cn,aE as Pn,bi as rr,bj as Rt,u as ir,bk as Mt,av as Ve,bl as ar}from"./antd-CXPM1OiB.js";import{c as or,a as p,R as me,g as Fn}from"./react-BUTTOX-3.js";import{j as M}from"./index-CHjq8S-S.js";var lr=u(u({},Yn),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});const Tn={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]},It={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},lr),timePickerLocale:Object.assign({},Tn)};It.lang.ok="确定";const Se="${label}不是一个有效的${type}",sr={locale:"zh-cn",Pagination:Kn,DatePicker:It,TimePicker:Tn,Calendar:It,global:{placeholder:"请选择"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:Se,method:Se,array:Se,object:Se,number:Se,date:Se,boolean:Se,integer:Se,float:Se,regexp:Se,email:Se,url:Se,hex:Se},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};var mt={exports:{}},gt={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xt;function cr(){if(Xt)return gt;Xt=1;var n=or();function e(b,h){return b===h&&(b!==0||1/b===1/h)||b!==b&&h!==h}var t=typeof Object.is=="function"?Object.is:e,r=n.useState,i=n.useEffect,a=n.useLayoutEffect,o=n.useDebugValue;function l(b,h){var v=h(),S=r({inst:{value:v,getSnapshot:h}}),f=S[0].inst,P=S[1];return a(function(){f.value=v,f.getSnapshot=h,s(f)&&P({inst:f})},[b,v,h]),i(function(){return s(f)&&P({inst:f}),b(function(){s(f)&&P({inst:f})})},[b]),o(v),v}function s(b){var h=b.getSnapshot;b=b.value;try{var v=h();return!t(b,v)}catch{return!0}}function d(b,h){return h()}var C=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?d:l;return gt.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:C,gt}var Zt;function dr(){return Zt||(Zt=1,mt.exports=cr()),mt.exports}var ur=dr();const wn=0,Rn=1,Mn=2,Jt=3;var Qt=Object.prototype.hasOwnProperty;function At(n,e){var t,r;if(n===e)return!0;if(n&&e&&(t=n.constructor)===e.constructor){if(t===Date)return n.getTime()===e.getTime();if(t===RegExp)return n.toString()===e.toString();if(t===Array){if((r=n.length)===e.length)for(;r--&&At(n[r],e[r]););return r===-1}if(!t||typeof n=="object"){r=0;for(t in n)if(Qt.call(n,t)&&++r&&!Qt.call(e,t)||!(t in e)||!At(n[t],e[t]))return!1;return Object.keys(e).length===r}}return n!==n&&e!==e}const Le=new WeakMap,Oe=()=>{},be=Oe(),lt=Object,te=n=>n===be,Me=n=>typeof n=="function",ke=(n,e)=>({...n,...e}),In=n=>Me(n.then),ht={},Qe={},Nt="undefined",Ze=typeof window!=Nt,Et=typeof document!=Nt,fr=Ze&&"Deno"in window,mr=()=>Ze&&typeof window.requestAnimationFrame!=Nt,An=(n,e)=>{const t=Le.get(n);return[()=>!te(e)&&n.get(e)||ht,r=>{if(!te(e)){const i=n.get(e);e in Qe||(Qe[e]=i),t[5](e,ke(i,r),i||ht)}},t[6],()=>!te(e)&&e in Qe?Qe[e]:!te(e)&&n.get(e)||ht]};let Dt=!0;const gr=()=>Dt,[Lt,kt]=Ze&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[Oe,Oe],hr=()=>{const n=Et&&document.visibilityState;return te(n)||n!=="hidden"},pr=n=>(Et&&document.addEventListener("visibilitychange",n),Lt("focus",n),()=>{Et&&document.removeEventListener("visibilitychange",n),kt("focus",n)}),vr=n=>{const e=()=>{Dt=!0,n()},t=()=>{Dt=!1};return Lt("online",e),Lt("offline",t),()=>{kt("online",e),kt("offline",t)}},br={isOnline:gr,isVisible:hr},yr={initFocus:pr,initReconnect:vr},en=!me.useId,Ye=!Ze||fr,xr=n=>mr()?window.requestAnimationFrame(n):setTimeout(n,1),it=Ye?p.useEffect:p.useLayoutEffect,pt=typeof navigator<"u"&&navigator.connection,tn=!Ye&&pt&&(["slow-2g","2g"].includes(pt.effectiveType)||pt.saveData),et=new WeakMap,vt=(n,e)=>lt.prototype.toString.call(n)===`[object ${e}]`;let Sr=0;const jt=n=>{const e=typeof n,t=vt(n,"Date"),r=vt(n,"RegExp"),i=vt(n,"Object");let a,o;if(lt(n)===n&&!t&&!r){if(a=et.get(n),a)return a;if(a=++Sr+"~",et.set(n,a),Array.isArray(n)){for(a="@",o=0;o<n.length;o++)a+=jt(n[o])+",";et.set(n,a)}if(i){a="#";const l=lt.keys(n).sort();for(;!te(o=l.pop());)te(n[o])||(a+=o+":"+jt(n[o])+",");et.set(n,a)}}else a=t?n.toJSON():e=="symbol"?n.toString():e=="string"?JSON.stringify(n):""+n;return a},Ht=n=>{if(Me(n))try{n=n()}catch{n=""}const e=n;return n=typeof n=="string"?n:(Array.isArray(n)?n.length:n)?jt(n):"",[n,e]};let Cr=0;const Ot=()=>++Cr;async function En(...n){const[e,t,r,i]=n,a=ke({populateCache:!0,throwOnError:!0},typeof i=="boolean"?{revalidate:i}:i||{});let o=a.populateCache;const l=a.rollbackOnError;let s=a.optimisticData;const d=h=>typeof l=="function"?l(h):l!==!1,C=a.throwOnError;if(Me(t)){const h=t,v=[],S=e.keys();for(const f of S)!/^\$(inf|sub)\$/.test(f)&&h(e.get(f)._k)&&v.push(f);return Promise.all(v.map(b))}return b(t);async function b(h){const[v]=Ht(h);if(!v)return;const[S,f]=An(e,v),[P,F,g,m]=Le.get(e),w=()=>{const G=P[v];return(Me(a.revalidate)?a.revalidate(S().data,h):a.revalidate!==!1)&&(delete g[v],delete m[v],G&&G[0])?G[0](Mn).then(()=>S().data):S().data};if(n.length<3)return w();let D=r,j;const V=Ot();F[v]=[V,0];const O=!te(s),H=S(),W=H.data,U=H._c,X=te(U)?W:U;if(O&&(s=Me(s)?s(X,W):s,f({data:s,_c:X})),Me(D))try{D=D(X)}catch(G){j=G}if(D&&In(D))if(D=await D.catch(G=>{j=G}),V!==F[v][0]){if(j)throw j;return D}else j&&O&&d(j)&&(o=!0,f({data:X,_c:be}));if(o&&!j)if(Me(o)){const G=o(D,X);f({data:G,error:be,_c:be})}else f({data:D,error:be,_c:be});if(F[v][1]=Ot(),Promise.resolve(w()).then(()=>{f({_c:be})}),j){if(C)throw j;return}return D}}const nn=(n,e)=>{for(const t in n)n[t][0]&&n[t][0](e)},Dn=(n,e)=>{if(!Le.has(n)){const t=ke(yr,e),r=Object.create(null),i=En.bind(be,n);let a=Oe;const o=Object.create(null),l=(C,b)=>{const h=o[C]||[];return o[C]=h,h.push(b),()=>h.splice(h.indexOf(b),1)},s=(C,b,h)=>{n.set(C,b);const v=o[C];if(v)for(const S of v)S(b,h)},d=()=>{if(!Le.has(n)&&(Le.set(n,[r,Object.create(null),Object.create(null),Object.create(null),i,s,l]),!Ye)){const C=t.initFocus(setTimeout.bind(be,nn.bind(be,r,wn))),b=t.initReconnect(setTimeout.bind(be,nn.bind(be,r,Rn)));a=()=>{C&&C(),b&&b(),Le.delete(n)}}};return d(),[n,i,d,a]}return[n,Le.get(n)[4]]},Pr=(n,e,t,r,i)=>{const a=t.errorRetryCount,o=i.retryCount,l=~~((Math.random()+.5)*(1<<(o<8?o:8)))*t.errorRetryInterval;!te(a)&&o>a||setTimeout(r,l,i)},Fr=At,[Vt,Tr]=Dn(new Map),Ln=ke({onLoadingSlow:Oe,onSuccess:Oe,onError:Oe,onErrorRetry:Pr,onDiscarded:Oe,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:tn?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:tn?5e3:3e3,compare:Fr,isPaused:()=>!1,cache:Vt,mutate:Tr,fallback:{}},br),kn=(n,e)=>{const t=ke(n,e);if(e){const{use:r,fallback:i}=n,{use:a,fallback:o}=e;r&&a&&(t.use=r.concat(a)),i&&o&&(t.fallback=ke(i,o))}return t},$t=p.createContext({}),wr=n=>{const{value:e}=n,t=p.useContext($t),r=Me(e),i=p.useMemo(()=>r?e(t):e,[r,t,e]),a=p.useMemo(()=>r?i:kn(t,i),[r,t,i]),o=i&&i.provider,l=p.useRef(be);o&&!l.current&&(l.current=Dn(o(a.cache||Vt),i));const s=l.current;return s&&(a.cache=s[0],a.mutate=s[1]),it(()=>{if(s)return s[2]&&s[2](),s[3]},[]),p.createElement($t.Provider,ke(n,{value:a}))},Rr="$inf$",jn=Ze&&window.__SWR_DEVTOOLS_USE__,Mr=jn?window.__SWR_DEVTOOLS_USE__:[],Ir=()=>{jn&&(window.__SWR_DEVTOOLS_REACT__=me)},Ar=n=>Me(n[1])?[n[0],n[1],n[2]||{}]:[n[0],null,(n[1]===null?n[2]:n[1])||{}],On=()=>ke(Ln,p.useContext($t)),Er=n=>(e,t,r)=>n(e,t&&((...a)=>{const[o]=Ht(e),[,,,l]=Le.get(Vt);if(o.startsWith(Rr))return t(...a);const s=l[o];return te(s)?t(...a):(delete l[o],s)}),r),Dr=Mr.concat(Er),Lr=n=>function(...t){const r=On(),[i,a,o]=Ar(t),l=kn(r,o);let s=n;const{use:d}=l,C=(d||[]).concat(Dr);for(let b=C.length;b--;)s=C[b](s);return s(i,a||l.fetcher||null,l)},kr=(n,e,t)=>{const r=e[n]||(e[n]=[]);return r.push(t),()=>{const i=r.indexOf(t);i>=0&&(r[i]=r[r.length-1],r.pop())}};Ir();const bt=me.use||(n=>{switch(n.status){case"pending":throw n;case"fulfilled":return n.value;case"rejected":throw n.reason;default:throw n.status="pending",n.then(e=>{n.status="fulfilled",n.value=e},e=>{n.status="rejected",n.reason=e}),n}}),yt={dedupe:!0},jr=(n,e,t)=>{const{cache:r,compare:i,suspense:a,fallbackData:o,revalidateOnMount:l,revalidateIfStale:s,refreshInterval:d,refreshWhenHidden:C,refreshWhenOffline:b,keepPreviousData:h}=t,[v,S,f,P]=Le.get(r),[F,g]=Ht(n),m=p.useRef(!1),w=p.useRef(!1),D=p.useRef(F),j=p.useRef(e),V=p.useRef(t),O=()=>V.current,H=()=>O().isVisible()&&O().isOnline(),[W,U,X,G]=An(r,F),z=p.useRef({}).current,E=te(o)?te(t.fallback)?be:t.fallback[F]:o,c=(q,ee)=>{for(const ce in z){const Z=ce;if(Z==="data"){if(!i(q[Z],ee[Z])&&(!te(q[Z])||!i(N,ee[Z])))return!1}else if(ee[Z]!==q[Z])return!1}return!0},y=p.useMemo(()=>{const q=!F||!e?!1:te(l)?O().isPaused()||a?!1:s!==!1:l,ee=ie=>{const ge=ke(ie);return delete ge._k,q?{isValidating:!0,isLoading:!0,...ge}:ge},ce=W(),Z=G(),ue=ee(ce),_=ce===Z?ue:ee(Z);let Y=ue;return[()=>{const ie=ee(W());return c(ie,Y)?(Y.data=ie.data,Y.isLoading=ie.isLoading,Y.isValidating=ie.isValidating,Y.error=ie.error,Y):(Y=ie,ie)},()=>_]},[r,F]),T=ur.useSyncExternalStore(p.useCallback(q=>X(F,(ee,ce)=>{c(ce,ee)||q()}),[r,F]),y[0],y[1]),I=!m.current,L=v[F]&&v[F].length>0,x=T.data,R=te(x)?E&&In(E)?bt(E):E:x,k=T.error,$=p.useRef(R),N=h?te(x)?te($.current)?R:$.current:x:R,Q=L&&!te(k)?!1:I&&!te(l)?l:O().isPaused()?!1:a?te(R)?!1:s:te(R)||s,A=!!(F&&e&&I&&Q),oe=te(T.isValidating)?A:T.isValidating,de=te(T.isLoading)?A:T.isLoading,K=p.useCallback(async q=>{const ee=j.current;if(!F||!ee||w.current||O().isPaused())return!1;let ce,Z,ue=!0;const _=q||{},Y=!f[F]||!_.dedupe,ie=()=>en?!w.current&&F===D.current&&m.current:F===D.current,ge={isValidating:!1,isLoading:!1},ye=()=>{U(ge)},J=()=>{const se=f[F];se&&se[1]===Z&&delete f[F]},le={isValidating:!0};te(W().data)&&(le.isLoading=!0);try{if(Y&&(U(le),t.loadingTimeout&&te(W().data)&&setTimeout(()=>{ue&&ie()&&O().onLoadingSlow(F,t)},t.loadingTimeout),f[F]=[ee(g),Ot()]),[ce,Z]=f[F],ce=await ce,Y&&setTimeout(J,t.dedupingInterval),!f[F]||f[F][1]!==Z)return Y&&ie()&&O().onDiscarded(F),!1;ge.error=be;const se=S[F];if(!te(se)&&(Z<=se[0]||Z<=se[1]||se[1]===0))return ye(),Y&&ie()&&O().onDiscarded(F),!1;const he=W().data;ge.data=i(he,ce)?he:ce,Y&&ie()&&O().onSuccess(ce,F,t)}catch(se){J();const he=O(),{shouldRetryOnError:dt}=he;he.isPaused()||(ge.error=se,Y&&ie()&&(he.onError(se,F,he),(dt===!0||Me(dt)&&dt(se))&&(!O().revalidateOnFocus||!O().revalidateOnReconnect||H())&&he.onErrorRetry(se,F,he,Wn=>{const ut=v[F];ut&&ut[0]&&ut[0](Jt,Wn)},{retryCount:(_.retryCount||0)+1,dedupe:!0})))}return ue=!1,ye(),!0},[F,r]),re=p.useCallback((...q)=>En(r,D.current,...q),[]);if(it(()=>{j.current=e,V.current=t,te(x)||($.current=x)}),it(()=>{if(!F)return;const q=K.bind(be,yt);let ee=0;O().revalidateOnFocus&&(ee=Date.now()+O().focusThrottleInterval);const Z=kr(F,v,(ue,_={})=>{if(ue==wn){const Y=Date.now();O().revalidateOnFocus&&Y>ee&&H()&&(ee=Y+O().focusThrottleInterval,q())}else if(ue==Rn)O().revalidateOnReconnect&&H()&&q();else{if(ue==Mn)return K();if(ue==Jt)return K(_)}});return w.current=!1,D.current=F,m.current=!0,U({_k:g}),Q&&(te(R)||Ye?q():xr(q)),()=>{w.current=!0,Z()}},[F]),it(()=>{let q;function ee(){const Z=Me(d)?d(W().data):d;Z&&q!==-1&&(q=setTimeout(ce,Z))}function ce(){!W().error&&(C||O().isVisible())&&(b||O().isOnline())?K(yt).then(ee):ee()}return ee(),()=>{q&&(clearTimeout(q),q=-1)}},[d,C,b,F]),p.useDebugValue(N),a&&te(R)&&F){if(!en&&Ye)throw new Error("Fallback data is required when using Suspense in SSR.");j.current=e,V.current=t,w.current=!1;const q=P[F];if(!te(q)){const ee=re(q);bt(ee)}if(te(k)){const ee=K(yt);te(N)||(ee.status="fulfilled",ee.value=!0),bt(ee)}else throw k}return{mutate:re,get data(){return z.data=!0,N},get error(){return z.error=!0,k},get isValidating(){return z.isValidating=!0,oe},get isLoading(){return z.isLoading=!0,de}}},Or=lt.defineProperty(wr,"defaultValue",{value:Ln}),Ro=Lr(jr),$r={moneySymbol:"$",form:{lightFilter:{more:"المزيد",clear:"نظف",confirm:"تأكيد",itemUnit:"عناصر"}},tableForm:{search:"ابحث",reset:"إعادة تعيين",submit:"ارسال",collapsed:"مُقلص",expand:"مُوسع",inputPlaceholder:"الرجاء الإدخال",selectPlaceholder:"الرجاء الإختيار"},alert:{clear:"نظف",selected:"محدد",item:"عنصر"},pagination:{total:{range:" ",total:"من",item:"عناصر"}},tableToolBar:{leftPin:"ثبت على اليسار",rightPin:"ثبت على اليمين",noPin:"الغاء التثبيت",leftFixedTitle:"لصق على اليسار",rightFixedTitle:"لصق على اليمين",noFixedTitle:"إلغاء الإلصاق",reset:"إعادة تعيين",columnDisplay:"الأعمدة المعروضة",columnSetting:"الإعدادات",fullScreen:"وضع كامل الشاشة",exitFullScreen:"الخروج من وضع كامل الشاشة",reload:"تحديث",density:"الكثافة",densityDefault:"افتراضي",densityLarger:"أكبر",densityMiddle:"وسط",densitySmall:"مدمج"},stepsForm:{next:"التالي",prev:"السابق",submit:"أنهى"},loginForm:{submitText:"تسجيل الدخول"},editableTable:{action:{save:"أنقذ",cancel:"إلغاء الأمر",delete:"حذف",add:"إضافة صف من البيانات"}},switch:{open:"مفتوح",close:"غلق"}},Br={moneySymbol:"€",form:{lightFilter:{more:"Més",clear:"Netejar",confirm:"Confirmar",itemUnit:"Elements"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col·lapsar",inputPlaceholder:"Introduïu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuració",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitjà",densitySmall:"Compacte"},stepsForm:{next:"Següent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Cancel·lar",delete:"Eliminar",add:"afegir una fila de dades"}},switch:{open:"obert",close:"tancat"}},zr={moneySymbol:"Kč",deleteThisLine:"Smazat tento řádek",copyThisLine:"Kopírovat tento řádek",form:{lightFilter:{more:"Víc",clear:"Vymazat",confirm:"Potvrdit",itemUnit:"Položky"}},tableForm:{search:"Dotaz",reset:"Resetovat",submit:"Odeslat",collapsed:"Zvětšit",expand:"Zmenšit",inputPlaceholder:"Zadejte prosím",selectPlaceholder:"Vyberte prosím"},alert:{clear:"Vymazat",selected:"Vybraný",item:"Položka"},pagination:{total:{range:" ",total:"z",item:"položek"}},tableToolBar:{leftPin:"Připnout doleva",rightPin:"Připnout doprava",noPin:"Odepnuto",leftFixedTitle:"Fixováno nalevo",rightFixedTitle:"Fixováno napravo",noFixedTitle:"Neopraveno",reset:"Resetovat",columnDisplay:"Zobrazení sloupců",columnSetting:"Nastavení",fullScreen:"Celá obrazovka",exitFullScreen:"Ukončete celou obrazovku",reload:"Obnovit",density:"Hustota",densityDefault:"Výchozí",densityLarger:"Větší",densityMiddle:"Střední",densitySmall:"Kompaktní"},stepsForm:{next:"Další",prev:"Předchozí",submit:"Dokončit"},loginForm:{submitText:"Přihlásit se"},editableTable:{onlyOneLineEditor:"Upravit lze pouze jeden řádek",action:{save:"Uložit",cancel:"Zrušit",delete:"Vymazat",add:"přidat řádek dat"}},switch:{open:"otevřít",close:"zavřít"}},Nr={moneySymbol:"€",form:{lightFilter:{more:"Mehr",clear:"Zurücksetzen",confirm:"Bestätigen",itemUnit:"Einträge"}},tableForm:{search:"Suchen",reset:"Zurücksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte auswählen"},alert:{clear:"Zurücksetzen",selected:"Ausgewählt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Einträgen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zurücksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Größer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zurück",submit:"Abschließen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"Löschen",add:"Hinzufügen einer Datenzeile"}},switch:{open:"offen",close:"schließen"}},Hr={moneySymbol:"£",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},Vr={moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},_r={moneySymbol:"€",form:{lightFilter:{more:"Más",clear:"Limpiar",confirm:"Confirmar",itemUnit:"artículos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"artículos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuración",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"añadir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}},Ur={moneySymbol:"تومان",form:{lightFilter:{more:"بیشتر",clear:"پاک کردن",confirm:"تایید",itemUnit:"مورد"}},tableForm:{search:"جستجو",reset:"بازنشانی",submit:"تایید",collapsed:"نمایش بیشتر",expand:"نمایش کمتر",inputPlaceholder:"پیدا کنید",selectPlaceholder:"انتخاب کنید"},alert:{clear:"پاک سازی",selected:"انتخاب",item:"مورد"},pagination:{total:{range:" ",total:"از",item:"مورد"}},tableToolBar:{leftPin:"سنجاق به چپ",rightPin:"سنجاق به راست",noPin:"سنجاق نشده",leftFixedTitle:"ثابت شده در چپ",rightFixedTitle:"ثابت شده در راست",noFixedTitle:"شناور",reset:"بازنشانی",columnDisplay:"نمایش همه",columnSetting:"تنظیمات",fullScreen:"تمام صفحه",exitFullScreen:"خروج از حالت تمام صفحه",reload:"تازه سازی",density:"تراکم",densityDefault:"پیش فرض",densityLarger:"بزرگ",densityMiddle:"متوسط",densitySmall:"کوچک"},stepsForm:{next:"بعدی",prev:"قبلی",submit:"اتمام"},loginForm:{submitText:"ورود"},editableTable:{action:{save:"ذخیره",cancel:"لغو",delete:"حذف",add:"یک ردیف داده اضافه کنید"}},switch:{open:"باز",close:"نزدیک"}},Wr={moneySymbol:"€",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"Réinitialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"Réduire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"Sélectionner une valeur"},alert:{clear:"Réinitialiser",selected:"Sélectionné",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"éléments"}},tableToolBar:{leftPin:"Épingler à gauche",rightPin:"Épingler à gauche",noPin:"Sans épingle",leftFixedTitle:"Fixer à gauche",rightFixedTitle:"Fixer à droite",noFixedTitle:"Non fixé",reset:"Réinitialiser",columnDisplay:"Affichage colonne",columnSetting:"Réglages",fullScreen:"Plein écran",exitFullScreen:"Quitter Plein écran",reload:"Rafraichir",density:"Densité",densityDefault:"Par défaut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Précédente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de données"}},switch:{open:"ouvert",close:"près"}},Yr={moneySymbol:"₪",deleteThisLine:"מחק שורה זו",copyThisLine:"העתק שורה זו",form:{lightFilter:{more:"יותר",clear:"נקה",confirm:"אישור",itemUnit:"פריטים"}},tableForm:{search:"חיפוש",reset:"איפוס",submit:"שלח",collapsed:"הרחב",expand:"כווץ",inputPlaceholder:"אנא הכנס",selectPlaceholder:"אנא בחר"},alert:{clear:"נקה",selected:"נבחר",item:"פריט"},pagination:{total:{range:" ",total:"מתוך",item:"פריטים"}},tableToolBar:{leftPin:"הצמד לשמאל",rightPin:"הצמד לימין",noPin:"לא מצורף",leftFixedTitle:"מוצמד לשמאל",rightFixedTitle:"מוצמד לימין",noFixedTitle:"לא מוצמד",reset:"איפוס",columnDisplay:"תצוגת עמודות",columnSetting:"הגדרות",fullScreen:"מסך מלא",exitFullScreen:"צא ממסך מלא",reload:"רענן",density:"רזולוציה",densityDefault:"ברירת מחדל",densityLarger:"גדול",densityMiddle:"בינוני",densitySmall:"קטן"},stepsForm:{next:"הבא",prev:"קודם",submit:"סיום"},loginForm:{submitText:"כניסה"},editableTable:{onlyOneLineEditor:"ניתן לערוך רק שורה אחת",action:{save:"שמור",cancel:"ביטול",delete:"מחיקה",add:"הוסף שורת נתונים"}},switch:{open:"פתח",close:"סגור"}},Kr={moneySymbol:"kn",form:{lightFilter:{more:"Više",clear:"Očisti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretraži",reset:"Poništi",submit:"Potvrdi",collapsed:"Raširi",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"Očisti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prikači lijevo",rightPin:"Prikači desno",noPin:"Bez prikačenja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Izađi iz punog zaslona",reload:"Ponovno učitaj",density:"Veličina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljedeći",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obriši",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}},Gr={moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}},qr={moneySymbol:"€",form:{lightFilter:{more:"più",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalità schermo intero",exitFullScreen:"Esci da modalità schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}},Xr={moneySymbol:"¥",form:{lightFilter:{more:"更に",clear:"クリア",confirm:"確認",itemUnit:"アイテム"}},tableForm:{search:"検索",reset:"リセット",submit:"送信",collapsed:"拡大",expand:"折畳",inputPlaceholder:"入力してください",selectPlaceholder:"選択してください"},alert:{clear:"クリア",selected:"選択した",item:"アイテム"},pagination:{total:{range:"レコード",total:"/合計",item:" "}},tableToolBar:{leftPin:"左に固定",rightPin:"右に固定",noPin:"キャンセル",leftFixedTitle:"左に固定された項目",rightFixedTitle:"右に固定された項目",noFixedTitle:"固定されてない項目",reset:"リセット",columnDisplay:"表示列",columnSetting:"列表示設定",fullScreen:"フルスクリーン",exitFullScreen:"終了",reload:"更新",density:"行高",densityDefault:"デフォルト",densityLarger:"大",densityMiddle:"中",densitySmall:"小"},stepsForm:{next:"次へ",prev:"前へ",submit:"送信"},loginForm:{submitText:"ログイン"},editableTable:{action:{save:"保存",cancel:"キャンセル",delete:"削除",add:"追加"}},switch:{open:"開く",close:"閉じる"}},Zr={moneySymbol:"₩",form:{lightFilter:{more:"더보기",clear:"초기화",confirm:"확인",itemUnit:"건수"}},tableForm:{search:"조회",reset:"초기화",submit:"제출",collapsed:"확장",expand:"닫기",inputPlaceholder:"입력해 주세요",selectPlaceholder:"선택해 주세요"},alert:{clear:"취소",selected:"선택",item:"건"},pagination:{total:{range:" ",total:"/ 총",item:"건"}},tableToolBar:{leftPin:"왼쪽으로 핀",rightPin:"오른쪽으로 핀",noPin:"핀 제거",leftFixedTitle:"왼쪽으로 고정",rightFixedTitle:"오른쪽으로 고정",noFixedTitle:"비고정",reset:"초기화",columnDisplay:"컬럼 표시",columnSetting:"설정",fullScreen:"전체 화면",exitFullScreen:"전체 화면 취소",reload:"새로 고침",density:"여백",densityDefault:"기본",densityLarger:"많은 여백",densityMiddle:"중간 여백",densitySmall:"좁은 여백"},stepsForm:{next:"다음",prev:"이전",submit:"종료"},loginForm:{submitText:"로그인"},editableTable:{action:{save:"저장",cancel:"취소",delete:"삭제",add:"데이터 행 추가"}},switch:{open:"열",close:"가까 운"}},Jr={moneySymbol:"₮",form:{lightFilter:{more:"Илүү",clear:"Цэвэрлэх",confirm:"Баталгаажуулах",itemUnit:"Нэгжүүд"}},tableForm:{search:"Хайх",reset:"Шинэчлэх",submit:"Илгээх",collapsed:"Өргөтгөх",expand:"Хураах",inputPlaceholder:"Утга оруулна уу",selectPlaceholder:"Утга сонгоно уу"},alert:{clear:"Цэвэрлэх",selected:"Сонгогдсон",item:"Нэгж"},pagination:{total:{range:" ",total:"Нийт",item:"мөр"}},tableToolBar:{leftPin:"Зүүн тийш бэхлэх",rightPin:"Баруун тийш бэхлэх",noPin:"Бэхлэхгүй",leftFixedTitle:"Зүүн зэрэгцүүлэх",rightFixedTitle:"Баруун зэрэгцүүлэх",noFixedTitle:"Зэрэгцүүлэхгүй",reset:"Шинэчлэх",columnDisplay:"Баганаар харуулах",columnSetting:"Тохиргоо",fullScreen:"Бүтэн дэлгэцээр",exitFullScreen:"Бүтэн дэлгэц цуцлах",reload:"Шинэчлэх",density:"Хэмжээ",densityDefault:"Хэвийн",densityLarger:"Том",densityMiddle:"Дунд",densitySmall:"Жижиг"},stepsForm:{next:"Дараах",prev:"Өмнөх",submit:"Дуусгах"},loginForm:{submitText:"Нэвтрэх"},editableTable:{action:{save:"Хадгалах",cancel:"Цуцлах",delete:"Устгах",add:"Мөр нэмэх"}},switch:{open:"Нээх",close:"Хаах"}},Qr={moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}},ei={moneySymbol:"€",deleteThisLine:"Verwijder deze regel",copyThisLine:"Kopieer deze regel",form:{lightFilter:{more:"Meer filters",clear:"Wissen",confirm:"Bevestigen",itemUnit:"item"}},tableForm:{search:"Zoeken",reset:"Resetten",submit:"Indienen",collapsed:"Uitvouwen",expand:"Inklappen",inputPlaceholder:"Voer in",selectPlaceholder:"Selecteer"},alert:{clear:"Selectie annuleren",selected:"Geselecteerd",item:"item"},pagination:{total:{range:"Van",total:"items/totaal",item:"items"}},tableToolBar:{leftPin:"Vastzetten aan begin",rightPin:"Vastzetten aan einde",noPin:"Niet vastzetten",leftFixedTitle:"Vastzetten aan de linkerkant",rightFixedTitle:"Vastzetten aan de rechterkant",noFixedTitle:"Niet vastzetten",reset:"Resetten",columnDisplay:"Kolomweergave",columnSetting:"Kolominstellingen",fullScreen:"Volledig scherm",exitFullScreen:"Verlaat volledig scherm",reload:"Vernieuwen",density:"Dichtheid",densityDefault:"Normaal",densityLarger:"Ruim",densityMiddle:"Gemiddeld",densitySmall:"Compact"},stepsForm:{next:"Volgende stap",prev:"Vorige stap",submit:"Indienen"},loginForm:{submitText:"Inloggen"},editableTable:{onlyOneLineEditor:"Slechts één regel tegelijk bewerken",action:{save:"Opslaan",cancel:"Annuleren",delete:"Verwijderen",add:"Een regel toevoegen"}},switch:{open:"Openen",close:"Sluiten"}},ti={moneySymbol:"zł",form:{lightFilter:{more:"Więcej",clear:"Wyczyść",confirm:"Potwierdź",itemUnit:"Ilość"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierdź",collapsed:"Pokaż wiecej",expand:"Pokaż mniej",inputPlaceholder:"Proszę podać",selectPlaceholder:"Proszę wybrać"},alert:{clear:"Wyczyść",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpisów"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypięte do lewej",rightFixedTitle:"Przypięte do prawej",noFixedTitle:"Nieprzypięte",reset:"Reset",columnDisplay:"Wyświetlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pełen ekran",exitFullScreen:"Zamknij pełen ekran",reload:"Odśwież",density:"Odstęp",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zurück",submit:"Abschließen"},loginForm:{submitText:"Zaloguj się"},editableTable:{action:{save:"Zapisać",cancel:"Anuluj",delete:"Usunąć",add:"dodawanie wiersza danych"}},switch:{open:"otwierać",close:"zamykać"}},ni={moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar à esquerda",rightPin:"Fixar à direita",noPin:"Desfixado",leftFixedTitle:"Fixado à esquerda",rightFixedTitle:"Fixado à direita",noFixedTitle:"Não fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configurações",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padrão",densityLarger:"Largo",densityMiddle:"Médio",densitySmall:"Compacto"},stepsForm:{next:"Próximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}},ri={moneySymbol:"RON",deleteThisLine:"Șterge acest rând",copyThisLine:"Copiază acest rând",form:{lightFilter:{more:"Mai multe filtre",clear:"Curăță",confirm:"Confirmă",itemUnit:"elemente"}},tableForm:{search:"Caută",reset:"Resetează",submit:"Trimite",collapsed:"Extinde",expand:"Restrânge",inputPlaceholder:"Introduceți",selectPlaceholder:"Selectați"},alert:{clear:"Anulează selecția",selected:"Selectat",item:"elemente"},pagination:{total:{range:"De la",total:"elemente/total",item:"elemente"}},tableToolBar:{leftPin:"Fixează la început",rightPin:"Fixează la sfârșit",noPin:"Nu fixa",leftFixedTitle:"Fixează în stânga",rightFixedTitle:"Fixează în dreapta",noFixedTitle:"Nu fixa",reset:"Resetează",columnDisplay:"Afișare coloane",columnSetting:"Setări coloane",fullScreen:"Ecran complet",exitFullScreen:"Ieși din ecran complet",reload:"Reîncarcă",density:"Densitate",densityDefault:"Normal",densityLarger:"Larg",densityMiddle:"Mediu",densitySmall:"Compact"},stepsForm:{next:"Pasul următor",prev:"Pasul anterior",submit:"Trimite"},loginForm:{submitText:"Autentificare"},editableTable:{onlyOneLineEditor:"Se poate edita doar un rând simultan",action:{save:"Salvează",cancel:"Anulează",delete:"Șterge",add:"Adaugă un rând"}},switch:{open:"Deschide",close:"Închide"}},ii={moneySymbol:"₽",form:{lightFilter:{more:"Еще",clear:"Очистить",confirm:"ОК",itemUnit:"Позиции"}},tableForm:{search:"Найти",reset:"Сброс",submit:"Отправить",collapsed:"Развернуть",expand:"Свернуть",inputPlaceholder:"Введите значение",selectPlaceholder:"Выберите значение"},alert:{clear:"Очистить",selected:"Выбрано",item:"элементов"},pagination:{total:{range:" ",total:"из",item:"элементов"}},tableToolBar:{leftPin:"Закрепить слева",rightPin:"Закрепить справа",noPin:"Открепить",leftFixedTitle:"Закреплено слева",rightFixedTitle:"Закреплено справа",noFixedTitle:"Не закреплено",reset:"Сброс",columnDisplay:"Отображение столбца",columnSetting:"Настройки",fullScreen:"Полный экран",exitFullScreen:"Выйти из полноэкранного режима",reload:"Обновить",density:"Размер",densityDefault:"По умолчанию",densityLarger:"Большой",densityMiddle:"Средний",densitySmall:"Сжатый"},stepsForm:{next:"Следующий",prev:"Предыдущий",submit:"Завершить"},loginForm:{submitText:"Вход"},editableTable:{action:{save:"Сохранить",cancel:"Отменить",delete:"Удалить",add:"добавить ряд данных"}},switch:{open:"Открытый чемпионат мира по теннису",close:"По адресу:"}},ai={moneySymbol:"€",deleteThisLine:"Odstrániť tento riadok",copyThisLine:"Skopírujte tento riadok",form:{lightFilter:{more:"Viac",clear:"Vyčistiť",confirm:"Potvrďte",itemUnit:"Položky"}},tableForm:{search:"Vyhladať",reset:"Resetovať",submit:"Odoslať",collapsed:"Rozbaliť",expand:"Zbaliť",inputPlaceholder:"Prosím, zadajte",selectPlaceholder:"Prosím, vyberte"},alert:{clear:"Vyčistiť",selected:"Vybraný",item:"Položka"},pagination:{total:{range:" ",total:"z",item:"položiek"}},tableToolBar:{leftPin:"Pripnúť vľavo",rightPin:"Pripnúť vpravo",noPin:"Odopnuté",leftFixedTitle:"Fixované na ľavo",rightFixedTitle:"Fixované na pravo",noFixedTitle:"Nefixované",reset:"Resetovať",columnDisplay:"Zobrazenie stĺpcov",columnSetting:"Nastavenia",fullScreen:"Celá obrazovka",exitFullScreen:"Ukončiť celú obrazovku",reload:"Obnoviť",density:"Hustota",densityDefault:"Predvolené",densityLarger:"Väčšie",densityMiddle:"Stredné",densitySmall:"Kompaktné"},stepsForm:{next:"Ďalšie",prev:"Predchádzajúce",submit:"Potvrdiť"},loginForm:{submitText:"Prihlásiť sa"},editableTable:{onlyOneLineEditor:"Upravovať možno iba jeden riadok",action:{save:"Uložiť",cancel:"Zrušiť",delete:"Odstrániť",add:"pridať riadok údajov"}},switch:{open:"otvoriť",close:"zavrieť"}},oi={moneySymbol:"RSD",form:{lightFilter:{more:"Više",clear:"Očisti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pronađi",reset:"Resetuj",submit:"Pošalji",collapsed:"Proširi",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"Očisti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zakači levo",rightPin:"Zakači desno",noPin:"Nije zakačeno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Podešavanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osveži",density:"Veličina",densityDefault:"Podrazumevana",densityLarger:"Veća",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sačuvaj",cancel:"Poništi",delete:"Obriši",add:"dodajte red podataka"}},switch:{open:"Отворите",close:"Затворите"}},li={moneySymbol:"SEK",deleteThisLine:"Radera denna rad",copyThisLine:"Kopiera denna rad",form:{lightFilter:{more:"Fler filter",clear:"Rensa",confirm:"Bekräfta",itemUnit:"objekt"}},tableForm:{search:"Sök",reset:"Återställ",submit:"Skicka",collapsed:"Expandera",expand:"Fäll ihop",inputPlaceholder:"Vänligen ange",selectPlaceholder:"Vänligen välj"},alert:{clear:"Avbryt val",selected:"Vald",item:"objekt"},pagination:{total:{range:"Från",total:"objekt/totalt",item:"objekt"}},tableToolBar:{leftPin:"Fäst till vänster",rightPin:"Fäst till höger",noPin:"Inte fäst",leftFixedTitle:"Fäst till vänster",rightFixedTitle:"Fäst till höger",noFixedTitle:"Inte fäst",reset:"Återställ",columnDisplay:"Kolumnvisning",columnSetting:"Kolumninställningar",fullScreen:"Fullskärm",exitFullScreen:"Avsluta fullskärm",reload:"Ladda om",density:"Täthet",densityDefault:"Normal",densityLarger:"Lös",densityMiddle:"Medium",densitySmall:"Kompakt"},stepsForm:{next:"Nästa steg",prev:"Föregående steg",submit:"Skicka"},loginForm:{submitText:"Logga in"},editableTable:{onlyOneLineEditor:"Endast en rad kan redigeras åt gången",action:{save:"Spara",cancel:"Avbryt",delete:"Radera",add:"Lägg till en rad"}},switch:{open:"Öppna",close:"Stäng"}},si={moneySymbol:"฿",deleteThisLine:"ลบบรรทัดนี้",copyThisLine:"คัดลอกบรรทัดนี้",form:{lightFilter:{more:"มากกว่า",clear:"ชัดเจน",confirm:"ยืนยัน",itemUnit:"รายการ"}},tableForm:{search:"สอบถาม",reset:"รีเซ็ต",submit:"ส่ง",collapsed:"ขยาย",expand:"ทรุด",inputPlaceholder:"กรุณาป้อน",selectPlaceholder:"โปรดเลือก"},alert:{clear:"ชัดเจน",selected:"เลือกแล้ว",item:"รายการ"},pagination:{total:{range:" ",total:"ของ",item:"รายการ"}},tableToolBar:{leftPin:"ปักหมุดไปทางซ้าย",rightPin:"ปักหมุดไปทางขวา",noPin:"เลิกตรึงแล้ว",leftFixedTitle:"แก้ไขด้านซ้าย",rightFixedTitle:"แก้ไขด้านขวา",noFixedTitle:"ไม่คงที่",reset:"รีเซ็ต",columnDisplay:"การแสดงคอลัมน์",columnSetting:"การตั้งค่า",fullScreen:"เต็มจอ",exitFullScreen:"ออกจากโหมดเต็มหน้าจอ",reload:"รีเฟรช",density:"ความหนาแน่น",densityDefault:"ค่าเริ่มต้น",densityLarger:"ขนาดใหญ่ขึ้น",densityMiddle:"กลาง",densitySmall:"กะทัดรัด"},stepsForm:{next:"ถัดไป",prev:"ก่อนหน้า",submit:"เสร็จ"},loginForm:{submitText:"เข้าสู่ระบบ"},editableTable:{onlyOneLineEditor:"แก้ไขได้เพียงบรรทัดเดียวเท่านั้น",action:{save:"บันทึก",cancel:"ยกเลิก",delete:"ลบ",add:"เพิ่มแถวของข้อมูล"}},switch:{open:"เปิด",close:"ปิด"}},ci={moneySymbol:"₺",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"Öğeler"}},tableForm:{search:"Filtrele",reset:"Sıfırla",submit:"Gönder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek için bir değer girin",selectPlaceholder:"Filtrelemek için bir değer seçin"},alert:{clear:"Temizle",selected:"Seçili",item:"Öğe"},pagination:{total:{range:" ",total:"Toplam",item:"Öğe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sağa sabitle",noPin:"Sabitlemeyi kaldır",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sağa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"Sıfırla",columnDisplay:"Kolon Görünümü",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan Çık",reload:"Yenile",density:"Kalınlık",densityDefault:"Varsayılan",densityLarger:"Büyük",densityMiddle:"Orta",densitySmall:"Küçük"},stepsForm:{next:"Sıradaki",prev:"Önceki",submit:"Gönder"},loginForm:{submitText:"Giriş Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazgeç",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"açık",close:"kapatmak"}},di={moneySymbol:"₴",deleteThisLine:"Видатили рядок",copyThisLine:"Скопіювати рядок",form:{lightFilter:{more:"Ще",clear:"Очистити",confirm:"Ок",itemUnit:"Позиції"}},tableForm:{search:"Пошук",reset:"Очистити",submit:"Відправити",collapsed:"Розгорнути",expand:"Згорнути",inputPlaceholder:"Введіть значення",selectPlaceholder:"Оберіть значення"},alert:{clear:"Очистити",selected:"Обрано",item:"елементів"},pagination:{total:{range:" ",total:"з",item:"елементів"}},tableToolBar:{leftPin:"Закріпити зліва",rightPin:"Закріпити справа",noPin:"Відкріпити",leftFixedTitle:"Закріплено зліва",rightFixedTitle:"Закріплено справа",noFixedTitle:"Не закріплено",reset:"Скинути",columnDisplay:"Відображення стовпців",columnSetting:"Налаштування",fullScreen:"Повноекранний режим",exitFullScreen:"Вийти з повноекранного режиму",reload:"Оновити",density:"Розмір",densityDefault:"За замовчуванням",densityLarger:"Великий",densityMiddle:"Середній",densitySmall:"Стислий"},stepsForm:{next:"Наступний",prev:"Попередній",submit:"Завершити"},loginForm:{submitText:"Вхіх"},editableTable:{onlyOneLineEditor:"Тільки один рядок може бути редагований одночасно",action:{save:"Зберегти",cancel:"Відмінити",delete:"Видалити",add:"додати рядок"}},switch:{open:"Відкрито",close:"Закрито"}},ui={moneySymbol:"UZS",form:{lightFilter:{more:"Yana",clear:"Tozalash",confirm:"OK",itemUnit:"Pozitsiyalar"}},tableForm:{search:"Qidirish",reset:"Qayta tiklash",submit:"Yuborish",collapsed:"Yig‘ish",expand:"Kengaytirish",inputPlaceholder:"Qiymatni kiriting",selectPlaceholder:"Qiymatni tanlang"},alert:{clear:"Tozalash",selected:"Tanlangan",item:"elementlar"},pagination:{total:{range:" ",total:"dan",item:"elementlar"}},tableToolBar:{leftPin:"Chapga mahkamlash",rightPin:"O‘ngga mahkamlash",noPin:"Mahkamlashni olib tashlash",leftFixedTitle:"Chapga mahkamlangan",rightFixedTitle:"O‘ngga mahkamlangan",noFixedTitle:"Mahkamlashsiz",reset:"Qayta tiklash",columnDisplay:"Ustunni ko‘rsatish",columnSetting:"Sozlamalar",fullScreen:"To‘liq ekran",exitFullScreen:"To‘liq ekrandan chiqish",reload:"Yangilash",density:"O‘lcham",densityDefault:"Standart",densityLarger:"Katta",densityMiddle:"O‘rtacha",densitySmall:"Kichik"},stepsForm:{next:"Keyingi",prev:"Oldingi",submit:"Tugatish"},loginForm:{submitText:"Kirish"},editableTable:{action:{save:"Saqlash",cancel:"Bekor qilish",delete:"O‘chirish",add:"maʼlumotlar qatorini qo‘shish"}},switch:{open:"Ochish",close:"Yopish"}},fi={moneySymbol:"₫",form:{lightFilter:{more:"Nhiều hơn",clear:"Trong",confirm:"Xác nhận",itemUnit:"Mục"}},tableForm:{search:"Tìm kiếm",reset:"Làm lại",submit:"Gửi đi",collapsed:"Mở rộng",expand:"Thu gọn",inputPlaceholder:"nhập dữ liệu",selectPlaceholder:"Vui lòng chọn"},alert:{clear:"Xóa",selected:"đã chọn",item:"mục"},pagination:{total:{range:" ",total:"trên",item:"mặt hàng"}},tableToolBar:{leftPin:"Ghim trái",rightPin:"Ghim phải",noPin:"Bỏ ghim",leftFixedTitle:"Cố định trái",rightFixedTitle:"Cố định phải",noFixedTitle:"Chưa cố định",reset:"Làm lại",columnDisplay:"Cột hiển thị",columnSetting:"Cấu hình",fullScreen:"Chế độ toàn màn hình",exitFullScreen:"Thoát chế độ toàn màn hình",reload:"Làm mới",density:"Mật độ hiển thị",densityDefault:"Mặc định",densityLarger:"Mặc định",densityMiddle:"Trung bình",densitySmall:"Chật"},stepsForm:{next:"Sau",prev:"Trước",submit:"Kết thúc"},loginForm:{submitText:"Đăng nhập"},editableTable:{action:{save:"Cứu",cancel:"Hủy",delete:"Xóa",add:"thêm một hàng dữ liệu"}},switch:{open:"mở",close:"đóng"}},mi={moneySymbol:"¥",deleteThisLine:"删除此项",copyThisLine:"复制此项",form:{lightFilter:{more:"更多筛选",clear:"清除",confirm:"确认",itemUnit:"项"}},tableForm:{search:"查询",reset:"重置",submit:"提交",collapsed:"展开",expand:"收起",inputPlaceholder:"请输入",selectPlaceholder:"请选择"},alert:{clear:"取消选择",selected:"已选择",item:"项"},pagination:{total:{range:"第",total:"条/总共",item:"条"}},tableToolBar:{leftPin:"固定在列首",rightPin:"固定在列尾",noPin:"不固定",leftFixedTitle:"固定在左侧",rightFixedTitle:"固定在右侧",noFixedTitle:"不固定",reset:"重置",columnDisplay:"列展示",columnSetting:"列设置",fullScreen:"全屏",exitFullScreen:"退出全屏",reload:"刷新",density:"密度",densityDefault:"正常",densityLarger:"宽松",densityMiddle:"中等",densitySmall:"紧凑"},stepsForm:{next:"下一步",prev:"上一步",submit:"提交"},loginForm:{submitText:"登录"},editableTable:{onlyOneLineEditor:"只能同时编辑一行",action:{save:"保存",cancel:"取消",delete:"删除",add:"添加一行数据"}},switch:{open:"打开",close:"关闭"}},gi={moneySymbol:"NT$",deleteThisLine:"刪除此项",copyThisLine:"複製此项",form:{lightFilter:{more:"更多篩選",clear:"清除",confirm:"確認",itemUnit:"項"}},tableForm:{search:"查詢",reset:"重置",submit:"提交",collapsed:"展開",expand:"收起",inputPlaceholder:"請輸入",selectPlaceholder:"請選擇"},alert:{clear:"取消選擇",selected:"已選擇",item:"項"},pagination:{total:{range:"第",total:"條/總共",item:"條"}},tableToolBar:{leftPin:"固定到左邊",rightPin:"固定到右邊",noPin:"不固定",leftFixedTitle:"固定在左側",rightFixedTitle:"固定在右側",noFixedTitle:"不固定",reset:"重置",columnDisplay:"列展示",columnSetting:"列設置",fullScreen:"全屏",exitFullScreen:"退出全屏",reload:"刷新",density:"密度",densityDefault:"正常",densityLarger:"寬鬆",densityMiddle:"中等",densitySmall:"緊湊"},stepsForm:{next:"下一步",prev:"上一步",submit:"完成"},loginForm:{submitText:"登入"},editableTable:{onlyOneLineEditor:"只能同時編輯一行",action:{save:"保存",cancel:"取消",delete:"刪除",add:"新增一行資料"}},switch:{open:"打開",close:"關閉"}};var ne=function(e,t){return{getMessage:function(i,a){var o=He(t,i.replace(/\[(\d+)\]/g,".$1").split("."))||"";if(o)return o;var l=e.replace("_","-");if(l==="zh-CN")return a;var s=Ke["zh-CN"];return s?s.getMessage(i,a):a},locale:e}},hi=ne("mn_MN",Jr),pi=ne("ar_EG",$r),_e=ne("zh_CN",mi),vi=ne("en_US",Vr),bi=ne("en_GB",Hr),yi=ne("vi_VN",fi),xi=ne("it_IT",qr),Si=ne("ja_JP",Xr),Ci=ne("es_ES",_r),Pi=ne("ca_ES",Br),Fi=ne("ru_RU",ii),Ti=ne("sr_RS",oi),wi=ne("ms_MY",Qr),Ri=ne("zh_TW",gi),Mi=ne("fr_FR",Wr),Ii=ne("pt_BR",ni),Ai=ne("ko_KR",Zr),Ei=ne("id_ID",Gr),Di=ne("de_DE",Nr),Li=ne("fa_IR",Ur),ki=ne("tr_TR",ci),ji=ne("pl_PL",ti),Oi=ne("hr_",Kr),$i=ne("th_TH",si),Bi=ne("cs_cz",zr),zi=ne("sk_SK",ai),Ni=ne("he_IL",Yr),Hi=ne("uk_UA",di),Vi=ne("uz_UZ",ui),_i=ne("nl_NL",ei),Ui=ne("ro_RO",ri),Wi=ne("sv_SE",li),Ke={"mn-MN":hi,"ar-EG":pi,"zh-CN":_e,"en-US":vi,"en-GB":bi,"vi-VN":yi,"it-IT":xi,"ja-JP":Si,"es-ES":Ci,"ca-ES":Pi,"ru-RU":Fi,"sr-RS":Ti,"ms-MY":wi,"zh-TW":Ri,"fr-FR":Mi,"pt-BR":Ii,"ko-KR":Ai,"id-ID":Ei,"de-DE":Di,"fa-IR":Li,"tr-TR":ki,"pl-PL":ji,"hr-HR":Oi,"th-TH":$i,"cs-CZ":Bi,"sk-SK":zi,"he-IL":Ni,"uk-UA":Hi,"uz-UZ":Vi,"nl-NL":_i,"ro-RO":Ui,"sv-SE":Wi},Yi=Object.keys(Ke),$n=function(e){var t=(e||"zh-CN").toLocaleLowerCase();return Yi.find(function(r){var i=r.toLocaleLowerCase();return i.includes(t)})};function ve(n,e){Ki(n)&&(n="100%");var t=Gi(n);return n=e===360?n:Math.min(e,Math.max(0,parseFloat(n))),t&&(n=parseInt(String(n*e),10)/100),Math.abs(n-e)<1e-6?1:(e===360?n=(n<0?n%e+e:n%e)/parseFloat(String(e)):n=n%e/parseFloat(String(e)),n)}function tt(n){return Math.min(1,Math.max(0,n))}function Ki(n){return typeof n=="string"&&n.indexOf(".")!==-1&&parseFloat(n)===1}function Gi(n){return typeof n=="string"&&n.indexOf("%")!==-1}function Bn(n){return n=parseFloat(n),(isNaN(n)||n<0||n>1)&&(n=1),n}function nt(n){return n<=1?"".concat(Number(n)*100,"%"):n}function Be(n){return n.length===1?"0"+n:String(n)}function qi(n,e,t){return{r:ve(n,255)*255,g:ve(e,255)*255,b:ve(t,255)*255}}function rn(n,e,t){n=ve(n,255),e=ve(e,255),t=ve(t,255);var r=Math.max(n,e,t),i=Math.min(n,e,t),a=0,o=0,l=(r+i)/2;if(r===i)o=0,a=0;else{var s=r-i;switch(o=l>.5?s/(2-r-i):s/(r+i),r){case n:a=(e-t)/s+(e<t?6:0);break;case e:a=(t-n)/s+2;break;case t:a=(n-e)/s+4;break}a/=6}return{h:a,s:o,l}}function xt(n,e,t){return t<0&&(t+=1),t>1&&(t-=1),t<1/6?n+(e-n)*(6*t):t<1/2?e:t<2/3?n+(e-n)*(2/3-t)*6:n}function Xi(n,e,t){var r,i,a;if(n=ve(n,360),e=ve(e,100),t=ve(t,100),e===0)i=t,a=t,r=t;else{var o=t<.5?t*(1+e):t+e-t*e,l=2*t-o;r=xt(l,o,n+1/3),i=xt(l,o,n),a=xt(l,o,n-1/3)}return{r:r*255,g:i*255,b:a*255}}function an(n,e,t){n=ve(n,255),e=ve(e,255),t=ve(t,255);var r=Math.max(n,e,t),i=Math.min(n,e,t),a=0,o=r,l=r-i,s=r===0?0:l/r;if(r===i)a=0;else{switch(r){case n:a=(e-t)/l+(e<t?6:0);break;case e:a=(t-n)/l+2;break;case t:a=(n-e)/l+4;break}a/=6}return{h:a,s,v:o}}function Zi(n,e,t){n=ve(n,360)*6,e=ve(e,100),t=ve(t,100);var r=Math.floor(n),i=n-r,a=t*(1-e),o=t*(1-i*e),l=t*(1-(1-i)*e),s=r%6,d=[t,o,a,a,l,t][s],C=[l,t,t,o,a,a][s],b=[a,a,l,t,t,o][s];return{r:d*255,g:C*255,b:b*255}}function on(n,e,t,r){var i=[Be(Math.round(n).toString(16)),Be(Math.round(e).toString(16)),Be(Math.round(t).toString(16))];return r&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function Ji(n,e,t,r,i){var a=[Be(Math.round(n).toString(16)),Be(Math.round(e).toString(16)),Be(Math.round(t).toString(16)),Be(Qi(r))];return i&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))&&a[3].startsWith(a[3].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}function Qi(n){return Math.round(parseFloat(n)*255).toString(16)}function ln(n){return Ce(n)/255}function Ce(n){return parseInt(n,16)}function ea(n){return{r:n>>16,g:(n&65280)>>8,b:n&255}}var Bt={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function ta(n){var e={r:0,g:0,b:0},t=1,r=null,i=null,a=null,o=!1,l=!1;return typeof n=="string"&&(n=ia(n)),typeof n=="object"&&(De(n.r)&&De(n.g)&&De(n.b)?(e=qi(n.r,n.g,n.b),o=!0,l=String(n.r).substr(-1)==="%"?"prgb":"rgb"):De(n.h)&&De(n.s)&&De(n.v)?(r=nt(n.s),i=nt(n.v),e=Zi(n.h,r,i),o=!0,l="hsv"):De(n.h)&&De(n.s)&&De(n.l)&&(r=nt(n.s),a=nt(n.l),e=Xi(n.h,r,a),o=!0,l="hsl"),Object.prototype.hasOwnProperty.call(n,"a")&&(t=n.a)),t=Bn(t),{ok:o,format:n.format||l,r:Math.min(255,Math.max(e.r,0)),g:Math.min(255,Math.max(e.g,0)),b:Math.min(255,Math.max(e.b,0)),a:t}}var na="[-\\+]?\\d+%?",ra="[-\\+]?\\d*\\.\\d+%?",$e="(?:".concat(ra,")|(?:").concat(na,")"),St="[\\s|\\(]+(".concat($e,")[,|\\s]+(").concat($e,")[,|\\s]+(").concat($e,")\\s*\\)?"),Ct="[\\s|\\(]+(".concat($e,")[,|\\s]+(").concat($e,")[,|\\s]+(").concat($e,")[,|\\s]+(").concat($e,")\\s*\\)?"),Re={CSS_UNIT:new RegExp($e),rgb:new RegExp("rgb"+St),rgba:new RegExp("rgba"+Ct),hsl:new RegExp("hsl"+St),hsla:new RegExp("hsla"+Ct),hsv:new RegExp("hsv"+St),hsva:new RegExp("hsva"+Ct),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function ia(n){if(n=n.trim().toLowerCase(),n.length===0)return!1;var e=!1;if(Bt[n])n=Bt[n],e=!0;else if(n==="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var t=Re.rgb.exec(n);return t?{r:t[1],g:t[2],b:t[3]}:(t=Re.rgba.exec(n),t?{r:t[1],g:t[2],b:t[3],a:t[4]}:(t=Re.hsl.exec(n),t?{h:t[1],s:t[2],l:t[3]}:(t=Re.hsla.exec(n),t?{h:t[1],s:t[2],l:t[3],a:t[4]}:(t=Re.hsv.exec(n),t?{h:t[1],s:t[2],v:t[3]}:(t=Re.hsva.exec(n),t?{h:t[1],s:t[2],v:t[3],a:t[4]}:(t=Re.hex8.exec(n),t?{r:Ce(t[1]),g:Ce(t[2]),b:Ce(t[3]),a:ln(t[4]),format:e?"name":"hex8"}:(t=Re.hex6.exec(n),t?{r:Ce(t[1]),g:Ce(t[2]),b:Ce(t[3]),format:e?"name":"hex"}:(t=Re.hex4.exec(n),t?{r:Ce(t[1]+t[1]),g:Ce(t[2]+t[2]),b:Ce(t[3]+t[3]),a:ln(t[4]+t[4]),format:e?"name":"hex8"}:(t=Re.hex3.exec(n),t?{r:Ce(t[1]+t[1]),g:Ce(t[2]+t[2]),b:Ce(t[3]+t[3]),format:e?"name":"hex"}:!1)))))))))}function De(n){return!!Re.CSS_UNIT.exec(String(n))}var aa=function(){function n(e,t){e===void 0&&(e=""),t===void 0&&(t={});var r;if(e instanceof n)return e;typeof e=="number"&&(e=ea(e)),this.originalInput=e;var i=ta(e);this.originalInput=e,this.r=i.r,this.g=i.g,this.b=i.b,this.a=i.a,this.roundA=Math.round(100*this.a)/100,this.format=(r=t.format)!==null&&r!==void 0?r:i.format,this.gradientType=t.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=i.ok}return n.prototype.isDark=function(){return this.getBrightness()<128},n.prototype.isLight=function(){return!this.isDark()},n.prototype.getBrightness=function(){var e=this.toRgb();return(e.r*299+e.g*587+e.b*114)/1e3},n.prototype.getLuminance=function(){var e=this.toRgb(),t,r,i,a=e.r/255,o=e.g/255,l=e.b/255;return a<=.03928?t=a/12.92:t=Math.pow((a+.055)/1.055,2.4),o<=.03928?r=o/12.92:r=Math.pow((o+.055)/1.055,2.4),l<=.03928?i=l/12.92:i=Math.pow((l+.055)/1.055,2.4),.2126*t+.7152*r+.0722*i},n.prototype.getAlpha=function(){return this.a},n.prototype.setAlpha=function(e){return this.a=Bn(e),this.roundA=Math.round(100*this.a)/100,this},n.prototype.isMonochrome=function(){var e=this.toHsl().s;return e===0},n.prototype.toHsv=function(){var e=an(this.r,this.g,this.b);return{h:e.h*360,s:e.s,v:e.v,a:this.a}},n.prototype.toHsvString=function(){var e=an(this.r,this.g,this.b),t=Math.round(e.h*360),r=Math.round(e.s*100),i=Math.round(e.v*100);return this.a===1?"hsv(".concat(t,", ").concat(r,"%, ").concat(i,"%)"):"hsva(".concat(t,", ").concat(r,"%, ").concat(i,"%, ").concat(this.roundA,")")},n.prototype.toHsl=function(){var e=rn(this.r,this.g,this.b);return{h:e.h*360,s:e.s,l:e.l,a:this.a}},n.prototype.toHslString=function(){var e=rn(this.r,this.g,this.b),t=Math.round(e.h*360),r=Math.round(e.s*100),i=Math.round(e.l*100);return this.a===1?"hsl(".concat(t,", ").concat(r,"%, ").concat(i,"%)"):"hsla(".concat(t,", ").concat(r,"%, ").concat(i,"%, ").concat(this.roundA,")")},n.prototype.toHex=function(e){return e===void 0&&(e=!1),on(this.r,this.g,this.b,e)},n.prototype.toHexString=function(e){return e===void 0&&(e=!1),"#"+this.toHex(e)},n.prototype.toHex8=function(e){return e===void 0&&(e=!1),Ji(this.r,this.g,this.b,this.a,e)},n.prototype.toHex8String=function(e){return e===void 0&&(e=!1),"#"+this.toHex8(e)},n.prototype.toHexShortString=function(e){return e===void 0&&(e=!1),this.a===1?this.toHexString(e):this.toHex8String(e)},n.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},n.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),r=Math.round(this.b);return this.a===1?"rgb(".concat(e,", ").concat(t,", ").concat(r,")"):"rgba(".concat(e,", ").concat(t,", ").concat(r,", ").concat(this.roundA,")")},n.prototype.toPercentageRgb=function(){var e=function(t){return"".concat(Math.round(ve(t,255)*100),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},n.prototype.toPercentageRgbString=function(){var e=function(t){return Math.round(ve(t,255)*100)};return this.a===1?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},n.prototype.toName=function(){if(this.a===0)return"transparent";if(this.a<1)return!1;for(var e="#"+on(this.r,this.g,this.b,!1),t=0,r=Object.entries(Bt);t<r.length;t++){var i=r[t],a=i[0],o=i[1];if(e===o)return a}return!1},n.prototype.toString=function(e){var t=!!e;e=e??this.format;var r=!1,i=this.a<1&&this.a>=0,a=!t&&i&&(e.startsWith("hex")||e==="name");return a?e==="name"&&this.a===0?this.toName():this.toRgbString():(e==="rgb"&&(r=this.toRgbString()),e==="prgb"&&(r=this.toPercentageRgbString()),(e==="hex"||e==="hex6")&&(r=this.toHexString()),e==="hex3"&&(r=this.toHexString(!0)),e==="hex4"&&(r=this.toHex8String(!0)),e==="hex8"&&(r=this.toHex8String()),e==="name"&&(r=this.toName()),e==="hsl"&&(r=this.toHslString()),e==="hsv"&&(r=this.toHsvString()),r||this.toHexString())},n.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},n.prototype.clone=function(){return new n(this.toString())},n.prototype.lighten=function(e){e===void 0&&(e=10);var t=this.toHsl();return t.l+=e/100,t.l=tt(t.l),new n(t)},n.prototype.brighten=function(e){e===void 0&&(e=10);var t=this.toRgb();return t.r=Math.max(0,Math.min(255,t.r-Math.round(255*-(e/100)))),t.g=Math.max(0,Math.min(255,t.g-Math.round(255*-(e/100)))),t.b=Math.max(0,Math.min(255,t.b-Math.round(255*-(e/100)))),new n(t)},n.prototype.darken=function(e){e===void 0&&(e=10);var t=this.toHsl();return t.l-=e/100,t.l=tt(t.l),new n(t)},n.prototype.tint=function(e){return e===void 0&&(e=10),this.mix("white",e)},n.prototype.shade=function(e){return e===void 0&&(e=10),this.mix("black",e)},n.prototype.desaturate=function(e){e===void 0&&(e=10);var t=this.toHsl();return t.s-=e/100,t.s=tt(t.s),new n(t)},n.prototype.saturate=function(e){e===void 0&&(e=10);var t=this.toHsl();return t.s+=e/100,t.s=tt(t.s),new n(t)},n.prototype.greyscale=function(){return this.desaturate(100)},n.prototype.spin=function(e){var t=this.toHsl(),r=(t.h+e)%360;return t.h=r<0?360+r:r,new n(t)},n.prototype.mix=function(e,t){t===void 0&&(t=50);var r=this.toRgb(),i=new n(e).toRgb(),a=t/100,o={r:(i.r-r.r)*a+r.r,g:(i.g-r.g)*a+r.g,b:(i.b-r.b)*a+r.b,a:(i.a-r.a)*a+r.a};return new n(o)},n.prototype.analogous=function(e,t){e===void 0&&(e=6),t===void 0&&(t=30);var r=this.toHsl(),i=360/t,a=[this];for(r.h=(r.h-(i*e>>1)+720)%360;--e;)r.h=(r.h+i)%360,a.push(new n(r));return a},n.prototype.complement=function(){var e=this.toHsl();return e.h=(e.h+180)%360,new n(e)},n.prototype.monochromatic=function(e){e===void 0&&(e=6);for(var t=this.toHsv(),r=t.h,i=t.s,a=t.v,o=[],l=1/e;e--;)o.push(new n({h:r,s:i,v:a})),a=(a+l)%1;return o},n.prototype.splitcomplement=function(){var e=this.toHsl(),t=e.h;return[this,new n({h:(t+72)%360,s:e.s,l:e.l}),new n({h:(t+216)%360,s:e.s,l:e.l})]},n.prototype.onBackground=function(e){var t=this.toRgb(),r=new n(e).toRgb(),i=t.a+r.a*(1-t.a);return new n({r:(t.r*t.a+r.r*r.a*(1-t.a))/i,g:(t.g*t.a+r.g*r.a*(1-t.a))/i,b:(t.b*t.a+r.b*r.a*(1-t.a))/i,a:i})},n.prototype.triad=function(){return this.polyad(3)},n.prototype.tetrad=function(){return this.polyad(4)},n.prototype.polyad=function(e){for(var t=this.toHsl(),r=t.h,i=[this],a=360/e,o=1;o<e;o++)i.push(new n({h:(r+o*a)%360,s:t.s,l:t.l}));return i},n.prototype.equals=function(e){return this.toRgbString()===new n(e).toRgbString()},n}(),Pt,Ge={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff7875",colorInfo:"#1677ff",colorTextBase:"#000",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:4,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,"blue-1":"#e6f4ff","blue-2":"#bae0ff","blue-3":"#91caff","blue-4":"#69b1ff","blue-5":"#4096ff","blue-6":"#1677ff","blue-7":"#0958d9","blue-8":"#003eb3","blue-9":"#002c8c","blue-10":"#001d66","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorText:"rgba(0, 0, 0, 0.88)",colorTextSecondary:"rgba(0, 0, 0, 0.65)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorFill:"rgba(0, 0, 0, 0.15)",colorFillSecondary:"rgba(0, 0, 0, 0.06)",colorFillTertiary:"rgba(0, 0, 0, 0.04)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",colorBgLayout:"hsl(220,23%,97%)",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#95de64",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ffa39e",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffd666",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f4ff",colorInfoBgHover:"#bae0ff",colorInfoBorder:"#91caff",colorInfoBorderHover:"#69b1ff",colorInfoHover:"#69b1ff",colorInfoActive:"#0958d9",colorInfoTextHover:"#4096ff",colorInfoText:"#1677ff",colorInfoTextActive:"#0958d9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorWhite:"#fff",sizeXXL:48,sizeXL:32,sizeLG:24,sizeMD:20,sizeMS:16,size:16,sizeSM:12,sizeXS:8,sizeXXS:4,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],lineWidthBold:2,borderRadiusXS:1,borderRadiusSM:4,borderRadiusLG:8,borderRadiusOuter:4,colorLink:"#1677ff",colorLinkHover:"#69b1ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.06)",colorFillContentHover:"rgba(0, 0, 0, 0.15)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.04)",colorBorderBg:"#ffffff",colorSplit:"rgba(5, 5, 5, 0.06)",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.88)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.45)",colorTextLightSolid:"#fff",colorHighlight:"#ff7875",colorBgTextHover:"rgba(0, 0, 0, 0.06)",colorBgTextActive:"rgba(0, 0, 0, 0.15)",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.88)",colorErrorOutline:"rgba(255, 38, 5, 0.06)",colorWarningOutline:"rgba(255, 215, 5, 0.1)",fontSizeSM:12,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.04)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.15)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"rgba(5, 145, 255, 0.1)",fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:4,paddingXS:8,paddingSM:12,padding:16,paddingMD:20,paddingLG:24,paddingXL:32,paddingContentHorizontalLG:24,paddingContentVerticalLG:16,paddingContentHorizontal:16,paddingContentVertical:12,paddingContentHorizontalSM:16,paddingContentVerticalSM:8,marginXXS:4,marginXS:8,marginSM:12,margin:16,marginMD:20,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",boxShadowSecondary:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowCard:"0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",boxShadowDrawerRight:"-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerLeft:"6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerUp:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerDown:"0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"19w80ff",_hashId:"css-dev-only-do-not-override-i2zu9q"},zn=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,r=3735928559^t,i=1103547991^t,a=0,o;a<e.length;a++)o=e.charCodeAt(a),r=Math.imul(r^o,2654435761),i=Math.imul(i^o,1597334677);return r=Math.imul(r^r>>>16,2246822507)^Math.imul(i^i>>>13,3266489909),i=Math.imul(i^i>>>16,2246822507)^Math.imul(r^r>>>13,3266489909),4294967296*(2097151&i)+(r>>>0)},_t=Gn(function(n){return n}),Nn={theme:_t,token:u(u({},Ge),Ie===null||Ie===void 0||(Pt=Ie.defaultAlgorithm)===null||Pt===void 0?void 0:Pt.call(Ie,Ie===null||Ie===void 0?void 0:Ie.defaultSeed)),hashId:"pro-".concat(zn(JSON.stringify(Ge)))},oa=function(){return Nn};const la=Object.freeze(Object.defineProperty({__proto__:null,defaultToken:Ge,emptyTheme:_t,hashCode:zn,token:Nn,useToken:oa},Symbol.toStringTag,{value:"Module"}));var Fe=function(e,t){return new aa(e).setAlpha(t).toRgbString()},sa=function(){return typeof Ie>"u"||!Ie?la:Ie},qe=sa(),ca=qe.useToken,Mo=function(e){return{boxSizing:"border-box",margin:0,padding:0,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,listStyle:"none"}};function Je(n,e){var t,r=p.useContext(Ue),i=r.token,a=i===void 0?{}:i,o=p.useContext(Ue),l=o.hashed,s=ca(),d=s.token,C=s.hashId,b=p.useContext(Ue);b.theme;var h=p.useContext(fe.ConfigContext),v=h.getPrefixCls,S=h.csp;return a.layout||(a=u({},d)),a.proComponentsCls=(t=a.proComponentsCls)!==null&&t!==void 0?t:".".concat(v("pro")),a.antCls=".".concat(v()),{wrapSSR:qn({token:a,path:[n],nonce:S==null?void 0:S.nonce,layer:{name:"antd-pro",dependencies:["antd"]}},function(){return e(a)}),hashId:l?C:""}}var da=function(e,t){var r,i,a,o,l,s=u({},e);return u(u({bgLayout:"linear-gradient(".concat(t.colorBgContainer,", ").concat(t.colorBgLayout," 28%)"),colorTextAppListIcon:t.colorTextSecondary,appListIconHoverBgColor:s==null||(r=s.sider)===null||r===void 0?void 0:r.colorBgMenuItemSelected,colorBgAppListIconHover:Fe(t.colorTextBase,.04),colorTextAppListIconHover:t.colorTextBase},s),{},{header:u({colorBgHeader:Fe(t.colorBgElevated,.6),colorBgScrollHeader:Fe(t.colorBgElevated,.8),colorHeaderTitle:t.colorText,colorBgMenuItemHover:Fe(t.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorBgMenuElevated:(s==null||(i=s.header)===null||i===void 0?void 0:i.colorBgHeader)!=="rgba(255, 255, 255, 0.6)"?(a=s.header)===null||a===void 0?void 0:a.colorBgHeader:t.colorBgElevated,colorTextMenuSelected:Fe(t.colorTextBase,.95),colorBgRightActionsItemHover:Fe(t.colorTextBase,.03),colorTextRightActionsItem:t.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextMenuActive:t.colorText},s.header),sider:u({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:0,colorBgCollapsedButton:t.colorBgElevated,colorTextCollapsedButtonHover:t.colorTextSecondary,colorTextCollapsedButton:Fe(t.colorTextBase,.25),colorMenuBackground:"transparent",colorMenuItemDivider:Fe(t.colorTextBase,.06),colorBgMenuItemHover:Fe(t.colorTextBase,.03),colorBgMenuItemSelected:Fe(t.colorTextBase,.04),colorTextMenuItemHover:t.colorText,colorTextMenuSelected:Fe(t.colorTextBase,.95),colorTextMenuActive:t.colorText,colorTextMenu:t.colorTextSecondary,colorTextMenuSecondary:t.colorTextTertiary,colorTextMenuTitle:t.colorText,colorTextSubMenuSelected:Fe(t.colorTextBase,.95)},s.sider),pageContainer:u({colorBgPageContainer:"transparent",paddingInlinePageContainerContent:((o=s.pageContainer)===null||o===void 0?void 0:o.marginInlinePageContainerContent)||40,paddingBlockPageContainerContent:((l=s.pageContainer)===null||l===void 0?void 0:l.marginBlockPageContainerContent)||32,colorBgPageContainerFixed:t.colorBgElevated},s.pageContainer)})},ua=function(){for(var e={},t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];for(var a=r.length,o,l=0;l<a;l+=1)for(o in r[l])r[l].hasOwnProperty(o)&&(we(e[o])==="object"&&we(r[l][o])==="object"&&e[o]!==void 0&&e[o]!==null&&!Array.isArray(e[o])&&!Array.isArray(r[l][o])?e[o]=u(u({},e[o]),r[l][o]):e[o]=r[l][o]);return e},fa=["locale","getPrefixCls"],ma=["locale","theme"],ga=function(e){var t={};if(Object.keys(e||{}).forEach(function(r){e[r]!==void 0&&(t[r]=e[r])}),!(Object.keys(t).length<1))return t},Ft=function(){var e,t;return!(typeof process<"u"&&(((e="production")===null||e===void 0?void 0:e.toUpperCase())==="TEST"||((t="production")===null||t===void 0?void 0:t.toUpperCase())==="DEV"))},Ne=me.createContext({intl:u(u({},_e),{},{locale:"default"}),valueTypeMap:{},theme:_t,hashed:!0,dark:!1,token:Ge});Ne.Consumer;var ha=function(){var e=On(),t=e.cache;return p.useEffect(function(){return function(){t.clear()}},[]),null},pa=function(e){var t,r=e.children,i=e.dark,a=e.valueTypeMap,o=e.autoClearCache,l=o===void 0?!1:o,s=e.token,d=e.prefixCls,C=e.intl,b=p.useContext(fe.ConfigContext),h=b.locale,v=b.getPrefixCls,S=Pe(b,fa),f=(t=qe.useToken)===null||t===void 0?void 0:t.call(qe),P=p.useContext(Ne),F=d?".".concat(d):".".concat(v(),"-pro"),g="."+v(),m="".concat(F),w=p.useMemo(function(){return da(s||{},f.token||Ge)},[s,f.token]),D=p.useMemo(function(){var c,y=h==null?void 0:h.locale,T=$n(y),I=C??(y&&((c=P.intl)===null||c===void 0?void 0:c.locale)==="default"?Ke[T]:P.intl||Ke[T]);return u(u({},P),{},{dark:i??P.dark,token:ua(P.token,f.token,{proComponentsCls:F,antCls:g,themeId:f.theme.id,layout:w}),intl:I||_e})},[h==null?void 0:h.locale,P,i,f.token,f.theme.id,F,g,w,C]),j=u(u({},D.token||{}),{},{proComponentsCls:F}),V=Xn(f.theme,[f.token,j??{}],{salt:m,override:j}),O=Ee(V,2),H=O[0],W=O[1],U=p.useMemo(function(){return!(e.hashed===!1||P.hashed===!1)},[P.hashed,e.hashed]),X=p.useMemo(function(){return e.hashed===!1||P.hashed===!1||Ft()===!1?"":f.hashId?f.hashId:W},[W,P.hashed,e.hashed]);p.useEffect(function(){ze.locale((h==null?void 0:h.locale)||"zh-cn")},[h==null?void 0:h.locale]);var G=p.useMemo(function(){return u(u({},S.theme),{},{hashId:X,hashed:U&&Ft()})},[S.theme,X,U,Ft()]),z=p.useMemo(function(){return u(u({},D),{},{valueTypeMap:a||(D==null?void 0:D.valueTypeMap),token:H,theme:f.theme,hashed:U,hashId:X})},[D,a,H,f.theme,U,X]),E=p.useMemo(function(){return M.jsx(fe,u(u({},S),{},{theme:G,children:M.jsx(Ne.Provider,{value:z,children:M.jsxs(M.Fragment,{children:[l&&M.jsx(ha,{}),r]})})}))},[S,G,z,l,r]);return l?M.jsx(Or,{value:{provider:function(){return new Map}},children:E}):E},Io=function(e){var t=e.needDeps,r=e.dark,i=e.token,a=p.useContext(Ne),o=p.useContext(fe.ConfigContext),l=o.locale,s=o.theme,d=Pe(o,ma),C=t&&a.hashId!==void 0&&Object.keys(e).sort().join("-")==="children-needDeps";if(C)return M.jsx(M.Fragment,{children:e.children});var b=function(){var S=r??a.dark;return S&&!Array.isArray(s==null?void 0:s.algorithm)?[qe.darkAlgorithm,s==null?void 0:s.algorithm].filter(Boolean):S&&Array.isArray(s==null?void 0:s.algorithm)?[qe.darkAlgorithm].concat(ot((s==null?void 0:s.algorithm)||[])).filter(Boolean):s==null?void 0:s.algorithm},h=u(u({},d),{},{locale:l||sr,theme:ga(u(u({},s),{},{algorithm:b()}))});return M.jsx(fe,u(u({},h),{},{children:M.jsx(pa,u(u({},e),{},{token:i}))}))};function ct(){var n=p.useContext(fe.ConfigContext),e=n.locale,t=p.useContext(Ne),r=t.intl;return r&&r.locale!=="default"?r||_e:e!=null&&e.locale&&Ke[$n(e.locale)]||_e}Ne.displayName="ProProvider";var Ue=Ne,va=function(e){return B({},e.componentCls,{display:"flex",justifyContent:"space-between",paddingBlock:8,paddingInlineStart:8,paddingInlineEnd:8,borderBlockStart:"1px solid ".concat(e.colorSplit)})};function ba(n){return Je("DropdownFooter",function(e){var t=u(u({},e),{},{componentCls:".".concat(n)});return[va(t)]})}var ya=function(e){var t=ct(),r=e.onClear,i=e.onConfirm,a=e.disabled,o=e.footerRender,l=p.useContext(fe.ConfigContext),s=l.getPrefixCls,d=s("pro-core-dropdown-footer"),C=ba(d),b=C.wrapSSR,h=C.hashId,v=[M.jsx(Tt,{style:{visibility:r?"visible":"hidden"},type:"link",size:"small",disabled:a,onClick:function(P){r&&r(P),P.stopPropagation()},children:t.getMessage("form.lightFilter.clear","清除")},"clear"),M.jsx(Tt,{"data-type":"confirm",type:"primary",size:"small",onClick:i,disabled:a,children:t.getMessage("form.lightFilter.confirm","确认")},"confirm")];if(o===!1||(o==null?void 0:o(i,r))===!1)return null;var S=(o==null?void 0:o(i,r))||v;return b(M.jsx("div",{className:xe(d,h),onClick:function(P){return P.target.getAttribute("data-type")!=="confirm"&&P.stopPropagation()},children:S}))},xa=function(e){return B({},e.componentCls,B(B(B(B(B(B(B(B({display:"inline-flex",gap:e.marginXXS,alignItems:"center",height:"30px",paddingBlock:0,paddingInline:8,fontSize:e.fontSize,lineHeight:"30px",borderRadius:"2px",cursor:"pointer","&:hover":{backgroundColor:e.colorBgTextHover},"&-active":B({paddingBlock:0,paddingInline:8,backgroundColor:e.colorBgTextHover},"&".concat(e.componentCls,"-allow-clear:hover:not(").concat(e.componentCls,"-disabled)"),B(B({},"".concat(e.componentCls,"-arrow"),{display:"none"}),"".concat(e.componentCls,"-close"),{display:"inline-flex"}))},"".concat(e.antCls,"-select"),B({},"".concat(e.antCls,"-select-clear"),{borderRadius:"50%"})),"".concat(e.antCls,"-picker"),B({},"".concat(e.antCls,"-picker-clear"),{borderRadius:"50%"})),"&-icon",B(B({color:e.colorIcon,transition:"color 0.3s",fontSize:12,verticalAlign:"middle"},"&".concat(e.componentCls,"-close"),{display:"none",fontSize:12,alignItems:"center",justifyContent:"center",color:e.colorTextPlaceholder,borderRadius:"50%"}),"&:hover",{color:e.colorIconHover})),"&-disabled",B({color:e.colorTextPlaceholder,cursor:"not-allowed"},"".concat(e.componentCls,"-icon"),{color:e.colorTextPlaceholder})),"&-small",B(B(B({height:"24px",paddingBlock:0,paddingInline:4,fontSize:e.fontSizeSM,lineHeight:"24px"},"&".concat(e.componentCls,"-active"),{paddingBlock:0,paddingInline:8}),"".concat(e.componentCls,"-icon"),{paddingBlock:0,paddingInline:0}),"".concat(e.componentCls,"-close"),{marginBlockStart:"-2px",paddingBlock:4,paddingInline:4,fontSize:"6px"})),"&-bordered",{height:"32px",paddingBlock:0,paddingInline:8,border:"".concat(e.lineWidth,"px solid ").concat(e.colorBorder),borderRadius:"@border-radius-base"}),"&-bordered&-small",{height:"24px",paddingBlock:0,paddingInline:8}),"&-bordered&-active",{backgroundColor:e.colorBgContainer}))};function Sa(n){return Je("FieldLabel",function(e){var t=u(u({},e),{},{componentCls:".".concat(n)});return[xa(t)]})}var Ca=function(e,t){var r,i,a,o=e.label,l=e.onClear,s=e.value,d=e.disabled,C=e.onLabelClick,b=e.ellipsis,h=e.placeholder,v=e.className,S=e.formatter,f=e.bordered,P=e.style,F=e.downIcon,g=e.allowClear,m=g===void 0?!0:g,w=e.valueMaxLength,D=w===void 0?41:w,j=(fe===null||fe===void 0||(r=fe.useConfig)===null||r===void 0?void 0:r.call(fe))||{componentSize:"middle"},V=j.componentSize,O=V,H=p.useContext(fe.ConfigContext),W=H.getPrefixCls,U=W("pro-core-field-label"),X=Sa(U),G=X.wrapSSR,z=X.hashId,E=ct(),c=p.useRef(null),y=p.useRef(null);p.useImperativeHandle(t,function(){return{labelRef:y,clearRef:c}});var T=function(R){return R.every(function(k){return typeof k=="string"})?R.join(","):R.map(function(k,$){var N=$===R.length-1?"":",";return typeof k=="string"?M.jsxs("span",{children:[k,N]},$):M.jsxs("span",{style:{display:"flex"},children:[k,N]},$)})},I=function(R){return S?S(R):Array.isArray(R)?T(R):R},L=function(R,k){if(k!=null&&k!==""&&(!Array.isArray(k)||k.length)){var $,N,Q=R?M.jsxs("span",{onClick:function(){C==null||C()},className:"".concat(U,"-text"),children:[R,": "]}):"",A=I(k);if(!b)return M.jsxs("span",{style:{display:"inline-flex",alignItems:"center"},children:[Q,I(k)]});var oe=function(){var re=Array.isArray(k)&&k.length>1,ae=E.getMessage("form.lightFilter.itemUnit","项");return typeof A=="string"&&A.length>D&&re?"...".concat(k.length).concat(ae):""},de=oe();return M.jsxs("span",{title:typeof A=="string"?A:void 0,style:{display:"inline-flex",alignItems:"center"},children:[Q,M.jsx("span",{style:{paddingInlineStart:4,display:"flex"},children:typeof A=="string"?A==null||($=A.toString())===null||$===void 0||(N=$.slice)===null||N===void 0?void 0:N.call($,0,D):A}),de]})}return R||h};return G(M.jsxs("span",{className:xe(U,z,"".concat(U,"-").concat((i=(a=e.size)!==null&&a!==void 0?a:O)!==null&&i!==void 0?i:"middle"),B(B(B(B({},"".concat(U,"-active"),(Array.isArray(s)?s.length>0:!!s)||s===0),"".concat(U,"-disabled"),d),"".concat(U,"-bordered"),f),"".concat(U,"-allow-clear"),m),v),style:P,ref:y,onClick:function(){var R;e==null||(R=e.onClick)===null||R===void 0||R.call(e)},children:[L(o,s),(s||s===0)&&m&&M.jsx(Zn,{role:"button",title:E.getMessage("form.lightFilter.clear","清除"),className:xe("".concat(U,"-icon"),z,"".concat(U,"-close")),onClick:function(R){d||l==null||l(),R.stopPropagation()},ref:c}),F!==!1?F??M.jsx(Jn,{className:xe("".concat(U,"-icon"),z,"".concat(U,"-arrow"))}):null]}))},Pa=me.forwardRef(Ca),je=function(e){var t={};if(Object.keys(e||{}).forEach(function(r){e[r]!==void 0&&(t[r]=e[r])}),!(Object.keys(t).length<1))return t},Fa=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,sn=function(e){return e==="*"||e==="x"||e==="X"},cn=function(e){var t=parseInt(e,10);return isNaN(t)?e:t},Ta=function(e,t){return we(e)!==we(t)?[String(e),String(t)]:[e,t]},wa=function(e,t){if(sn(e)||sn(t))return 0;var r=Ta(cn(e),cn(t)),i=Ee(r,2),a=i[0],o=i[1];return a>o?1:a<o?-1:0},Ra=function(e,t){for(var r=0;r<Math.max(e.length,t.length);r++){var i=wa(e[r]||"0",t[r]||"0");if(i!==0)return i}return 0},dn=function(e){var t,r=e.match(Fa);return r==null||(t=r.shift)===null||t===void 0||t.call(r),r},Ma=function(e,t){var r=dn(e),i=dn(t),a=r.pop(),o=i.pop(),l=Ra(r,i);return l!==0?l:a||o?a?-1:1:0},Ia=function(){var e;return typeof process>"u"?Kt:((e=process)===null||e===void 0||(e=e.env)===null||e===void 0?void 0:e.ANTD_VERSION)||Kt},Aa=function(e,t){var r=Ma(Ia(),"4.23.0")>-1?{open:e,onOpenChange:t}:{visible:e,onVisibleChange:t};return je(r)},Ea=function(e){return B(B(B({},"".concat(e.componentCls,"-label"),{cursor:"pointer"}),"".concat(e.componentCls,"-overlay"),{minWidth:"200px",marginBlockStart:"4px"}),"".concat(e.componentCls,"-content"),{paddingBlock:16,paddingInline:16})};function Da(n){return Je("FilterDropdown",function(e){var t=u(u({},e),{},{componentCls:".".concat(n)});return[Ea(t)]})}var La=function(e){var t=e.children,r=e.label,i=e.footer,a=e.open,o=e.onOpenChange,l=e.disabled,s=e.onVisibleChange,d=e.visible,C=e.footerRender,b=e.placement,h=p.useContext(fe.ConfigContext),v=h.getPrefixCls,S=v("pro-core-field-dropdown"),f=Da(S),P=f.wrapSSR,F=f.hashId,g=Aa(a||d||!1,o||s),m=p.useRef(null);return P(M.jsx(Qn,u(u({placement:b,trigger:["click"]},g),{},{overlayInnerStyle:{padding:0},content:M.jsxs("div",{ref:m,className:xe("".concat(S,"-overlay"),B(B({},"".concat(S,"-overlay-").concat(b),b),"hashId",F)),children:[M.jsx(fe,{getPopupContainer:function(){return m.current||document.body},children:M.jsx("div",{className:"".concat(S,"-content ").concat(F).trim(),children:t})}),i&&M.jsx(ya,u({disabled:l,footerRender:C},i))]}),children:M.jsx("span",{className:"".concat(S,"-label ").concat(F).trim(),children:r})})))},Hn=me.createContext({}),at={exports:{}},ka=at.exports,un;function ja(){return un||(un=1,function(n,e){(function(t,r){n.exports=r()})(ka,function(){var t="month",r="quarter";return function(i,a){var o=a.prototype;o.quarter=function(d){return this.$utils().u(d)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(d-1))};var l=o.add;o.add=function(d,C){return d=Number(d),this.$utils().p(C)===r?this.add(3*d,t):l.bind(this)(d,C)};var s=o.startOf;o.startOf=function(d,C){var b=this.$utils(),h=!!b.u(C)||C;if(b.p(d)===r){var v=this.quarter()-1;return h?this.month(3*v).startOf(t).startOf("day"):this.month(3*v+2).endOf(t).endOf("day")}return s.bind(this)(d,C)}}})}(at)),at.exports}var Oa=ja();const $a=Fn(Oa);var fn=function(e){return e==null};ze.extend($a);var Vn={time:"HH:mm:ss",timeRange:"HH:mm:ss",date:"YYYY-MM-DD",dateWeek:"YYYY-wo",dateMonth:"YYYY-MM",dateQuarter:"YYYY-[Q]Q",dateYear:"YYYY",dateRange:"YYYY-MM-DD",dateTime:"YYYY-MM-DD HH:mm:ss",dateTimeRange:"YYYY-MM-DD HH:mm:ss"};function mn(n){return Object.prototype.toString.call(n)==="[object Object]"}function Ba(n){if(mn(n)===!1)return!1;var e=n.constructor;if(e===void 0)return!0;var t=e.prototype;return!(mn(t)===!1||t.hasOwnProperty("isPrototypeOf")===!1)}var zt=function(e){return!!(e!=null&&e._isAMomentObject)},gn=function(e,t,r){if(!t)return e;if(ze.isDayjs(e)||zt(e)){if(t==="number")return e.valueOf();if(t==="string")return e.format(Vn[r]||"YYYY-MM-DD HH:mm:ss");if(typeof t=="string"&&t!=="string")return e.format(t);if(typeof t=="function")return t(e,r)}return e},Ao=function n(e,t,r,i,a){var o={};return typeof window>"u"||we(e)!=="object"||fn(e)||e instanceof Blob||Array.isArray(e)?e:(Object.keys(e).forEach(function(l){var s=a?[a,l].flat(1):[l],d=He(r,s)||"text",C="text",b;typeof d=="string"?C=d:(C=d.valueType,b=d.dateFormat);var h=e[l];if(!(fn(h)&&i)){if(Ba(h)&&!Array.isArray(h)&&!ze.isDayjs(h)&&!zt(h)){o[l]=n(h,t,r,i,s);return}if(Array.isArray(h)){o[l]=h.map(function(v,S){return ze.isDayjs(v)||zt(v)?gn(v,b||t,C):n(v,t,r,i,[l,"".concat(S)].flat(1))});return}o[l]=gn(h,b||t,C)}}),o)},hn=function(e,t){return typeof t=="function"?t(ze(e)):ze(e).format(t)},za=function(e,t){var r=Array.isArray(e)?e:[],i=Ee(r,2),a=i[0],o=i[1],l,s;Array.isArray(t)?(l=t[0],s=t[1]):we(t)==="object"&&t.type==="mask"?(l=t.format,s=t.format):(l=t,s=t);var d=a?hn(a,l):"",C=o?hn(o,s):"",b=d&&C?"".concat(d," ~ ").concat(C):"";return b};function Na(n){if(typeof n=="function"){for(var e=arguments.length,t=new Array(e>1?e-1:0),r=1;r<e;r++)t[r-1]=arguments[r];return n.apply(void 0,t)}return n}var st=function(e){var t=p.useRef(null);return t.current=e,p.useCallback(function(){for(var r,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return(r=t.current)===null||r===void 0?void 0:r.call.apply(r,[t].concat(ot(a)))},[])};function Ha(n,e){var t=st(n),r=p.useRef(),i=p.useCallback(function(){r.current&&(clearTimeout(r.current),r.current=null)},[]),a=p.useCallback(Ae(pe().mark(function o(){var l,s,d,C=arguments;return pe().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:for(l=C.length,s=new Array(l),d=0;d<l;d++)s[d]=C[d];if(!(e===0||e===void 0)){h.next=3;break}return h.abrupt("return",t.apply(void 0,s));case 3:return i(),h.abrupt("return",new Promise(function(v){r.current=setTimeout(Ae(pe().mark(function S(){return pe().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.t0=v,P.next=3,t.apply(void 0,s);case 3:return P.t1=P.sent,(0,P.t0)(P.t1),P.abrupt("return");case 6:case"end":return P.stop()}},S)})),e)}));case 5:case"end":return h.stop()}},o)})),[t,i,e]);return p.useEffect(function(){return i},[i]),{run:a,cancel:i}}function We(n,e,t,r){if(n===e)return!0;if(n&&e&&we(n)==="object"&&we(e)==="object"){if(n.constructor!==e.constructor)return!1;var i,a,o;if(Array.isArray(n)){if(i=n.length,i!=e.length)return!1;for(a=i;a--!==0;)if(!We(n[a],e[a],t,r))return!1;return!0}if(n instanceof Map&&e instanceof Map){if(n.size!==e.size)return!1;var l=ft(n.entries()),s;try{for(l.s();!(s=l.n()).done;)if(a=s.value,!e.has(a[0]))return!1}catch(S){l.e(S)}finally{l.f()}var d=ft(n.entries()),C;try{for(d.s();!(C=d.n()).done;)if(a=C.value,!We(a[1],e.get(a[0]),t,r))return!1}catch(S){d.e(S)}finally{d.f()}return!0}if(n instanceof Set&&e instanceof Set){if(n.size!==e.size)return!1;var b=ft(n.entries()),h;try{for(b.s();!(h=b.n()).done;)if(a=h.value,!e.has(a[0]))return!1}catch(S){b.e(S)}finally{b.f()}return!0}if(ArrayBuffer.isView(n)&&ArrayBuffer.isView(e)){if(i=n.length,i!=e.length)return!1;for(a=i;a--!==0;)if(n[a]!==e[a])return!1;return!0}if(n.constructor===RegExp)return n.source===e.source&&n.flags===e.flags;if(n.valueOf!==Object.prototype.valueOf&&n.valueOf)return n.valueOf()===e.valueOf();if(n.toString!==Object.prototype.toString&&n.toString)return n.toString()===e.toString();if(o=Object.keys(n),i=o.length,i!==Object.keys(e).length)return!1;for(a=i;a--!==0;)if(!Object.prototype.hasOwnProperty.call(e,o[a]))return!1;for(a=i;a--!==0;){var v=o[a];if(!(t!=null&&t.includes(v))&&!(v==="_owner"&&n.$$typeof)&&!We(n[v],e[v],t,r))return!1}return!0}return n!==n&&e!==e}var Va=function(e,t,r){return We(e,t,r)};function Ut(n,e){var t=p.useRef();return Va(n,t.current,e)||(t.current=n),t.current}function Eo(n,e,t){p.useEffect(n,Ut(e||[],t))}function Do(n,e,t,r){var i=Ha(Ae(pe().mark(function a(){return pe().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:n();case 1:case"end":return l.stop()}},a)})),r||16);p.useEffect(function(){i.run()},Ut(e||[],t))}function Te(n,e){return me.useMemo(n,Ut(e))}var _a=function(e){var t=!1;return(typeof e=="string"&&e.startsWith("date")&&!e.endsWith("Range")||e==="select"||e==="time")&&(t=!0),t},Ua=function(){for(var e={},t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];for(var a=r.length,o,l=0;l<a;l+=1)for(o in r[l])r[l].hasOwnProperty(o)&&(we(e[o])==="object"&&we(r[l][o])==="object"&&e[o]!==void 0&&e[o]!==null&&!Array.isArray(e[o])&&!Array.isArray(r[l][o])?e[o]=u(u({},e[o]),r[l][o]):e[o]=r[l][o]);return e},pn=0,vn=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:21;if(typeof window>"u"||!window.crypto)return(pn+=1).toFixed(0);for(var t="",r=crypto.getRandomValues(new Uint8Array(e));e--;){var i=63&r[e];t+=i<36?i.toString(36):i<62?(i-26).toString(36).toUpperCase():i<63?"_":"-"}return t},Wa=function(){return typeof window>"u"?vn():window.crypto&&window.crypto.randomUUID&&typeof crypto.randomUUID=="function"?crypto.randomUUID():vn()},Ya=["colon","dependencies","extra","getValueFromEvent","getValueProps","hasFeedback","help","htmlFor","initialValue","noStyle","label","labelAlign","labelCol","name","preserve","normalize","required","rules","shouldUpdate","trigger","validateFirst","validateStatus","validateTrigger","valuePropName","wrapperCol","hidden","addonBefore","addonAfter","addonWarpStyle"];function Ka(n){var e={};return Ya.forEach(function(t){n[t]!==void 0&&(e[t]=n[t])}),e}var rt={exports:{}},bn;function Ga(){return bn||(bn=1,function(n,e){const{hasOwnProperty:t}=Object.prototype,r=F();r.configure=F,r.stringify=r,r.default=r,e.stringify=r,e.configure=F,n.exports=r;const i=/[\u0000-\u001f\u0022\u005c\ud800-\udfff]/;function a(g){return g.length<5e3&&!i.test(g)?`"${g}"`:JSON.stringify(g)}function o(g,m){if(g.length>200||m)return g.sort(m);for(let w=1;w<g.length;w++){const D=g[w];let j=w;for(;j!==0&&g[j-1]>D;)g[j]=g[j-1],j--;g[j]=D}return g}const l=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Object.getPrototypeOf(new Int8Array)),Symbol.toStringTag).get;function s(g){return l.call(g)!==void 0&&g.length!==0}function d(g,m,w){g.length<w&&(w=g.length);const D=m===","?"":" ";let j=`"0":${D}${g[0]}`;for(let V=1;V<w;V++)j+=`${m}"${V}":${D}${g[V]}`;return j}function C(g){if(t.call(g,"circularValue")){const m=g.circularValue;if(typeof m=="string")return`"${m}"`;if(m==null)return m;if(m===Error||m===TypeError)return{toString(){throw new TypeError("Converting circular structure to JSON")}};throw new TypeError('The "circularValue" argument must be of type string or the value null or undefined')}return'"[Circular]"'}function b(g){let m;if(t.call(g,"deterministic")&&(m=g.deterministic,typeof m!="boolean"&&typeof m!="function"))throw new TypeError('The "deterministic" argument must be of type boolean or comparator function');return m===void 0?!0:m}function h(g,m){let w;if(t.call(g,m)&&(w=g[m],typeof w!="boolean"))throw new TypeError(`The "${m}" argument must be of type boolean`);return w===void 0?!0:w}function v(g,m){let w;if(t.call(g,m)){if(w=g[m],typeof w!="number")throw new TypeError(`The "${m}" argument must be of type number`);if(!Number.isInteger(w))throw new TypeError(`The "${m}" argument must be an integer`);if(w<1)throw new RangeError(`The "${m}" argument must be >= 1`)}return w===void 0?1/0:w}function S(g){return g===1?"1 item":`${g} items`}function f(g){const m=new Set;for(const w of g)(typeof w=="string"||typeof w=="number")&&m.add(String(w));return m}function P(g){if(t.call(g,"strict")){const m=g.strict;if(typeof m!="boolean")throw new TypeError('The "strict" argument must be of type boolean');if(m)return w=>{let D=`Object can not safely be stringified. Received type ${typeof w}`;throw typeof w!="function"&&(D+=` (${w.toString()})`),new Error(D)}}}function F(g){g={...g};const m=P(g);m&&(g.bigint===void 0&&(g.bigint=!1),"circularValue"in g||(g.circularValue=Error));const w=C(g),D=h(g,"bigint"),j=b(g),V=typeof j=="function"?j:void 0,O=v(g,"maximumDepth"),H=v(g,"maximumBreadth");function W(E,c,y,T,I,L){let x=c[E];switch(typeof x=="object"&&x!==null&&typeof x.toJSON=="function"&&(x=x.toJSON(E)),x=T.call(c,E,x),typeof x){case"string":return a(x);case"object":{if(x===null)return"null";if(y.indexOf(x)!==-1)return w;let R="",k=",";const $=L;if(Array.isArray(x)){if(x.length===0)return"[]";if(O<y.length+1)return'"[Array]"';y.push(x),I!==""&&(L+=I,R+=`
${L}`,k=`,
${L}`);const K=Math.min(x.length,H);let re=0;for(;re<K-1;re++){const q=W(String(re),x,y,T,I,L);R+=q!==void 0?q:"null",R+=k}const ae=W(String(re),x,y,T,I,L);if(R+=ae!==void 0?ae:"null",x.length-1>H){const q=x.length-H-1;R+=`${k}"... ${S(q)} not stringified"`}return I!==""&&(R+=`
${$}`),y.pop(),`[${R}]`}let N=Object.keys(x);const Q=N.length;if(Q===0)return"{}";if(O<y.length+1)return'"[Object]"';let A="",oe="";I!==""&&(L+=I,k=`,
${L}`,A=" ");const de=Math.min(Q,H);j&&!s(x)&&(N=o(N,V)),y.push(x);for(let K=0;K<de;K++){const re=N[K],ae=W(re,x,y,T,I,L);ae!==void 0&&(R+=`${oe}${a(re)}:${A}${ae}`,oe=k)}if(Q>H){const K=Q-H;R+=`${oe}"...":${A}"${S(K)} not stringified"`,oe=k}return I!==""&&oe.length>1&&(R=`
${L}${R}
${$}`),y.pop(),`{${R}}`}case"number":return isFinite(x)?String(x):m?m(x):"null";case"boolean":return x===!0?"true":"false";case"undefined":return;case"bigint":if(D)return String(x);default:return m?m(x):void 0}}function U(E,c,y,T,I,L){switch(typeof c=="object"&&c!==null&&typeof c.toJSON=="function"&&(c=c.toJSON(E)),typeof c){case"string":return a(c);case"object":{if(c===null)return"null";if(y.indexOf(c)!==-1)return w;const x=L;let R="",k=",";if(Array.isArray(c)){if(c.length===0)return"[]";if(O<y.length+1)return'"[Array]"';y.push(c),I!==""&&(L+=I,R+=`
${L}`,k=`,
${L}`);const Q=Math.min(c.length,H);let A=0;for(;A<Q-1;A++){const de=U(String(A),c[A],y,T,I,L);R+=de!==void 0?de:"null",R+=k}const oe=U(String(A),c[A],y,T,I,L);if(R+=oe!==void 0?oe:"null",c.length-1>H){const de=c.length-H-1;R+=`${k}"... ${S(de)} not stringified"`}return I!==""&&(R+=`
${x}`),y.pop(),`[${R}]`}y.push(c);let $="";I!==""&&(L+=I,k=`,
${L}`,$=" ");let N="";for(const Q of T){const A=U(Q,c[Q],y,T,I,L);A!==void 0&&(R+=`${N}${a(Q)}:${$}${A}`,N=k)}return I!==""&&N.length>1&&(R=`
${L}${R}
${x}`),y.pop(),`{${R}}`}case"number":return isFinite(c)?String(c):m?m(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(D)return String(c);default:return m?m(c):void 0}}function X(E,c,y,T,I){switch(typeof c){case"string":return a(c);case"object":{if(c===null)return"null";if(typeof c.toJSON=="function"){if(c=c.toJSON(E),typeof c!="object")return X(E,c,y,T,I);if(c===null)return"null"}if(y.indexOf(c)!==-1)return w;const L=I;if(Array.isArray(c)){if(c.length===0)return"[]";if(O<y.length+1)return'"[Array]"';y.push(c),I+=T;let A=`
${I}`;const oe=`,
${I}`,de=Math.min(c.length,H);let K=0;for(;K<de-1;K++){const ae=X(String(K),c[K],y,T,I);A+=ae!==void 0?ae:"null",A+=oe}const re=X(String(K),c[K],y,T,I);if(A+=re!==void 0?re:"null",c.length-1>H){const ae=c.length-H-1;A+=`${oe}"... ${S(ae)} not stringified"`}return A+=`
${L}`,y.pop(),`[${A}]`}let x=Object.keys(c);const R=x.length;if(R===0)return"{}";if(O<y.length+1)return'"[Object]"';I+=T;const k=`,
${I}`;let $="",N="",Q=Math.min(R,H);s(c)&&($+=d(c,k,H),x=x.slice(c.length),Q-=c.length,N=k),j&&(x=o(x,V)),y.push(c);for(let A=0;A<Q;A++){const oe=x[A],de=X(oe,c[oe],y,T,I);de!==void 0&&($+=`${N}${a(oe)}: ${de}`,N=k)}if(R>H){const A=R-H;$+=`${N}"...": "${S(A)} not stringified"`,N=k}return N!==""&&($=`
${I}${$}
${L}`),y.pop(),`{${$}}`}case"number":return isFinite(c)?String(c):m?m(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(D)return String(c);default:return m?m(c):void 0}}function G(E,c,y){switch(typeof c){case"string":return a(c);case"object":{if(c===null)return"null";if(typeof c.toJSON=="function"){if(c=c.toJSON(E),typeof c!="object")return G(E,c,y);if(c===null)return"null"}if(y.indexOf(c)!==-1)return w;let T="";const I=c.length!==void 0;if(I&&Array.isArray(c)){if(c.length===0)return"[]";if(O<y.length+1)return'"[Array]"';y.push(c);const $=Math.min(c.length,H);let N=0;for(;N<$-1;N++){const A=G(String(N),c[N],y);T+=A!==void 0?A:"null",T+=","}const Q=G(String(N),c[N],y);if(T+=Q!==void 0?Q:"null",c.length-1>H){const A=c.length-H-1;T+=`,"... ${S(A)} not stringified"`}return y.pop(),`[${T}]`}let L=Object.keys(c);const x=L.length;if(x===0)return"{}";if(O<y.length+1)return'"[Object]"';let R="",k=Math.min(x,H);I&&s(c)&&(T+=d(c,",",H),L=L.slice(c.length),k-=c.length,R=","),j&&(L=o(L,V)),y.push(c);for(let $=0;$<k;$++){const N=L[$],Q=G(N,c[N],y);Q!==void 0&&(T+=`${R}${a(N)}:${Q}`,R=",")}if(x>H){const $=x-H;T+=`${R}"...":"${S($)} not stringified"`}return y.pop(),`{${T}}`}case"number":return isFinite(c)?String(c):m?m(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(D)return String(c);default:return m?m(c):void 0}}function z(E,c,y){if(arguments.length>1){let T="";if(typeof y=="number"?T=" ".repeat(Math.min(y,10)):typeof y=="string"&&(T=y.slice(0,10)),c!=null){if(typeof c=="function")return W("",{"":E},[],c,T,"");if(Array.isArray(c))return U("",E,[],f(c),T,"")}if(T.length!==0)return X("",E,[],T,"")}return G("",E,[])}return z}}(rt,rt.exports)),rt.exports}var qa=Ga();const Xa=Fn(qa),Za=Xa.configure;var yn=Za({bigint:!0,circularValue:"Magic circle!",deterministic:!1,maximumDepth:4}),Wt=me.createContext({}),Ja=["children","Wrapper"],Qa=["children","Wrapper"],eo=p.createContext({grid:!1,colProps:void 0,rowProps:void 0}),to=function(e){var t=e.grid,r=e.rowProps,i=e.colProps;return{grid:!!t,RowWrapper:function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=o.children,s=o.Wrapper,d=Pe(o,Ja);return t?M.jsx(tr,u(u(u({gutter:8},r),d),{},{children:l})):s?M.jsx(s,{children:l}):l},ColWrapper:function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=o.children,s=o.Wrapper,d=Pe(o,Qa),C=p.useMemo(function(){var b=u(u({},i),d);return typeof b.span>"u"&&typeof b.xs>"u"&&(b.xs=24),b},[d]);return t?M.jsx(er,u(u({},C),{},{children:l})):s?M.jsx(s,{children:l}):l}}},Yt=function(e){var t=p.useMemo(function(){return we(e)==="object"?e:{grid:e}},[e]),r=p.useContext(eo),i=r.grid,a=r.colProps;return p.useMemo(function(){return to({grid:!!(i||t.grid),rowProps:t==null?void 0:t.rowProps,colProps:(t==null?void 0:t.colProps)||a,Wrapper:t==null?void 0:t.Wrapper})},[t==null?void 0:t.Wrapper,t.grid,i,JSON.stringify([a,t==null?void 0:t.colProps,t==null?void 0:t.rowProps])])},no=["valueType","customLightMode","lightFilterLabelFormatter","valuePropName","ignoreWidth","defaultProps"],ro=["label","tooltip","placeholder","width","bordered","messageVariables","ignoreFormItem","transform","convertValue","readonly","allowClear","colSize","getFormItemProps","getFieldProps","filedConfig","cacheForSwr","proFieldProps"],xn={xs:104,s:216,sm:216,m:328,md:328,l:440,lg:440,xl:552},io=["switch","radioButton","radio","rate"];function Lo(n,e){n.displayName="ProFormComponent";var t=function(a){var o=u(u({},a==null?void 0:a.filedConfig),e),l=o.valueType,s=o.customLightMode,d=o.lightFilterLabelFormatter,C=o.valuePropName,b=C===void 0?"value":C,h=o.ignoreWidth,v=o.defaultProps,S=Pe(o,no),f=u(u({},v),a),P=f.label,F=f.tooltip,g=f.placeholder,m=f.width,w=f.bordered,D=f.messageVariables,j=f.ignoreFormItem,V=f.transform,O=f.convertValue,H=f.readonly,W=f.allowClear;f.colSize;var U=f.getFormItemProps,X=f.getFieldProps;f.filedConfig;var G=f.cacheForSwr,z=f.proFieldProps,E=Pe(f,ro),c=l||E.valueType,y=p.useMemo(function(){return h||io.includes(c)},[h,c]),T=p.useState(),I=Ee(T,2),L=I[1],x=p.useState(),R=Ee(x,2),k=R[0],$=R[1],N=me.useContext(Wt),Q=Te(function(){return{formItemProps:U==null?void 0:U(),fieldProps:X==null?void 0:X()}},[X,U,E.dependenciesValues,k]),A=Te(function(){var J=u(u(u(u({},j?je({value:E.value}):{}),{},{placeholder:g,disabled:a.disabled},N.fieldProps),Q.fieldProps),E.fieldProps);return J.style=je(J==null?void 0:J.style),J},[j,E.value,E.fieldProps,g,a.disabled,N.fieldProps,Q.fieldProps]),oe=Ka(E),de=Te(function(){return u(u(u(u({},N.formItemProps),oe),Q.formItemProps),E.formItemProps)},[Q.formItemProps,N.formItemProps,E.formItemProps,oe]),K=Te(function(){return u(u({messageVariables:D},S),de)},[S,de,D]);wt(!E.defaultValue,"请不要在 Form 中使用 defaultXXX。如果需要默认值请使用 initialValues 和 initialValue。");var re=p.useContext(nr),ae=re.prefixName,q=Te(function(){var J,le=K==null?void 0:K.name;Array.isArray(le)&&(le=le.join("_")),Array.isArray(ae)&&le&&(le="".concat(ae.join("."),".").concat(le));var se=le&&"form-".concat((J=N.formKey)!==null&&J!==void 0?J:"","-field-").concat(le);return se},[yn(K==null?void 0:K.name),ae,N.formKey]),ee=st(function(){var J;U||X?$([]):E.renderFormItem&&L([]);for(var le=arguments.length,se=new Array(le),he=0;he<le;he++)se[he]=arguments[he];A==null||(J=A.onChange)===null||J===void 0||J.call.apply(J,[A].concat(se))}),ce=Te(function(){var J=u({width:m&&!xn[m]?m:N.grid?"100%":void 0},A==null?void 0:A.style);return y&&Reflect.deleteProperty(J,"width"),je(J)},[yn(A==null?void 0:A.style),N.grid,y,m]),Z=Te(function(){var J=m&&xn[m];return xe(A==null?void 0:A.className,B({"pro-field":J},"pro-field-".concat(m),J&&!y))||void 0},[m,A==null?void 0:A.className,y]),ue=Te(function(){return je(u(u({},N.proFieldProps),{},{mode:E==null?void 0:E.mode,readonly:H,params:E.params,proFieldKey:q,cacheForSwr:G},z))},[N.proFieldProps,E==null?void 0:E.mode,E.params,H,q,G,z]),_=Te(function(){return u(u({onChange:ee,allowClear:W},A),{},{style:ce,className:Z})},[W,Z,ee,A,ce]),Y=Te(function(){return M.jsx(n,u(u({},E),{},{fieldProps:_,proFieldProps:ue,ref:a==null?void 0:a.fieldRef}),a.proFormFieldKey||a.name)},[ue,_,E]),ie=Te(function(){var J,le,se,he;return M.jsx(yo,u(u({label:P&&(z==null?void 0:z.light)!==!0?P:void 0,tooltip:(z==null?void 0:z.light)!==!0&&F,valuePropName:b},K),{},{ignoreFormItem:j,transform:V,dataFormat:A==null?void 0:A.format,valueType:c,messageVariables:u({label:P||""},K==null?void 0:K.messageVariables),convertValue:O,lightProps:je(u(u(u({},A),{},{valueType:c,bordered:w,allowClear:(le=Y==null||(se=Y.props)===null||se===void 0?void 0:se.allowClear)!==null&&le!==void 0?le:W,light:z==null?void 0:z.light,label:P,customLightMode:s,labelFormatter:d,valuePropName:b,footerRender:Y==null||(he=Y.props)===null||he===void 0?void 0:he.footerRender},E.lightProps),K.lightProps)),children:Y}),a.proFormFieldKey||((J=K.name)===null||J===void 0?void 0:J.toString()))},[P,z==null?void 0:z.light,F,b,a.proFormFieldKey,K,j,V,A,c,O,w,Y,W,s,d,E.lightProps]),ge=Yt(E),ye=ge.ColWrapper;return M.jsx(ye,{children:ie})},r=function(a){var o=a.dependencies;return o?M.jsx(Un,{name:o,originDependencies:a==null?void 0:a.originDependencies,children:function(s){return M.jsx(t,u({dependenciesValues:s,dependencies:o},a))}}):M.jsx(t,u({dependencies:o},a))};return r}var _n=me.createContext({mode:"edit"}),ao=["creatorButtonProps","deleteIconProps","copyIconProps","itemContainerRender","itemRender","alwaysShowItemLabel","prefixCls","creatorRecord","action","actionGuard","children","actionRender","fields","meta","field","index","formInstance","originName","containerClassName","containerStyle","min","max","count"],oo=function(e){return Array.isArray(e)?e:typeof e=="function"?[e]:rr(e)},lo=function(e){var t,r;e.creatorButtonProps;var i=e.deleteIconProps,a=e.copyIconProps,o=e.itemContainerRender,l=e.itemRender,s=e.alwaysShowItemLabel,d=e.prefixCls;e.creatorRecord;var C=e.action;e.actionGuard;var b=e.children,h=e.actionRender,v=e.fields,S=e.meta,f=e.field,P=e.index,F=e.formInstance,g=e.originName,m=e.containerClassName,w=e.containerStyle,D=e.min,j=e.max,V=e.count,O=Pe(e,ao),H=p.useContext(Ue),W=H.hashId,U=((t=fe.useConfig)===null||t===void 0?void 0:t.call(fe))||{componentSize:"middle"},X=U.componentSize,G=p.useContext(Xe),z=p.useRef(!1),E=p.useContext(_n),c=E.mode,y=p.useState(!1),T=Ee(y,2),I=T[0],L=T[1],x=p.useState(!1),R=Ee(x,2),k=R[0],$=R[1];p.useEffect(function(){return function(){z.current=!0}},[]);var N=function(){return F.getFieldValue([G.listName,g,P==null?void 0:P.toString()].flat(1).filter(function(Y){return Y!=null}))},Q={getCurrentRowData:N,setCurrentRowData:function(Y){var ie,ge=(F==null||(ie=F.getFieldsValue)===null||ie===void 0?void 0:ie.call(F))||{},ye=[G.listName,g,P==null?void 0:P.toString()].flat(1).filter(function(le){return le!=null}),J=Rt(ge,ye,u(u({},N()),Y||{}));return F.setFieldsValue(J)}},A=oo(b).map(function(_){return typeof _=="function"?_==null?void 0:_(f,P,u(u({},C),Q),V):_}).map(function(_,Y){if(me.isValidElement(_)){var ie;return me.cloneElement(_,u({key:_.key||(_==null||(ie=_.props)===null||ie===void 0?void 0:ie.name)||Y},(_==null?void 0:_.props)||{}))}return _}),oe=p.useMemo(function(){if(c==="read"||a===!1||j===V)return null;var _=a,Y=_.Icon,ie=Y===void 0?Cn:Y,ge=_.tooltipText;return M.jsx(Gt,{title:ge,children:k?M.jsx(qt,{}):M.jsx(ie,{className:xe("".concat(d,"-action-icon action-copy"),W),onClick:Ae(pe().mark(function ye(){var J;return pe().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return $(!0),J=F==null?void 0:F.getFieldValue([G.listName,g,f.name].filter(function(he){return he!==void 0}).flat(1)),se.next=4,C.add(J);case 4:$(!1);case 5:case"end":return se.stop()}},ye)}))})},"copy")},[a,j,V,k,d,W,F,G.listName,f.name,g,C]),de=p.useMemo(function(){if(c==="read"||i===!1||D===V)return null;var _=i,Y=_.Icon,ie=Y===void 0?Pn:Y,ge=_.tooltipText;return M.jsx(Gt,{title:ge,children:I?M.jsx(qt,{}):M.jsx(ie,{className:xe("".concat(d,"-action-icon action-remove"),W),onClick:Ae(pe().mark(function ye(){return pe().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return L(!0),le.next=3,C.remove(f.name);case 3:z.current||L(!1);case 4:case"end":return le.stop()}},ye)}))})},"delete")},[i,D,V,I,d,W,C,f.name]),K=p.useMemo(function(){return[oe,de].filter(function(_){return _!=null})},[oe,de]),re=(h==null?void 0:h(f,C,K,V))||K,ae=re.length>0&&c!=="read"?M.jsx("div",{className:xe("".concat(d,"-action"),B({},"".concat(d,"-action-small"),X==="small"),W),children:re}):null,q={name:O.name,field:f,index:P,record:F==null||(r=F.getFieldValue)===null||r===void 0?void 0:r.call(F,[G.listName,g,f.name].filter(function(_){return _!==void 0}).flat(1)),fields:v,operation:C,meta:S},ee=Yt(),ce=ee.grid,Z=(o==null?void 0:o(A,q))||A,ue=(l==null?void 0:l({listDom:M.jsx("div",{className:xe("".concat(d,"-container"),m,W),style:u({width:ce?"100%":void 0},w),children:Z}),action:ae},q))||M.jsxs("div",{className:xe("".concat(d,"-item"),W,B(B({},"".concat(d,"-item-default"),s===void 0),"".concat(d,"-item-show-label"),s)),style:{display:"flex",alignItems:"flex-end"},children:[M.jsx("div",{className:xe("".concat(d,"-container"),m,W),style:u({width:ce?"100%":void 0},w),children:Z}),ae]});return M.jsx(Xe.Provider,{value:u(u({},f),{},{listName:[G.listName,g,f.name].filter(function(_){return _!==void 0}).flat(1)}),children:ue})},so=function(e){var t=ct(),r=e.creatorButtonProps,i=e.prefixCls,a=e.children,o=e.creatorRecord,l=e.action,s=e.fields,d=e.actionGuard,C=e.max,b=e.fieldExtraRender,h=e.meta,v=e.containerClassName,S=e.containerStyle,f=e.onAfterAdd,P=e.onAfterRemove,F=p.useContext(Ue),g=F.hashId,m=p.useRef(new Map),w=p.useState(!1),D=Ee(w,2),j=D[0],V=D[1],O=p.useMemo(function(){return s.map(function(z){var E,c;if(!((E=m.current)!==null&&E!==void 0&&E.has(z.key.toString()))){var y;(y=m.current)===null||y===void 0||y.set(z.key.toString(),Wa())}var T=(c=m.current)===null||c===void 0?void 0:c.get(z.key.toString());return u(u({},z),{},{uuid:T})})},[s]),H=p.useMemo(function(){var z=u({},l),E=O.length;return d!=null&&d.beforeAddRow?z.add=Ae(pe().mark(function c(){var y,T,I,L,x,R=arguments;return pe().wrap(function($){for(;;)switch($.prev=$.next){case 0:for(y=R.length,T=new Array(y),I=0;I<y;I++)T[I]=R[I];return $.next=3,d.beforeAddRow.apply(d,T.concat([E]));case 3:if(L=$.sent,!L){$.next=8;break}return x=l.add.apply(l,T),f==null||f.apply(void 0,T.concat([E+1])),$.abrupt("return",x);case 8:return $.abrupt("return",!1);case 9:case"end":return $.stop()}},c)})):z.add=Ae(pe().mark(function c(){var y,T,I,L,x=arguments;return pe().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:for(y=x.length,T=new Array(y),I=0;I<y;I++)T[I]=x[I];return L=l.add.apply(l,T),f==null||f.apply(void 0,T.concat([E+1])),k.abrupt("return",L);case 4:case"end":return k.stop()}},c)})),d!=null&&d.beforeRemoveRow?z.remove=Ae(pe().mark(function c(){var y,T,I,L,x,R=arguments;return pe().wrap(function($){for(;;)switch($.prev=$.next){case 0:for(y=R.length,T=new Array(y),I=0;I<y;I++)T[I]=R[I];return $.next=3,d.beforeRemoveRow.apply(d,T.concat([E]));case 3:if(L=$.sent,!L){$.next=8;break}return x=l.remove.apply(l,T),P==null||P.apply(void 0,T.concat([E-1])),$.abrupt("return",x);case 8:return $.abrupt("return",!1);case 9:case"end":return $.stop()}},c)})):z.remove=Ae(pe().mark(function c(){var y,T,I,L,x=arguments;return pe().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:for(y=x.length,T=new Array(y),I=0;I<y;I++)T[I]=x[I];return L=l.remove.apply(l,T),P==null||P.apply(void 0,T.concat([E-1])),k.abrupt("return",L);case 4:case"end":return k.stop()}},c)})),z},[l,d==null?void 0:d.beforeAddRow,d==null?void 0:d.beforeRemoveRow,f,P,O.length]),W=p.useMemo(function(){if(r===!1||O.length===C)return null;var z=r||{},E=z.position,c=E===void 0?"bottom":E,y=z.creatorButtonText,T=y===void 0?t.getMessage("editableTable.action.add","添加一行数据"):y;return M.jsx(Tt,u(u({className:"".concat(i,"-creator-button-").concat(c," ").concat(g||"").trim(),type:"dashed",loading:j,block:!0,icon:M.jsx(ir,{})},Mt(r||{},["position","creatorButtonText"])),{},{onClick:Ae(pe().mark(function I(){var L,x;return pe().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:return V(!0),x=O.length,c==="top"&&(x=0),k.next=5,H.add((L=Na(o))!==null&&L!==void 0?L:{},x);case 5:V(!1);case 6:case"end":return k.stop()}},I)})),children:T}))},[r,O.length,C,t,i,g,j,H,o]),U=p.useContext(_n),X=u({width:"max-content",maxWidth:"100%",minWidth:"100%"},S),G=p.useMemo(function(){return O.map(function(z,E){return p.createElement(lo,u(u({},e),{},{key:z.uuid,field:z,index:E,action:H,count:O.length}),a)})},[a,e,O,H]);return U.mode==="read"||e.readonly===!0?M.jsx(M.Fragment,{children:G}):M.jsxs("div",{style:X,className:v,children:[r!==!1&&(r==null?void 0:r.position)==="top"&&W,G,b&&b(H,h),r!==!1&&(r==null?void 0:r.position)!=="top"&&W]})},co=function(e){return B(B({},"".concat(e.antCls,"-pro"),B({},"".concat(e.antCls,"-form:not(").concat(e.antCls,"-form-horizontal)"),B({},e.componentCls,B({},"&-item:not(".concat(e.componentCls,"-item-show-label)"),B({},"".concat(e.antCls,"-form-item-label"),{display:"none"}))))),e.componentCls,B(B({maxWidth:"100%","&-item":{"&&-show-label":B({},"".concat(e.antCls,"-form-item-label"),{display:"inline-block"}),"&&-default:first-child":{"div:first-of-type":B({},"".concat(e.antCls,"-form-item"),B({},"".concat(e.antCls,"-form-item-label"),{display:"inline-block"}))},"&&-default:not(:first-child)":{"div:first-of-type":B({},"".concat(e.antCls,"-form-item"),B({},"".concat(e.antCls,"-form-item-label"),{display:"none"}))}},"&-action":{display:"flex",height:e.controlHeight,marginBlockEnd:e.marginLG,lineHeight:e.controlHeight+"px","&-small":{height:e.controlHeightSM,lineHeight:e.controlHeightSM}},"&-action-icon":{marginInlineStart:8,cursor:"pointer",transition:"color 0.3s ease-in-out","&:hover":{color:e.colorPrimaryTextHover}}},"".concat(e.proComponentsCls,"-card ").concat(e.proComponentsCls,"-card-extra"),B({},e.componentCls,{"&-action":{marginBlockEnd:0}})),"&-creator-button-top",{marginBlockEnd:24}))};function uo(n){return Je("ProFormList",function(e){var t=u(u({},e),{},{componentCls:".".concat(n)});return[co(t)]})}var fo=["transform","actionRender","creatorButtonProps","label","alwaysShowItemLabel","tooltip","creatorRecord","itemRender","rules","itemContainerRender","fieldExtraRender","copyIconProps","children","deleteIconProps","actionRef","style","prefixCls","actionGuard","min","max","colProps","wrapperCol","rowProps","onAfterAdd","onAfterRemove","isValidateList","emptyListMessage","className","containerClassName","containerStyle","readonly"],Xe=me.createContext({});function ko(n){var e=p.useRef(),t=p.useContext(fe.ConfigContext),r=p.useContext(Xe),i=t.getPrefixCls("pro-form-list"),a=ct(),o=me.useContext(Wt),l=o.setFieldValueType,s=n.transform,d=n.actionRender,C=n.creatorButtonProps,b=n.label,h=n.alwaysShowItemLabel,v=n.tooltip,S=n.creatorRecord,f=n.itemRender,P=n.rules,F=n.itemContainerRender,g=n.fieldExtraRender,m=n.copyIconProps,w=m===void 0?{Icon:Cn,tooltipText:a.getMessage("copyThisLine","复制此项")}:m,D=n.children,j=n.deleteIconProps,V=j===void 0?{Icon:Pn,tooltipText:a.getMessage("deleteThisLine","删除此项")}:j,O=n.actionRef,H=n.style,W=n.prefixCls,U=n.actionGuard,X=n.min,G=n.max,z=n.colProps,E=n.wrapperCol,c=n.rowProps,y=n.onAfterAdd,T=n.onAfterRemove,I=n.isValidateList,L=I===void 0?!1:I,x=n.emptyListMessage,R=x===void 0?"列表不能为空":x,k=n.className,$=n.containerClassName,N=n.containerStyle,Q=n.readonly,A=Pe(n,fo),oe=Yt({colProps:z,rowProps:c}),de=oe.ColWrapper,K=oe.RowWrapper,re=p.useContext(Hn),ae=p.useMemo(function(){return r.name===void 0?[A.name].flat(1):[r.name,A.name].flat(1)},[r.name,A.name]);p.useImperativeHandle(O,function(){return u(u({},e.current),{},{get:function(ue){return re.formRef.current.getFieldValue([].concat(ot(ae),[ue]))},getList:function(){return re.formRef.current.getFieldValue(ot(ae))}})},[ae,re.formRef]),p.useEffect(function(){wt(!!re.formRef,"ProFormList 必须要放到 ProForm 中,否则会造成行为异常。"),wt(!!re.formRef,"Proformlist must be placed in ProForm, otherwise it will cause abnormal behavior.")},[re.formRef]),p.useEffect(function(){!l||!n.name||l([n.name].flat(1).filter(function(Z){return Z!==void 0}),{valueType:"formList",transform:s})},[n.name,l,s]);var q=uo(i),ee=q.wrapSSR,ce=q.hashId;return re.formRef?ee(M.jsx(de,{children:M.jsx("div",{className:xe(i,ce),style:H,children:M.jsx(Ve.Item,u(u({label:b,prefixCls:W,tooltip:v,style:H,required:P==null?void 0:P.some(function(Z){return Z.required}),wrapperCol:E,className:k},A),{},{name:L?ae:void 0,rules:L?[{validator:function(ue,_){return!_||_.length===0?Promise.reject(new Error(R)):Promise.resolve()},required:!0}]:void 0,children:M.jsx(Ve.List,u(u({rules:P},A),{},{name:ae,children:function(ue,_,Y){return e.current=_,M.jsxs(K,{children:[M.jsx(so,{name:ae,readonly:!!Q,originName:A.name,copyIconProps:w,deleteIconProps:V,formInstance:re.formRef.current,prefixCls:i,meta:Y,fields:ue,itemContainerRender:F,itemRender:f,fieldExtraRender:g,creatorButtonProps:C,creatorRecord:S,actionRender:d,action:_,actionGuard:U,alwaysShowItemLabel:h,min:X,max:G,count:ue.length,onAfterAdd:function(ge,ye,J){L&&re.formRef.current.validateFields([ae]),y==null||y(ge,ye,J)},onAfterRemove:function(ge,ye){L&&ye===0&&re.formRef.current.validateFields([ae]),T==null||T(ge,ye)},containerClassName:$,containerStyle:N,children:D}),M.jsx(Ve.ErrorList,{errors:Y.errors})]})}}))}))})})):null}var mo=["name","originDependencies","children","ignoreFormListField"],Un=function(e){var t=e.name,r=e.originDependencies,i=r===void 0?t:r,a=e.children,o=e.ignoreFormListField,l=Pe(e,mo),s=p.useContext(Hn),d=p.useContext(Xe),C=p.useMemo(function(){return t.map(function(b){var h,v=[b];return!o&&d.name!==void 0&&(h=d.listName)!==null&&h!==void 0&&h.length&&v.unshift(d.listName),v.flat(1)})},[d.listName,d.name,o,t==null?void 0:t.toString()]);return M.jsx(Ve.Item,u(u({},l),{},{noStyle:!0,shouldUpdate:function(h,v,S){if(typeof l.shouldUpdate=="boolean")return l.shouldUpdate;if(typeof l.shouldUpdate=="function"){var f;return(f=l.shouldUpdate)===null||f===void 0?void 0:f.call(l,h,v,S)}return C.some(function(P){return!We(He(h,P),He(v,P))})},children:function(h){for(var v={},S=0;S<t.length;S++){var f,P=C[S],F=i[S],g=[F].flat(1),m=(f=s.getFieldFormatValueObject)===null||f===void 0?void 0:f.call(s,P);if(m&&Object.keys(m).length)v=Ua({},v,m),He(m,P)&&(v=Rt(v,g,He(m,P)));else{var w;m=(w=h.getFieldValue)===null||w===void 0?void 0:w.call(h,P),typeof m<"u"&&(v=Rt(v,g,m))}}return a==null?void 0:a(v,u(u({},h),s))}}))};Un.displayName="ProFormDependency";var go=["children","onChange","onBlur","ignoreFormItem","valuePropName"],ho=["children","addonAfter","addonBefore","valuePropName","addonWarpStyle","convertValue","help"],po=["valueType","transform","dataFormat","ignoreFormItem","lightProps","children"],vo=me.createContext({}),bo=function(e){var t,r,i=e.children,a=e.onChange,o=e.onBlur;e.ignoreFormItem;var l=e.valuePropName,s=l===void 0?"value":l,d=Pe(e,go),C=(i==null||(t=i.type)===null||t===void 0?void 0:t.displayName)!=="ProFormComponent",b=!me.isValidElement(i),h=st(function(){for(var g,m,w,D,j=arguments.length,V=new Array(j),O=0;O<j;O++)V[O]=arguments[O];a==null||a.apply(void 0,V),!C&&(b||(i==null||(g=i.props)===null||g===void 0||(m=g.onChange)===null||m===void 0||m.call.apply(m,[g].concat(V)),i==null||(w=i.props)===null||w===void 0||(w=w.fieldProps)===null||w===void 0||(D=w.onChange)===null||D===void 0||D.call.apply(D,[w].concat(V))))}),v=st(function(){var g,m,w,D;if(!C&&!b){for(var j=arguments.length,V=new Array(j),O=0;O<j;O++)V[O]=arguments[O];o==null||o.apply(void 0,V),i==null||(g=i.props)===null||g===void 0||(m=g.onBlur)===null||m===void 0||m.call.apply(m,[g].concat(V)),i==null||(w=i.props)===null||w===void 0||(w=w.fieldProps)===null||w===void 0||(D=w.onBlur)===null||D===void 0||D.call.apply(D,[w].concat(V))}}),S=Te(function(){var g;return Mt((i==null||(g=i.props)===null||g===void 0?void 0:g.fieldProps)||{},["onBlur","onChange"])},[Mt((i==null||(r=i.props)===null||r===void 0?void 0:r.fieldProps)||{},["onBlur","onChange"])]),f=e[s],P=p.useMemo(function(){if(!C&&!b)return je(u(u(B({id:d.id},s,f),S),{},{onBlur:v,onChange:h}))},[f,S,v,h,d.id,s]),F=p.useMemo(function(){if(!P&&me.isValidElement(i))return function(){for(var g,m,w=arguments.length,D=new Array(w),j=0;j<w;j++)D[j]=arguments[j];a==null||a.apply(void 0,D),i==null||(g=i.props)===null||g===void 0||(m=g.onChange)===null||m===void 0||m.call.apply(m,[g].concat(D))}},[P,i,a]);return me.isValidElement(i)?me.cloneElement(i,je(u(u(u({},d),{},B({},s,e[s]),i.props),{},{onChange:F,fieldProps:P,onBlur:C&&!b&&o}))):M.jsx(M.Fragment,{children:i})},Sn=function(e){var t=e.children,r=e.addonAfter,i=e.addonBefore,a=e.valuePropName,o=e.addonWarpStyle,l=e.convertValue,s=e.help,d=Pe(e,ho),C=p.useMemo(function(){var b=function(v){var S,f=(S=l==null?void 0:l(v,d.name))!==null&&S!==void 0?S:v;return d.getValueProps?d.getValueProps(f):B({},a||"value",f)};return!l&&!d.getValueProps&&(b=void 0),!r&&!i?M.jsx(Ve.Item,u(u({},d),{},{valuePropName:a,getValueProps:b,children:t})):M.jsx(Ve.Item,u(u(u({},d),{},{help:typeof s!="function"?s:void 0,valuePropName:a,_internalItemRender:{mark:"pro_table_render",render:function(v,S){return M.jsxs(M.Fragment,{children:[M.jsxs("div",{style:u({display:"flex",alignItems:"center",flexWrap:"wrap"},o),children:[i?M.jsx("div",{style:{marginInlineEnd:8},children:i}):null,S.input,r?M.jsx("div",{style:{marginInlineStart:8},children:r}):null]}),typeof s=="function"?s({errors:v.errors,warnings:v.warnings}):S.errorList,S.extra]})}}},d),{},{getValueProps:b,children:t}))},[r,i,t,l==null?void 0:l.toString(),d]);return M.jsx(vo.Provider,{value:{name:d.name,label:d.label},children:C})},yo=function(e){var t,r,i,a,o=(fe===null||fe===void 0||(t=fe.useConfig)===null||t===void 0?void 0:t.call(fe))||{componentSize:"middle"},l=o.componentSize,s=l,d=e.valueType,C=e.transform,b=e.dataFormat,h=e.ignoreFormItem,v=e.lightProps;e.children;var S=Pe(e,po),f=p.useContext(Xe),P=p.useMemo(function(){return e.name===void 0?e.name:f.name!==void 0?[f.name,e.name].flat(1):e.name},[f.name,e.name]),F=me.useContext(Wt),g=F.setFieldValueType,m=F.formItemProps;p.useEffect(function(){!g||!e.name||g([f.listName,e.name].flat(1).filter(function(H){return H!==void 0}),{valueType:d||"text",dateFormat:b,transform:C})},[f.listName,P,b,e.name,g,C,d]);var w=me.isValidElement(e.children)&&_a(d||e.children.props.valueType),D=p.useMemo(function(){return!!(!(v!=null&&v.light)||v!=null&&v.customLightMode||w)},[v==null?void 0:v.customLightMode,w,v==null?void 0:v.light]);if(typeof e.children=="function"){var j;return p.createElement(Sn,u(u({},S),{},{name:P,key:S.proFormFieldKey||((j=S.name)===null||j===void 0?void 0:j.toString())}),e.children)}var V=M.jsx(bo,{valuePropName:e.valuePropName,children:e.children},S.proFormFieldKey||((r=S.name)===null||r===void 0?void 0:r.toString())),O=D?V:p.createElement(Po,u(u({},v),{},{key:S.proFormFieldKey||((i=S.name)===null||i===void 0?void 0:i.toString()),size:s}),V);return h?M.jsx(M.Fragment,{children:O}):M.jsx(Sn,u(u(u({},m),S),{},{name:P,isListField:f.name!==void 0,children:O}),S.proFormFieldKey||((a=S.name)===null||a===void 0?void 0:a.toString()))},xo=function(e){return B(B({},"".concat(e.componentCls,"-collapse-label"),{paddingInline:1,paddingBlock:1}),"".concat(e.componentCls,"-container"),B({},"".concat(e.antCls,"-form-item"),{marginBlockEnd:0}))};function So(n){return Je("LightWrapper",function(e){var t=u(u({},e),{},{componentCls:".".concat(n)});return[xo(t)]})}var Co=["label","size","disabled","onChange","className","style","children","valuePropName","placeholder","labelFormatter","bordered","footerRender","allowClear","otherFieldProps","valueType","placement"],Po=function(e){var t=e.label,r=e.size,i=e.disabled,a=e.onChange,o=e.className,l=e.style,s=e.children,d=e.valuePropName,C=e.placeholder,b=e.labelFormatter,h=e.bordered,v=e.footerRender,S=e.allowClear,f=e.otherFieldProps,P=e.valueType,F=e.placement,g=Pe(e,Co),m=p.useContext(fe.ConfigContext),w=m.getPrefixCls,D=w("pro-field-light-wrapper"),j=So(D),V=j.wrapSSR,O=j.hashId,H=p.useState(e[d]),W=Ee(H,2),U=W[0],X=W[1],G=ar(!1),z=Ee(G,2),E=z[0],c=z[1],y=function(){for(var x,R=arguments.length,k=new Array(R),$=0;$<R;$++)k[$]=arguments[$];f==null||(x=f.onChange)===null||x===void 0||x.call.apply(x,[f].concat(k)),a==null||a.apply(void 0,k)},T=e[d],I=p.useMemo(function(){var L;return T&&(P!=null&&(L=P.toLowerCase())!==null&&L!==void 0&&L.endsWith("range")&&P!=="digitRange"&&!b?za(T,Vn[P]||"YYYY-MM-DD"):Array.isArray(T)?T.map(function(x){return we(x)==="object"&&x.label&&x.value?x.label:x}):T)},[T,P,b]);return V(M.jsx(La,{disabled:i,open:E,onOpenChange:c,placement:F,label:M.jsx(Pa,{ellipsis:!0,size:r,onClear:function(){y==null||y(),X(null)},bordered:h,style:l,className:o,label:t,placeholder:C,value:I,disabled:i,formatter:b,allowClear:S}),footer:{onClear:function(){return X(null)},onConfirm:function(){y==null||y(U),c(!1)}},footerRender:v,children:M.jsx("div",{className:xe("".concat(D,"-container"),O,o),style:l,children:me.cloneElement(s,u(u({},g),{},B(B({},d,U),"onChange",function(x){X(x!=null&&x.target?x.target.value:x)}),s.props))})}))};export{Ia as A,Do as B,Ha as C,Mo as D,_n as E,Wt as F,eo as G,La as H,Ue as I,ko as J,yn as K,Fe as L,yo as P,Yt as a,Ro as b,Lo as c,Ma as d,Pa as e,ct as f,st as g,Te as h,fn as i,Eo as j,ca as k,Ke as l,Ua as m,Wa as n,Aa as o,qe as p,Ne as q,je as r,We as s,Na as t,Je as u,Io as v,Xe as w,Hn as x,Ao as y,Un as z};
