{"version": 3, "sources": ["../../.pnpm/spin-delay@2.0.1_react@18.3.1/node_modules/spin-delay/src/index.ts"], "sourcesContent": ["import { useState, useEffect, useRef } from 'react';\n\ninterface SpinDelayOptions {\n  /**\n   * The delay in milliseconds before the spinner is displayed.\n   * @default 500\n   */\n  delay?: number;\n  /**\n   * The minimum duration in milliseconds the spinner is displayed.\n   * @default 200\n   */\n  minDuration?: number;\n  /**\n   * Whether to enable the spinner on the server side. If true, `delay` will be\n   * ignored, and the spinner will be shown immediately if `loading` is true.\n   * @default true\n   */\n  ssr?: boolean;\n}\n\ntype State = 'IDLE' | 'DELAY' | 'DISPLAY' | 'EXPIRE';\n\nexport const defaultOptions = {\n  delay: 500,\n  minDuration: 200,\n  ssr: true,\n};\n\nfunction useIsSSR() {\n  const [isSSR, setIsSSR] = useState(true);\n\n  useEffect(() => {\n    setIsSSR(false);\n  }, []);\n\n  return isSSR;\n}\n\nexport function useSpinDelay(\n  loading: boolean,\n  options?: SpinDelayOptions,\n): boolean {\n  options = Object.assign({}, defaultOptions, options);\n\n  const isSSR = useIsSSR() && options.ssr;\n  const initialState = isSSR && loading ? 'DISPLAY' : 'IDLE';\n  const [state, setState] = useState<State>(initialState);\n  const timeout = useRef(null);\n\n  useEffect(() => {\n    if (loading && (state === 'IDLE' || isSSR)) {\n      clearTimeout(timeout.current);\n\n      const delay = isSSR ? 0 : options.delay;\n      timeout.current = setTimeout(() => {\n        if (!loading) {\n          return setState('IDLE');\n        }\n\n        timeout.current = setTimeout(() => {\n          setState('EXPIRE');\n        }, options.minDuration);\n\n        setState('DISPLAY');\n      }, delay);\n\n      if (!isSSR) {\n        setState('DELAY');\n      }\n    }\n\n    if (!loading && state !== 'DISPLAY') {\n      clearTimeout(timeout.current);\n      setState('IDLE');\n    }\n  }, [loading, state, options.delay, options.minDuration, isSSR]);\n\n  useEffect(() => {\n    return () => clearTimeout(timeout.current);\n  }, []);\n\n  return state === 'DISPLAY' || state === 'EXPIRE';\n}\n"], "mappings": ";;;;;;;;;;;;;YAuBaA,iBAAiB;QAC5BC,OAAO;QACPC,aAAa;QACbC,KAAK;MAHuB;AAM9B,eAASC,WAAT;AACE,cAAM,CAACC,OAAOC,QAAR,IAAoBC,MAAAA,SAAS,IAAD;AAElCC,cAAAA,UAAU,MAAA;AACRF,mBAAS,KAAD;QACT,GAAE,CAAA,CAFM;AAIT,eAAOD;MACR;eAEeI,aACdC,SACAC,SAAAA;AAEAA,kBAAUC,OAAOC,OAAO,CAAA,GAAIb,gBAAgBW,OAAlC;AAEV,cAAMN,QAAQD,SAAQ,KAAMO,QAAQR;AACpC,cAAMW,eAAeT,SAASK,UAAU,YAAY;AACpD,cAAM,CAACK,OAAOC,QAAR,IAAoBT,MAAAA,SAAgBO,YAAR;AAClC,cAAMG,UAAUC,MAAAA,OAAO,IAAD;AAEtBV,cAAAA,UAAU,MAAA;AACR,cAAIE,YAAYK,UAAU,UAAUV,QAAQ;AAC1Cc,yBAAaF,QAAQG,OAAT;AAEZ,kBAAMnB,QAAQI,QAAQ,IAAIM,QAAQV;AAClCgB,oBAAQG,UAAUC,WAAW,MAAA;AAC3B,kBAAI,CAACX,SAAS;AACZ,uBAAOM,SAAS,MAAD;cAChB;AAEDC,sBAAQG,UAAUC,WAAW,MAAA;AAC3BL,yBAAS,QAAD;cACT,GAAEL,QAAQT,WAFiB;AAI5Bc,uBAAS,SAAD;YACT,GAAEf,KAVyB;AAY5B,gBAAI,CAACI,OAAO;AACVW,uBAAS,OAAD;YACT;UACF;AAED,cAAI,CAACN,WAAWK,UAAU,WAAW;AACnCI,yBAAaF,QAAQG,OAAT;AACZJ,qBAAS,MAAD;UACT;QACF,GAAE,CAACN,SAASK,OAAOJ,QAAQV,OAAOU,QAAQT,aAAaG,KAArD,CA1BM;AA4BTG,cAAAA,UAAU,MAAA;AACR,iBAAO,MAAMW,aAAaF,QAAQG,OAAT;QAC1B,GAAE,CAAA,CAFM;AAIT,eAAOL,UAAU,aAAaA,UAAU;MACzC;;;;;;", "names": ["defaultOptions", "delay", "minDuration", "ssr", "useIsSSR", "isSSR", "setIsSSR", "useState", "useEffect", "useSpinDelay", "loading", "options", "Object", "assign", "initialState", "state", "setState", "timeout", "useRef", "clearTimeout", "current", "setTimeout"]}