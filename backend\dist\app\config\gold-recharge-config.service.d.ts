import { Repository } from 'typeorm';
import { GoldRechargeConfig } from './entities/gold-recharge-config.entity';
import { CreateGoldRechargeConfigDto, UpdateGoldRechargeConfigDto, GoldRechargeConfigQueryDto } from './dto/gold-recharge-config.dto';
export declare class GoldRechargeConfigService {
    private goldRechargeConfigRepository;
    constructor(goldRechargeConfigRepository: Repository<GoldRechargeConfig>);
    create(createDto: CreateGoldRechargeConfigDto, userId: number): Promise<GoldRechargeConfig>;
    findAll(query: GoldRechargeConfigQueryDto): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        tierName: string;
        goldAmount: number;
        price: number;
        activityBonusGold: number;
        activityStartTime: Date;
        activityEndTime: Date;
        sortOrder: number;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }[]>;
    findOne(id: number): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        tierName: string;
        goldAmount: number;
        price: number;
        activityBonusGold: number;
        activityStartTime: Date;
        activityEndTime: Date;
        sortOrder: number;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }>;
    update(id: number, updateDto: UpdateGoldRechargeConfigDto, userId: number): Promise<GoldRechargeConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAllEffective(): Promise<{
        id: number;
        tierName: string;
        goldAmount: number;
        price: number;
        effectiveGoldAmount: number;
        activityBonusGold: number;
        isActivityActive: boolean;
        activityStatusDescription: string;
        sortOrder: number;
    }[]>;
    getActiveActivityConfigs(): Promise<GoldRechargeConfig[]>;
    updateActivityTime(startTime: Date, endTime: Date, userId: number): Promise<{
        message: string;
    }>;
}
