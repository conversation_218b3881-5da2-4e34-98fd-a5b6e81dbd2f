"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RechargeConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const gold_recharge_config_service_1 = require("./gold-recharge-config.service");
const balance_recharge_config_service_1 = require("./balance-recharge-config.service");
const recharge_config_controller_1 = require("./recharge-config.controller");
const gold_recharge_config_entity_1 = require("./entities/gold-recharge-config.entity");
const balance_recharge_config_entity_1 = require("./entities/balance-recharge-config.entity");
const balance_recharge_limit_entity_1 = require("./entities/balance-recharge-limit.entity");
let RechargeConfigModule = class RechargeConfigModule {
};
exports.RechargeConfigModule = RechargeConfigModule;
exports.RechargeConfigModule = RechargeConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                gold_recharge_config_entity_1.GoldRechargeConfig,
                balance_recharge_config_entity_1.BalanceRechargeConfig,
                balance_recharge_limit_entity_1.BalanceRechargeLimit,
            ]),
        ],
        controllers: [recharge_config_controller_1.RechargeConfigController],
        providers: [gold_recharge_config_service_1.GoldRechargeConfigService, balance_recharge_config_service_1.BalanceRechargeConfigService],
        exports: [gold_recharge_config_service_1.GoldRechargeConfigService, balance_recharge_config_service_1.BalanceRechargeConfigService],
    })
], RechargeConfigModule);
//# sourceMappingURL=recharge-config.module.js.map