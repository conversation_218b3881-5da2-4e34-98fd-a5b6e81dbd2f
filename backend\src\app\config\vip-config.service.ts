import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { VipConfig } from './entities/vip-config.entity';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction } from '../entities/cash-transaction.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';
import { CreateVipConfigDto } from './dto/create-vip-config.dto';
import { UpdateVipConfigDto } from './dto/update-vip-config.dto';
import { VipConfigQueryDto } from './dto/vip-config-query.dto';

@Injectable()
export class VipConfigService {
  constructor(
    @InjectRepository(VipConfig)
    private vipConfigRepository: Repository<VipConfig>,
    @InjectRepository(AppUser)
    private appUserRepository: Repository<AppUser>,
    @InjectRepository(CashTransaction)
    private cashTransactionRepository: Repository<CashTransaction>,
    @InjectRepository(GoldTransaction)
    private goldTransactionRepository: Repository<GoldTransaction>,
    private dataSource: DataSource,
  ) {}

  async create(createVipConfigDto: CreateVipConfigDto, userId: number) {
    // 检查VIP等级是否已存在
    const existingConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: createVipConfigDto.vipLevel },
    });
    if (existingConfig) {
      throw new ConflictException(`VIP等级 ${createVipConfigDto.vipLevel} 已存在`);
    }

    // 验证积分递增规则
    await this.validatePointsIncrement(createVipConfigDto.vipLevel, createVipConfigDto.requiredPoints);

    const vipConfig = this.vipConfigRepository.create({
      ...createVipConfigDto,
      createdBy: userId,
      updatedBy: userId,
    });

    return await this.vipConfigRepository.save(vipConfig);
  }

  async findAll(query: VipConfigQueryDto) {
    const { page = 1, pageSize = 10, vipLevel, status, levelName } = query;
    const queryBuilder = this.vipConfigRepository
      .createQueryBuilder('config')
      .leftJoinAndSelect('config.creator', 'creator')
      .leftJoinAndSelect('config.updater', 'updater');

    // 添加筛选条件
    if (vipLevel !== undefined) {
      queryBuilder.andWhere('config.vipLevel = :vipLevel', { vipLevel });
    }
    if (status !== undefined) {
      queryBuilder.andWhere('config.status = :status', { status });
    }
    if (levelName) {
      queryBuilder.andWhere('config.levelName LIKE :levelName', { levelName: `%${levelName}%` });
    }

    // 排序
    queryBuilder.orderBy('config.vipLevel', 'ASC');

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    const [list, total] = await queryBuilder.getManyAndCount();

    return {
      list: list.map(config => ({
        ...config,
        creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
        updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async findOne(id: number) {
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });

    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }

    return {
      ...vipConfig,
      creator: vipConfig.creator ? { id: vipConfig.creator.id, username: vipConfig.creator.username } : null,
      updater: vipConfig.updater ? { id: vipConfig.updater.id, username: vipConfig.updater.username } : null,
    };
  }

  async update(id: number, updateVipConfigDto: UpdateVipConfigDto, userId: number) {
    const vipConfig = await this.vipConfigRepository.findOne({ where: { id } });
    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }

    // 如果更新VIP等级，检查是否冲突
    if (updateVipConfigDto.vipLevel !== undefined && updateVipConfigDto.vipLevel !== vipConfig.vipLevel) {
      const existingConfig = await this.vipConfigRepository.findOne({
        where: { vipLevel: updateVipConfigDto.vipLevel },
      });
      if (existingConfig) {
        throw new ConflictException(`VIP等级 ${updateVipConfigDto.vipLevel} 已存在`);
      }
    }

    // 验证积分递增规则
    if (updateVipConfigDto.requiredPoints !== undefined || updateVipConfigDto.vipLevel !== undefined) {
      const newLevel = updateVipConfigDto.vipLevel ?? vipConfig.vipLevel;
      const newPoints = updateVipConfigDto.requiredPoints ?? vipConfig.requiredPoints;
      await this.validatePointsIncrement(newLevel, newPoints, id);
    }

    Object.assign(vipConfig, updateVipConfigDto, { updatedBy: userId });
    const savedConfig = await this.vipConfigRepository.save(vipConfig);
    return savedConfig;
  }

  async remove(id: number) {
    const vipConfig = await this.vipConfigRepository.findOne({ where: { id } });
    if (!vipConfig) {
      throw new NotFoundException('VIP配置不存在');
    }

    // 检查是否有用户使用此VIP等级
    const userCount = await this.appUserRepository.count({
      where: { vipLevel: vipConfig.vipLevel },
    });

    if (userCount > 0) {
      throw new ConflictException(`该VIP等级已被 ${userCount} 个用户使用，无法删除`);
    }

    await this.vipConfigRepository.remove(vipConfig);
    return { message: '删除成功' };
  }

  // 验证积分递增规则
  private async validatePointsIncrement(vipLevel: number, requiredPoints: number, excludeId?: number) {
    if (vipLevel === 0) return; // 等级0不需要验证

    const queryBuilder = this.vipConfigRepository.createQueryBuilder('config');
    if (excludeId) {
      queryBuilder.where('config.id != :excludeId', { excludeId });
    }

    // 检查前一等级
    const prevConfig = await queryBuilder
      .andWhere('config.vipLevel = :prevLevel', { prevLevel: vipLevel - 1 })
      .andWhere('config.status = 1')
      .getOne();

    if (prevConfig && requiredPoints <= prevConfig.requiredPoints) {
      throw new BadRequestException(
        `VIP等级${vipLevel}的所需积分(${requiredPoints})必须大于前一等级的积分(${prevConfig.requiredPoints})`
      );
    }

    // 检查后一等级
    const nextConfig = await queryBuilder
      .andWhere('config.vipLevel = :nextLevel', { nextLevel: vipLevel + 1 })
      .andWhere('config.status = 1')
      .getOne();

    if (nextConfig && requiredPoints >= nextConfig.requiredPoints) {
      throw new BadRequestException(
        `VIP等级${vipLevel}的所需积分(${requiredPoints})必须小于下一等级的积分(${nextConfig.requiredPoints})`
      );
    }
  }

  // 计算用户积分
  async calculateUserPoints(userId: number): Promise<{ totalPoints: number; breakdown: any }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 获取当前用户的VIP等级配置
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });

    if (!vipConfig) {
      throw new NotFoundException('用户VIP等级配置不存在');
    }

    // 计算累计消耗现金（支出交易）
    const totalCashSpent = await this.cashTransactionRepository
      .createQueryBuilder('transaction')
      .select('COALESCE(SUM(transaction.amount), 0)', 'total')
      .where('transaction.userId = :userId', { userId })
      .andWhere('transaction.status = 2') // 2表示支出
      .getRawOne();

    // 计算累计消耗金币（支出交易）
    const totalGoldSpent = await this.goldTransactionRepository
      .createQueryBuilder('transaction')
      .select('COALESCE(SUM(transaction.amount), 0)', 'total')
      .where('transaction.userId = :userId', { userId })
      .andWhere('transaction.status = 2') // 2表示支出
      .getRawOne();

    // 计算各部分积分
    const balancePoints = user.withdrawableBalance * vipConfig.balanceRatio;
    const cashPoints = parseFloat(totalCashSpent.total) * vipConfig.cashRatio;
    const goldPoints = parseFloat(totalGoldSpent.total) * vipConfig.goldRatio;
    const totalPoints = Math.floor(balancePoints + cashPoints + goldPoints);

    return {
      totalPoints,
      breakdown: {
        withdrawableBalance: user.withdrawableBalance,
        balanceRatio: vipConfig.balanceRatio,
        balancePoints: Math.floor(balancePoints),
        totalCashSpent: parseFloat(totalCashSpent.total),
        cashRatio: vipConfig.cashRatio,
        cashPoints: Math.floor(cashPoints),
        totalGoldSpent: parseFloat(totalGoldSpent.total),
        goldRatio: vipConfig.goldRatio,
        goldPoints: Math.floor(goldPoints),
        dailyGoldReward: vipConfig.dailyGoldReward,
      },
    };
  }

  // 更新用户VIP等级
  async updateUserVipLevel(userId: number): Promise<{ oldLevel: number; newLevel: number; points: number }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const { totalPoints } = await this.calculateUserPoints(userId);
    const oldLevel = user.vipLevel;

    // 获取所有启用的VIP配置，按等级降序排列
    const vipConfigs = await this.vipConfigRepository.find({
      where: { status: 1 },
      order: { vipLevel: 'DESC' },
    });

    // 找到用户应该达到的最高等级
    let newLevel = 0;
    for (const config of vipConfigs) {
      if (totalPoints >= config.requiredPoints) {
        newLevel = config.vipLevel;
        break;
      }
    }

    // 更新用户VIP等级和经验值
    if (newLevel !== oldLevel) {
      await this.appUserRepository.update(userId, {
        vipLevel: newLevel,
        vipExp: totalPoints,
      });
    } else {
      // 即使等级没变，也要更新经验值
      await this.appUserRepository.update(userId, {
        vipExp: totalPoints,
      });
    }

    return { oldLevel, newLevel, points: totalPoints };
  }

  // 批量重新计算所有用户VIP等级
  async recalculateAllUserVipLevels(): Promise<{ processed: number; upgraded: number }> {
    const users = await this.appUserRepository.find();
    let processed = 0;
    let upgraded = 0;

    for (const user of users) {
      try {
        const result = await this.updateUserVipLevel(user.id);
        processed++;
        if (result.oldLevel !== result.newLevel) {
          upgraded++;
        }
      } catch (error) {
        console.error(`更新用户 ${user.id} VIP等级失败:`, error.message);
      }
    }

    return { processed, upgraded };
  }

  // 获取所有启用的VIP等级配置（用于前端选择）
  async getActiveVipLevels() {
    return await this.vipConfigRepository.find({
      where: { status: 1 },
      order: { vipLevel: 'ASC' },
      select: ['id', 'vipLevel', 'levelName', 'requiredPoints'],
    });
  }
}
