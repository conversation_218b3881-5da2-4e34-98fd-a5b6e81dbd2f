import { NestFactory } from '@nestjs/core';
import { ValidationPipe, BadRequestException } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { TransformInterceptor } from './common/interceptors/transform.interceptor';
import { tap } from 'rxjs/operators';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 安全中间件
  app.use(helmet());
  app.use(compression());

  // 添加全局请求拦截器
  app.useGlobalInterceptors({
    intercept(context, next) {
      const request = context.switchToHttp().getRequest();
      if (request.url.includes('/channels')) {
        console.log('[GLOBAL_INTERCEPTOR] ===== 请求开始 =====');
        console.log('[GLOBAL_INTERCEPTOR] URL:', request.url);
        console.log('[GLOBAL_INTERCEPTOR] 方法:', request.method);
        console.log('[GLOBAL_INTERCEPTOR] 查询参数:', request.query);
        console.log('[GLOBAL_INTERCEPTOR] 请求体:', request.body);
        console.log('[GLOBAL_INTERCEPTOR] Headers:', {
          'content-type': request.headers['content-type'],
          'authorization': request.headers['authorization'] ? 'Bearer ***' : 'None'
        });
      }

      return next.handle().pipe(
        tap({
          next: (data) => {
            if (request.url.includes('/channels')) {
              console.log('[GLOBAL_INTERCEPTOR] 响应成功:', typeof data === 'object' ? JSON.stringify(data).substring(0, 200) + '...' : data);
            }
          },
          error: (error) => {
            if (request.url.includes('/channels')) {
              console.log('[GLOBAL_INTERCEPTOR] 响应错误:', error.message);
              console.log('[GLOBAL_INTERCEPTOR] 错误堆栈:', error.stack);
            }
          }
        })
      );
    }
  });

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors) => {
        console.log('[VALIDATION_PIPE] ===== 验证失败 =====');
        console.log('[VALIDATION_PIPE] 错误详情:', JSON.stringify(errors, null, 2));

        // 获取当前请求的上下文信息
        const firstError = errors[0];
        const firstConstraint = Object.values(firstError.constraints || {})[0];

        console.log('[VALIDATION_PIPE] 第一个错误约束:', firstConstraint);
        console.log('[VALIDATION_PIPE] 错误字段:', firstError.property);
        console.log('[VALIDATION_PIPE] 错误值:', firstError.value);
        console.log('[VALIDATION_PIPE] 错误值类型:', typeof firstError.value);
        console.log('[VALIDATION_PIPE] 错误目标:', firstError.target);
        console.log('[VALIDATION_PIPE] 错误目标构造函数:', firstError.target?.constructor?.name);

        // 如果是余额充值限制相关的错误，提供更多信息
        if (firstConstraint && firstConstraint.includes('numeric string')) {
          console.log('[VALIDATION_PIPE] 🔍 数字字符串验证失败详情:');
          console.log('[VALIDATION_PIPE] - 字段名:', firstError.property);
          console.log('[VALIDATION_PIPE] - 期望类型: numeric string');
          console.log('[VALIDATION_PIPE] - 实际值:', firstError.value);
          console.log('[VALIDATION_PIPE] - 实际类型:', typeof firstError.value);
          console.log('[VALIDATION_PIPE] - 是否为数字:', !isNaN(Number(firstError.value)));
          console.log('[VALIDATION_PIPE] - 转换后的数字:', Number(firstError.value));
        }

        return new BadRequestException(firstConstraint);
      },
    }),
  );

  // 添加请求日志中间件
  app.use((req, res, next) => {
    if (req.url.includes('/channels')) {
      console.log('[REQUEST_MIDDLEWARE] ===== 渠道相关API请求 =====');
      console.log('[REQUEST_MIDDLEWARE] 方法:', req.method);
      console.log('[REQUEST_MIDDLEWARE] URL:', req.url);
      console.log('[REQUEST_MIDDLEWARE] 查询参数:', req.query);
      console.log('[REQUEST_MIDDLEWARE] 查询参数原型:', Object.getPrototypeOf(req.query));
      console.log('[REQUEST_MIDDLEWARE] 查询参数构造函数:', req.query.constructor);
      console.log('[REQUEST_MIDDLEWARE] 查询参数类型:', Object.entries(req.query).map(([key, value]) => ({
        key,
        value,
        type: typeof value
      })));
      console.log('[REQUEST_MIDDLEWARE] 查询参数是否为null原型:', Object.getPrototypeOf(req.query) === null);
    }
    next();
  });

  // 全局过滤器和拦截器
  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalInterceptors(new TransformInterceptor());

  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN'),
    credentials: true,
  });

  // API前缀
  app.setGlobalPrefix(configService.get('APP_PREFIX') || 'api');

  // Swagger配置
  const config = new DocumentBuilder()
    .setTitle('InApp2 Backend API')
    .setDescription('InApp2 后台管理系统API文档 - 包含System管理中台和App业务端两大模块')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('系统认证', 'System模块 - 管理员认证相关接口')
    .addTag('系统用户管理', 'System模块 - 管理员用户管理')
    .addTag('系统角色管理', 'System模块 - 角色管理')
    .addTag('系统权限管理', 'System模块 - 权限管理')
    .addTag('系统菜单管理', 'System模块 - 菜单管理')
    .addTag('系统部门管理', 'System模块 - 部门管理')
    .addTag('系统动态路由', 'System模块 - 动态路由')
    .addTag('应用认证', 'App模块 - 用户认证相关接口')
    .addTag('应用用户管理', 'App模块 - 用户信息管理')
    .addTag('商品管理', 'App模块 - 商品相关接口')
    .addTag('订单管理', 'App模块 - 订单相关接口')
    .build();
  const document = SwaggerModule.createDocument(app as any, config);
  SwaggerModule.setup('docs', app as any, document);

  const port = configService.get('APP_PORT') || 3000;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Swagger docs: http://localhost:${port}/docs`);
}
bootstrap();
