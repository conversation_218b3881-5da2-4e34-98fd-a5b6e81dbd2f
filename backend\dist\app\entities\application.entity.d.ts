import { ApplicationProvider } from './application-provider.entity';
export declare class Application {
    id: number;
    appUuid: string;
    appCode: string;
    name: string;
    providerId: number;
    provider: ApplicationProvider;
    launchCode: string;
    categories: string[];
    platforms: string[];
    orientation: string;
    supportedVirtualCurrencies: string[];
    rtp: number;
    volatility: string;
    maxWinMultiplier: number;
    iconUrl: string;
    posterUrl: string;
    status: string;
    metadata: Record<string, any>;
    tags: string[];
    supplierIdentifier: string;
    features: string[];
    minBet: number;
    maxBet: number;
    hasDemo: boolean;
    hasMobile: boolean;
    hasDesktop: boolean;
    createdAt: Date;
    updatedAt: Date;
}
