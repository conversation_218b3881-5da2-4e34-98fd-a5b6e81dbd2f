import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Transform } from 'class-transformer';
import { SysUser } from '../../../system/entities/sys-user.entity';

@Entity('balance_recharge_limits')
export class BalanceRechargeLimit {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'limit_name',
    type: 'varchar',
    length: 50,
    comment: '限制配置名称'
  })
  limitName: string;

  @Column({
    name: 'min_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    comment: '最低充值金额'
  })
  // @Transform(({ value }) => parseFloat(value))
  minAmount: number;

  @Column({
    name: 'max_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0.00,
    comment: '最高充值金额，0表示不限制'
  })
  // @Transform(({ value }) => parseFloat(value))
  maxAmount: number;

  @Column({ default: 1, comment: '状态：1-启用，0-禁用' })
  status: number;

  @Column({
    name: 'remark',
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: '备注信息'
  })
  remark: string;

  @Column({ name: 'created_by', nullable: true, comment: '创建人ID' })
  createdBy: number;

  @Column({ name: 'updated_by', nullable: true, comment: '最后更新人ID' })
  updatedBy: number;

  @CreateDateColumn({ name: 'create_time' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time' })
  updateTime: Date;

  // 关联创建人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator: SysUser;

  // 关联更新人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: SysUser;

  // 验证充值金额是否在允许范围内
  isAmountValid(amount: number): boolean {
    const minAmount = Number(this.minAmount);
    const maxAmount = Number(this.maxAmount);

    if (amount < minAmount) {
      return false;
    }

    if (maxAmount > 0 && amount > maxAmount) {
      return false;
    }

    return true;
  }

  // 获取充值限制描述
  getLimitDescription(): string {
    const maxAmount = Number(this.maxAmount);
    const minAmount = Number(this.minAmount);

    if (maxAmount > 0) {
      return `充值金额范围：¥${minAmount} - ¥${maxAmount}`;
    } else {
      return `最低充值金额：¥${minAmount}，无上限`;
    }
  }

  // 获取充值金额验证错误信息
  getAmountValidationError(amount: number): string | null {
    if (amount < this.minAmount) {
      return `充值金额不能低于 ¥${this.minAmount}`;
    }
    
    if (this.maxAmount > 0 && amount > this.maxAmount) {
      return `充值金额不能超过 ¥${this.maxAmount}`;
    }
    
    return null;
  }
}
