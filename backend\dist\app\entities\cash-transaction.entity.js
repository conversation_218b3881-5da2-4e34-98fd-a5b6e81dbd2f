"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashTransactionStatusLabels = exports.CashTransactionTypeLabels = exports.CashTransaction = exports.CashTransactionType = exports.CashTransactionStatus = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
var CashTransactionStatus;
(function (CashTransactionStatus) {
    CashTransactionStatus[CashTransactionStatus["INCOME"] = 1] = "INCOME";
    CashTransactionStatus[CashTransactionStatus["EXPENSE"] = 2] = "EXPENSE";
})(CashTransactionStatus || (exports.CashTransactionStatus = CashTransactionStatus = {}));
var CashTransactionType;
(function (CashTransactionType) {
    CashTransactionType[CashTransactionType["BET"] = 1] = "BET";
    CashTransactionType[CashTransactionType["WIN"] = 2] = "WIN";
    CashTransactionType[CashTransactionType["DEPOSIT"] = 3] = "DEPOSIT";
    CashTransactionType[CashTransactionType["WITHDRAW"] = 4] = "WITHDRAW";
    CashTransactionType[CashTransactionType["GOLD_EXCHANGE"] = 5] = "GOLD_EXCHANGE";
    CashTransactionType[CashTransactionType["RECHARGE_EXCHANGE"] = 6] = "RECHARGE_EXCHANGE";
})(CashTransactionType || (exports.CashTransactionType = CashTransactionType = {}));
let CashTransaction = class CashTransaction {
    id;
    userId;
    transactionId;
    amount;
    balanceBefore;
    balanceAfter;
    status;
    transactionType;
    description;
    createdAt;
    user;
};
exports.CashTransaction = CashTransaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], CashTransaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transaction_id' }),
    __metadata("design:type", String)
], CashTransaction.prototype, "transactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_before', type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "balanceBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_after', type: 'decimal', precision: 18, scale: 4 }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "balanceAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', comment: '状态：1-收入，2-支出' }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-充值，4-提取，5-金币兑换，6-充值余额兑换' }),
    __metadata("design:type", Number)
], CashTransaction.prototype, "transactionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 255 }),
    __metadata("design:type", String)
], CashTransaction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], CashTransaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], CashTransaction.prototype, "user", void 0);
exports.CashTransaction = CashTransaction = __decorate([
    (0, typeorm_1.Entity)('cash_transactions')
], CashTransaction);
exports.CashTransactionTypeLabels = {
    [CashTransactionType.BET]: '下注',
    [CashTransactionType.WIN]: '赢金',
    [CashTransactionType.DEPOSIT]: '充值',
    [CashTransactionType.WITHDRAW]: '提取',
    [CashTransactionType.GOLD_EXCHANGE]: '金币兑换',
    [CashTransactionType.RECHARGE_EXCHANGE]: '充值余额兑换',
};
exports.CashTransactionStatusLabels = {
    [CashTransactionStatus.INCOME]: '收入',
    [CashTransactionStatus.EXPENSE]: '支出',
};
//# sourceMappingURL=cash-transaction.entity.js.map