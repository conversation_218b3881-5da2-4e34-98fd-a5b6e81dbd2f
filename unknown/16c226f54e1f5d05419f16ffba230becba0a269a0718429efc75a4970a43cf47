import { Repository } from 'typeorm';
import { BalanceRechargeConfig } from './entities/balance-recharge-config.entity';
import { BalanceRechargeLimit } from './entities/balance-recharge-limit.entity';
import { CreateBalanceRechargeConfigDto, UpdateBalanceRechargeConfigDto, BalanceRechargeConfigQueryDto, UpdateBalanceRechargeLimitDto } from './dto/balance-recharge-config.dto';
export declare class BalanceRechargeConfigService {
    private balanceRechargeConfigRepository;
    private balanceRechargeLimitRepository;
    constructor(balanceRechargeConfigRepository: Repository<BalanceRechargeConfig>, balanceRechargeLimitRepository: Repository<BalanceRechargeLimit>);
    create(createDto: CreateBalanceRechargeConfigDto, userId: number): Promise<BalanceRechargeConfig>;
    findAll(query: BalanceRechargeConfigQueryDto): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        tierName: string;
        rechargeAmount: number;
        activityBonusAmount: number;
        activityStartTime: Date;
        activityEndTime: Date;
        sortOrder: number;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }[]>;
    findOne(id: number): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        tierName: string;
        rechargeAmount: number;
        activityBonusAmount: number;
        activityStartTime: Date;
        activityEndTime: Date;
        sortOrder: number;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }>;
    update(id: number, updateDto: UpdateBalanceRechargeConfigDto, userId: number): Promise<BalanceRechargeConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAllEffective(): Promise<{
        id: number;
        tierName: string;
        rechargeAmount: number;
        effectiveRechargeAmount: number;
        activityBonusAmount: number;
        isActivityActive: boolean;
        activityStatusDescription: string;
        sortOrder: number;
    }[]>;
    getActiveActivityConfigs(): Promise<BalanceRechargeConfig[]>;
    updateActivityTime(startTime: Date, endTime: Date, userId: number): Promise<{
        message: string;
    }>;
    getRechargeLimit(): Promise<{
        id: number;
        limitName: string;
        minAmount: number;
        maxAmount: number;
        status: number;
        remark: string;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
        limitDescription: string;
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
    }>;
    updateRechargeLimit(updateDto: UpdateBalanceRechargeLimitDto, userId: number): Promise<BalanceRechargeLimit>;
    validateRechargeAmount(amount: number): Promise<{
        isValid: boolean;
        error?: string;
    }>;
}
