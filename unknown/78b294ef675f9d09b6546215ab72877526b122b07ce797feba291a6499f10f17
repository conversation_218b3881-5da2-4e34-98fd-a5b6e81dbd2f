"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceRechargeConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const balance_recharge_config_entity_1 = require("./entities/balance-recharge-config.entity");
const balance_recharge_limit_entity_1 = require("./entities/balance-recharge-limit.entity");
let BalanceRechargeConfigService = class BalanceRechargeConfigService {
    balanceRechargeConfigRepository;
    balanceRechargeLimitRepository;
    constructor(balanceRechargeConfigRepository, balanceRechargeLimitRepository) {
        this.balanceRechargeConfigRepository = balanceRechargeConfigRepository;
        this.balanceRechargeLimitRepository = balanceRechargeLimitRepository;
    }
    async create(createDto, userId) {
        if (createDto.activityStartTime && createDto.activityEndTime) {
            const startTime = new Date(createDto.activityStartTime);
            const endTime = new Date(createDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        const config = this.balanceRechargeConfigRepository.create();
        Object.assign(config, createDto, {
            activityStartTime: createDto.activityStartTime ? new Date(createDto.activityStartTime) : null,
            activityEndTime: createDto.activityEndTime ? new Date(createDto.activityEndTime) : null,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.balanceRechargeConfigRepository.save(config);
    }
    async findAll(query) {
        const queryBuilder = this.balanceRechargeConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater');
        if (query.status !== undefined) {
            queryBuilder.andWhere('config.status = :status', { status: query.status });
        }
        queryBuilder.orderBy('config.sortOrder', 'ASC').addOrderBy('config.id', 'ASC');
        const configs = await queryBuilder.getMany();
        return configs.map(config => ({
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        }));
    }
    async findOne(id) {
        const config = await this.balanceRechargeConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!config) {
            throw new common_1.NotFoundException('余额充值配置不存在');
        }
        return {
            ...config,
            creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
            updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
        };
    }
    async update(id, updateDto, userId) {
        const config = await this.balanceRechargeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('余额充值配置不存在');
        }
        if (updateDto.activityStartTime && updateDto.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            const endTime = new Date(updateDto.activityEndTime);
            if (startTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityStartTime && !updateDto.activityEndTime && config.activityEndTime) {
            const startTime = new Date(updateDto.activityStartTime);
            if (startTime >= config.activityEndTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        if (updateDto.activityEndTime && !updateDto.activityStartTime && config.activityStartTime) {
            const endTime = new Date(updateDto.activityEndTime);
            if (config.activityStartTime >= endTime) {
                throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
            }
        }
        Object.assign(config, updateDto, {
            updatedBy: userId,
            activityStartTime: updateDto.activityStartTime ? new Date(updateDto.activityStartTime) : config.activityStartTime,
            activityEndTime: updateDto.activityEndTime ? new Date(updateDto.activityEndTime) : config.activityEndTime,
        });
        const savedConfig = await this.balanceRechargeConfigRepository.save(config);
        return savedConfig;
    }
    async remove(id) {
        const config = await this.balanceRechargeConfigRepository.findOne({ where: { id } });
        if (!config) {
            throw new common_1.NotFoundException('余额充值配置不存在');
        }
        await this.balanceRechargeConfigRepository.remove(config);
        return { message: '删除成功' };
    }
    async findAllEffective() {
        const configs = await this.balanceRechargeConfigRepository.find({
            where: { status: 1 },
            order: { sortOrder: 'ASC', id: 'ASC' },
        });
        return configs.map(config => {
            const activityStatus = config.getActivityStatus();
            return {
                id: config.id,
                tierName: config.tierName,
                rechargeAmount: config.rechargeAmount,
                effectiveRechargeAmount: config.getEffectiveRechargeAmount(),
                activityBonusAmount: config.getActivityBonusAmount(),
                isActivityActive: config.isActivityActive(),
                activityStatusDescription: activityStatus.description,
                sortOrder: config.sortOrder,
            };
        });
    }
    async getActiveActivityConfigs() {
        const allConfigs = await this.balanceRechargeConfigRepository.find({
            where: { status: 1 },
            order: { sortOrder: 'ASC' },
        });
        return allConfigs.filter(config => config.isActivityActive());
    }
    async updateActivityTime(startTime, endTime, userId) {
        if (startTime >= endTime) {
            throw new common_1.BadRequestException('活动开始时间必须早于结束时间');
        }
        await this.balanceRechargeConfigRepository.update({}, {
            activityStartTime: startTime,
            activityEndTime: endTime,
            updatedBy: userId,
        });
        return { message: '批量更新活动时间成功' };
    }
    async getRechargeLimit() {
        console.log('🔍 Service: 开始查询余额充值限制...');
        const limit = await this.balanceRechargeLimitRepository.findOne({
            where: { status: 1 },
            relations: ['creator', 'updater'],
        });
        console.log('📊 Service: 查询结果:', limit);
        if (!limit) {
            console.log('❌ Service: 没有找到充值限制配置');
            throw new common_1.NotFoundException('充值限制配置不存在');
        }
        console.log('🔍 Service: 字段类型检查:');
        console.log('- minAmount:', typeof limit.minAmount, '值:', limit.minAmount);
        console.log('- maxAmount:', typeof limit.maxAmount, '值:', limit.maxAmount);
        try {
            const result = {
                id: limit.id,
                limitName: limit.limitName,
                minAmount: parseFloat(String(limit.minAmount)),
                maxAmount: parseFloat(String(limit.maxAmount)),
                status: limit.status,
                remark: limit.remark,
                createdBy: limit.createdBy,
                updatedBy: limit.updatedBy,
                createTime: limit.createTime,
                updateTime: limit.updateTime,
                limitDescription: `充值金额范围：¥${parseFloat(String(limit.minAmount))} - ¥${parseFloat(String(limit.maxAmount))}`,
                creator: limit.creator ? { id: limit.creator.id, username: limit.creator.username } : null,
                updater: limit.updater ? { id: limit.updater.id, username: limit.updater.username } : null,
            };
            console.log('✅ Service: 最终返回结果:', result);
            return result;
        }
        catch (error) {
            console.error('❌ Service: 处理过程中出错:', error);
            throw error;
        }
    }
    async updateRechargeLimit(updateDto, userId) {
        if (updateDto.maxAmount > 0 && updateDto.maxAmount <= updateDto.minAmount) {
            throw new common_1.BadRequestException('最高充值金额必须大于最低充值金额');
        }
        const limit = await this.balanceRechargeLimitRepository.findOne({
            where: { status: 1 },
        });
        if (!limit) {
            const newLimit = this.balanceRechargeLimitRepository.create({
                ...updateDto,
                createdBy: userId,
                updatedBy: userId,
            });
            return await this.balanceRechargeLimitRepository.save(newLimit);
        }
        else {
            Object.assign(limit, updateDto, { updatedBy: userId });
            return await this.balanceRechargeLimitRepository.save(limit);
        }
    }
    async validateRechargeAmount(amount) {
        const limit = await this.balanceRechargeLimitRepository.findOne({
            where: { status: 1 },
        });
        if (!limit) {
            return { isValid: true };
        }
        if (!limit.isAmountValid(amount)) {
            return {
                isValid: false,
                error: limit.getAmountValidationError(amount) || undefined
            };
        }
        return { isValid: true };
    }
};
exports.BalanceRechargeConfigService = BalanceRechargeConfigService;
exports.BalanceRechargeConfigService = BalanceRechargeConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(balance_recharge_config_entity_1.BalanceRechargeConfig)),
    __param(1, (0, typeorm_1.InjectRepository)(balance_recharge_limit_entity_1.BalanceRechargeLimit)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], BalanceRechargeConfigService);
//# sourceMappingURL=balance-recharge-config.service.js.map