import { SysUser } from '../../../system/entities/sys-user.entity';
import { Application } from '../../entities/application.entity';
import { AppHomeGameCategory } from './app-home-game-category.entity';
export declare class AppHomeCategoryGame {
    id: number;
    homeCategoryId: number;
    applicationId: number;
    sortOrder: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    homeCategory: AppHomeGameCategory;
    application: Application;
    creator: SysUser;
    updater: SysUser;
    isEnabled(): boolean;
    getGameInfo(): {
        id: number;
        name: string;
        iconUrl: string;
        posterUrl: string;
    } | null;
    getGameDetails(): {
        id: number;
        name: string;
        iconUrl: string;
        posterUrl: string;
        categories: string[];
        rtp: number;
        volatility: string;
        status: string;
    } | null;
}
