const { Client } = require('pg');

const client = new Client({
  host: '**************',
  port: 5435,
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
  database: 'inapp2'
});

async function checkTableStructure() {
  try {
    await client.connect();
    console.log('✅ 数据库连接成功');
    
    // 检查cash_transactions表结构
    const cashFields = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'cash_transactions' 
      ORDER BY ordinal_position;
    `);
    
    console.log('\n📊 cash_transactions表字段:');
    cashFields.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // 检查gold_transactions表结构
    const goldFields = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'gold_transactions' 
      ORDER BY ordinal_position;
    `);
    
    console.log('\n🪙 gold_transactions表字段:');
    goldFields.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
    // 检查recharge_transactions表结构
    const rechargeFields = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'recharge_transactions' 
      ORDER BY ordinal_position;
    `);
    
    console.log('\n💳 recharge_transactions表字段:');
    rechargeFields.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (nullable: ${row.is_nullable})`);
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await client.end();
  }
}

checkTableStructure();
