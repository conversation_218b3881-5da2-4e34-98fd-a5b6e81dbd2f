"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMenuService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sys_menu_entity_1 = require("../entities/sys-menu.entity");
let SystemMenuService = class SystemMenuService {
    menuRepository;
    constructor(menuRepository) {
        this.menuRepository = menuRepository;
    }
    async create(createMenuDto) {
        const menu = this.menuRepository.create(createMenuDto);
        return await this.menuRepository.save(menu);
    }
    async findAll() {
        const menus = await this.menuRepository.find({
            where: { status: 1 },
            order: { order: 'ASC', id: 'ASC' },
        });
        return this.buildMenuTree(menus);
    }
    async findAllFlat() {
        const menus = await this.menuRepository.find({
            order: { order: 'ASC', id: 'ASC' },
        });
        return menus.map(menu => ({
            ...menu,
            name: menu.title,
            currentActiveMenu: '',
            iframeLink: '',
            keepAlive: 0,
            externalLink: '',
            hideInMenu: 0,
            ignoreAccess: 0,
        }));
    }
    async findOne(id) {
        const menu = await this.menuRepository.findOne({ where: { id } });
        if (!menu) {
            throw new common_1.NotFoundException('菜单不存在');
        }
        return menu;
    }
    async update(id, updateMenuDto) {
        const menu = await this.findOne(id);
        Object.assign(menu, updateMenuDto);
        return await this.menuRepository.save(menu);
    }
    async remove(id) {
        const menu = await this.findOne(id);
        const childMenus = await this.menuRepository.find({
            where: { parentId: id },
        });
        if (childMenus.length > 0) {
            await this.menuRepository.remove(childMenus);
        }
        await this.menuRepository.remove(menu);
        return { message: '删除成功' };
    }
    buildMenuTree(menus, parentId = null) {
        return menus
            .filter((menu) => menu.parentId === parentId)
            .map((menu) => ({
            ...menu,
            children: this.buildMenuTree(menus, menu.id),
        }));
    }
    async getMenusByRoles(roleCodes, isSuperAdmin = false, userPermissions = []) {
        console.log(`[MENU_SERVICE] 获取菜单请求:`, {
            roleCodes,
            isSuperAdmin,
            userPermissions: userPermissions.length
        });
        const queryBuilder = this.menuRepository
            .createQueryBuilder('menu')
            .where('menu.status = :status', { status: 1 })
            .orderBy('menu.order', 'ASC')
            .addOrderBy('menu.id', 'ASC');
        if (isSuperAdmin) {
            console.log(`[MENU_SERVICE] 超级管理员，返回所有菜单`);
            const menus = await queryBuilder.getMany();
            console.log(`[MENU_SERVICE] 查询到 ${menus.length} 个菜单`);
            return this.buildMenuTree(menus);
        }
        if (userPermissions.length > 0) {
            queryBuilder.andWhere('(menu.permissionCode IS NULL OR menu.permissionCode = :emptyString OR menu.permissionCode IN (:...permissions))', { emptyString: '', permissions: userPermissions });
        }
        else {
            queryBuilder.andWhere('(menu.permissionCode IS NULL OR menu.permissionCode = :emptyString)', { emptyString: '' });
        }
        const menus = await queryBuilder.getMany();
        return this.buildMenuTree(menus);
    }
    async getUserMenus(roleCodes, isSuperAdmin = false, userPermissions = []) {
        const menus = await this.getMenusByRoles(roleCodes, isSuperAdmin, userPermissions);
        if (!isSuperAdmin && userPermissions.length > 0) {
            this.filterMenuPermissions(menus, userPermissions);
        }
        return this.convertToRoutes(menus);
    }
    filterMenuPermissions(menus, userPermissions) {
        menus.forEach(menu => {
            if (menu.buttonPermissions && menu.buttonPermissions.length > 0) {
                menu.buttonPermissions = menu.buttonPermissions.filter((permission) => userPermissions.includes(permission));
            }
            if (menu.children && menu.children.length > 0) {
                this.filterMenuPermissions(menu.children, userPermissions);
            }
        });
    }
    convertToRoutes(menus) {
        return menus.map(menu => {
            const route = {
                path: menu.path,
                handle: {
                    icon: menu.icon,
                    title: menu.meta?.title || menu.title,
                    order: menu.order,
                },
            };
            if (menu.component) {
                route.component = menu.component;
            }
            if (menu.permissionCode) {
                route.handle.permissionCode = menu.permissionCode;
            }
            if (menu.buttonPermissions && menu.buttonPermissions.length > 0) {
                route.handle.permissions = menu.buttonPermissions;
            }
            if (menu.children && menu.children.length > 0) {
                route.children = this.convertToRoutes(menu.children);
            }
            return route;
        });
    }
};
exports.SystemMenuService = SystemMenuService;
exports.SystemMenuService = SystemMenuService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_menu_entity_1.SysMenu)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SystemMenuService);
//# sourceMappingURL=menu.service.js.map