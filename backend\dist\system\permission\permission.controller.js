"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemPermissionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const permission_service_1 = require("./permission.service");
const create_permission_dto_1 = require("./dto/create-permission.dto");
const update_permission_dto_1 = require("./dto/update-permission.dto");
const query_permission_dto_1 = require("./dto/query-permission.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let SystemPermissionController = class SystemPermissionController {
    permissionService;
    constructor(permissionService) {
        this.permissionService = permissionService;
    }
    async create(createPermissionDto) {
        const result = await this.permissionService.create(createPermissionDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(queryPermissionDto) {
        const result = await this.permissionService.findAll(queryPermissionDto);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findAllSimple() {
        const result = await this.permissionService.findAllSimple();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findByType(type) {
        const result = await this.permissionService.findByType(type);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.permissionService.findOne(+id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updatePermissionDto) {
        const result = await this.permissionService.update(+id, updatePermissionDto);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.permissionService.remove(+id);
        return {
            code: 200,
            message: '删除成功',
            result,
        };
    }
};
exports.SystemPermissionController = SystemPermissionController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建权限' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_permission_dto_1.CreatePermissionDto]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('list'),
    (0, swagger_1.ApiOperation)({ summary: '获取权限列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_permission_dto_1.QueryPermissionDto]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('simple'),
    (0, swagger_1.ApiOperation)({ summary: '获取简单权限列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "findAllSimple", null);
__decorate([
    (0, common_1.Get)('type/:type'),
    (0, swagger_1.ApiOperation)({ summary: '根据类型获取权限列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取权限详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_permission_dto_1.UpdatePermissionDto]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除权限' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SystemPermissionController.prototype, "remove", null);
exports.SystemPermissionController = SystemPermissionController = __decorate([
    (0, swagger_1.ApiTags)('系统权限管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('permissions'),
    __metadata("design:paramtypes", [permission_service_1.SystemPermissionService])
], SystemPermissionController);
//# sourceMappingURL=permission.controller.js.map