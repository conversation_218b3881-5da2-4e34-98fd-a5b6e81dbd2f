import{r as e}from"./index-CHjq8S-S.js";function c(){return e.get("config/vip").json()}function a(n,o){return e.patch(`config/vip/${n}`,{json:o}).json()}function s(){return e.post("config/vip/recalculate-all").json()}function f(){return e.get("config/membership-cards").json()}function i(n,o){return e.patch(`config/membership-cards/${n}`,{json:o}).json()}function r(){return e.get("config/recharge/gold").json()}function g(n,o){return e.patch(`config/recharge/gold/${n}`,{json:o}).json()}function h(){return e.get("config/recharge/balance").json()}function p(n,o){return e.patch(`config/recharge/balance/${n}`,{json:o}).json()}function u(){return e.get("config/recharge/balance/limits").json()}function j(n){return e.patch("config/recharge/balance/limits",{json:n}).json()}function l(n){return e.get("config/ads",{searchParams:n}).json()}function d(n){return e.post("config/ads",{json:n}).json()}function m(n,o){return e.patch(`config/ads/${n}`,{json:o}).json()}function C(n){return e.delete(`config/ads/${n}`).json()}function A(n){return e.patch(`config/ads/${n}/toggle-status`).json()}function $(n){return e.get("config/app-home",{searchParams:n}).json()}function b(n){return e.get(`config/app-home/${n}`).json()}function R(n){return e.post("config/app-home",{json:n}).json()}function U(n,o){return e.patch(`config/app-home/${n}`,{json:o}).json()}function H(n){return e.delete(`config/app-home/${n}`).json()}function v(n){return e.patch(`config/app-home/${n}/toggle-status`).json()}export{A as a,C as b,m as c,d,$ as e,l as f,R as g,U as h,b as i,v as j,H as k,f as l,i as m,r as n,h as o,u as p,g as q,p as r,j as s,c as t,s as u,a as v};
