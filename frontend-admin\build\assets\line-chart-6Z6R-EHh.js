import{u as d,j as a}from"./index-CHjq8S-S.js";import{f as l}from"./home-BOsRgn0t.js";import{E as x}from"./index-Dq9acWLp.js";import{a as r}from"./react-BUTTOX-3.js";import{aq as c,aw as o}from"./antd-CXPM1OiB.js";function g(){const{t:e}=d(),[t,i]=r.useState("week"),[n,h]=r.useState([]),u={week:[e("home.monday"),e("home.thursday"),e("home.wednesday"),e("home.thursday"),e("home.friday"),e("home.saturday"),e("home.sunday")]},m={dataZoom:{type:t==="week"?"inside":"slider"},title:{text:"",subtext:""},xAxis:{type:"category",data:u[t]},yAxis:{type:"value"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},series:[{type:"line",data:n}]};return r.useEffect(()=>{t&&l({range:t}).then(({result:s})=>{h(s)})},[t]),a.jsx(c,{title:e("home.sales"),extra:a.jsxs(o.Group,{defaultValue:"week",buttonStyle:"solid",value:t,onChange:s=>i(s.target.value),children:[a.jsx(o.Button,{value:"week",children:e("home.thisWeek")}),a.jsx(o.Button,{value:"month",children:e("home.thisMonth")}),a.jsx(o.Button,{value:"year",children:e("home.thisYear")})]}),children:a.jsx(x,{opts:{height:"auto",width:"auto"},option:m})})}export{g as default};
