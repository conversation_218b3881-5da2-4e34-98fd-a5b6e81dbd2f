"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BalanceRechargeLimit = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
let BalanceRechargeLimit = class BalanceRechargeLimit {
    id;
    minAmount;
    maxAmount;
    status;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    isAmountValid(amount) {
        if (amount < this.minAmount) {
            return false;
        }
        if (this.maxAmount > 0 && amount > this.maxAmount) {
            return false;
        }
        return true;
    }
    getLimitDescription() {
        if (this.maxAmount > 0) {
            return `充值金额范围：¥${this.minAmount} - ¥${this.maxAmount}`;
        }
        else {
            return `最低充值金额：¥${this.minAmount}，无上限`;
        }
    }
    getAmountValidationError(amount) {
        if (amount < this.minAmount) {
            return `充值金额不能低于 ¥${this.minAmount}`;
        }
        if (this.maxAmount > 0 && amount > this.maxAmount) {
            return `充值金额不能超过 ¥${this.maxAmount}`;
        }
        return null;
    }
};
exports.BalanceRechargeLimit = BalanceRechargeLimit;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'min_amount',
        type: 'decimal',
        precision: 10,
        scale: 2,
        comment: '最低充值金额'
    }),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "minAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'max_amount',
        type: 'decimal',
        precision: 10,
        scale: 2,
        default: 0.00,
        comment: '最高充值金额，0表示不限制'
    }),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "maxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], BalanceRechargeLimit.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], BalanceRechargeLimit.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], BalanceRechargeLimit.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], BalanceRechargeLimit.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], BalanceRechargeLimit.prototype, "updater", void 0);
exports.BalanceRechargeLimit = BalanceRechargeLimit = __decorate([
    (0, typeorm_1.Entity)('balance_recharge_limits')
], BalanceRechargeLimit);
//# sourceMappingURL=balance-recharge-limit.entity.js.map