"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppOrder = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
const app_order_item_entity_1 = require("./app-order-item.entity");
let AppOrder = class AppOrder {
    id;
    orderNo;
    userId;
    totalAmount;
    discountAmount;
    shippingFee;
    actualAmount;
    status;
    paymentMethod;
    paymentTime;
    shippingTime;
    deliveryTime;
    receiverName;
    receiverPhone;
    receiverAddress;
    remark;
    user;
    items;
    createTime;
    updateTime;
};
exports.AppOrder = AppOrder;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppOrder.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'order_no', unique: true, length: 50 }),
    __metadata("design:type", String)
], AppOrder.prototype, "orderNo", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id', nullable: true }),
    __metadata("design:type", Number)
], AppOrder.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'total_amount', type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AppOrder.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'discount_amount', type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AppOrder.prototype, "discountAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'shipping_fee', type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AppOrder.prototype, "shippingFee", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'actual_amount', type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AppOrder.prototype, "actualAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-待付款，2-待发货，3-待收货，4-已完成，5-已取消，6-已退款' }),
    __metadata("design:type", Number)
], AppOrder.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'payment_method', nullable: true, length: 20 }),
    __metadata("design:type", String)
], AppOrder.prototype, "paymentMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'payment_time', nullable: true }),
    __metadata("design:type", Date)
], AppOrder.prototype, "paymentTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'shipping_time', nullable: true }),
    __metadata("design:type", Date)
], AppOrder.prototype, "shippingTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'delivery_time', nullable: true }),
    __metadata("design:type", Date)
], AppOrder.prototype, "deliveryTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'receiver_name', nullable: true, length: 50 }),
    __metadata("design:type", String)
], AppOrder.prototype, "receiverName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'receiver_phone', nullable: true, length: 20 }),
    __metadata("design:type", String)
], AppOrder.prototype, "receiverPhone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'receiver_address', nullable: true, length: 300 }),
    __metadata("design:type", String)
], AppOrder.prototype, "receiverAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'text' }),
    __metadata("design:type", String)
], AppOrder.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser, (user) => user.orders),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], AppOrder.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_order_item_entity_1.AppOrderItem, (orderItem) => orderItem.order),
    __metadata("design:type", Array)
], AppOrder.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppOrder.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppOrder.prototype, "updateTime", void 0);
exports.AppOrder = AppOrder = __decorate([
    (0, typeorm_1.Entity)('app_orders')
], AppOrder);
//# sourceMappingURL=app-order.entity.js.map