import{u as w,j as o,F as f,a4 as M}from"./index-CHjq8S-S.js";import{b as k,c as R}from"./index-aswgGRDw.js";import{u as S}from"./index-BJOYrg_r.js";import{a}from"./react-BUTTOX-3.js";import{B as g}from"./index-DDI4OfxQ.js";import{B as I}from"./index-S5qMhsyX.js";import{Detail as P}from"./detail-DQ29vtZ1.js";import{getConstantColumns as $}from"./constants-DshMr_Vi.js";import{aD as z,d as A,bB as E}from"./antd-CXPM1OiB.js";import{a as i}from"./constants-BR8N8nwY.js";import"./Table-DTyH0rpt.js";import"./index-CZVy594C.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";import"./index-CnuJ2TqD.js";import"./index-DVrd-Evt.js";import"./index-DMJlwb4Y.js";import"./index-CreErp7h.js";import"./index-BvyzE_eE.js";function te(){const{t:e}=w(),{hasAccessByCodes:m}=S(),[h,l]=a.useState(!1),[x,c]=a.useState(""),[y,u]=a.useState({}),[C,T]=a.useState([]),d=a.useRef(null),j=async(n,t)=>{var s,p;const r=await R(n);await((s=t==null?void 0:t.reload)==null?void 0:s.call(t)),(p=window.$message)==null||p.success(`${e("common.deleteSuccess")} id = ${r.result}`)},B=[...$(e),{title:e("common.action"),valueType:"option",key:"option",width:120,fixed:"right",render:(n,t,r,s)=>[o.jsx(f,{type:"link",size:"small",disabled:!m(i.update),onClick:async()=>{l(!0),c(e("system.menu.editMenu")),u({...t})},children:e("common.edit")},"editable"),o.jsx(z,{title:e("common.confirmDelete"),onConfirm:()=>j(t.id,s),okText:e("common.confirm"),cancelText:e("common.cancel"),children:o.jsx(f,{type:"link",size:"small",disabled:!m(i.delete),children:e("common.delete")})},"delete")]}],D=()=>{l(!1),u({})},b=()=>{var n;(n=d.current)==null||n.reload()};return o.jsxs(g,{className:"h-full",children:[o.jsx(I,{columns:B,actionRef:d,request:async n=>{const t=await k(n),r=M(t.result.list);return T(t.result.list.filter(s=>Number(s.menuType)===0).map(s=>({...s,name:e(s.name)}))),{...t,data:r,total:t.result.total}},headerTitle:e("common.menu.menu"),toolBarRender:()=>[o.jsx(A,{icon:o.jsx(E,{}),type:"primary",disabled:!m(i.add),onClick:()=>{l(!0),c(e("system.menu.addMenu"))},children:e("common.add")},"add-role")]}),o.jsx(P,{title:x,open:h,flatParentMenus:C,onCloseChange:D,detailData:y,refreshTable:b})]})}export{te as default};
