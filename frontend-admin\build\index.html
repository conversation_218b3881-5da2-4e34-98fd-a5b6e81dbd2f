<!doctype html>
<html lang="en">

<head>
	<meta charset="utf-8" />
	<link rel="icon" href="/react-antd-admin/favicon.ico" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name="theme-color" content="#000000" />
	<meta name="description" content="Web site created using Vite React App" />
	<link rel="apple-touch-icon" href="/react-antd-admin/logo192.png" />
	<!--
			manifest.json provides metadata used when your web app is installed on a
			user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
		-->
	<link rel="manifest" href="/react-antd-admin/manifest.json" />
	<!-- Inject the VITE_GLOB_APP_TITLE variable by Vite, configured in the .env file -->
	<title>React Antd Admin</title>
  <script type="module" crossorigin src="/react-antd-admin/assets/index-CHjq8S-S.js"></script>
  <link rel="modulepreload" crossorigin href="/react-antd-admin/assets/react-BUTTOX-3.js">
  <link rel="modulepreload" crossorigin href="/react-antd-admin/assets/antd-CXPM1OiB.js">
  <link rel="stylesheet" crossorigin href="/react-antd-admin/assets/index-zCIQ4-Q5.css">
</head>

<body>
	<noscript>You need to enable JavaScript to run this app.</noscript>
	<div id="root"></div>
</body>

</html>
