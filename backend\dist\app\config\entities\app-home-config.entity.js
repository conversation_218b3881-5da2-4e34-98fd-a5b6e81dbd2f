"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfig = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
const ad_config_entity_1 = require("./ad-config.entity");
const app_home_recommended_game_entity_1 = require("./app-home-recommended-game.entity");
const app_home_game_category_entity_1 = require("./app-home-game-category.entity");
let AppHomeConfig = class AppHomeConfig {
    id;
    configName;
    description;
    topFloatAdId;
    carouselAdId;
    homeGridAdId;
    splashPopupAdId;
    floatAdId;
    status;
    sortOrder;
    remark;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    creator;
    updater;
    topFloatAd;
    carouselAd;
    homeGridAd;
    splashPopupAd;
    floatAd;
    recommendedGames;
    gameCategories;
    isEnabled() {
        return this.status === 1;
    }
    hasRequiredAds() {
        return !!(this.topFloatAdId && this.carouselAdId);
    }
    getAdCount() {
        let count = 0;
        if (this.topFloatAdId)
            count++;
        if (this.carouselAdId)
            count++;
        if (this.homeGridAdId)
            count++;
        if (this.splashPopupAdId)
            count++;
        if (this.floatAdId)
            count++;
        return count;
    }
    validateConfiguration() {
        const errors = [];
        if (!this.configName?.trim()) {
            errors.push('配置名称不能为空');
        }
        if (!this.topFloatAdId) {
            errors.push('顶部浮动广告为必填项');
        }
        if (!this.carouselAdId) {
            errors.push('轮播广告为必填项');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
};
exports.AppHomeConfig = AppHomeConfig;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'config_name', length: 100, comment: '配置名称' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "configName", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '配置描述' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'top_float_ad_id', nullable: true, comment: '顶部浮动广告ID（必填）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "topFloatAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'carousel_ad_id', nullable: true, comment: '轮播广告ID（必填）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "carouselAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'home_grid_ad_id', nullable: true, comment: '首页九宫格ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "homeGridAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'splash_popup_ad_id', nullable: true, comment: '开屏弹窗广告ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "splashPopupAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'float_ad_id', nullable: true, comment: '浮点广告ID（可选）' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "floatAdId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sort_order', default: 0, comment: '排序，数字越小越靠前' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '备注说明' }),
    __metadata("design:type", String)
], AppHomeConfig.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], AppHomeConfig.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppHomeConfig.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppHomeConfig.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeConfig.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeConfig.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'top_float_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "topFloatAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'carousel_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "carouselAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'home_grid_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "homeGridAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'splash_popup_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "splashPopupAd", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ad_config_entity_1.AdConfig, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'float_ad_id' }),
    __metadata("design:type", ad_config_entity_1.AdConfig)
], AppHomeConfig.prototype, "floatAd", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_home_recommended_game_entity_1.AppHomeRecommendedGame, (recommendedGame) => recommendedGame.homeConfig),
    __metadata("design:type", Array)
], AppHomeConfig.prototype, "recommendedGames", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_home_game_category_entity_1.AppHomeGameCategory, (gameCategory) => gameCategory.homeConfig),
    __metadata("design:type", Array)
], AppHomeConfig.prototype, "gameCategories", void 0);
exports.AppHomeConfig = AppHomeConfig = __decorate([
    (0, typeorm_1.Entity)('app_home_configs')
], AppHomeConfig);
//# sourceMappingURL=app-home-config.entity.js.map