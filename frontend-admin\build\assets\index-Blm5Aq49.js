import{j as e}from"./index-CHjq8S-S.js";import{a as m}from"./react-BUTTOX-3.js";import{u as x,B as ne}from"./BalanceDetailModal-BTmSPC5b.js";import{av as s,aq as B,I as u,p as M,aF as le,Q as I,d as r,f as se,ak as ie,az as re,al as f,z as V,ar as l,an as c,M as v,n as j,aB as oe,aG as de,aH as ce,aI as he,aJ as me,J as xe,aK as ue,s as N}from"./antd-CXPM1OiB.js";import{u as p}from"./index-CV7jXd_p.js";import je from"./InvitationTree-CpHJ2f3a.js";const{RangePicker:pe}=le,{Option:T}=M,o={NORMAL:0,BANNED:1,DEACTIVATED:2},ge={INTERNAL_EMPLOYEE:1},Ne=()=>{var z;const[R]=s.useForm(),[b]=s.useForm(),[d,D]=m.useState({page:1,pageSize:20}),[n,g]=m.useState(null),[U,Y]=m.useState(!1),[F,w]=m.useState(!1),[$,k]=m.useState(!1),[O,E]=m.useState(!1),{data:h,loading:A,run:C}=x(()=>(console.log("🔍 前端发起用户列表请求，参数:",d),p.getUserList(d).then(t=>(console.log("📊 前端收到用户列表响应:",t),t))),{refreshDeps:[d]}),{run:P,loading:H}=x(t=>p.getUserDetail(t),{manual:!0,onSuccess:t=>{g(t),Y(!0)}}),{run:q}=x((t,a)=>p.updateUserStatus(t,a),{manual:!0,onSuccess:()=>{N.success("用户状态更新成功"),C()}}),{run:J,loading:_}=x((t,a)=>p.updateUserTags(t,a),{manual:!0,onSuccess:()=>{N.success("用户标签更新成功"),w(!1),C()}}),{run:K,loading:Q}=x(t=>(console.log("🔍 前端发起邀请关系请求，用户ID:",t),p.getInvitationRelationship(t).then(a=>(console.log("📊 前端收到邀请关系响应:",a),a)).catch(a=>{throw console.error("❌ 邀请关系请求失败:",a),a})),{manual:!0,onSuccess:t=>{console.log("✅ 邀请关系请求成功:",t),k(!0)},onError:t=>{console.error("❌ 邀请关系请求错误:",t),N.error("获取邀请关系失败")}}),G=t=>{const a={page:1,pageSize:d.pageSize,...t};t.dateRange&&t.dateRange.length===2&&(a.startDate=t.dateRange[0].format("YYYY-MM-DD"),a.endDate=t.dateRange[1].format("YYYY-MM-DD")),delete a.dateRange,D(a)},W=()=>{R.resetFields(),D({page:1,pageSize:20})},X=t=>{D({...d,page:t.current,pageSize:t.pageSize})},L=(t,a)=>{const i=a===o.BANNED?"封禁":"解封";v.confirm({title:`确认${i}用户`,content:`确定要${i}用户 "${t.username}" 吗？`,icon:e.jsx(ue,{}),onOk:()=>{q(t.id,{status:a})}})},Z=t=>{g(t),b.setFieldsValue({tags:t.tags||[]}),w(!0)},ee=()=>{b.validateFields().then(t=>{n&&J(n.id,t)})},te=t=>{g(t),K(t.id)},S=t=>{g(t),E(!0)},y=t=>{const i={[o.NORMAL]:{color:"green",text:"正常"},[o.BANNED]:{color:"red",text:"已封禁"},[o.DEACTIVATED]:{color:"gray",text:"已注销"}}[t]||{color:"default",text:"未知"};return e.jsx(xe,{status:i.color,text:i.text})},ae=[{title:"UID",dataIndex:"uid",width:100,fixed:"left"},{title:"用户名",dataIndex:"username",width:120,fixed:"left"},{title:"邮箱",dataIndex:"email",width:180},{title:"手机号",dataIndex:"phone",width:120},{title:"状态",dataIndex:"status",width:100,render:y},{title:"VIP",dataIndex:"vipLevel",width:80,render:t=>e.jsxs(c,{color:"gold",children:["V",t]})},{title:"充值余额",dataIndex:"rechargeBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>S(a),style:{padding:0,height:"auto"},children:i.toFixed(4)})}},{title:"金币余额",dataIndex:"goldBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>S(a),style:{padding:0,height:"auto"},children:i.toLocaleString()})}},{title:"可提现余额",dataIndex:"withdrawableBalance",width:120,render:(t,a)=>{const i=Number(t)||0;return e.jsx(r,{type:"link",size:"small",onClick:()=>S(a),style:{padding:0,height:"auto"},children:i.toFixed(4)})}},{title:"渠道",dataIndex:"channelName",width:120},{title:"广告标识",dataIndex:"adIdentifier",width:120,render:t=>t||"-"},{title:"标签",dataIndex:"tags",width:150,render:t=>e.jsxs(I,{wrap:!0,children:[t==null?void 0:t.slice(0,2).map(a=>e.jsx(c,{children:a},a)),(t==null?void 0:t.length)>2&&e.jsxs(c,{children:["+",t.length-2]})]})},{title:"最后登录",dataIndex:"lastLoginTime",width:160,render:t=>t?f(t).format("YYYY-MM-DD HH:mm"):"从未登录"},{title:"未登天数",dataIndex:"daysNotLoggedIn",width:100,render:t=>t===0?"今天":t===1?"1天":t<=7?e.jsxs(c,{color:"orange",children:[t,"天"]}):t<=30?e.jsxs(c,{color:"red",children:[t,"天"]}):e.jsxs(c,{color:"volcano",children:[t,"天"]})},{title:"注册时间",dataIndex:"createTime",width:160,render:t=>f(t).format("YYYY-MM-DD HH:mm")},{title:"操作",key:"action",width:200,fixed:"right",render:(t,a)=>e.jsxs(I,{size:"small",children:[e.jsx(j,{title:"查看详情",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(oe,{}),onClick:()=>P(a.id)})}),a.status===o.NORMAL?e.jsx(j,{title:"封禁用户",children:e.jsx(r,{type:"link",size:"small",danger:!0,icon:e.jsx(de,{}),onClick:()=>L(a,o.BANNED)})}):e.jsx(j,{title:"解封用户",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(ce,{}),onClick:()=>L(a,o.NORMAL)})}),e.jsx(j,{title:"编辑标签",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(he,{}),onClick:()=>Z(a)})}),e.jsx(j,{title:"邀请关系",children:e.jsx(r,{type:"link",size:"small",icon:e.jsx(me,{}),onClick:()=>te(a)})})]})}];return e.jsxs("div",{className:"p-6",children:[e.jsx(B,{title:"APP用户列表",className:"mb-4",children:e.jsxs(s,{form:R,layout:"inline",onFinish:G,className:"mb-4",children:[e.jsx(s.Item,{name:"id",children:e.jsx(u,{placeholder:"用户ID",type:"number"})}),e.jsx(s.Item,{name:"username",children:e.jsx(u,{placeholder:"用户名"})}),e.jsx(s.Item,{name:"email",children:e.jsx(u,{placeholder:"邮箱"})}),e.jsx(s.Item,{name:"phone",children:e.jsx(u,{placeholder:"手机号"})}),e.jsx(s.Item,{name:"status",children:e.jsxs(M,{placeholder:"用户状态",allowClear:!0,style:{width:120},children:[e.jsx(T,{value:o.NORMAL,children:"正常"}),e.jsx(T,{value:o.BANNED,children:"已封禁"}),e.jsx(T,{value:o.DEACTIVATED,children:"已注销"})]})}),e.jsx(s.Item,{name:"dateRange",children:e.jsx(pe,{placeholder:["开始日期","结束日期"]})}),e.jsx(s.Item,{children:e.jsxs(I,{children:[e.jsx(r,{type:"primary",htmlType:"submit",icon:e.jsx(se,{}),loading:A,children:"搜索"}),e.jsx(r,{icon:e.jsx(ie,{}),onClick:W,children:"重置"})]})})]})}),e.jsx(B,{children:e.jsx(re,{columns:ae,dataSource:(h==null?void 0:h.list)||[],rowKey:"id",loading:A,scroll:{x:2100},pagination:{current:d.page,pageSize:d.pageSize,total:(h==null?void 0:h.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:t=>`共 ${t} 条记录`},onChange:X})}),e.jsx(V,{title:"用户详情",placement:"right",width:600,open:U,onClose:()=>Y(!1),loading:H,children:n&&e.jsxs(l,{column:1,bordered:!0,children:[e.jsx(l.Item,{label:"UID",children:n.uid}),e.jsx(l.Item,{label:"用户名",children:n.username}),e.jsx(l.Item,{label:"邮箱",children:n.email}),e.jsx(l.Item,{label:"手机号",children:n.phone}),e.jsx(l.Item,{label:"状态",children:y(n.status)}),e.jsx(l.Item,{label:"账户类型",children:n.accountType===ge.INTERNAL_EMPLOYEE?"内部员工":"普通用户"}),e.jsxs(l.Item,{label:"VIP等级",children:["V",n.vipLevel]}),e.jsx(l.Item,{label:"充值余额",children:(Number(n.rechargeBalance)||0).toFixed(4)}),e.jsx(l.Item,{label:"金币余额",children:(Number(n.goldBalance)||0).toLocaleString()}),e.jsx(l.Item,{label:"可提现余额",children:(Number(n.withdrawableBalance)||0).toFixed(4)}),e.jsx(l.Item,{label:"渠道",children:n.channelName||"-"}),e.jsx(l.Item,{label:"广告",children:n.adName||"-"}),e.jsx(l.Item,{label:"获取标签",children:n.acquisitionTag||"-"}),e.jsx(l.Item,{label:"用户标签",children:e.jsx(I,{wrap:!0,children:(z=n.tags)==null?void 0:z.map(t=>e.jsx(c,{children:t},t))})}),e.jsx(l.Item,{label:"注册IP",children:n.registerIp}),e.jsx(l.Item,{label:"最后登录时间",children:n.lastLoginTime?f(n.lastLoginTime).format("YYYY-MM-DD HH:mm:ss"):"-"}),e.jsx(l.Item,{label:"注册时间",children:f(n.createTime).format("YYYY-MM-DD HH:mm:ss")})]})}),e.jsx(v,{title:"编辑用户标签",open:F,onOk:ee,onCancel:()=>w(!1),confirmLoading:_,children:e.jsxs(s,{form:b,layout:"vertical",children:[e.jsx(s.Item,{name:"tags",label:"用户标签",help:"输入标签后按回车添加",children:e.jsx(M,{mode:"tags",style:{width:"100%"},placeholder:"请输入标签",tokenSeparators:[","]})}),e.jsx(s.Item,{name:"reason",label:"操作原因",children:e.jsx(u.TextArea,{placeholder:"请输入操作原因",rows:3})})]})}),e.jsx(V,{title:n?`${n.username} 的邀请关系`:"邀请关系",placement:"right",width:1e3,open:$,onClose:()=>k(!1),destroyOnClose:!0,children:n&&e.jsx(je,{userId:n.id,loading:Q})}),n&&e.jsx(ne,{visible:O,onCancel:()=>E(!1),userId:n.id,userName:n.username})]})};export{Ne as default};
