"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfigApiController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_home_config_service_1 = require("../app/config/app-home-config.service");
let AppHomeConfigApiController = class AppHomeConfigApiController {
    appHomeConfigService;
    constructor(appHomeConfigService) {
        this.appHomeConfigService = appHomeConfigService;
    }
    async getAppHomeConfig(id) {
        const result = await this.appHomeConfigService.getAppHomeConfig(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.AppHomeConfigApiController = AppHomeConfigApiController;
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: '获取APP首页配置数据',
        description: '根据配置ID获取APP首页需要的所有数据，包括广告、推荐游戏、分类组等'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配置ID', type: 'number' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在或已禁用' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AppHomeConfigApiController.prototype, "getAppHomeConfig", null);
exports.AppHomeConfigApiController = AppHomeConfigApiController = __decorate([
    (0, swagger_1.ApiTags)('APP首页配置API'),
    (0, common_1.Controller)('app-home-config'),
    __metadata("design:paramtypes", [app_home_config_service_1.AppHomeConfigService])
], AppHomeConfigApiController);
//# sourceMappingURL=app-home-config.controller.js.map