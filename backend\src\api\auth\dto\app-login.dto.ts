import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEmail, IsPhoneNumber } from 'class-validator';

export class AppLoginDto {
  @ApiProperty({ description: '登录方式：username/email/phone', example: 'username' })
  @IsString()
  loginType: 'username' | 'email' | 'phone';

  @ApiProperty({ description: '登录标识（用户名/邮箱/手机号）', example: 'testuser1' })
  @IsString()
  identifier: string;

  @ApiProperty({ description: '密码', example: '123456' })
  @IsString()
  password: string;

  @ApiProperty({ description: '设备ID', required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: '设备信息', required: false })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class AppGoogleLoginDto {
  @ApiProperty({ description: 'Google ID Token' })
  @IsString()
  idToken: string;

  @ApiProperty({ description: '设备ID', required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: '设备信息', required: false })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class AppRegisterDto {
  @ApiProperty({ description: '用户名', example: 'newuser' })
  @IsString()
  username: string;

  @ApiProperty({ description: '邮箱', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: '手机号', example: '13800138888', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: '密码', example: '123456' })
  @IsString()
  password: string;

  @ApiProperty({ description: '昵称', required: false })
  @IsOptional()
  @IsString()
  nickname?: string;

  @ApiProperty({ description: '邀请码', required: false })
  @IsOptional()
  @IsString()
  inviteCode?: string;

  @ApiProperty({ description: '渠道标识', required: false })
  @IsOptional()
  @IsString()
  channelIdentifier?: string;

  @ApiProperty({ description: '广告标识', required: false })
  @IsOptional()
  @IsString()
  adIdentifier?: string;

  @ApiProperty({ description: '推广页面标识', required: false })
  @IsOptional()
  @IsString()
  pageIdentifier?: string;

  @ApiProperty({ description: '设备ID', required: false })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiProperty({ description: '设备信息', required: false })
  @IsOptional()
  @IsString()
  deviceInfo?: string;
}

export class AppLoginResponseDto {
  @ApiProperty({ description: '访问令牌' })
  accessToken: string;

  @ApiProperty({ description: '刷新令牌' })
  refreshToken: string;

  @ApiProperty({ description: '令牌类型', example: 'Bearer' })
  tokenType: string;

  @ApiProperty({ description: '过期时间（秒）' })
  expiresIn: number;

  @ApiProperty({ description: '用户信息' })
  user: {
    id: number;
    uid: number;
    username: string;
    email: string;
    nickname: string;
    avatar: string;
    vipLevel: number;
    rechargeBalance: number;
    goldBalance: number;
  };
}
