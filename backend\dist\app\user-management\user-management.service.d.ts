import { Repository } from 'typeorm';
import { AppUser, MarketingChannel, MarketingAd, PromotionalPage, RiskEvent } from '../entities';
import { QueryAppUserDto, AppUserDetailDto, AppUserListResponseDto } from './dto';
export declare class UserManagementService {
    private readonly userRepository;
    private readonly channelRepository;
    private readonly adRepository;
    private readonly pageRepository;
    private readonly riskEventRepository;
    constructor(userRepository: Repository<AppUser>, channelRepository: Repository<MarketingChannel>, adRepository: Repository<MarketingAd>, pageRepository: Repository<PromotionalPage>, riskEventRepository: Repository<RiskEvent>);
    findUsers(queryDto: QueryAppUserDto): Promise<AppUserListResponseDto>;
    private addSearchConditions;
    findUserById(id: number): Promise<AppUserDetailDto>;
}
