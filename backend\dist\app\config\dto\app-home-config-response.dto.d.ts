import { CategoryTitle } from '../entities/app-home-game-category.entity';
import { ImageItem } from '../entities/ad-config.entity';
export declare class ImageItemResponseDto implements ImageItem {
    imageUrl: string;
    jumpType: number;
    jumpTarget: string;
    title?: string;
    description?: string;
}
export declare class AdInfoResponseDto {
    id: number;
    adIdentifier: string;
    title: string;
    adType: number;
    imageItems: ImageItemResponseDto[];
}
export declare class GameInfoResponseDto {
    id: number;
    name: string;
    iconUrl: string;
    posterUrl: string;
    categories: string[];
    rtp: number;
    volatility: string;
    status: string;
}
export declare class RecommendedGameResponseDto {
    id: number;
    sortOrder: number;
    game: GameInfoResponseDto;
}
export declare class CategoryGameResponseDto {
    id: number;
    sortOrder: number;
    game: GameInfoResponseDto;
}
export declare class GameCategoryResponseDto {
    id: number;
    categoryTitle: CategoryTitle;
    sortOrder: number;
    status: number;
    games: CategoryGameResponseDto[];
}
export declare class AppHomeConfigDetailResponseDto {
    id: number;
    configName: string;
    description: string;
    status: number;
    sortOrder: number;
    remark: string;
    createTime: Date;
    updateTime: Date;
    topFloatAd?: AdInfoResponseDto;
    carouselAd?: AdInfoResponseDto;
    homeGridAd?: AdInfoResponseDto;
    splashPopupAd?: AdInfoResponseDto;
    floatAd?: AdInfoResponseDto;
    recommendedGames: RecommendedGameResponseDto[];
    gameCategories: GameCategoryResponseDto[];
}
export declare class AppHomeConfigListResponseDto {
    id: number;
    configName: string;
    description: string;
    status: number;
    sortOrder: number;
    recommendedGameCount: number;
    categoryCount: number;
    createTime: Date;
    updateTime: Date;
}
export declare class AppHomeConfigPageResponseDto {
    list: AppHomeConfigListResponseDto[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
