"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemPermissionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sys_permission_entity_1 = require("../entities/sys-permission.entity");
let SystemPermissionService = class SystemPermissionService {
    permissionRepository;
    constructor(permissionRepository) {
        this.permissionRepository = permissionRepository;
    }
    async create(createPermissionDto) {
        const { code } = createPermissionDto;
        const existingPermission = await this.permissionRepository.findOne({
            where: { code },
        });
        if (existingPermission) {
            throw new common_1.ConflictException('权限代码已存在');
        }
        const permission = this.permissionRepository.create(createPermissionDto);
        return await this.permissionRepository.save(permission);
    }
    async findAll(queryPermissionDto) {
        const { name, code, type, status, current = 1, pageSize = 10 } = queryPermissionDto;
        const queryBuilder = this.permissionRepository.createQueryBuilder('permission');
        if (name) {
            queryBuilder.andWhere('permission.name LIKE :name', {
                name: `%${name}%`,
            });
        }
        if (code) {
            queryBuilder.andWhere('permission.code LIKE :code', {
                code: `%${code}%`,
            });
        }
        if (type) {
            queryBuilder.andWhere('permission.type = :type', { type });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('permission.status = :status', { status });
        }
        queryBuilder.orderBy('permission.createTime', 'DESC');
        const total = await queryBuilder.getCount();
        const list = await queryBuilder
            .skip((current - 1) * pageSize)
            .take(pageSize)
            .getMany();
        return {
            list,
            total,
            current,
            pageSize,
        };
    }
    async findOne(id) {
        const permission = await this.permissionRepository.findOne({
            where: { id },
        });
        if (!permission) {
            throw new common_1.NotFoundException('权限不存在');
        }
        return permission;
    }
    async update(id, updatePermissionDto) {
        const permission = await this.findOne(id);
        if (updatePermissionDto.code && updatePermissionDto.code !== permission.code) {
            const existingPermission = await this.permissionRepository.findOne({
                where: { code: updatePermissionDto.code },
            });
            if (existingPermission) {
                throw new common_1.ConflictException('权限代码已存在');
            }
        }
        Object.assign(permission, updatePermissionDto);
        return await this.permissionRepository.save(permission);
    }
    async remove(id) {
        const permission = await this.findOne(id);
        await this.permissionRepository.remove(permission);
        return { message: '删除成功' };
    }
    async findAllSimple() {
        return await this.permissionRepository.find({
            where: { status: 1 },
            select: ['id', 'name', 'code', 'type', 'description'],
            order: { type: 'ASC', createTime: 'ASC' },
        });
    }
    async findByType(type) {
        return await this.permissionRepository.find({
            where: { type, status: 1 },
            select: ['id', 'name', 'code', 'type', 'description'],
            order: { createTime: 'ASC' },
        });
    }
};
exports.SystemPermissionService = SystemPermissionService;
exports.SystemPermissionService = SystemPermissionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_permission_entity_1.SysPermission)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SystemPermissionService);
//# sourceMappingURL=permission.service.js.map