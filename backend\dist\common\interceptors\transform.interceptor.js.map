{"version": 3, "file": "transform.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/interceptors/transform.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAKwB;AAExB,8CAAqC;AAU9B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAG/B,SAAS,CACP,OAAyB,EACzB,IAAiB;QAEjB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,CAAC,IAAI,EAAE,EAAE;YAEX,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACvD,OAAO;oBACL,GAAG,IAAI;oBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC;YACJ,CAAC;YAGD,OAAO;gBACL,IAAI,EAAE,GAAG;gBACT,OAAO,EAAE,SAAS;gBAClB,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AA3BY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;GACA,oBAAoB,CA2BhC"}