/**
 * 测试配置系统
 */

// 模拟环境变量
const mockEnv = {
  VITE_ENVIRONMENT: 'supabase',
  VITE_SUPABASE_URL: 'https://ytrftwscazjboxbwnrxp.supabase.co',
  VITE_SUPABASE_ANON_KEY: 'test_anon_key',
  VITE_SUPABASE_S3_ACCESS_KEY_ID: '6d0ebd129ee95173fe4719b9bbb6ac49',
  VITE_SUPABASE_S3_SECRET_ACCESS_KEY: '****************************************************************',
  VITE_SUPABASE_S3_ENDPOINT: 'https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/s3',
  VITE_SUPABASE_S3_REGION: 'ap-southeast-1',
  VITE_SUPABASE_S3_BUCKET: 'inda',
  VITE_SUPABASE_S3_FOLDER: 'ads',
  VITE_MAX_FILE_SIZE: '5242880',
  VITE_ALLOWED_IMAGE_TYPES: 'image/jpeg,image/png,image/gif,image/svg+xml,image/webp',
  VITE_COMPRESSION_MAX_WIDTH: '1920',
  VITE_COMPRESSION_MAX_HEIGHT: '1080',
  VITE_COMPRESSION_QUALITY: '0.8'
};

// 模拟 import.meta.env
global.import = {
  meta: {
    env: {
      DEV: true,
      ...mockEnv
    }
  }
};

// 测试不同环境配置
function testEnvironmentConfigs() {
  console.log('=== 测试环境配置 ===\n');
  
  const environments = ['local', 'supabase', 'production'];
  
  environments.forEach(env => {
    console.log(`--- ${env.toUpperCase()} 环境 ---`);
    
    // 模拟环境切换
    global.import.meta.env.VITE_ENVIRONMENT = env;
    
    // 这里应该导入配置模块，但由于是测试文件，我们手动模拟
    const config = getConfigByEnvironment(env);
    
    console.log(`环境类型: ${config.environment}`);
    console.log(`存储桶: ${config.s3.bucket}`);
    console.log(`文件夹: ${config.s3.folder}`);
    console.log(`最大文件大小: ${(config.upload.maxFileSize / 1024 / 1024).toFixed(1)}MB`);
    console.log(`压缩质量: ${config.upload.compressionOptions.quality}`);
    console.log(`压缩尺寸: ${config.upload.compressionOptions.maxWidth}x${config.upload.compressionOptions.maxHeight}`);
    console.log('');
  });
}

// 模拟配置函数
function getConfigByEnvironment(env) {
  const baseConfig = {
    environment: 'supabase',
    supabaseUrl: 'https://ytrftwscazjboxbwnrxp.supabase.co',
    supabaseAnonKey: 'test_key',
    s3: {
      accessKeyId: '6d0ebd129ee95173fe4719b9bbb6ac49',
      secretAccessKey: '****************************************************************',
      endpoint: 'https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/s3',
      region: 'ap-southeast-1',
      bucket: 'inda',
      folder: 'ads'
    },
    upload: {
      maxFileSize: 5 * 1024 * 1024,
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp'],
      compressionOptions: {
        maxWidth: 1920,
        maxHeight: 1080,
        quality: 0.8
      }
    }
  };
  
  switch (env) {
    case 'local':
      return {
        ...baseConfig,
        environment: 'local',
        s3: {
          ...baseConfig.s3,
          folder: 'local-ads'
        },
        upload: {
          ...baseConfig.upload,
          maxFileSize: 3 * 1024 * 1024,
          compressionOptions: {
            maxWidth: 1280,
            maxHeight: 720,
            quality: 0.7
          }
        }
      };
      
    case 'production':
      return {
        ...baseConfig,
        environment: 'production',
        upload: {
          ...baseConfig.upload,
          maxFileSize: 10 * 1024 * 1024,
          compressionOptions: {
            maxWidth: 2560,
            maxHeight: 1440,
            quality: 0.9
          }
        }
      };
      
    default:
      return baseConfig;
  }
}

// 测试配置验证
function testConfigValidation() {
  console.log('=== 测试配置验证 ===\n');
  
  // 有效配置
  const validConfig = getConfigByEnvironment('supabase');
  const validResult = validateConfig(validConfig);
  console.log('有效配置验证:', validResult.valid ? '✅ 通过' : '❌ 失败');
  if (!validResult.valid) {
    console.log('错误:', validResult.errors);
  }
  
  // 无效配置
  const invalidConfig = {
    ...validConfig,
    supabaseUrl: '',
    s3: {
      ...validConfig.s3,
      accessKeyId: ''
    }
  };
  const invalidResult = validateConfig(invalidConfig);
  console.log('无效配置验证:', invalidResult.valid ? '✅ 通过' : '❌ 失败');
  if (!invalidResult.valid) {
    console.log('预期错误:', invalidResult.errors);
  }
  console.log('');
}

// 模拟验证函数
function validateConfig(config) {
  const errors = [];
  
  if (!config.supabaseUrl) {
    errors.push('Supabase URL is required');
  }
  
  if (!config.s3.accessKeyId) {
    errors.push('S3 Access Key ID is required');
  }
  
  if (!config.s3.secretAccessKey) {
    errors.push('S3 Secret Access Key is required');
  }
  
  if (!config.s3.endpoint) {
    errors.push('S3 Endpoint is required');
  }
  
  if (!config.s3.bucket) {
    errors.push('S3 Bucket is required');
  }
  
  if (config.upload.maxFileSize <= 0) {
    errors.push('Max file size must be greater than 0');
  }
  
  if (config.upload.allowedTypes.length === 0) {
    errors.push('At least one allowed file type is required');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// 测试文件类型验证
function testFileTypeValidation() {
  console.log('=== 测试文件类型验证 ===\n');
  
  const config = getConfigByEnvironment('supabase');
  const allowedTypes = config.upload.allowedTypes;
  
  const testFiles = [
    { name: 'test.jpg', type: 'image/jpeg', size: 1024 * 1024 },
    { name: 'test.png', type: 'image/png', size: 2 * 1024 * 1024 },
    { name: 'test.gif', type: 'image/gif', size: 500 * 1024 },
    { name: 'test.svg', type: 'image/svg+xml', size: 100 * 1024 },
    { name: 'test.webp', type: 'image/webp', size: 800 * 1024 },
    { name: 'test.bmp', type: 'image/bmp', size: 1024 * 1024 }, // 不支持
    { name: 'test.txt', type: 'text/plain', size: 1024 }, // 不支持
    { name: 'large.jpg', type: 'image/jpeg', size: 10 * 1024 * 1024 } // 太大
  ];
  
  testFiles.forEach(file => {
    const typeValid = allowedTypes.includes(file.type);
    const sizeValid = file.size <= config.upload.maxFileSize;
    const valid = typeValid && sizeValid;
    
    console.log(`${file.name}: ${valid ? '✅' : '❌'} ${typeValid ? '' : '(类型不支持)'} ${sizeValid ? '' : '(文件太大)'}`);
  });
  
  console.log('');
}

// 运行所有测试
function runAllTests() {
  console.log('🧪 开始配置系统测试\n');
  
  testEnvironmentConfigs();
  testConfigValidation();
  testFileTypeValidation();
  
  console.log('✅ 配置系统测试完成');
}

// 直接运行测试
runAllTests();
