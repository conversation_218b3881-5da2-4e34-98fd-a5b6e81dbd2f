"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppProduct = void 0;
const typeorm_1 = require("typeorm");
const app_category_entity_1 = require("./app-category.entity");
const app_order_item_entity_1 = require("./app-order-item.entity");
let AppProduct = class AppProduct {
    id;
    name;
    description;
    categoryId;
    price;
    originalPrice;
    stock;
    sales;
    images;
    specifications;
    status;
    isFeatured;
    weight;
    category;
    orderItems;
    createTime;
    updateTime;
};
exports.AppProduct = AppProduct;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppProduct.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200 }),
    __metadata("design:type", String)
], AppProduct.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'text' }),
    __metadata("design:type", String)
], AppProduct.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'category_id', nullable: true }),
    __metadata("design:type", Number)
], AppProduct.prototype, "categoryId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AppProduct.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'original_price', type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], AppProduct.prototype, "originalPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], AppProduct.prototype, "stock", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '销量' }),
    __metadata("design:type", Number)
], AppProduct.prototype, "sales", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'jsonb', comment: '商品图片数组' }),
    __metadata("design:type", Object)
], AppProduct.prototype, "images", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'jsonb', comment: '商品规格' }),
    __metadata("design:type", Object)
], AppProduct.prototype, "specifications", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：0-下架，1-上架，2-草稿' }),
    __metadata("design:type", Number)
], AppProduct.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_featured', default: 0, comment: '是否推荐：1-推荐，0-不推荐' }),
    __metadata("design:type", Number)
], AppProduct.prototype, "isFeatured", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, type: 'decimal', precision: 8, scale: 2, comment: '重量(kg)' }),
    __metadata("design:type", Number)
], AppProduct.prototype, "weight", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_category_entity_1.AppCategory, (category) => category.products),
    (0, typeorm_1.JoinColumn)({ name: 'category_id' }),
    __metadata("design:type", app_category_entity_1.AppCategory)
], AppProduct.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_order_item_entity_1.AppOrderItem, (orderItem) => orderItem.product),
    __metadata("design:type", Array)
], AppProduct.prototype, "orderItems", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppProduct.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppProduct.prototype, "updateTime", void 0);
exports.AppProduct = AppProduct = __decorate([
    (0, typeorm_1.Entity)('app_products')
], AppProduct);
//# sourceMappingURL=app-product.entity.js.map