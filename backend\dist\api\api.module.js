"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiModule = void 0;
const common_1 = require("@nestjs/common");
const app_auth_module_1 = require("./auth/app-auth.module");
const user_management_module_1 = require("../app/user-management/user-management.module");
const transaction_module_1 = require("../app/transaction/transaction.module");
const app_home_config_module_1 = require("../app/config/app-home-config.module");
const app_home_config_controller_1 = require("./app-home-config.controller");
let ApiModule = class ApiModule {
};
exports.ApiModule = ApiModule;
exports.ApiModule = ApiModule = __decorate([
    (0, common_1.Module)({
        imports: [
            app_auth_module_1.AppAuthModule,
            user_management_module_1.UserManagementModule,
            transaction_module_1.TransactionModule,
            app_home_config_module_1.AppHomeConfigModule,
        ],
        controllers: [
            app_home_config_controller_1.AppHomeConfigApiController,
        ],
        exports: [
            app_auth_module_1.AppAuthModule,
            user_management_module_1.UserManagementModule,
            transaction_module_1.TransactionModule,
            app_home_config_module_1.AppHomeConfigModule,
        ],
    })
], ApiModule);
//# sourceMappingURL=api.module.js.map