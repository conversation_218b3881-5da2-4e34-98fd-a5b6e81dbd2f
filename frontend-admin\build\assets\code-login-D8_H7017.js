import{y as oe,z as ie,A as ve,B as D,C as J,D as C,I as je,E as be,f as we,q as Te,j as a,u as H,F as R,b as Ee}from"./index-CHjq8S-S.js";import{g as Ne,a as d,R as Ie,i as Pe,h as Fe,L as te}from"./react-BUTTOX-3.js";import{U as le,P as ce,M as Le}from"./rules-Br-WHaJV.js";import{al as Se,av as P,aZ as X,a_ as _e,a$ as $,I as O,d as V,b0 as se,b1 as U,Q as W,T as ee,b2 as ue,s as ke,ae as $e,q as Oe}from"./antd-CXPM1OiB.js";import{c as Ae}from"./index-BKSqgtRx.js";var Y,ne;function Ce(){return ne||(ne=1,Y={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}),Y}var Me=Ce();const Re=Ne(Me);var De=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function re(e){var n={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=e.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(n.name=t[1],(Re[t[1]]||e.charAt(e.length-2)==="/")&&(n.voidElement=!0),n.name.startsWith("!--"))){var s=e.indexOf("-->");return{type:"comment",comment:s!==-1?e.slice(4,s):""}}for(var o=new RegExp(De),i=null;(i=o.exec(e))!==null;)if(i[0].trim())if(i[1]){var l=i[1].trim(),f=[l,""];l.indexOf("=")>-1&&(f=l.split("=")),n.attrs[f[0]]=f[1],o.lastIndex--}else i[2]&&(n.attrs[i[2]]=i[3].trim().substring(1,i[3].length-1));return n}var Ve=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,qe=/^\s*$/,Be=Object.create(null);function me(e,n){switch(n.type){case"text":return e+n.content;case"tag":return e+="<"+n.name+(n.attrs?function(t){var s=[];for(var o in t)s.push(o+'="'+t[o]+'"');return s.length?" "+s.join(" "):""}(n.attrs):"")+(n.voidElement?"/>":">"),n.voidElement?e:e+n.children.reduce(me,"")+"</"+n.name+">";case"comment":return e+"<!--"+n.comment+"-->"}}var Ge={parse:function(e,n){n||(n={}),n.components||(n.components=Be);var t,s=[],o=[],i=-1,l=!1;if(e.indexOf("<")!==0){var f=e.indexOf("<");s.push({type:"text",content:f===-1?e:e.substring(0,f)})}return e.replace(Ve,function(p,b){if(l){if(p!=="</"+t.name+">")return;l=!1}var g,y=p.charAt(1)!=="/",T=p.startsWith("<!--"),r=b+p.length,m=e.charAt(r);if(T){var v=re(p);return i<0?(s.push(v),s):((g=o[i]).children.push(v),s)}if(y&&(i++,(t=re(p)).type==="tag"&&n.components[t.name]&&(t.type="component",l=!0),t.voidElement||l||!m||m==="<"||t.children.push({type:"text",content:e.slice(r,e.indexOf("<",r))}),i===0&&s.push(t),(g=o[i-1])&&g.children.push(t),o[i]=t),(!y||t.voidElement)&&(i>-1&&(t.voidElement||t.name===p.slice(2,-1))&&(i--,t=i===-1?s:o[i]),!l&&m!=="<"&&m)){g=i===-1?s:o[i].children;var c=e.indexOf("<",r),x=e.slice(r,c===-1?void 0:c);qe.test(x)&&(x=" "),(c>-1&&i+g.length>=0||x!==" ")&&g.push({type:"text",content:x})}}),s},stringify:function(e){return e.reduce(function(n,t){return n+me("",t)},"")}};const Z=(e,n)=>{var s;if(!e)return!1;const t=((s=e.props)==null?void 0:s.children)??e.children;return n?t.length>0:!!t},Q=e=>{var t,s;if(!e)return[];const n=((t=e.props)==null?void 0:t.children)??e.children;return(s=e.props)!=null&&s.i18nIsDynamicList?M(n):n},Ue=e=>Array.isArray(e)&&e.every(d.isValidElement),M=e=>Array.isArray(e)?e:[e],He=(e,n)=>{const t={...n};return t.props=Object.assign(e.props,n.props),t},de=(e,n,t,s)=>{if(!e)return"";let o="";const i=M(e),l=n!=null&&n.transSupportBasicHtmlNodes?n.transKeepBasicHtmlNodesFor??[]:[];return i.forEach((f,p)=>{if(D(f)){o+=`${f}`;return}if(d.isValidElement(f)){const{props:b,type:g}=f,y=Object.keys(b).length,T=l.indexOf(g)>-1,r=b.children;if(!r&&T&&!y){o+=`<${g}/>`;return}if(!r&&(!T||y)||b.i18nIsDynamicList){o+=`<${p}></${p}>`;return}if(T&&y===1&&D(r)){o+=`<${g}>${r}</${g}>`;return}const m=de(r,n,t,s);o+=`<${p}>${m}</${p}>`;return}if(f===null){J(t,"TRANS_NULL_VALUE","Passed in a null value as child",{i18nKey:s});return}if(C(f)){const{format:b,...g}=f,y=Object.keys(g);if(y.length===1){const T=b?`${y[0]}, ${b}`:y[0];o+=`{{${T}}}`;return}J(t,"TRANS_INVALID_OBJ","Invalid child - Object should only have keys {{ value, format }} (format is optional).",{i18nKey:s,child:f});return}J(t,"TRANS_INVALID_VAR","Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.",{i18nKey:s,child:f})}),o},We=(e,n,t,s,o,i)=>{if(n==="")return[];const l=s.transKeepBasicHtmlNodesFor||[],f=n&&new RegExp(l.map(c=>`<${c}`).join("|")).test(n);if(!e&&!f&&!i)return[n];const p={},b=c=>{M(c).forEach(h=>{D(h)||(Z(h)?b(Q(h)):C(h)&&!d.isValidElement(h)&&Object.assign(p,h))})};b(e);const g=Ge.parse(`<0>${n}</0>`),y={...p,...o},T=(c,x,h)=>{var j;const N=Q(c),_=m(N,x.children,h);return Ue(N)&&_.length===0||(j=c.props)!=null&&j.i18nIsDynamicList?N:_},r=(c,x,h,N,_)=>{c.dummy?(c.children=x,h.push(d.cloneElement(c,{key:N},_?void 0:x))):h.push(...d.Children.map([c],j=>{const u={...j.props};return delete u.i18nIsDynamicList,d.createElement(j.type,{...u,key:N,ref:j.ref},_?null:x)}))},m=(c,x,h)=>{const N=M(c);return M(x).reduce((j,u,w)=>{var k,I;const F=((I=(k=u.children)==null?void 0:k[0])==null?void 0:I.content)&&t.services.interpolator.interpolate(u.children[0].content,y,t.language);if(u.type==="tag"){let E=N[parseInt(u.name,10)];h.length===1&&!E&&(E=h[0][u.name]),E||(E={});const L=Object.keys(u.attrs).length!==0?He({props:u.attrs},E):E,A=d.isValidElement(L),q=A&&Z(u,!0)&&!u.voidElement,B=f&&C(L)&&L.dummy&&!A,G=C(e)&&Object.hasOwnProperty.call(e,u.name);if(D(L)){const S=t.services.interpolator.interpolate(L,y,t.language);j.push(S)}else if(Z(L)||q){const S=T(L,u,h);r(L,S,j,w)}else if(B){const S=m(N,u.children,h);r(L,S,j,w)}else if(Number.isNaN(parseFloat(u.name)))if(G){const S=T(L,u,h);r(L,S,j,w,u.voidElement)}else if(s.transSupportBasicHtmlNodes&&l.indexOf(u.name)>-1)if(u.voidElement)j.push(d.createElement(u.name,{key:`${u.name}-${w}`}));else{const S=m(N,u.children,h);j.push(d.createElement(u.name,{key:`${u.name}-${w}`},S))}else if(u.voidElement)j.push(`<${u.name} />`);else{const S=m(N,u.children,h);j.push(`<${u.name}>${S}</${u.name}>`)}else if(C(L)&&!A){const S=u.children[0]?F:null;S&&j.push(S)}else r(L,F,j,w,u.children.length!==1||!F)}else if(u.type==="text"){const E=s.transWrapTextNodes,L=i?s.unescape(t.services.interpolator.interpolate(u.content,y,t.language)):t.services.interpolator.interpolate(u.content,y,t.language);E?j.push(d.createElement(E,{key:`${u.name}-${w}`},L)):j.push(L)}return j},[])},v=m([{dummy:!0,children:e||[]}],g,M(e||[]));return Q(v[0])},fe=(e,n,t)=>{const s=e.key||n,o=d.cloneElement(e,{key:s});if(!o.props||!o.props.children||t.indexOf(`${n}/>`)<0&&t.indexOf(`${n} />`)<0)return o;function i(){return d.createElement(d.Fragment,null,o)}return d.createElement(i,{key:s})},ze=(e,n)=>e.map((t,s)=>fe(t,s,n)),Je=(e,n)=>{const t={};return Object.keys(e).forEach(s=>{Object.assign(t,{[s]:fe(e[s],s,n)})}),t},Xe=(e,n,t,s)=>e?Array.isArray(e)?ze(e,n):C(e)?Je(e,n):(oe(t,"TRANS_INVALID_COMPONENTS",'<Trans /> "components" prop expects an object or array',{i18nKey:s}),null):null;function Ye({children:e,count:n,parent:t,i18nKey:s,context:o,tOptions:i={},values:l,defaults:f,components:p,ns:b,i18n:g,t:y,shouldUnescape:T,...r}){var L,A,q,B,G,S;const m=g||ie();if(!m)return oe(m,"NO_I18NEXT_INSTANCE","Trans: You need to pass in an i18next instance using i18nextReactModule",{i18nKey:s}),e;const v=y||m.t.bind(m)||(xe=>xe),c={...ve(),...(L=m.options)==null?void 0:L.react};let x=b||v.ns||((A=m.options)==null?void 0:A.defaultNS);x=D(x)?[x]:x||["translation"];const h=de(e,c,m,s),N=f||h||c.transEmptyNodeValue||s,{hashTransKey:_}=c,j=s||(_?_(h||N):h||N);(B=(q=m.options)==null?void 0:q.interpolation)!=null&&B.defaultVariables&&(l=l&&Object.keys(l).length>0?{...l,...m.options.interpolation.defaultVariables}:{...m.options.interpolation.defaultVariables});const u=l||n!==void 0&&!((S=(G=m.options)==null?void 0:G.interpolation)!=null&&S.alwaysFormat)||!e?i.interpolation:{interpolation:{...i.interpolation,prefix:"#$?",suffix:"?$#"}},w={...i,context:o||i.context,count:n,...l,...u,defaultValue:N,ns:x},F=j?v(j,w):N,k=Xe(p,F,m,s),I=We(k||e,F,m,c,w,T),E=t??c.defaultTransParent;return E?d.createElement(E,r,I):I}function Ze({children:e,count:n,parent:t,i18nKey:s,context:o,tOptions:i={},values:l,defaults:f,components:p,ns:b,i18n:g,t:y,shouldUnescape:T,...r}){var h;const{i18n:m,defaultNS:v}=d.useContext(je)||{},c=g||m||ie(),x=y||(c==null?void 0:c.t.bind(c));return Ye({children:e,count:n,parent:t,i18nKey:s,context:o,tOptions:i,values:l,defaults:f,components:p,ns:b||(x==null?void 0:x.ns)||v||((h=c==null?void 0:c.options)==null?void 0:h.defaultNS),i18n:c,t:y,shouldUnescape:T,...r})}var K=function(e){if(!e)return 0;var n=Se(e).valueOf()-Date.now();return n<0?0:n},Qe=function(e){return{days:Math.floor(e/864e5),hours:Math.floor(e/36e5)%24,minutes:Math.floor(e/6e4)%60,seconds:Math.floor(e/1e3)%60,milliseconds:Math.floor(e)%1e3}},Ke=function(e){e===void 0&&(e={});var n=e||{},t=n.leftTime,s=n.targetDate,o=n.interval,i=o===void 0?1e3:o,l=n.onEnd,f=d.useMemo(function(){return be(t)&&t>0?Date.now()+t:void 0},[t]),p="leftTime"in e?f:s,b=we(d.useState(function(){return K(p)}),2),g=b[0],y=b[1],T=Te(l);d.useEffect(function(){if(!p){y(0);return}y(K(p));var m=setInterval(function(){var v,c=K(p);y(c),c===0&&(clearInterval(m),(v=T.current)===null||v===void 0||v.call(T))},i);return function(){return clearInterval(m)}},[p,i]);var r=d.useMemo(function(){return Qe(g)},[g]);return[g,r]},et=["rules","name","phoneName","fieldProps","onTiming","captchaTextRender","captchaProps"],tt=Ie.forwardRef(function(e,n){var t=P.useFormInstance(),s=d.useState(e.countDown||60),o=X(s,2),i=o[0],l=o[1],f=d.useState(!1),p=X(f,2),b=p[0],g=p[1],y=d.useState(),T=X(y,2),r=T[0],m=T[1];e.rules,e.name;var v=e.phoneName,c=e.fieldProps,x=e.onTiming,h=e.captchaTextRender,N=h===void 0?function(w,F){return w?"".concat(F," 秒后重新获取"):"获取验证码"}:h,_=e.captchaProps,j=_e(e,et),u=function(){var w=se(U().mark(function F(k){return U().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return E.prev=0,m(!0),E.next=4,j.onGetCaptcha(k);case 4:m(!1),g(!0),E.next=13;break;case 8:E.prev=8,E.t0=E.catch(0),g(!1),m(!1),console.log(E.t0);case 13:case"end":return E.stop()}},F,null,[[0,8]])}));return function(k){return w.apply(this,arguments)}}();return d.useImperativeHandle(n,function(){return{startTiming:function(){return g(!0)},endTiming:function(){return g(!1)}}}),d.useEffect(function(){var w=0,F=e.countDown;return b&&(w=window.setInterval(function(){l(function(k){return k<=1?(g(!1),clearInterval(w),F||60):k-1})},1e3)),function(){return clearInterval(w)}},[b]),d.useEffect(function(){x&&x(i)},[i,x]),a.jsxs("div",{style:$($({},c==null?void 0:c.style),{},{display:"flex",alignItems:"center"}),ref:n,children:[a.jsx(O,$($({},c),{},{style:$({flex:1,transition:"width .3s",marginRight:8},c==null?void 0:c.style)})),a.jsx(V,$($({style:{display:"block"},disabled:b,loading:r},_),{},{onClick:se(U().mark(function w(){var F;return U().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:if(I.prev=0,!v){I.next=9;break}return I.next=4,t.validateFields([v].flat(1));case 4:return F=t.getFieldValue([v].flat(1)),I.next=7,u(F);case 7:I.next=11;break;case 9:return I.next=11,u("");case 11:I.next=16;break;case 13:I.prev=13,I.t0=I.catch(0),console.log(I.t0);case 16:case"end":return I.stop()}},w,null,[[0,13]])})),children:N(b,i)}))]})}),st=Ae(tt);const{Title:nt}=ee,rt={email:""};function pe(){const[e,n]=d.useState(0),[t]=Ke({targetDate:e,onEnd:()=>{n(0)}}),[s,o]=d.useState(!1),[i]=P.useForm(),{t:l}=H(),{setFormMode:f}=d.useContext(z),p=async()=>{o(!0),n(new Date().getTime()+1e3*30),setTimeout(()=>{o(!1)},1e3)};return a.jsxs(a.Fragment,{children:[a.jsxs(W,{direction:"vertical",children:[a.jsx(nt,{level:3,children:l("authority.forgotPassword")}),a.jsx("p",{className:"text-xs opacity-80",children:l("authority.forgotPasswordSubtitle")})]}),a.jsxs(P,{name:"forgotForm",form:i,layout:"vertical",initialValues:rt,onFinish:p,children:[a.jsx(P.Item,{label:l("authority.email"),name:"email",rules:[{required:!0},{type:"email",message:l("form.email.invalid")}],children:a.jsx(O,{placeholder:l("form.email.required")})}),a.jsx(P.Item,{children:a.jsx(V,{block:!0,type:"primary",htmlType:"submit",loading:s,disabled:t>0,children:t>0?l("authority.retryAfterText",{count:Math.floor(t/1e3)}):l("authority.sendResetLink")})}),a.jsx("div",{className:"text-sm text-center",children:a.jsx(R,{type:"link",icon:a.jsx(ue,{}),className:"px-1",onPointerDown:()=>{f("login")},children:l("common.back")})})]})]})}const pt=Object.freeze(Object.defineProperty({__proto__:null,ForgotPassword:pe},Symbol.toStringTag,{value:"Module"})),at={username:"admin",password:"123456789admin"};function he(){const[e,n]=d.useState(!1),[t]=P.useForm(),{t:s}=H(),[o,i]=ke.useMessage(),[l]=Pe(),f=Fe(),p=Ee(y=>y.login),{setFormMode:b}=d.useContext(z),g=async y=>{n(!0),o==null||o.loading(s("authority.loginInProgress"),0),console.log("[LOGIN] 开始登录请求",y.username),console.log("[LOGIN] 请求URL:","/api/auth/login"),console.log("[LOGIN] 环境变量 VITE_API_BASE_URL:","/api");const T=Date.now();try{const r=await fetch("/api");console.log("[LOGIN] 代理测试 - 状态:",r.status)}catch(r){console.error("[LOGIN] 代理测试失败:",r)}p(y).then(()=>{var v;const r=Date.now();console.log(`[LOGIN] 登录成功 - 总耗时: ${r-T}ms`),o==null||o.destroy(),(v=window.$message)==null||v.success(s("authority.loginSuccess"));const m=l.get("redirect");f(m?`/${m.slice(1)}`:"/home")}).catch(r=>{var c,x,h,N,_,j,u,w,F;const m=Date.now();console.error(`[LOGIN] 登录失败 - 总耗时: ${m-T}ms`,r),o==null||o.destroy();let v=s("authority.loginFailed");console.log("[LOGIN] 错误对象详情:",{error:r,response:r==null?void 0:r.response,errorMessage:(c=r==null?void 0:r.response)==null?void 0:c.errorMessage,data:(x=r==null?void 0:r.response)==null?void 0:x.data,message:r==null?void 0:r.message,name:r==null?void 0:r.name}),(h=r==null?void 0:r.response)!=null&&h.errorMessage?v=r.response.errorMessage:(_=(N=r==null?void 0:r.response)==null?void 0:N.data)!=null&&_.message?v=r.response.data.message:(u=(j=r==null?void 0:r.response)==null?void 0:j.data)!=null&&u.error?v=r.response.data.error:((w=r==null?void 0:r.response)==null?void 0:w.status)===401?v="用户名或密码错误":r!=null&&r.message&&!r.message.includes("Request failed with status code")&&(v=r.message),console.log("[LOGIN] 最终显示错误消息:",v),(F=window.$message)==null||F.error(v)}).finally(()=>{o==null||o.destroy(),setTimeout(()=>{var r;(r=window.$message)==null||r.destroy(),n(!1)},1e3)})};return a.jsxs(a.Fragment,{children:[i,a.jsxs(W,{direction:"vertical",children:[a.jsxs("h2",{className:"text-colorText mb-3 text-3xl font-bold leading-9 tracking-tight lg:text-4xl",children:[s("authority.welcomeBack"),"  👏"]}),a.jsx("p",{className:"lg:text-base text-sm text-colorTextSecondary",children:s("authority.loginDescription")})]}),a.jsxs(P,{name:"passwordLoginForm",form:t,layout:"vertical",initialValues:at,onFinish:g,children:[a.jsx(P.Item,{label:s("authority.username"),name:"username",rules:le(s),children:a.jsx(O,{placeholder:s("form.username.required")})}),a.jsx(P.Item,{label:s("authority.password"),name:"password",rules:ce(s),children:a.jsx(O.Password,{placeholder:s("form.password.required")})}),a.jsxs(P.Item,{children:[a.jsxs("div",{className:"flex justify-between mb-5 -mt-1 text-sm",children:[a.jsx(R,{type:"link",className:"p-0",onPointerDown:()=>{b("codeLogin")},children:s("authority.codeLogin")}),a.jsx(R,{type:"link",className:"p-0",onPointerDown:()=>{b("forgotPassword")},children:s("authority.forgotPassword")})]}),a.jsx(V,{block:!0,type:"primary",htmlType:"submit",loading:e,children:s("authority.login")})]}),a.jsxs("div",{className:"text-sm text-center",children:[s("authority.noAccountYet"),a.jsx(R,{type:"link",className:"px-1",onPointerDown:()=>{b("register")},children:s("authority.goToRegister")})]})]})]})}const ht=Object.freeze(Object.defineProperty({__proto__:null,PasswordLogin:he},Symbol.toStringTag,{value:"Module"})),{Title:ae}=ee,ot={username:"",password:"",confirmPassword:""};function ge(){const[e]=d.useState(!1),[n]=P.useForm(),{t}=H(),{setFormMode:s}=d.useContext(z),o=async()=>{var i;(i=window.$message)==null||i.success("注册成功")};return a.jsxs(a.Fragment,{children:[a.jsxs(W,{direction:"vertical",children:[a.jsx(ae,{level:3,children:"Hello, Welcome to"}),a.jsx(ae,{className:"mt-0",level:5,children:"React Antd Admin"})]}),a.jsxs(P,{name:"registerForm",form:n,layout:"vertical",initialValues:ot,onFinish:o,children:[a.jsx(P.Item,{label:t("authority.username"),name:"username",rules:le(t),children:a.jsx(O,{placeholder:t("form.username.required")})}),a.jsx(P.Item,{label:t("authority.password"),name:"password",rules:ce(t),children:a.jsx(O.Password,{placeholder:t("form.password.required")})}),a.jsx(P.Item,{name:"confirm",label:t("authority.confirmPassword"),dependencies:["password"],hasFeedback:!0,rules:[{required:!0,message:t("form.confirmPassword.required")},({getFieldValue:i})=>({validator(l,f){return!f||i("password")===f?Promise.resolve():Promise.reject(new Error(t("form.confirmPassword.invalid")))}})],children:a.jsx(O.Password,{placeholder:t("form.confirmPassword.required")})}),a.jsx(P.Item,{rules:[()=>({validator(i,l){return l!==!0?Promise.reject(new Error(t("form.agree.required"))):Promise.resolve()}})],name:"termsAgreement",valuePropName:"checked",children:a.jsx($e,{children:a.jsx("div",{className:"flex flex-wrap text-xs",children:a.jsx(Ze,{i18nKey:"authority.agree",components:[a.jsx(te,{to:"/terms-of-service",target:"_blank"},0),a.jsx(te,{to:"/privacy-policy",target:"_blank"},1)]})})})}),a.jsx(P.Item,{children:a.jsx(V,{block:!0,type:"primary",htmlType:"submit",loading:e,children:t("authority.register")})}),a.jsxs("div",{className:"text-sm text-center",children:[t("authority.alreadyHaveAnAccount"),a.jsx(R,{type:"link",className:"px-1",onPointerDown:()=>{s("login")},children:t("authority.goToLogin")})]})]})]})}const gt=Object.freeze(Object.defineProperty({__proto__:null,RegisterPassword:ge},Symbol.toStringTag,{value:"Module"})),yt={login:d.createElement(he),register:d.createElement(ge),forgotPassword:d.createElement(pe),codeLogin:d.createElement(ye)},z=d.createContext({formMode:"login",setFormMode:()=>{}}),{Title:it}=ee,lt={phoneNumber:"",captcha:""};function ye(){const[e,n]=d.useState(!1),[t]=P.useForm(),{t:s}=H(),{setFormMode:o}=d.useContext(z),i=async()=>{n(!0),setTimeout(()=>{var l;n(!1),(l=window.$message)==null||l.success(s("common.success"))},1e3)};return a.jsxs(a.Fragment,{children:[a.jsx(W,{direction:"vertical",children:a.jsx(it,{level:3,children:s("authority.codeLogin")})}),a.jsxs(P,{name:"codeLoginForm",form:t,layout:"vertical",initialValues:lt,onFinish:i,children:[a.jsx(P.Item,{label:s("authority.mobile"),name:"phoneNumber",rules:Le(s),children:a.jsx(Oe,{controls:!1,className:"w-full",placeholder:s("form.mobile.required")})}),a.jsx(st,{label:s("authority.code"),placeholder:s("form.code.required"),captchaTextRender:(l,f)=>l?s("authority.sendText",{second:f}):s("authority.sendCode"),onGetCaptcha:()=>{var l;return(l=window.$message)==null||l.success(s("common.success")),Promise.resolve()},rules:[{required:!0}],phoneName:"phoneNumber",name:"captcha"}),a.jsx(P.Item,{children:a.jsx(V,{block:!0,type:"primary",htmlType:"submit",loading:e,children:s("authority.login")})}),a.jsx("div",{className:"text-sm text-center",children:a.jsx(R,{type:"link",icon:a.jsx(ue,{}),className:"px-1",onPointerDown:()=>{o("login")},children:s("common.back")})})]})]})}const xt=Object.freeze(Object.defineProperty({__proto__:null,CodeLogin:ye},Symbol.toStringTag,{value:"Module"}));export{z as F,yt as a,xt as c,pt as f,ht as p,gt as r};
