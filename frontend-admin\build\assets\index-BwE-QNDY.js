import{j as e}from"./index-CHjq8S-S.js";import{a}from"./react-BUTTOX-3.js";import{B as P}from"./index-DDI4OfxQ.js";import{t as v,u as q,v as N}from"./index-jASoOOIw.js";import{av as r,aq as F,ar as i,az as S,Q as x,d as h,aU as $,ak as A,aM as y,M as L,I as M,q as l,s as t,an as p,aC as E}from"./antd-CXPM1OiB.js";const D=()=>{const[I,d]=a.useState([]),[g,f]=a.useState(!1),[b,c]=a.useState(!1),[j,R]=a.useState(null),[u]=r.useForm(),m=async()=>{f(!0);try{const s=await v();s.code===200?s.result&&s.result.list?d(Array.isArray(s.result.list)?s.result.list:[]):Array.isArray(s.result)?d(s.result):d([]):t.error(s.message||"获取VIP配置失败")}catch(s){t.error("获取VIP配置失败"),console.error("获取VIP配置失败:",s)}finally{f(!1)}},C=async(s,n)=>{try{const o=await N(s,n);o.code===200?(t.success("更新成功"),c(!1),m()):t.error(o.message||"更新失败")}catch(o){t.error("更新失败"),console.error("更新VIP配置失败:",o)}},V=async()=>{try{const s=await q();s.code===200?t.success("重新计算完成"):t.error(s.message||"重新计算失败")}catch(s){t.error("重新计算失败"),console.error("重新计算失败:",s)}};a.useEffect(()=>{m()},[]);const w=[{title:"VIP等级",dataIndex:"vipLevel",key:"vipLevel",render:s=>e.jsxs(p,{color:"gold",icon:e.jsx(y,{}),children:["VIP",s]})},{title:"等级名称",dataIndex:"levelName",key:"levelName"},{title:"所需积分",dataIndex:"requiredPoints",key:"requiredPoints",render:s=>Number(s||0).toLocaleString()},{title:"每日金币奖励",dataIndex:"dailyGoldReward",key:"dailyGoldReward",render:s=>`${Number(s||0).toLocaleString()} 金币`},{title:"余额比例",dataIndex:"balanceRatio",key:"balanceRatio",render:s=>`${Number(s||0).toFixed(4)}`},{title:"现金比例",dataIndex:"cashRatio",key:"cashRatio",render:s=>`${Number(s||0).toFixed(4)}`},{title:"金币比例",dataIndex:"goldRatio",key:"goldRatio",render:s=>`${Number(s||0).toFixed(4)}`},{title:"状态",dataIndex:"status",key:"status",render:s=>e.jsx(p,{color:s===1?"green":"red",children:s===1?"启用":"禁用"})},{title:"操作",key:"action",render:(s,n)=>e.jsx(x,{children:e.jsx(h,{type:"primary",size:"small",icon:e.jsx(E,{}),onClick:()=>{R(n),u.setFieldsValue(n),c(!0)},children:"编辑"})})}],k=async s=>{j&&await C(j.id,s)};return e.jsxs(P,{children:[e.jsxs(F,{title:e.jsxs(x,{children:[e.jsx(y,{}),"VIP积分配置管理"]}),extra:e.jsxs(x,{children:[e.jsx(h,{type:"primary",icon:e.jsx($,{}),onClick:V,children:"重新计算所有用户等级"}),e.jsx(h,{icon:e.jsx(A,{}),onClick:m,children:"刷新"})]}),children:[e.jsx("div",{style:{marginBottom:16},children:e.jsxs(i,{column:4,bordered:!0,size:"small",children:[e.jsx(i.Item,{label:"积分计算规则",children:"总积分 = 余额 × 余额比例 + 累计现金消费 × 现金比例 + 累计金币消费 × 金币比例"}),e.jsx(i.Item,{label:"等级升级",children:"当用户积分达到对应等级要求时自动升级"}),e.jsx(i.Item,{label:"每日奖励",children:"用户每日可领取对应VIP等级的金币奖励"}),e.jsx(i.Item,{label:"配置说明",children:"修改配置后建议重新计算所有用户等级"})]})}),e.jsx(S,{columns:w,dataSource:I,rowKey:"id",loading:g,pagination:!1,size:"middle"})]}),e.jsx(L,{title:"编辑VIP配置",open:b,onCancel:()=>c(!1),onOk:()=>u.submit(),width:600,children:e.jsxs(r,{form:u,layout:"vertical",onFinish:k,children:[e.jsx(r.Item,{label:"等级名称",name:"levelName",rules:[{required:!0,message:"请输入等级名称"}],children:e.jsx(M,{})}),e.jsx(r.Item,{label:"所需积分",name:"requiredPoints",rules:[{required:!0,message:"请输入所需积分"}],children:e.jsx(l,{min:0,style:{width:"100%"}})}),e.jsx(r.Item,{label:"每日金币奖励",name:"dailyGoldReward",rules:[{required:!0,message:"请输入每日金币奖励"}],children:e.jsx(l,{min:0,style:{width:"100%"}})}),e.jsx(r.Item,{label:"余额比例",name:"balanceRatio",rules:[{required:!0,message:"请输入余额比例"}],children:e.jsx(l,{min:0,step:1e-4,precision:4,style:{width:"100%"}})}),e.jsx(r.Item,{label:"现金比例",name:"cashRatio",rules:[{required:!0,message:"请输入现金比例"}],children:e.jsx(l,{min:0,step:1e-4,precision:4,style:{width:"100%"}})}),e.jsx(r.Item,{label:"金币比例",name:"goldRatio",rules:[{required:!0,message:"请输入金币比例"}],children:e.jsx(l,{min:0,step:1e-4,precision:4,style:{width:"100%"}})})]})})]})};export{D as default};
