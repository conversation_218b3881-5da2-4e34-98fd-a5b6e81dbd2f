import { VipConfigService } from './vip-config.service';
export declare class TestVipConfigController {
    private readonly vipConfigService;
    constructor(vipConfigService: VipConfigService);
    findAll(): Promise<{
        code: number;
        message: string;
        result: {
            list: {
                creator: {
                    id: number;
                    username: string;
                } | null;
                updater: {
                    id: number;
                    username: string;
                } | null;
                id: number;
                vipLevel: number;
                levelName: string;
                requiredPoints: number;
                balanceRatio: number;
                cashRatio: number;
                goldRatio: number;
                dailyGoldReward: number;
                status: number;
                remark: string;
                createdBy: number;
                updatedBy: number;
                createTime: Date;
                updateTime: Date;
            }[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
        debug: {
            timestamp: string;
            note: string;
            stack?: undefined;
        };
        error?: undefined;
    } | {
        code: number;
        message: string;
        error: any;
        debug: {
            timestamp: string;
            stack: any;
            note?: undefined;
        };
        result?: undefined;
    }>;
    findAllRaw(): Promise<{
        code: number;
        message: string;
        result: any;
        debug: {
            timestamp: string;
            count: any;
            note: string;
            stack?: undefined;
        };
        error?: undefined;
    } | {
        code: number;
        message: string;
        error: any;
        debug: {
            timestamp: string;
            stack: any;
            count?: undefined;
            note?: undefined;
        };
        result?: undefined;
    }>;
}
