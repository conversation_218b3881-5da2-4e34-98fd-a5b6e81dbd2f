"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const app_user_entity_1 = require("../entities/app-user.entity");
const cash_transaction_entity_1 = require("../entities/cash-transaction.entity");
const gold_transaction_entity_1 = require("../entities/gold-transaction.entity");
const recharge_transaction_entity_1 = require("../entities/recharge-transaction.entity");
let TransactionService = class TransactionService {
    userRepository;
    cashTransactionRepository;
    goldTransactionRepository;
    rechargeTransactionRepository;
    dataSource;
    constructor(userRepository, cashTransactionRepository, goldTransactionRepository, rechargeTransactionRepository, dataSource) {
        this.userRepository = userRepository;
        this.cashTransactionRepository = cashTransactionRepository;
        this.goldTransactionRepository = goldTransactionRepository;
        this.rechargeTransactionRepository = rechargeTransactionRepository;
        this.dataSource = dataSource;
    }
    async createCashTransaction(dto) {
        return this.dataSource.transaction(async (manager) => {
            const user = await manager.findOne(app_user_entity_1.AppUser, { where: { id: dto.userId } });
            if (!user) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const balanceBefore = Number(user.withdrawableBalance) || 0;
            const amount = Number(dto.amount);
            const balanceAfter = dto.status === cash_transaction_entity_1.CashTransactionStatus.INCOME
                ? balanceBefore + amount
                : balanceBefore - amount;
            const transaction = manager.create(cash_transaction_entity_1.CashTransaction, {
                userId: dto.userId,
                uid: user.uid,
                amount,
                balanceBefore,
                balanceAfter,
                status: dto.status,
                transactionType: dto.transactionType,
                orderId: dto.orderId,
                description: dto.description,
                remark: dto.remark,
                operatorId: dto.operatorId,
            });
            await manager.update(app_user_entity_1.AppUser, dto.userId, { withdrawableBalance: balanceAfter });
            return manager.save(cash_transaction_entity_1.CashTransaction, transaction);
        });
    }
    async createGoldTransaction(dto) {
        return this.dataSource.transaction(async (manager) => {
            const user = await manager.findOne(app_user_entity_1.AppUser, { where: { id: dto.userId } });
            if (!user) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const balanceBefore = Number(user.goldBalance) || 0;
            const amount = Number(dto.amount);
            const balanceAfter = dto.status === gold_transaction_entity_1.GoldTransactionStatus.INCOME
                ? balanceBefore + amount
                : balanceBefore - amount;
            const transaction = manager.create(gold_transaction_entity_1.GoldTransaction, {
                userId: dto.userId,
                uid: user.uid,
                amount,
                balanceBefore,
                balanceAfter,
                status: dto.status,
                transactionType: dto.transactionType,
                orderId: dto.orderId,
                description: dto.description,
                remark: dto.remark,
                operatorId: dto.operatorId,
            });
            await manager.update(app_user_entity_1.AppUser, dto.userId, { goldBalance: balanceAfter });
            return manager.save(gold_transaction_entity_1.GoldTransaction, transaction);
        });
    }
    async createRechargeTransaction(dto) {
        return this.dataSource.transaction(async (manager) => {
            const user = await manager.findOne(app_user_entity_1.AppUser, { where: { id: dto.userId } });
            if (!user) {
                throw new common_1.NotFoundException('用户不存在');
            }
            const balanceBefore = Number(user.rechargeBalance) || 0;
            const amount = Number(dto.amount);
            const balanceAfter = dto.status === recharge_transaction_entity_1.RechargeTransactionStatus.INCOME
                ? balanceBefore + amount
                : balanceBefore - amount;
            const transaction = manager.create(recharge_transaction_entity_1.RechargeTransaction, {
                userId: dto.userId,
                uid: user.uid,
                amount,
                balanceBefore,
                balanceAfter,
                status: dto.status,
                transactionType: dto.transactionType,
                orderId: dto.orderId,
                description: dto.description,
                remark: dto.remark,
                operatorId: dto.operatorId,
            });
            await manager.update(app_user_entity_1.AppUser, dto.userId, { rechargeBalance: balanceAfter });
            return manager.save(recharge_transaction_entity_1.RechargeTransaction, transaction);
        });
    }
    async getCashStatistics(userId) {
        const result = await this.cashTransactionRepository
            .createQueryBuilder('t')
            .select([
            'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
            'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
            'COUNT(*) as transactionCount',
            'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType IN (3, 5, 6) THEN t.amount ELSE 0 END) as inviteActivityAmount'
        ])
            .where('t.userId = :userId', { userId })
            .getRawOne();
        const user = await this.userRepository.findOne({ where: { id: userId } });
        const betAmount = Number(result.betAmount) || 0;
        const winAmount = Number(result.winAmount) || 0;
        const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;
        return {
            totalIncome: Number(result.totalIncome) || 0,
            totalExpense: Number(result.totalExpense) || 0,
            balance: Number(user?.withdrawableBalance) || 0,
            transactionCount: Number(result.transactionCount) || 0,
            betAmount,
            winAmount,
            betWinRatio: Number(betWinRatio.toFixed(2)),
            inviteActivityAmount: Number(result.inviteActivityAmount) || 0,
        };
    }
    async getGoldStatistics(userId) {
        const result = await this.goldTransactionRepository
            .createQueryBuilder('t')
            .select([
            'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
            'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
            'COUNT(*) as transactionCount',
            'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType = 3 THEN t.amount ELSE 0 END) as vipCardAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType IN (4, 5, 6) THEN t.amount ELSE 0 END) as activityAmount'
        ])
            .where('t.userId = :userId', { userId })
            .getRawOne();
        const user = await this.userRepository.findOne({ where: { id: userId } });
        const betAmount = Number(result.betAmount) || 0;
        const winAmount = Number(result.winAmount) || 0;
        const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;
        return {
            totalIncome: Number(result.totalIncome) || 0,
            totalExpense: Number(result.totalExpense) || 0,
            balance: Number(user?.goldBalance) || 0,
            transactionCount: Number(result.transactionCount) || 0,
            betAmount,
            winAmount,
            betWinRatio: Number(betWinRatio.toFixed(2)),
            vipCardAmount: Number(result.vipCardAmount) || 0,
            activityAmount: Number(result.activityAmount) || 0,
        };
    }
    async getRechargeStatistics(userId) {
        const result = await this.rechargeTransactionRepository
            .createQueryBuilder('t')
            .select([
            'SUM(CASE WHEN t.status = 1 THEN t.amount ELSE 0 END) as totalIncome',
            'SUM(CASE WHEN t.status = 2 THEN t.amount ELSE 0 END) as totalExpense',
            'COUNT(*) as transactionCount',
            'SUM(CASE WHEN t.status = 2 AND t.transactionType = 1 THEN t.amount ELSE 0 END) as betAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType = 2 THEN t.amount ELSE 0 END) as winAmount',
            'SUM(CASE WHEN t.status = 1 AND t.transactionType = 3 THEN t.amount ELSE 0 END) as depositAmount'
        ])
            .where('t.userId = :userId', { userId })
            .getRawOne();
        const user = await this.userRepository.findOne({ where: { id: userId } });
        const betAmount = Number(result.betAmount) || 0;
        const winAmount = Number(result.winAmount) || 0;
        const betWinRatio = betAmount > 0 ? (winAmount / betAmount) * 100 : 0;
        return {
            totalIncome: Number(result.totalIncome) || 0,
            totalExpense: Number(result.totalExpense) || 0,
            balance: Number(user?.rechargeBalance) || 0,
            transactionCount: Number(result.transactionCount) || 0,
            betAmount,
            winAmount,
            betWinRatio: Number(betWinRatio.toFixed(2)),
            depositAmount: Number(result.depositAmount) || 0,
        };
    }
    async getCashTransactionList(query) {
        const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;
        const queryBuilder = this.cashTransactionRepository
            .createQueryBuilder('t')
            .where('t.userId = :userId', { userId })
            .orderBy('t.createdAt', 'DESC');
        if (status) {
            queryBuilder.andWhere('t.status = :status', { status });
        }
        if (transactionType) {
            queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
        }
        if (startDate) {
            const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
            queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
        }
        if (endDate) {
            const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
            queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
        }
        const [list, total] = await queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return {
            list,
            total,
            page,
            pageSize,
        };
    }
    async getGoldTransactionList(query) {
        const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;
        const queryBuilder = this.goldTransactionRepository
            .createQueryBuilder('t')
            .where('t.userId = :userId', { userId })
            .orderBy('t.createdAt', 'DESC');
        if (status) {
            queryBuilder.andWhere('t.status = :status', { status });
        }
        if (transactionType) {
            queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
        }
        if (startDate) {
            const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
            queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
        }
        if (endDate) {
            const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
            queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
        }
        const [list, total] = await queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return {
            list,
            total,
            page,
            pageSize,
        };
    }
    async getRechargeTransactionList(query) {
        const { userId, status, transactionType, startDate, endDate, page = 1, pageSize = 20 } = query;
        const queryBuilder = this.rechargeTransactionRepository
            .createQueryBuilder('t')
            .where('t.userId = :userId', { userId })
            .orderBy('t.createdAt', 'DESC');
        if (status) {
            queryBuilder.andWhere('t.status = :status', { status });
        }
        if (transactionType) {
            queryBuilder.andWhere('t.transactionType = :transactionType', { transactionType });
        }
        if (startDate) {
            const startDateObj = typeof startDate === 'string' ? new Date(startDate) : startDate;
            queryBuilder.andWhere('t.createdAt >= :startDate', { startDate: startDateObj });
        }
        if (endDate) {
            const endDateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
            queryBuilder.andWhere('t.createdAt <= :endDate', { endDate: endDateObj });
        }
        const [list, total] = await queryBuilder
            .skip((page - 1) * pageSize)
            .take(pageSize)
            .getManyAndCount();
        return {
            list,
            total,
            page,
            pageSize,
        };
    }
    async getUserAssetStatistics(userId) {
        const [cashStats, goldStats, rechargeStats] = await Promise.all([
            this.getCashStatistics(userId),
            this.getGoldStatistics(userId),
            this.getRechargeStatistics(userId),
        ]);
        return {
            cash: cashStats,
            gold: goldStats,
            recharge: rechargeStats,
        };
    }
};
exports.TransactionService = TransactionService;
exports.TransactionService = TransactionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(app_user_entity_1.AppUser)),
    __param(1, (0, typeorm_1.InjectRepository)(cash_transaction_entity_1.CashTransaction)),
    __param(2, (0, typeorm_1.InjectRepository)(gold_transaction_entity_1.GoldTransaction)),
    __param(3, (0, typeorm_1.InjectRepository)(recharge_transaction_entity_1.RechargeTransaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], TransactionService);
//# sourceMappingURL=transaction.service.js.map