"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUserDetailDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AppUserDetailDto {
    id;
    uid;
    username;
    email;
    phone;
    googleId;
    nickname;
    avatar;
    gender;
    birthday;
    rechargeBalance;
    goldBalance;
    withdrawableBalance;
    vipLevel;
    vipExp;
    status;
    accountType;
    isVerified;
    riskScore;
    kycStatus;
    kycRejectReason;
    inviterId;
    invitationPath;
    channelId;
    adId;
    adIdentifier;
    promotionalPageId;
    acquisitionTag;
    tags;
    lastLoginTime;
    daysNotLoggedIn;
    lastLoginIp;
    registerIp;
    createTime;
    updateTime;
    inviter;
    channel;
    ad;
    promotionalPage;
}
exports.AppUserDetailDto = AppUserDetailDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户ID' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '对外展示的用户ID' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "uid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户名' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邮箱' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '手机号' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Google ID', required: false }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "googleId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '昵称' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "nickname", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '头像URL' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "avatar", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '性别：0-未知，1-男，2-女' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '生日' }),
    __metadata("design:type", Date)
], AppUserDetailDto.prototype, "birthday", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '充值余额：用户直接现金充值的钱' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "rechargeBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '金币余额：用户充值购买的金币，VIP卡领取,和各种活动赠送的游戏体验币' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "goldBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '可提现余额：用户各种推广奖励，或者游戏中的现金' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "withdrawableBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP等级' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "vipLevel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'VIP经验值' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "vipExp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户状态：0-正常，1-已封禁，2-已注销' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '账户类型：0-普通用户，1-内部员工' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "accountType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '是否认证：0-未认证，1-已认证' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "isVerified", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '风险分值' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "riskScore", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KYC状态：0-未认证，1-审核中，2-已通过，3-已拒绝' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "kycStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'KYC拒绝原因', required: false }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "kycRejectReason", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请人ID', required: false }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "inviterId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请路径', required: false }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "invitationPath", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道ID', required: false }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "channelId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告ID', required: false }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "adId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告标识', required: false }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "adIdentifier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推广页面ID', required: false }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "promotionalPageId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '获取标签', required: false }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "acquisitionTag", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '用户标签', required: false }),
    __metadata("design:type", Object)
], AppUserDetailDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录时间' }),
    __metadata("design:type", Date)
], AppUserDetailDto.prototype, "lastLoginTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '未登录天数' }),
    __metadata("design:type", Number)
], AppUserDetailDto.prototype, "daysNotLoggedIn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '最后登录IP' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "lastLoginIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '注册IP' }),
    __metadata("design:type", String)
], AppUserDetailDto.prototype, "registerIp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '创建时间' }),
    __metadata("design:type", Date)
], AppUserDetailDto.prototype, "createTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '更新时间' }),
    __metadata("design:type", Date)
], AppUserDetailDto.prototype, "updateTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '邀请人信息', required: false }),
    __metadata("design:type", Object)
], AppUserDetailDto.prototype, "inviter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '渠道信息', required: false }),
    __metadata("design:type", Object)
], AppUserDetailDto.prototype, "channel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '广告信息', required: false }),
    __metadata("design:type", Object)
], AppUserDetailDto.prototype, "ad", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: '推广页面信息', required: false }),
    __metadata("design:type", Object)
], AppUserDetailDto.prototype, "promotionalPage", void 0);
//# sourceMappingURL=app-user-detail.dto.js.map