"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DailyRewardService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const vip_config_entity_1 = require("./entities/vip-config.entity");
const app_user_entity_1 = require("../entities/app-user.entity");
const gold_transaction_entity_1 = require("../entities/gold-transaction.entity");
let DailyRewardService = class DailyRewardService {
    vipConfigRepository;
    appUserRepository;
    goldTransactionRepository;
    constructor(vipConfigRepository, appUserRepository, goldTransactionRepository) {
        this.vipConfigRepository = vipConfigRepository;
        this.appUserRepository = appUserRepository;
        this.goldTransactionRepository = goldTransactionRepository;
    }
    async checkTodayRewardClaimed(userId) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);
        const rewardTransaction = await this.goldTransactionRepository
            .createQueryBuilder('transaction')
            .where('transaction.userId = :userId', { userId })
            .andWhere('transaction.transactionType = :transactionType', { transactionType: 6 })
            .andWhere('transaction.description = :description', { description: 'VIP每日金币奖励' })
            .andWhere('transaction.createTime >= :startOfDay', { startOfDay })
            .andWhere('transaction.createTime <= :endOfDay', { endOfDay })
            .getOne();
        return !!rewardTransaction;
    }
    async claimDailyGoldReward(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const alreadyClaimed = await this.checkTodayRewardClaimed(userId);
        if (alreadyClaimed) {
            throw new common_1.BadRequestException('今日VIP金币奖励已领取');
        }
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP等级配置不存在');
        }
        if (vipConfig.dailyGoldReward <= 0) {
            throw new common_1.BadRequestException('当前VIP等级无每日金币奖励');
        }
        const newGoldBalance = user.goldBalance + vipConfig.dailyGoldReward;
        await this.appUserRepository.update(userId, {
            goldBalance: newGoldBalance,
        });
        const goldTransaction = this.goldTransactionRepository.create({
            userId,
            uid: user.uid,
            amount: vipConfig.dailyGoldReward,
            balanceBefore: user.goldBalance,
            balanceAfter: newGoldBalance,
            status: 1,
            transactionType: 6,
            description: 'VIP每日金币奖励',
            remark: `VIP${user.vipLevel}等级每日奖励`,
        });
        await this.goldTransactionRepository.save(goldTransaction);
        return {
            success: true,
            goldAmount: vipConfig.dailyGoldReward,
            message: `成功领取VIP${user.vipLevel}等级每日金币奖励 ${vipConfig.dailyGoldReward} 枚`,
        };
    }
    async getDailyRewardStatus(userId) {
        const user = await this.appUserRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const vipConfig = await this.vipConfigRepository.findOne({
            where: { vipLevel: user.vipLevel, status: 1 },
        });
        if (!vipConfig) {
            throw new common_1.NotFoundException('VIP等级配置不存在');
        }
        const alreadyClaimed = await this.checkTodayRewardClaimed(userId);
        const canClaim = !alreadyClaimed && vipConfig.dailyGoldReward > 0;
        return {
            canClaim,
            goldAmount: vipConfig.dailyGoldReward,
            vipLevel: user.vipLevel,
            levelName: vipConfig.levelName,
            alreadyClaimed,
        };
    }
    async getDailyRewardHistory(userId, page = 1, pageSize = 10) {
        const skip = (page - 1) * pageSize;
        const [transactions, total] = await this.goldTransactionRepository.findAndCount({
            where: {
                userId,
                transactionType: 6,
                description: 'VIP每日金币奖励',
            },
            order: { createTime: 'DESC' },
            skip,
            take: pageSize,
        });
        return {
            list: transactions.map(transaction => ({
                id: transaction.id,
                amount: transaction.amount,
                vipLevel: transaction.remark?.match(/VIP(\d+)/)?.[1] || '未知',
                claimTime: transaction.createTime,
                remark: transaction.remark,
            })),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
};
exports.DailyRewardService = DailyRewardService;
exports.DailyRewardService = DailyRewardService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(vip_config_entity_1.VipConfig)),
    __param(1, (0, typeorm_1.InjectRepository)(app_user_entity_1.AppUser)),
    __param(2, (0, typeorm_1.InjectRepository)(gold_transaction_entity_1.GoldTransaction)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], DailyRewardService);
//# sourceMappingURL=daily-reward.service.js.map