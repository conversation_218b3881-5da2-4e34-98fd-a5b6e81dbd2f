import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { GoldRechargeConfigService } from './gold-recharge-config.service';
import { BalanceRechargeConfigService } from './balance-recharge-config.service';
import { 
  CreateGoldRechargeConfigDto, 
  UpdateGoldRechargeConfigDto, 
  GoldRechargeConfigQueryDto 
} from './dto/gold-recharge-config.dto';
import { 
  CreateBalanceRechargeConfigDto, 
  UpdateBalanceRechargeConfigDto, 
  BalanceRechargeConfigQueryDto,
  UpdateBalanceRechargeLimitDto
} from './dto/balance-recharge-config.dto';
import { SystemJwtAuthGuard } from '../../system/auth/guards/jwt-auth.guard';

@ApiTags('充值配置管理')
@ApiBearerAuth()
@UseGuards(SystemJwtAuthGuard)
@Controller('config/recharge')
export class RechargeConfigController {
  constructor(
    private readonly goldRechargeConfigService: GoldRechargeConfigService,
    private readonly balanceRechargeConfigService: BalanceRechargeConfigService,
  ) {}

  // ==================== 金币充值配置 ====================

  @Post('gold')
  @ApiOperation({ summary: '创建金币充值配置' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createGoldConfig(@Body() createDto: CreateGoldRechargeConfigDto, @Request() req) {
    const result = await this.goldRechargeConfigService.create(createDto, req.user.id);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Get('gold')
  @ApiOperation({ summary: '获取金币充值配置列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAllGoldConfigs(@Query() query: GoldRechargeConfigQueryDto) {
    const result = await this.goldRechargeConfigService.findAll(query);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('gold/effective')
  @ApiOperation({ summary: '获取生效的金币充值配置（考虑活动时间）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAllEffectiveGoldConfigs() {
    const result = await this.goldRechargeConfigService.findAllEffective();
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('gold/active-activities')
  @ApiOperation({ summary: '获取当前活动中的金币充值配置' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getActiveGoldActivityConfigs() {
    const result = await this.goldRechargeConfigService.getActiveActivityConfigs();
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('gold/:id')
  @ApiOperation({ summary: '获取金币充值配置详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async findOneGoldConfig(@Param('id', ParseIntPipe) id: number) {
    const result = await this.goldRechargeConfigService.findOne(id);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Patch('gold/:id')
  @ApiOperation({ summary: '更新金币充值配置' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async updateGoldConfig(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateGoldRechargeConfigDto,
    @Request() req,
  ) {
    const result = await this.goldRechargeConfigService.update(id, updateDto, req.user.id);
    return {
      code: 200,
      message: '更新成功',
      result,
    };
  }

  @Delete('gold/:id')
  @ApiOperation({ summary: '删除金币充值配置' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async removeGoldConfig(@Param('id', ParseIntPipe) id: number) {
    const result = await this.goldRechargeConfigService.remove(id);
    return {
      code: 200,
      message: result.message,
    };
  }

  @Patch('gold/batch/activity-time')
  @ApiOperation({ summary: '批量更新金币充值配置的活动时间' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateGoldActivityTime(
    @Body() body: { startTime: string; endTime: string },
    @Request() req,
  ) {
    const startTime = new Date(body.startTime);
    const endTime = new Date(body.endTime);
    
    const result = await this.goldRechargeConfigService.updateActivityTime(startTime, endTime, req.user.id);
    return {
      code: 200,
      message: result.message,
    };
  }

  // ==================== 余额充值配置 ====================

  @Post('balance')
  @ApiOperation({ summary: '创建余额充值配置' })
  @ApiResponse({ status: 201, description: '创建成功' })
  async createBalanceConfig(@Body() createDto: CreateBalanceRechargeConfigDto, @Request() req) {
    const result = await this.balanceRechargeConfigService.create(createDto, req.user.id);
    return {
      code: 200,
      message: '创建成功',
      result,
    };
  }

  @Get('balance')
  @ApiOperation({ summary: '获取余额充值配置列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAllBalanceConfigs(@Query() query: BalanceRechargeConfigQueryDto) {
    const result = await this.balanceRechargeConfigService.findAll(query);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('balance/effective')
  @ApiOperation({ summary: '获取生效的余额充值配置（考虑活动时间）' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async findAllEffectiveBalanceConfigs() {
    const result = await this.balanceRechargeConfigService.findAllEffective();
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Get('balance/active-activities')
  @ApiOperation({ summary: '获取当前活动中的余额充值配置' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getActiveBalanceActivityConfigs() {
    const result = await this.balanceRechargeConfigService.getActiveActivityConfigs();
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  // ==================== 余额充值限制 ====================

  @Get('balance/limits')
  @ApiOperation({ summary: '获取余额充值限制配置' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getBalanceRechargeLimit() {
    try {
      console.log('🔍 开始获取余额充值限制配置...');
      const result = await this.balanceRechargeConfigService.getRechargeLimit();
      console.log('✅ 获取成功，结果:', result);
      return {
        code: 200,
        message: '获取成功',
        result,
      };
    } catch (error) {
      console.error('❌ 获取余额充值限制配置失败:', error);
      throw error;
    }
  }

  @Patch('balance/limits')
  @ApiOperation({ summary: '更新余额充值限制配置' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateBalanceRechargeLimit(
    @Body() updateDto: UpdateBalanceRechargeLimitDto,
    @Request() req,
  ) {
    const result = await this.balanceRechargeConfigService.updateRechargeLimit(updateDto, req.user.id);
    return {
      code: 200,
      message: '更新成功',
      result,
    };
  }

  @Post('balance/validate-amount')
  @ApiOperation({ summary: '验证余额充值金额是否在允许范围内' })
  @ApiResponse({ status: 200, description: '验证完成' })
  async validateBalanceRechargeAmount(@Body() body: { amount: number }) {
    const result = await this.balanceRechargeConfigService.validateRechargeAmount(body.amount);
    return {
      code: 200,
      message: '验证完成',
      result,
    };
  }

  @Get('balance/:id')
  @ApiOperation({ summary: '获取余额充值配置详情' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async findOneBalanceConfig(@Param('id', ParseIntPipe) id: number) {
    const result = await this.balanceRechargeConfigService.findOne(id);
    return {
      code: 200,
      message: '获取成功',
      result,
    };
  }

  @Patch('balance/:id')
  @ApiOperation({ summary: '更新余额充值配置' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async updateBalanceConfig(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateBalanceRechargeConfigDto,
    @Request() req,
  ) {
    const result = await this.balanceRechargeConfigService.update(id, updateDto, req.user.id);
    return {
      code: 200,
      message: '更新成功',
      result,
    };
  }

  @Delete('balance/:id')
  @ApiOperation({ summary: '删除余额充值配置' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '配置不存在' })
  async removeBalanceConfig(@Param('id', ParseIntPipe) id: number) {
    const result = await this.balanceRechargeConfigService.remove(id);
    return {
      code: 200,
      message: result.message,
    };
  }

  @Patch('balance/batch/activity-time')
  @ApiOperation({ summary: '批量更新余额充值配置的活动时间' })
  @ApiResponse({ status: 200, description: '更新成功' })
  async updateBalanceActivityTime(
    @Body() body: { startTime: string; endTime: string },
    @Request() req,
  ) {
    const startTime = new Date(body.startTime);
    const endTime = new Date(body.endTime);
    
    const result = await this.balanceRechargeConfigService.updateActivityTime(startTime, endTime, req.user.id);
    return {
      code: 200,
      message: result.message,
    };
  }
}
