"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemWorkbookService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sys_workbook_entity_1 = require("../entities/sys-workbook.entity");
let SystemWorkbookService = class SystemWorkbookService {
    workbookRepository;
    constructor(workbookRepository) {
        this.workbookRepository = workbookRepository;
    }
    async create(createWorkbookDto) {
        const { type, text, value, valueType = 'text', ...rest } = createWorkbookDto;
        const existingWorkbook = await this.workbookRepository.findOne({
            where: { type, value },
        });
        if (existingWorkbook) {
            throw new common_1.ConflictException(`类型 ${type} 下已存在值为 ${value} 的记录`);
        }
        const workbook = this.workbookRepository.create({
            type,
            text,
            value,
            valueType,
            ...rest,
        });
        return await this.workbookRepository.save(workbook);
    }
    async findAll(queryWorkbookDto) {
        const { type, text, value, valueType, status, current = 1, pageSize = 10, } = queryWorkbookDto;
        const queryBuilder = this.workbookRepository.createQueryBuilder('workbook');
        if (type) {
            queryBuilder.andWhere('workbook.type = :type', { type });
        }
        if (text) {
            queryBuilder.andWhere('workbook.text LIKE :text', {
                text: `%${text}%`,
            });
        }
        if (value) {
            queryBuilder.andWhere('workbook.value LIKE :value', {
                value: `%${value}%`,
            });
        }
        if (valueType) {
            queryBuilder.andWhere('workbook.valueType = :valueType', { valueType });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('workbook.status = :status', { status });
        }
        queryBuilder.orderBy('workbook.type', 'ASC');
        queryBuilder.addOrderBy('workbook.order', 'ASC');
        queryBuilder.addOrderBy('workbook.id', 'ASC');
        const total = await queryBuilder.getCount();
        const list = await queryBuilder
            .skip((current - 1) * pageSize)
            .take(pageSize)
            .getMany();
        return {
            list,
            total,
            current,
            pageSize,
        };
    }
    async findOne(id) {
        const workbook = await this.workbookRepository.findOne({
            where: { id },
        });
        if (!workbook) {
            throw new common_1.NotFoundException('数据字典记录不存在');
        }
        return workbook;
    }
    async update(id, updateWorkbookDto) {
        const workbook = await this.findOne(id);
        const { type, value, ...rest } = updateWorkbookDto;
        if ((type && type !== workbook.type) || (value && value !== workbook.value)) {
            const finalType = type || workbook.type;
            const finalValue = value || workbook.value;
            const existingWorkbook = await this.workbookRepository.findOne({
                where: { type: finalType, value: finalValue },
            });
            if (existingWorkbook && existingWorkbook.id !== id) {
                throw new common_1.ConflictException(`类型 ${finalType} 下已存在值为 ${finalValue} 的记录`);
            }
        }
        Object.assign(workbook, { type, value, ...rest });
        return await this.workbookRepository.save(workbook);
    }
    async remove(id) {
        const workbook = await this.findOne(id);
        await this.workbookRepository.remove(workbook);
        return { message: '删除成功' };
    }
    async findByType(type) {
        return await this.workbookRepository.find({
            where: { type, status: 1 },
            order: { order: 'ASC', id: 'ASC' },
        });
    }
    async getTypes() {
        const result = await this.workbookRepository
            .createQueryBuilder('workbook')
            .select('DISTINCT workbook.type', 'type')
            .where('workbook.status = :status', { status: 1 })
            .orderBy('workbook.type', 'ASC')
            .getRawMany();
        return result.map(item => item.type);
    }
    async updateOrder(items) {
        const promises = items.map(item => this.workbookRepository.update(item.id, { order: item.order }));
        await Promise.all(promises);
        return { message: '排序更新成功' };
    }
};
exports.SystemWorkbookService = SystemWorkbookService;
exports.SystemWorkbookService = SystemWorkbookService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_workbook_entity_1.SysWorkbook)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], SystemWorkbookService);
//# sourceMappingURL=workbook.service.js.map