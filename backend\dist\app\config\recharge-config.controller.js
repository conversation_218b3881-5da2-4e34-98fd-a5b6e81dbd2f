"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RechargeConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const gold_recharge_config_service_1 = require("./gold-recharge-config.service");
const balance_recharge_config_service_1 = require("./balance-recharge-config.service");
const gold_recharge_config_dto_1 = require("./dto/gold-recharge-config.dto");
const balance_recharge_config_dto_1 = require("./dto/balance-recharge-config.dto");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
let RechargeConfigController = class RechargeConfigController {
    goldRechargeConfigService;
    balanceRechargeConfigService;
    constructor(goldRechargeConfigService, balanceRechargeConfigService) {
        this.goldRechargeConfigService = goldRechargeConfigService;
        this.balanceRechargeConfigService = balanceRechargeConfigService;
    }
    async createGoldConfig(createDto, req) {
        const result = await this.goldRechargeConfigService.create(createDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAllGoldConfigs(query) {
        const result = await this.goldRechargeConfigService.findAll(query);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findAllEffectiveGoldConfigs() {
        const result = await this.goldRechargeConfigService.findAllEffective();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getActiveGoldActivityConfigs() {
        const result = await this.goldRechargeConfigService.getActiveActivityConfigs();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOneGoldConfig(id) {
        const result = await this.goldRechargeConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateGoldConfig(id, updateDto, req) {
        const result = await this.goldRechargeConfigService.update(id, updateDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async removeGoldConfig(id) {
        const result = await this.goldRechargeConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async updateGoldActivityTime(body, req) {
        const startTime = new Date(body.startTime);
        const endTime = new Date(body.endTime);
        const result = await this.goldRechargeConfigService.updateActivityTime(startTime, endTime, req.user.id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async createBalanceConfig(createDto, req) {
        const result = await this.balanceRechargeConfigService.create(createDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAllBalanceConfigs(query) {
        const result = await this.balanceRechargeConfigService.findAll(query);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findAllEffectiveBalanceConfigs() {
        const result = await this.balanceRechargeConfigService.findAllEffective();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getActiveBalanceActivityConfigs() {
        const result = await this.balanceRechargeConfigService.getActiveActivityConfigs();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getBalanceRechargeLimit() {
        try {
            console.log('🔍 开始获取余额充值限制配置...');
            const result = await this.balanceRechargeConfigService.getRechargeLimit();
            console.log('✅ 获取成功，结果:', result);
            return {
                code: 200,
                message: '获取成功',
                result,
            };
        }
        catch (error) {
            console.error('❌ 获取余额充值限制配置失败:', error);
            throw error;
        }
    }
    async updateBalanceRechargeLimit(updateDto, req) {
        const result = await this.balanceRechargeConfigService.updateRechargeLimit(updateDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async validateBalanceRechargeAmount(body) {
        const result = await this.balanceRechargeConfigService.validateRechargeAmount(body.amount);
        return {
            code: 200,
            message: '验证完成',
            result,
        };
    }
    async findOneBalanceConfig(id) {
        const result = await this.balanceRechargeConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async updateBalanceConfig(id, updateDto, req) {
        const result = await this.balanceRechargeConfigService.update(id, updateDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async removeBalanceConfig(id) {
        const result = await this.balanceRechargeConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async updateBalanceActivityTime(body, req) {
        const startTime = new Date(body.startTime);
        const endTime = new Date(body.endTime);
        const result = await this.balanceRechargeConfigService.updateActivityTime(startTime, endTime, req.user.id);
        return {
            code: 200,
            message: result.message,
        };
    }
};
exports.RechargeConfigController = RechargeConfigController;
__decorate([
    (0, common_1.Post)('gold'),
    (0, swagger_1.ApiOperation)({ summary: '创建金币充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [gold_recharge_config_dto_1.CreateGoldRechargeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "createGoldConfig", null);
__decorate([
    (0, common_1.Get)('gold'),
    (0, swagger_1.ApiOperation)({ summary: '获取金币充值配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [gold_recharge_config_dto_1.GoldRechargeConfigQueryDto]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findAllGoldConfigs", null);
__decorate([
    (0, common_1.Get)('gold/effective'),
    (0, swagger_1.ApiOperation)({ summary: '获取生效的金币充值配置（考虑活动时间）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findAllEffectiveGoldConfigs", null);
__decorate([
    (0, common_1.Get)('gold/active-activities'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前活动中的金币充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "getActiveGoldActivityConfigs", null);
__decorate([
    (0, common_1.Get)('gold/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取金币充值配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findOneGoldConfig", null);
__decorate([
    (0, common_1.Patch)('gold/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新金币充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, gold_recharge_config_dto_1.UpdateGoldRechargeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "updateGoldConfig", null);
__decorate([
    (0, common_1.Delete)('gold/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除金币充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "removeGoldConfig", null);
__decorate([
    (0, common_1.Patch)('gold/batch/activity-time'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新金币充值配置的活动时间' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "updateGoldActivityTime", null);
__decorate([
    (0, common_1.Post)('balance'),
    (0, swagger_1.ApiOperation)({ summary: '创建余额充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [balance_recharge_config_dto_1.CreateBalanceRechargeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "createBalanceConfig", null);
__decorate([
    (0, common_1.Get)('balance'),
    (0, swagger_1.ApiOperation)({ summary: '获取余额充值配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [balance_recharge_config_dto_1.BalanceRechargeConfigQueryDto]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findAllBalanceConfigs", null);
__decorate([
    (0, common_1.Get)('balance/effective'),
    (0, swagger_1.ApiOperation)({ summary: '获取生效的余额充值配置（考虑活动时间）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findAllEffectiveBalanceConfigs", null);
__decorate([
    (0, common_1.Get)('balance/active-activities'),
    (0, swagger_1.ApiOperation)({ summary: '获取当前活动中的余额充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "getActiveBalanceActivityConfigs", null);
__decorate([
    (0, common_1.Get)('balance/limits'),
    (0, swagger_1.ApiOperation)({ summary: '获取余额充值限制配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "getBalanceRechargeLimit", null);
__decorate([
    (0, common_1.Patch)('balance/limits'),
    (0, swagger_1.ApiOperation)({ summary: '更新余额充值限制配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [balance_recharge_config_dto_1.UpdateBalanceRechargeLimitDto, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "updateBalanceRechargeLimit", null);
__decorate([
    (0, common_1.Post)('balance/validate-amount'),
    (0, swagger_1.ApiOperation)({ summary: '验证余额充值金额是否在允许范围内' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '验证完成' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "validateBalanceRechargeAmount", null);
__decorate([
    (0, common_1.Get)('balance/:id'),
    (0, swagger_1.ApiOperation)({ summary: '获取余额充值配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "findOneBalanceConfig", null);
__decorate([
    (0, common_1.Patch)('balance/:id'),
    (0, swagger_1.ApiOperation)({ summary: '更新余额充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, balance_recharge_config_dto_1.UpdateBalanceRechargeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "updateBalanceConfig", null);
__decorate([
    (0, common_1.Delete)('balance/:id'),
    (0, swagger_1.ApiOperation)({ summary: '删除余额充值配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "removeBalanceConfig", null);
__decorate([
    (0, common_1.Patch)('balance/batch/activity-time'),
    (0, swagger_1.ApiOperation)({ summary: '批量更新余额充值配置的活动时间' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], RechargeConfigController.prototype, "updateBalanceActivityTime", null);
exports.RechargeConfigController = RechargeConfigController = __decorate([
    (0, swagger_1.ApiTags)('充值配置管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/recharge'),
    __metadata("design:paramtypes", [gold_recharge_config_service_1.GoldRechargeConfigService,
        balance_recharge_config_service_1.BalanceRechargeConfigService])
], RechargeConfigController);
//# sourceMappingURL=recharge-config.controller.js.map