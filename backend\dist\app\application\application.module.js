"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const application_service_1 = require("./application.service");
const application_controller_1 = require("./application.controller");
const hub88_sync_service_1 = require("./hub88-sync.service");
const hub88_sync_controller_1 = require("./hub88-sync.controller");
const entities_1 = require("../entities");
let ApplicationModule = class ApplicationModule {
};
exports.ApplicationModule = ApplicationModule;
exports.ApplicationModule = ApplicationModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([entities_1.Application, entities_1.ApplicationProvider])],
        controllers: [application_controller_1.ApplicationController, hub88_sync_controller_1.Hub88SyncController],
        providers: [application_service_1.ApplicationService, hub88_sync_service_1.Hub88SyncService],
        exports: [application_service_1.ApplicationService, hub88_sync_service_1.Hub88SyncService],
    })
], ApplicationModule);
//# sourceMappingURL=application.module.js.map