import{j as e}from"./index-CHjq8S-S.js";import{a as i}from"./react-BUTTOX-3.js";import{B as ie}from"./index-DDI4OfxQ.js";import{e as oe,f as ce,g as de,h as he,i as ue,j as me,k as xe}from"./index-jASoOOIw.js";import{f as je}from"./index-C13Ae5CQ.js";import{av as r,aq as pe,ao as u,ap as n,T as ge,ab as fe,D as w,I as P,p as h,Q as q,d,u as ye,ak as we,az as Ce,M as H,q as Ie,an as C,s as l,n as I,aB as Se,aC as ve,S as be,aD as ke,aE as Te}from"./antd-CXPM1OiB.js";const{Option:m}=h,{TextArea:$}=P,{Title:y,Text:a}=ge,O=[{value:1,label:"启用",color:"green"},{value:0,label:"禁用",color:"red"}],Ne=()=>{var V,B;const[F,x]=i.useState(!1),[G,L]=i.useState([]),[E,Q]=i.useState(0),[S,v]=i.useState(1),[b,U]=i.useState(10),[k]=r.useForm(),[j]=r.useForm(),[_,D]=i.useState(!1),[R,J]=i.useState("create"),[z,N]=i.useState(null),[K,T]=i.useState(!1),[o,W]=i.useState(null),[f,X]=i.useState([]),[Ae,Y]=i.useState([]),c=async s=>{try{x(!0);const t=k.getFieldsValue(),p={page:S,pageSize:b,...t,...s},g=await oe(p);g.code===200?(L(g.result.list),Q(g.result.total)):l.error(g.message||"获取数据失败")}catch(t){console.error("加载数据失败:",t),l.error("加载数据失败")}finally{x(!1)}},Z=async()=>{try{const s=await ce({pageSize:1e3,status:1});s.code===200&&X(s.result.list);const t=await je({pageSize:1e3,status:"active"});t.code===200&&Y(t.result.list)}catch(s){console.error("加载选项数据失败:",s)}};i.useEffect(()=>{c(),Z()},[S,b]);const ee=()=>{v(1),c()},se=()=>{k.resetFields(),v(1),c()},M=(s,t)=>{J(s),N(t||null),D(!0),s==="edit"&&t?j.setFieldsValue({configName:t.configName,description:t.description,status:t.status,sortOrder:t.sortOrder}):(j.resetFields(),j.setFieldsValue({status:1,sortOrder:0}))},A=()=>{D(!1),N(null),j.resetFields()},te=async()=>{try{const s=await j.validateFields();if(x(!0),R==="create"){const t=await de(s);t.code===200?(l.success("创建成功"),A(),c()):l.error(t.message||"创建失败")}else if(z){const t=await he(z.id,s);t.code===200?(l.success("更新成功"),A(),c()):l.error(t.message||"更新失败")}}catch(s){console.error("提交失败:",s),l.error("操作失败")}finally{x(!1)}},re=async s=>{try{const t=await me(s.id);t.code===200?(l.success(`${s.status===1?"禁用":"启用"}成功`),c()):l.error(t.message||"操作失败")}catch(t){console.error("切换状态失败:",t),l.error("操作失败")}},le=async s=>{try{const t=await xe(s.id);t.code===200?(l.success("删除成功"),c()):l.error(t.message||"删除失败")}catch(t){console.error("删除失败:",t),l.error("删除失败")}},ae=async s=>{try{x(!0);const t=await ue(s.id);t.code===200?(W(t.result),T(!0)):l.error(t.message||"获取详情失败")}catch(t){console.error("获取详情失败:",t),l.error("获取详情失败")}finally{x(!1)}},ne=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"配置名称",dataIndex:"configName",key:"configName",ellipsis:!0},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0,render:s=>s||"-"},{title:"推荐游戏数",dataIndex:"recommendedGameCount",key:"recommendedGameCount",width:120,render:s=>e.jsxs(C,{color:"blue",children:[s,"个"]})},{title:"分类组数",dataIndex:"categoryCount",key:"categoryCount",width:100,render:s=>e.jsxs(C,{color:"green",children:[s,"个"]})},{title:"状态",dataIndex:"status",key:"status",width:100,render:s=>{const t=O.find(p=>p.value===s);return e.jsx(C,{color:t==null?void 0:t.color,children:t==null?void 0:t.label})}},{title:"排序",dataIndex:"sortOrder",key:"sortOrder",width:80},{title:"创建时间",dataIndex:"createTime",key:"createTime",width:180,render:s=>new Date(s).toLocaleString()},{title:"操作",key:"action",width:200,render:(s,t)=>e.jsxs(q,{size:"small",children:[e.jsx(I,{title:"查看详情",children:e.jsx(d,{type:"link",size:"small",icon:e.jsx(Se,{}),onClick:()=>ae(t)})}),e.jsx(I,{title:"编辑",children:e.jsx(d,{type:"link",size:"small",icon:e.jsx(ve,{}),onClick:()=>M("edit",t)})}),e.jsx(I,{title:t.status===1?"禁用":"启用",children:e.jsx(be,{size:"small",checked:t.status===1,onChange:()=>re(t)})}),e.jsx(ke,{title:"确定要删除这个配置吗？",onConfirm:()=>le(t),okText:"确定",cancelText:"取消",children:e.jsx(I,{title:"删除",children:e.jsx(d,{type:"link",size:"small",danger:!0,icon:e.jsx(Te,{})})})})]})}];return e.jsxs(ie,{children:[e.jsxs(pe,{children:[e.jsx("div",{style:{marginBottom:16},children:e.jsx(u,{gutter:16,children:e.jsxs(n,{span:24,children:[e.jsxs(y,{level:4,children:[e.jsx(fe,{style:{marginRight:8}}),"APP首页配置管理"]}),e.jsx(a,{type:"secondary",children:"管理APP首页展示的广告位、推荐游戏和分类组配置"})]})})}),e.jsx(w,{}),e.jsxs(r,{form:k,layout:"inline",style:{marginBottom:16},children:[e.jsx(r.Item,{name:"configName",label:"配置名称",children:e.jsx(P,{placeholder:"请输入配置名称",allowClear:!0})}),e.jsx(r.Item,{name:"status",label:"状态",children:e.jsx(h,{placeholder:"请选择状态",allowClear:!0,style:{width:120},children:O.map(s=>e.jsx(m,{value:s.value,children:s.label},s.value))})}),e.jsx(r.Item,{children:e.jsxs(q,{children:[e.jsx(d,{type:"primary",onClick:ee,children:"搜索"}),e.jsx(d,{onClick:se,children:"重置"}),e.jsx(d,{type:"primary",icon:e.jsx(ye,{}),onClick:()=>M("create"),children:"新增配置"}),e.jsx(d,{icon:e.jsx(we,{}),onClick:()=>c(),children:"刷新"})]})})]}),e.jsx(Ce,{columns:ne,dataSource:G,rowKey:"id",loading:F,pagination:{current:S,pageSize:b,total:E,showSizeChanger:!0,showQuickJumper:!0,showTotal:s=>`共 ${s} 条记录`,onChange:(s,t)=>{v(s),U(t||10)}}})]}),e.jsx(H,{title:R==="create"?"新增APP首页配置":"编辑APP首页配置",open:_,onOk:te,onCancel:A,width:800,confirmLoading:F,destroyOnClose:!0,children:e.jsxs(r,{form:j,layout:"vertical",preserve:!1,children:[e.jsxs(u,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(r.Item,{name:"configName",label:"配置名称",rules:[{required:!0,message:"请输入配置名称"}],children:e.jsx(P,{placeholder:"请输入配置名称"})})}),e.jsx(n,{span:12,children:e.jsx(r.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:e.jsx(h,{placeholder:"请选择状态",children:O.map(s=>e.jsx(m,{value:s.value,children:s.label},s.value))})})})]}),e.jsx(r.Item,{name:"description",label:"配置描述",children:e.jsx($,{placeholder:"请输入配置描述",rows:3,maxLength:500,showCount:!0})}),e.jsxs(u,{gutter:16,children:[e.jsx(n,{span:12,children:e.jsx(r.Item,{name:"topFloatAdId",label:"顶部浮动广告",rules:[{required:!0,message:"请选择顶部浮动广告"}],children:e.jsx(h,{placeholder:"请选择顶部浮动广告",showSearch:!0,children:f.map(s=>e.jsxs(m,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id))})})}),e.jsx(n,{span:12,children:e.jsx(r.Item,{name:"carouselAdId",label:"轮播广告",rules:[{required:!0,message:"请选择轮播广告"}],children:e.jsx(h,{placeholder:"请选择轮播广告",showSearch:!0,children:f.map(s=>e.jsxs(m,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id))})})})]}),e.jsxs(u,{gutter:16,children:[e.jsx(n,{span:8,children:e.jsx(r.Item,{name:"homeGridAdId",label:"首页九宫格",children:e.jsx(h,{placeholder:"请选择首页九宫格",allowClear:!0,showSearch:!0,children:f.map(s=>e.jsxs(m,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id))})})}),e.jsx(n,{span:8,children:e.jsx(r.Item,{name:"splashPopupAdId",label:"开屏弹窗广告",children:e.jsx(h,{placeholder:"请选择开屏弹窗广告",allowClear:!0,showSearch:!0,children:f.map(s=>e.jsxs(m,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id))})})}),e.jsx(n,{span:8,children:e.jsx(r.Item,{name:"floatAdId",label:"浮点广告",children:e.jsx(h,{placeholder:"请选择浮点广告",allowClear:!0,showSearch:!0,children:f.map(s=>e.jsxs(m,{value:s.id,children:[s.title," (",s.adIdentifier,")"]},s.id))})})})]}),e.jsx(u,{gutter:16,children:e.jsx(n,{span:12,children:e.jsx(r.Item,{name:"sortOrder",label:"排序",children:e.jsx(Ie,{placeholder:"请输入排序值",min:0,max:9999,style:{width:"100%"}})})})}),e.jsx(r.Item,{name:"remark",label:"备注",children:e.jsx($,{placeholder:"请输入备注",rows:2,maxLength:500,showCount:!0})})]})}),e.jsx(H,{title:"APP首页配置详情",open:K,onCancel:()=>T(!1),footer:[e.jsx(d,{onClick:()=>T(!1),children:"关闭"},"close")],width:1e3,children:o&&e.jsxs("div",{children:[e.jsx(y,{level:5,children:"基本信息"}),e.jsxs(u,{gutter:16,children:[e.jsxs(n,{span:8,children:[e.jsx(a,{strong:!0,children:"配置名称："}),e.jsx(a,{children:o.configName})]}),e.jsxs(n,{span:8,children:[e.jsx(a,{strong:!0,children:"状态："}),e.jsx(C,{color:o.status===1?"green":"red",children:o.status===1?"启用":"禁用"})]}),e.jsxs(n,{span:8,children:[e.jsx(a,{strong:!0,children:"排序："}),e.jsx(a,{children:o.sortOrder})]})]}),e.jsxs("div",{style:{marginTop:16},children:[e.jsx(a,{strong:!0,children:"描述："}),e.jsx(a,{children:o.description||"-"})]}),e.jsx(w,{}),e.jsx(y,{level:5,children:"广告配置"}),e.jsxs(u,{gutter:16,children:[e.jsxs(n,{span:12,children:[e.jsx(a,{strong:!0,children:"顶部浮动广告："}),e.jsx(a,{children:((V=o.topFloatAd)==null?void 0:V.title)||"-"})]}),e.jsxs(n,{span:12,children:[e.jsx(a,{strong:!0,children:"轮播广告："}),e.jsx(a,{children:((B=o.carouselAd)==null?void 0:B.title)||"-"})]})]}),e.jsx(w,{}),e.jsxs(y,{level:5,children:["推荐游戏 (",o.recommendedGames.length,"个)"]}),o.recommendedGames.map((s,t)=>e.jsx("div",{style:{marginBottom:8},children:e.jsxs(a,{children:[t+1,". ",s.game.name]})},s.id)),e.jsx(w,{}),e.jsxs(y,{level:5,children:["游戏分类组 (",o.gameCategories.length,"个)"]}),o.gameCategories.map((s,t)=>e.jsxs("div",{style:{marginBottom:16},children:[e.jsxs(a,{strong:!0,children:[t+1,". ",s.categoryTitle["zh-CN"]," (",s.games.length,"个游戏)"]}),e.jsx("div",{style:{marginLeft:16,marginTop:4},children:s.games.map((p,g)=>e.jsx("div",{children:e.jsxs(a,{type:"secondary",children:[g+1,". ",p.game.name]})},p.id))})]},s.id))]})})]})};export{Ne as default};
