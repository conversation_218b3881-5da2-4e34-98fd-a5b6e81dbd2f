{"lastValidatedTimestamp": 1750944140514, "projects": {"E:\\inwork\\inapp\\frontend-admin": {"name": "react-antd-admin", "version": "0.0.0"}, "E:\\inwork\\inapp\\frontend-admin\\docs": {"name": "docs", "version": "0.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["docs"]}, "filteredInstall": true}