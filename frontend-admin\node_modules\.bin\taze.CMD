@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules\taze\bin\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules\taze\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules\taze\bin\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules\taze\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\taze@19.0.2\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\taze\bin\taze.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\taze\bin\taze.mjs" %*
)
