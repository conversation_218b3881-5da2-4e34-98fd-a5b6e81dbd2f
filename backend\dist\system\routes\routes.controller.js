"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoutesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const routes_service_1 = require("./routes.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let SystemRoutesController = class SystemRoutesController {
    routesService;
    constructor(routesService) {
        this.routesService = routesService;
    }
    async getRoutes(req) {
        const userRoles = req.user.roles || [];
        const isSuperAdmin = req.user.isSuperAdmin || false;
        console.log(`[ROUTES_CONTROLLER] 获取路由请求:`, {
            userId: req.user.userId || req.user.sub,
            username: req.user.username,
            userRoles,
            isSuperAdmin,
            fullUser: req.user
        });
        const userPermissions = await this.routesService.getUserPermissions(userRoles, isSuperAdmin);
        console.log(`[ROUTES_CONTROLLER] 用户权限:`, userPermissions);
        const result = await this.routesService.getRoutes(userRoles, isSuperAdmin, userPermissions);
        console.log(`[ROUTES_CONTROLLER] 返回路由数量:`, result.length);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getDefaultRoutes() {
        const result = await this.routesService.getDefaultRoutes();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getUserPermissions(req) {
        const userRoles = req.user.roles || [];
        const isSuperAdmin = req.user.isSuperAdmin || false;
        const result = await this.routesService.getUserPermissions(userRoles, isSuperAdmin);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.SystemRoutesController = SystemRoutesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取用户动态路由' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemRoutesController.prototype, "getRoutes", null);
__decorate([
    (0, common_1.Get)('default'),
    (0, swagger_1.ApiOperation)({ summary: '获取默认路由配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SystemRoutesController.prototype, "getDefaultRoutes", null);
__decorate([
    (0, common_1.Get)('permissions'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户权限列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SystemRoutesController.prototype, "getUserPermissions", null);
exports.SystemRoutesController = SystemRoutesController = __decorate([
    (0, swagger_1.ApiTags)('系统动态路由'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('routes'),
    __metadata("design:paramtypes", [routes_service_1.SystemRoutesService])
], SystemRoutesController);
//# sourceMappingURL=routes.controller.js.map