const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库连接配置
const pool = new Pool({
  host: '**************',
  port: 5435,
  database: 'inapp2',
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
});

async function fixGoldRechargeConfigTable() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 连接到数据库...');
    
    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'fix-gold-recharge-config-table.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('📋 开始修复金币充值配置表...');
    
    // 执行SQL
    await client.query(sql);
    
    console.log('✅ 金币充值配置表修复完成！');
    
    // 验证修复结果
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'gold_recharge_configs' 
      ORDER BY ordinal_position
    `);
    
    console.log('\n📊 修复后的表结构:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // 检查数据
    const dataResult = await client.query('SELECT id, tier_name, gold_amount, price FROM gold_recharge_configs ORDER BY id');
    console.log('\n📊 表数据:');
    dataResult.rows.forEach(row => {
      console.log(`  ID ${row.id}: ${row.tier_name} - ${row.gold_amount}金币 - ¥${row.price}`);
    });
    
  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行修复
fixGoldRechargeConfigTable();
