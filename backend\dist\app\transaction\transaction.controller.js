"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const transaction_service_1 = require("./transaction.service");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const transaction_dto_1 = require("./dto/transaction.dto");
let TransactionController = class TransactionController {
    transactionService;
    constructor(transactionService) {
        this.transactionService = transactionService;
    }
    async createCashTransaction(createTransactionDto) {
        const result = await this.transactionService.createCashTransaction(createTransactionDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async createGoldTransaction(createTransactionDto) {
        const result = await this.transactionService.createGoldTransaction(createTransactionDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async createRechargeTransaction(createTransactionDto) {
        const result = await this.transactionService.createRechargeTransaction(createTransactionDto);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async getUserAssetStatistics(userId) {
        const result = await this.transactionService.getUserAssetStatistics(userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getCashStatistics(userId) {
        const result = await this.transactionService.getCashStatistics(userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getGoldStatistics(userId) {
        const result = await this.transactionService.getGoldStatistics(userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getRechargeStatistics(userId) {
        const result = await this.transactionService.getRechargeStatistics(userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getCashTransactionList(userId, query) {
        const result = await this.transactionService.getCashTransactionList({ ...query, userId });
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getGoldTransactionList(userId, query) {
        const result = await this.transactionService.getGoldTransactionList({ ...query, userId });
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getRechargeTransactionList(userId, query) {
        const result = await this.transactionService.getRechargeTransactionList({ ...query, userId });
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.TransactionController = TransactionController;
__decorate([
    (0, common_1.Post)('cash'),
    (0, swagger_1.ApiOperation)({ summary: '创建现金交易记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transaction_dto_1.CreateTransactionDto]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "createCashTransaction", null);
__decorate([
    (0, common_1.Post)('gold'),
    (0, swagger_1.ApiOperation)({ summary: '创建金币交易记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transaction_dto_1.CreateTransactionDto]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "createGoldTransaction", null);
__decorate([
    (0, common_1.Post)('recharge'),
    (0, swagger_1.ApiOperation)({ summary: '创建充值余额交易记录' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [transaction_dto_1.CreateTransactionDto]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "createRechargeTransaction", null);
__decorate([
    (0, common_1.Get)('users/:userId/statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户资产统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.UserAssetStatisticsResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getUserAssetStatistics", null);
__decorate([
    (0, common_1.Get)('users/:userId/cash/statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户现金统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionStatisticsResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getCashStatistics", null);
__decorate([
    (0, common_1.Get)('users/:userId/gold/statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户金币统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionStatisticsResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getGoldStatistics", null);
__decorate([
    (0, common_1.Get)('users/:userId/recharge/statistics'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户充值余额统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionStatisticsResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getRechargeStatistics", null);
__decorate([
    (0, common_1.Get)('users/:userId/cash/list'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户现金交易明细' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionListResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getCashTransactionList", null);
__decorate([
    (0, common_1.Get)('users/:userId/gold/list'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户金币交易明细' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionListResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getGoldTransactionList", null);
__decorate([
    (0, common_1.Get)('users/:userId/recharge/list'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户充值余额交易明细' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功', type: transaction_dto_1.TransactionListResponseDto }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], TransactionController.prototype, "getRechargeTransactionList", null);
exports.TransactionController = TransactionController = __decorate([
    (0, swagger_1.ApiTags)('交易流水管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('transactions'),
    __metadata("design:paramtypes", [transaction_service_1.TransactionService])
], TransactionController);
//# sourceMappingURL=transaction.controller.js.map