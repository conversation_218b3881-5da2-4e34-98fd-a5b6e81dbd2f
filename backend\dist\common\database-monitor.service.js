"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var DatabaseMonitorService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMonitorService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
let DatabaseMonitorService = DatabaseMonitorService_1 = class DatabaseMonitorService {
    dataSource;
    logger = new common_1.Logger(DatabaseMonitorService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async checkConnectionPoolStatus() {
        try {
            if (!this.dataSource.isInitialized) {
                this.logger.warn('数据库连接未初始化');
                return;
            }
            const driver = this.dataSource.driver;
            if (driver && driver.master && driver.master.pool) {
                const pool = driver.master.pool;
                const poolInfo = {
                    totalCount: pool.totalCount || 0,
                    idleCount: pool.idleCount || 0,
                    waitingCount: pool.waitingCount || 0,
                    maxConnections: pool.options?.max || 'unknown',
                    minConnections: pool.options?.min || 'unknown',
                };
                const usageRate = poolInfo.totalCount > 0 ?
                    (poolInfo.totalCount - poolInfo.idleCount) / poolInfo.totalCount : 0;
                if (usageRate > 0.8) {
                    this.logger.warn(`数据库连接池使用率较高: ${Math.round(usageRate * 100)}%`, poolInfo);
                }
                else if (poolInfo.waitingCount > 0) {
                    this.logger.warn(`有 ${poolInfo.waitingCount} 个连接在等待`, poolInfo);
                }
                if (new Date().getMinutes() % 10 === 0) {
                    this.logger.log(`连接池状态: 总连接=${poolInfo.totalCount}, 空闲=${poolInfo.idleCount}, 等待=${poolInfo.waitingCount}`);
                }
            }
        }
        catch (error) {
            this.logger.error('检查连接池状态失败:', error);
        }
    }
    async performHealthCheck() {
        const startTime = Date.now();
        try {
            await this.dataSource.query('SELECT 1');
            const latency = Date.now() - startTime;
            const driver = this.dataSource.driver;
            let connectionCount = 0;
            if (driver && driver.master && driver.master.pool) {
                connectionCount = driver.master.pool.totalCount || 0;
            }
            return {
                isHealthy: true,
                latency,
                connectionCount,
            };
        }
        catch (error) {
            const latency = Date.now() - startTime;
            this.logger.error('数据库健康检查失败:', error);
            return {
                isHealthy: false,
                latency,
                connectionCount: 0,
                error: error.message,
            };
        }
    }
    async getDatabaseStats() {
        try {
            const queries = [
                `SELECT count(*) as active_connections 
         FROM pg_stat_activity 
         WHERE state = 'active' AND application_name = 'inapp2-backend'`,
                `SELECT pg_size_pretty(pg_database_size(current_database())) as database_size`,
                `SELECT query, calls, total_time, mean_time 
         FROM pg_stat_statements 
         WHERE application_name = 'inapp2-backend'
         ORDER BY mean_time DESC 
         LIMIT 5`,
            ];
            const results = await Promise.allSettled(queries.map(query => this.dataSource.query(query)));
            return {
                activeConnections: results[0].status === 'fulfilled' ? results[0].value[0] : null,
                databaseSize: results[1].status === 'fulfilled' ? results[1].value[0] : null,
                slowQueries: results[2].status === 'fulfilled' ? results[2].value : null,
            };
        }
        catch (error) {
            this.logger.error('获取数据库统计信息失败:', error);
            return null;
        }
    }
    async optimizeConnections() {
        try {
            const driver = this.dataSource.driver;
            if (driver && driver.master && driver.master.pool) {
                const pool = driver.master.pool;
                if (pool.idleCount > 10) {
                    this.logger.log('清理多余的空闲连接');
                }
            }
            await this.dataSource.query('ANALYZE');
            this.logger.log('数据库统计信息已更新');
        }
        catch (error) {
            this.logger.error('优化数据库连接失败:', error);
        }
    }
    startPeriodicMonitoring() {
        setInterval(() => {
            this.checkConnectionPoolStatus();
        }, 60 * 1000);
        setInterval(() => {
            this.logger.log('开始执行每小时数据库优化');
            this.optimizeConnections();
        }, 60 * 60 * 1000);
        setInterval(() => {
            this.logger.log('开始执行每日数据库统计');
            this.getDatabaseStats().then(stats => {
                if (stats) {
                    this.logger.log('每日数据库统计:', stats);
                }
            });
        }, 24 * 60 * 60 * 1000);
    }
};
exports.DatabaseMonitorService = DatabaseMonitorService;
exports.DatabaseMonitorService = DatabaseMonitorService = DatabaseMonitorService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectDataSource)()),
    __metadata("design:paramtypes", [typeorm_2.DataSource])
], DatabaseMonitorService);
//# sourceMappingURL=database-monitor.service.js.map