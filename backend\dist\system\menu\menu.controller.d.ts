import { SystemMenuService } from './menu.service';
import { CreateMenuDto } from './dto/create-menu.dto';
import { UpdateMenuDto } from './dto/update-menu.dto';
export declare class SystemMenuController {
    private readonly menuService;
    constructor(menuService: SystemMenuService);
    create(createMenuDto: CreateMenuDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysMenu;
    }>;
    findAll(): Promise<{
        code: number;
        message: string;
        result: {
            list: {
                name: string;
                currentActiveMenu: string;
                iframeLink: string;
                keepAlive: number;
                externalLink: string;
                hideInMenu: number;
                ignoreAccess: number;
                id: number;
                title: string;
                path: string;
                icon: string;
                parentId: number;
                order: number;
                menuType: number;
                status: number;
                component: string;
                meta: any;
                permissionCode: string;
                buttonPermissions: string[];
                parent: import("../entities").SysMenu;
                children: import("../entities").SysMenu[];
                createTime: Date;
                updateTime: Date;
            }[];
            total: number;
        };
    }>;
    findAllFlat(): Promise<{
        code: number;
        message: string;
        result: {
            name: string;
            currentActiveMenu: string;
            iframeLink: string;
            keepAlive: number;
            externalLink: string;
            hideInMenu: number;
            ignoreAccess: number;
            id: number;
            title: string;
            path: string;
            icon: string;
            parentId: number;
            order: number;
            menuType: number;
            status: number;
            component: string;
            meta: any;
            permissionCode: string;
            buttonPermissions: string[];
            parent: import("../entities").SysMenu;
            children: import("../entities").SysMenu[];
            createTime: Date;
            updateTime: Date;
        }[];
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysMenu;
    }>;
    update(id: string, updateMenuDto: UpdateMenuDto): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysMenu;
    }>;
    remove(id: string): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
}
