export declare class QueryApplicationDto {
    page?: number;
    pageSize?: number;
    search?: string;
    providerId?: number;
    categories?: string[];
    status?: string;
    tags?: string[];
    platforms?: string[];
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    features?: string[];
    hasDemo?: boolean;
    supplierIdentifier?: string;
}
export interface ApplicationListResponse {
    list: any[];
    total: number;
    page: number;
    pageSize: number;
}
