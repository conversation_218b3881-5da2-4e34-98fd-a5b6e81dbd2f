"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderEnvironment = void 0;
const typeorm_1 = require("typeorm");
const application_provider_entity_1 = require("./application-provider.entity");
let ProviderEnvironment = class ProviderEnvironment {
    id;
    providerId;
    environmentType;
    isActive;
    apiBaseUrl;
    apiKey;
    secretKey;
    operatorToken;
    serverIpWhitelist;
    extraConfig;
    provider;
    createdAt;
    updatedAt;
};
exports.ProviderEnvironment = ProviderEnvironment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], ProviderEnvironment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'provider_id' }),
    __metadata("design:type", Number)
], ProviderEnvironment.prototype, "providerId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'environment_type', length: 20 }),
    __metadata("design:type", String)
], ProviderEnvironment.prototype, "environmentType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_active', default: true }),
    __metadata("design:type", Boolean)
], ProviderEnvironment.prototype, "isActive", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_base_url', length: 512 }),
    __metadata("design:type", String)
], ProviderEnvironment.prototype, "apiBaseUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'api_key', length: 255, nullable: true }),
    __metadata("design:type", String)
], ProviderEnvironment.prototype, "apiKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'secret_key', length: 512, nullable: true }),
    __metadata("design:type", String)
], ProviderEnvironment.prototype, "secretKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'operator_token', length: 255, nullable: true }),
    __metadata("design:type", String)
], ProviderEnvironment.prototype, "operatorToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'server_ip_whitelist', type: 'jsonb', nullable: true }),
    __metadata("design:type", Array)
], ProviderEnvironment.prototype, "serverIpWhitelist", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'extra_config', type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], ProviderEnvironment.prototype, "extraConfig", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => application_provider_entity_1.ApplicationProvider, (provider) => provider.environments, {
        onDelete: 'CASCADE',
    }),
    (0, typeorm_1.JoinColumn)({ name: 'provider_id' }),
    __metadata("design:type", application_provider_entity_1.ApplicationProvider)
], ProviderEnvironment.prototype, "provider", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], ProviderEnvironment.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], ProviderEnvironment.prototype, "updatedAt", void 0);
exports.ProviderEnvironment = ProviderEnvironment = __decorate([
    (0, typeorm_1.Entity)('provider_environments'),
    (0, typeorm_1.Unique)(['providerId', 'environmentType'])
], ProviderEnvironment);
//# sourceMappingURL=provider-environment.entity.js.map