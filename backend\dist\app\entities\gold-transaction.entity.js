"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoldTransactionStatusLabels = exports.GoldTransactionTypeLabels = exports.GoldTransaction = exports.GoldTransactionType = exports.GoldTransactionStatus = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
var GoldTransactionStatus;
(function (GoldTransactionStatus) {
    GoldTransactionStatus[GoldTransactionStatus["INCOME"] = 1] = "INCOME";
    GoldTransactionStatus[GoldTransactionStatus["EXPENSE"] = 2] = "EXPENSE";
})(GoldTransactionStatus || (exports.GoldTransactionStatus = GoldTransactionStatus = {}));
var GoldTransactionType;
(function (GoldTransactionType) {
    GoldTransactionType[GoldTransactionType["BET"] = 1] = "BET";
    GoldTransactionType[GoldTransactionType["WIN"] = 2] = "WIN";
    GoldTransactionType[GoldTransactionType["VIP_CARD_CLAIM"] = 3] = "VIP_CARD_CLAIM";
    GoldTransactionType[GoldTransactionType["GIFT"] = 4] = "GIFT";
    GoldTransactionType[GoldTransactionType["TRADE_ACQUIRE"] = 5] = "TRADE_ACQUIRE";
    GoldTransactionType[GoldTransactionType["ACTIVITY_CLAIM"] = 6] = "ACTIVITY_CLAIM";
})(GoldTransactionType || (exports.GoldTransactionType = GoldTransactionType = {}));
let GoldTransaction = class GoldTransaction {
    id;
    userId;
    transactionId;
    amount;
    balanceBefore;
    balanceAfter;
    status;
    transactionType;
    description;
    createdAt;
    user;
};
exports.GoldTransaction = GoldTransaction;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transaction_id' }),
    __metadata("design:type", String)
], GoldTransaction.prototype, "transactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_before', type: 'bigint' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "balanceBefore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'balance_after', type: 'bigint' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "balanceAfter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', comment: '状态：1-收入，2-支出' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-会员卡领取，4-赠送，5-交易获取，6-活动领取' }),
    __metadata("design:type", Number)
], GoldTransaction.prototype, "transactionType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 255 }),
    __metadata("design:type", String)
], GoldTransaction.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], GoldTransaction.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], GoldTransaction.prototype, "user", void 0);
exports.GoldTransaction = GoldTransaction = __decorate([
    (0, typeorm_1.Entity)('gold_transactions')
], GoldTransaction);
exports.GoldTransactionTypeLabels = {
    [GoldTransactionType.BET]: '下注',
    [GoldTransactionType.WIN]: '赢金',
    [GoldTransactionType.VIP_CARD_CLAIM]: '会员卡领取',
    [GoldTransactionType.GIFT]: '赠送',
    [GoldTransactionType.TRADE_ACQUIRE]: '交易获取',
    [GoldTransactionType.ACTIVITY_CLAIM]: '活动领取',
};
exports.GoldTransactionStatusLabels = {
    [GoldTransactionStatus.INCOME]: '收入',
    [GoldTransactionStatus.EXPENSE]: '支出',
};
//# sourceMappingURL=gold-transaction.entity.js.map