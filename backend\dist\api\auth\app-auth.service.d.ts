import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { AppUser, MarketingChannel, MarketingAd, PromotionalPage, DeviceLogRealtime } from '../../app/entities';
import { AppLoginDto, AppRegisterDto, AppLoginResponseDto } from './dto/app-login.dto';
export declare class AppAuthService {
    private readonly userRepository;
    private readonly channelRepository;
    private readonly adRepository;
    private readonly pageRepository;
    private readonly deviceLogRepository;
    private readonly jwtService;
    private readonly configService;
    constructor(userRepository: Repository<AppUser>, channelRepository: Repository<MarketingChannel>, adRepository: Repository<MarketingAd>, pageRepository: Repository<PromotionalPage>, deviceLogRepository: Repository<DeviceLogRealtime>, jwtService: JwtService, configService: ConfigService);
    login(loginDto: AppLoginDto, ip: string, userAgent: string): Promise<AppLoginResponseDto>;
    register(registerDto: AppRegisterDto, ip: string, userAgent: string): Promise<AppLoginResponseDto>;
    private generateTokens;
    private recordDeviceLog;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
}
