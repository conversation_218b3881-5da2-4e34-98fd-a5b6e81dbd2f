{"version": 3, "file": "query-channel.dto.js", "sourceRoot": "", "sources": ["../../../../src/app/channel/dto/query-channel.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAuE;AACvE,yDAAyC;AAEzC,MAAa,eAAe;IAK1B,IAAI,GAAY,CAAC,CAAC;IAMlB,QAAQ,GAAY,EAAE,CAAC;IAKvB,MAAM,CAAU;IAOhB,MAAM,CAAU;IAKhB,MAAM,GAAY,WAAW,CAAC;IAM9B,SAAS,GAAoB,MAAM,CAAC;CACrC;AAnCD,0CAmCC;AA9BC;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/D,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;;6CACO;AAMlB;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAClE,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;;iDACY;AAKvB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC5D,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACK;AAOhB;IALC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC7E,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;+CACG;AAKhB;IAHC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC3E,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;+CACmB;AAM9B;IAJC,IAAA,qBAAW,EAAC,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC7F,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,sBAAI,EAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;;kDACc"}