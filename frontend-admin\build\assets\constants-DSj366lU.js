import{j as a}from"./index-CHjq8S-S.js";import{an as r}from"./antd-CXPM1OiB.js";import"./react-BUTTOX-3.js";function d(e){return[{title:"ID",dataIndex:"id",key:"id",width:80,search:!1},{title:e("workbook.type"),dataIndex:"type",key:"type",width:120,ellipsis:!0},{title:e("workbook.text"),dataIndex:"text",key:"text",width:150,ellipsis:!0},{title:e("workbook.value"),dataIndex:"value",key:"value",width:120,ellipsis:!0},{title:e("workbook.valueType"),dataIndex:"valueType",key:"valueType",width:100,search:!1,render:t=>{const o={text:"blue",number:"green",json:"orange"},s={text:e("workbook.valueTypeOptions.text"),number:e("workbook.valueTypeOptions.number"),json:e("workbook.valueTypeOptions.json")};return a.jsx(r,{color:o[t]||"default",children:s[t]||t})},valueEnum:{text:{text:e("workbook.valueTypeOptions.text")},number:{text:e("workbook.valueTypeOptions.number")},json:{text:e("workbook.valueTypeOptions.json")}}},{title:e("workbook.remark"),dataIndex:"remark",key:"remark",width:200,search:!1,ellipsis:!0,render:t=>t||"-"},{title:e("workbook.order"),dataIndex:"order",key:"order",width:80,search:!1},{title:e("common.status"),dataIndex:"status",key:"status",width:100,valueType:"select",render:(t,o)=>a.jsx(r,{color:o.status===1?"success":"default",children:o.status===1?e("common.enabled"):e("common.disabled")}),valueEnum:{1:{text:e("common.enabled")},0:{text:e("common.disabled")}}},{title:e("common.createTime"),dataIndex:"createTime",key:"createTime",width:180,search:!1,valueType:"dateTime"}]}export{d as getConstantColumns};
