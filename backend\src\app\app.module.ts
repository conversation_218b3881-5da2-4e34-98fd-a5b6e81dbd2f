import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';

// App entities
import {
  AppUser,
  AppCategory,
  AppProduct,
  AppOrder,
  AppOrderItem,
  AppUserAddress,
  ApplicationProvider,
  ProviderEnvironment,
  Application,
  MarketingChannel,
  MarketingAd,
  PromotionalPage,
  RiskEvent,
  DeviceLogRealtime,
  DeviceLogHistory,
  CashTransaction,
  GoldTransaction,
  RechargeTransaction,
} from './entities';
import { SupplierModule } from './supplier/supplier.module';
import { ApplicationModule } from './application/application.module';
import { ChannelModule } from './channel/channel.module';
import { TransactionModule } from './transaction/transaction.module';
import { ConfigModule as AppConfigModule } from './config/config.module';
// UserManagementModule 已移至 API 模块

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AppUser,
      AppCategory,
      AppProduct,
      AppOrder,
      AppOrderItem,
      AppUserAddress,
      ApplicationProvider,
      ProviderEnvironment,
      Application,
      MarketingChannel,
      MarketingAd,
      PromotionalPage,
      RiskEvent,
      DeviceLogRealtime,
      DeviceLogHistory,
      CashTransaction,
      GoldTransaction,
      RechargeTransaction,
    ]),
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get('JWT_EXPIRES_IN'),
        },
      }),
      inject: [ConfigService],
    }),
    SupplierModule,
    ApplicationModule,
    ChannelModule,
    TransactionModule,
    AppConfigModule,
    // UserManagementModule 已移至 API 模块
  ],
  controllers: [
    // TODO: 添加控制器
    // AppAuthController,
    // AppUserController,
    // AppProductController,
    // AppCategoryController,
    // AppOrderController,
  ],
  providers: [
    // TODO: 添加服务
    // AppAuthService,
    // AppUserService,
    // AppProductService,
    // AppCategoryService,
    // AppOrderService,
  ],
  exports: [
    TypeOrmModule,
  ],
})
export class AppBusinessModule {}
