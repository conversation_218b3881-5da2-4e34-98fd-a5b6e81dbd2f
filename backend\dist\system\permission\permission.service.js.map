{"version": 3, "file": "permission.service.js", "sourceRoot": "", "sources": ["../../../src/system/permission/permission.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,6CAAmD;AACnD,qCAAqC;AACrC,6EAAkE;AAM3D,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAGxB;IAFV,YAEU,oBAA+C;QAA/C,yBAAoB,GAApB,oBAAoB,CAA2B;IACtD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,mBAAwC;QACnD,MAAM,EAAE,IAAI,EAAE,GAAG,mBAAmB,CAAC;QAGrC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACjE,KAAK,EAAE,EAAE,IAAI,EAAE;SAChB,CAAC,CAAC;QACH,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACzE,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,kBAAsC;QAClD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,kBAAkB,CAAC;QAEpF,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;QAEhF,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE;gBAClD,IAAI,EAAE,IAAI,IAAI,GAAG;aAClB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,4BAA4B,EAAE;gBAClD,IAAI,EAAE,IAAI,IAAI,GAAG;aAClB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,yBAAyB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,CAAC,6BAA6B,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACnE,CAAC;QAED,YAAY,CAAC,OAAO,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;QAEtD,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,YAAY;aAC5B,IAAI,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;aAC9B,IAAI,CAAC,QAAQ,CAAC;aACd,OAAO,EAAE,CAAC;QAEb,OAAO;YACL,IAAI;YACJ,KAAK;YACL,OAAO;YACP,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YACzD,KAAK,EAAE,EAAE,EAAE,EAAE;SACd,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,mBAAwC;QAC/D,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAG1C,IAAI,mBAAmB,CAAC,IAAI,IAAI,mBAAmB,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,EAAE,CAAC;YAC7E,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;gBACjE,KAAK,EAAE,EAAE,IAAI,EAAE,mBAAmB,CAAC,IAAI,EAAE;aAC1C,CAAC,CAAC;YACH,IAAI,kBAAkB,EAAE,CAAC;gBACvB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC;QACH,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;QAC/C,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACnD,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC1C,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC;YACrD,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE;SAC1C,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAY;QAC3B,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAC1C,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE;YAC1B,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC;YACrD,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAhHY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;qCACF,oBAAU;GAH/B,uBAAuB,CAgHnC"}