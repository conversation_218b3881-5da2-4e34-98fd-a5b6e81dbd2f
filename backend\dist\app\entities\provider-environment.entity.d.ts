import { ApplicationProvider } from './application-provider.entity';
export declare class ProviderEnvironment {
    id: number;
    providerId: number;
    environmentType: 'staging' | 'production';
    isActive: boolean;
    apiBaseUrl: string;
    apiKey: string;
    secretKey: string;
    operatorToken: string;
    serverIpWhitelist: string[];
    extraConfig: any;
    provider: ApplicationProvider;
    createdAt: Date;
    updatedAt: Date;
}
