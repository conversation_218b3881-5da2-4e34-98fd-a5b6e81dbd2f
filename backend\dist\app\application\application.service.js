"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const crypto_1 = require("crypto");
const entities_1 = require("../entities");
let ApplicationService = class ApplicationService {
    applicationRepository;
    constructor(applicationRepository) {
        this.applicationRepository = applicationRepository;
    }
    async create(createApplicationDto) {
        const existingApp = await this.applicationRepository.findOne({
            where: { appCode: createApplicationDto.appCode },
        });
        if (existingApp) {
            throw new common_1.ConflictException(`应用代码 ${createApplicationDto.appCode} 已存在`);
        }
        const appUuid = (0, crypto_1.randomUUID)();
        const application = this.applicationRepository.create({
            ...createApplicationDto,
            appUuid,
        });
        const savedApplication = await this.applicationRepository.save(application);
        return this.findOne(savedApplication.id);
    }
    async findAll(queryDto) {
        const { page = 1, pageSize = 10, search, providerId, categories, status, tags, platforms, features, hasDemo, supplierIdentifier, sortBy = 'createdAt', sortOrder = 'DESC', } = queryDto;
        const queryBuilder = this.applicationRepository
            .createQueryBuilder('app')
            .leftJoinAndSelect('app.provider', 'provider');
        if (search) {
            queryBuilder.andWhere('(app.name ILIKE :search OR app.appCode ILIKE :search)', { search: `%${search}%` });
        }
        if (providerId) {
            queryBuilder.andWhere('app.providerId = :providerId', { providerId });
        }
        if (categories && categories.length > 0) {
            const categoryConditions = categories.map((category, index) => `app.categories @> '"${category}"'`).join(' OR ');
            queryBuilder.andWhere(`(${categoryConditions})`);
        }
        if (status) {
            queryBuilder.andWhere('app.status = :status', { status });
        }
        if (tags && tags.length > 0) {
            const tagConditions = tags.map((tag, index) => `app.tags @> '"${tag}"'`).join(' OR ');
            queryBuilder.andWhere(`(${tagConditions})`);
        }
        if (platforms && platforms.length > 0) {
            const platformConditions = platforms.map((platform, index) => `app.platforms @> '"${platform}"'`).join(' OR ');
            queryBuilder.andWhere(`(${platformConditions})`);
        }
        if (features && features.length > 0) {
            const featureConditions = features.map((feature, index) => `app.features @> '"${feature}"'`).join(' OR ');
            queryBuilder.andWhere(`(${featureConditions})`);
        }
        if (hasDemo !== undefined) {
            queryBuilder.andWhere('app.hasDemo = :hasDemo', { hasDemo });
        }
        if (supplierIdentifier) {
            queryBuilder.andWhere('app.supplierIdentifier = :supplierIdentifier', { supplierIdentifier });
        }
        const validSortFields = ['createdAt', 'updatedAt', 'name', 'appCode', 'status'];
        const sortField = validSortFields.includes(sortBy || '') ? sortBy : 'createdAt';
        queryBuilder.orderBy(`app.${sortField}`, sortOrder);
        const offset = (page - 1) * pageSize;
        queryBuilder.skip(offset).take(pageSize);
        const [list, total] = await queryBuilder.getManyAndCount();
        return {
            list,
            total,
            page,
            pageSize,
        };
    }
    async findOne(id) {
        const application = await this.applicationRepository.findOne({
            where: { id },
            relations: ['provider'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`应用 ID ${id} 不存在`);
        }
        return application;
    }
    async findByUuid(appUuid) {
        const application = await this.applicationRepository.findOne({
            where: { appUuid },
            relations: ['provider'],
        });
        if (!application) {
            throw new common_1.NotFoundException(`应用 UUID ${appUuid} 不存在`);
        }
        return application;
    }
    async update(id, updateApplicationDto) {
        const application = await this.findOne(id);
        if (updateApplicationDto.appCode && updateApplicationDto.appCode !== application.appCode) {
            const existingApp = await this.applicationRepository.findOne({
                where: { appCode: updateApplicationDto.appCode },
            });
            if (existingApp) {
                throw new common_1.ConflictException(`应用代码 ${updateApplicationDto.appCode} 已存在`);
            }
        }
        Object.assign(application, updateApplicationDto);
        await this.applicationRepository.save(application);
        return this.findOne(id);
    }
    async remove(id) {
        const application = await this.findOne(id);
        await this.applicationRepository.remove(application);
        return { message: '删除成功' };
    }
    async toggleStatus(id, status) {
        const application = await this.findOne(id);
        application.status = status;
        await this.applicationRepository.save(application);
        return this.findOne(id);
    }
};
exports.ApplicationService = ApplicationService;
exports.ApplicationService = ApplicationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.Application)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ApplicationService);
//# sourceMappingURL=application.service.js.map