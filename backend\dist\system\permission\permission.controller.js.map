{"version": 3, "file": "permission.controller.js", "sourceRoot": "", "sources": ["../../../src/system/permission/permission.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAUwB;AACxB,6CAKyB;AACzB,6DAA+D;AAC/D,uEAAkE;AAClE,uEAAkE;AAClE,qEAAgE;AAChE,kEAAmE;AAM5D,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IACR;IAA7B,YAA6B,iBAA0C;QAA1C,sBAAiB,GAAjB,iBAAiB,CAAyB;IAAG,CAAC;IAKrE,AAAN,KAAK,CAAC,MAAM,CAAS,mBAAwC;QAC3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACxE,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,OAAO,CAAU,kBAAsC;QAC3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;QACxE,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,aAAa;QACjB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,CAAC;QAC5D,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,UAAU,CAAgB,IAAY;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC7D,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,OAAO,CAAc,EAAU;QACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QACzD,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,MAAM,CACG,EAAU,EACf,mBAAwC;QAEhD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAC7E,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;IAKK,AAAN,KAAK,CAAC,MAAM,CAAc,EAAU;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACxD,OAAO;YACL,IAAI,EAAE,GAAG;YACT,OAAO,EAAE,MAAM;YACf,MAAM;SACP,CAAC;IACJ,CAAC;CACF,CAAA;AAzFY,gEAA0B;AAM/B;IAHL,IAAA,aAAI,GAAE;IACN,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAsB,2CAAmB;;wDAO5D;AAKK;IAHL,IAAA,YAAG,EAAC,MAAM,CAAC;IACX,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACnC,WAAA,IAAA,cAAK,GAAE,CAAA;;qCAAqB,yCAAkB;;yDAO5D;AAKK;IAHL,IAAA,YAAG,EAAC,QAAQ,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACrC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;;;;+DAQjD;AAKK;IAHL,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACvC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAChC,WAAA,IAAA,cAAK,EAAC,MAAM,CAAC,CAAA;;;;4DAO9B;AAKK;IAHL,IAAA,YAAG,EAAC,KAAK,CAAC;IACV,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACnC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACnC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;yDAOzB;AAKK;IAHL,IAAA,cAAK,EAAC,KAAK,CAAC;IACZ,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IAE/C,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;IACX,WAAA,IAAA,aAAI,GAAE,CAAA;;6CAAsB,2CAAmB;;wDAQjD;AAKK;IAHL,IAAA,eAAM,EAAC,KAAK,CAAC;IACb,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IACjC,IAAA,qBAAW,EAAC,EAAE,MAAM,EAAE,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,CAAC;IACpC,WAAA,IAAA,cAAK,EAAC,IAAI,CAAC,CAAA;;;;wDAOxB;qCAxFU,0BAA0B;IAJtC,IAAA,iBAAO,EAAC,QAAQ,CAAC;IACjB,IAAA,uBAAa,GAAE;IACf,IAAA,kBAAS,EAAC,mCAAkB,CAAC;IAC7B,IAAA,mBAAU,EAAC,aAAa,CAAC;qCAEwB,4CAAuB;GAD5D,0BAA0B,CAyFtC"}