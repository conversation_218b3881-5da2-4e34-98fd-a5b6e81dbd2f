"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseHealthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const database_health_service_1 = require("./database-health.service");
let DatabaseHealthController = class DatabaseHealthController {
    databaseHealthService;
    constructor(databaseHealthService) {
        this.databaseHealthService = databaseHealthService;
    }
    async checkHealth() {
        return await this.databaseHealthService.checkHealth();
    }
    async getPerformanceStats() {
        return await this.databaseHealthService.getPerformanceStats();
    }
    async testLatency(iterations) {
        const testIterations = iterations && iterations > 0 && iterations <= 20 ? iterations : 5;
        return await this.databaseHealthService.testLatency(testIterations);
    }
};
exports.DatabaseHealthController = DatabaseHealthController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '检查数据库健康状态' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '数据库健康状态',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', enum: ['healthy', 'unhealthy'] },
                latency: { type: 'number', description: '延迟(毫秒)' },
                connectionCount: { type: 'number', description: '当前连接数' },
                details: { type: 'object', description: '详细信息' },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "checkHealth", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: '获取数据库性能统计' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '数据库性能统计信息' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "getPerformanceStats", null);
__decorate([
    (0, common_1.Get)('latency'),
    (0, swagger_1.ApiOperation)({ summary: '测试数据库连接延迟' }),
    (0, swagger_1.ApiQuery)({
        name: 'iterations',
        required: false,
        type: Number,
        description: '测试次数，默认5次'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '延迟测试结果',
        schema: {
            type: 'object',
            properties: {
                min: { type: 'number', description: '最小延迟(毫秒)' },
                max: { type: 'number', description: '最大延迟(毫秒)' },
                avg: { type: 'number', description: '平均延迟(毫秒)' },
                results: {
                    type: 'array',
                    items: { type: 'number' },
                    description: '每次测试的结果'
                },
            },
        },
    }),
    __param(0, (0, common_1.Query)('iterations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], DatabaseHealthController.prototype, "testLatency", null);
exports.DatabaseHealthController = DatabaseHealthController = __decorate([
    (0, swagger_1.ApiTags)('数据库健康检查'),
    (0, common_1.Controller)('health/database'),
    __metadata("design:paramtypes", [database_health_service_1.DatabaseHealthService])
], DatabaseHealthController);
//# sourceMappingURL=database-health.controller.js.map