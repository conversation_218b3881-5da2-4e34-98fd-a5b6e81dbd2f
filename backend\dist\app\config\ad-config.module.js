"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const ad_config_service_1 = require("./ad-config.service");
const ad_config_controller_1 = require("./ad-config.controller");
const ad_config_entity_1 = require("./entities/ad-config.entity");
let AdConfigModule = class AdConfigModule {
};
exports.AdConfigModule = AdConfigModule;
exports.AdConfigModule = AdConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                ad_config_entity_1.AdConfig,
            ]),
        ],
        controllers: [ad_config_controller_1.AdConfigController],
        providers: [ad_config_service_1.AdConfigService],
        exports: [ad_config_service_1.AdConfigService],
    })
], AdConfigModule);
//# sourceMappingURL=ad-config.module.js.map