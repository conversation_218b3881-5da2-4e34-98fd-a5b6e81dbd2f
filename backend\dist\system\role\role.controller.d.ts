import { SystemRoleService } from './role.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { QueryRoleDto } from './dto/query-role.dto';
export declare class SystemRoleController {
    private readonly roleService;
    constructor(roleService: SystemRoleService);
    private checkSuperAdminPermission;
    create(createRoleDto: CreateRoleDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysRole;
    }>;
    getSimpleRoles(): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysRole[];
    }>;
    findAll(queryRoleDto: QueryRoleDto): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").SysRole[];
            total: number;
            current: number;
            pageSize: number;
        };
    }>;
    findAllSimple(): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysRole[];
    }>;
    findOne(id: string): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysRole;
    }>;
    update(id: string, updateRoleDto: UpdateRoleDto, req: any): Promise<{
        code: number;
        message: string;
        result: import("../entities").SysRole;
    }>;
    remove(id: string, req: any): Promise<{
        code: number;
        message: string;
        result: {
            message: string;
        };
    }>;
}
