"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VipConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vip_config_service_1 = require("./vip-config.service");
const daily_reward_service_1 = require("./daily-reward.service");
const create_vip_config_dto_1 = require("./dto/create-vip-config.dto");
const update_vip_config_dto_1 = require("./dto/update-vip-config.dto");
const vip_config_query_dto_1 = require("./dto/vip-config-query.dto");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
let VipConfigController = class VipConfigController {
    vipConfigService;
    dailyRewardService;
    constructor(vipConfigService, dailyRewardService) {
        this.vipConfigService = vipConfigService;
        this.dailyRewardService = dailyRewardService;
    }
    async create(createVipConfigDto, req) {
        const result = await this.vipConfigService.create(createVipConfigDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(query) {
        const result = await this.vipConfigService.findAll(query);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getActiveLevels() {
        const result = await this.vipConfigService.getActiveVipLevels();
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.vipConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateVipConfigDto, req) {
        const result = await this.vipConfigService.update(id, updateVipConfigDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.vipConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async calculateUserPoints(userId) {
        const result = await this.vipConfigService.calculateUserPoints(userId);
        return {
            code: 200,
            message: '计算成功',
            result,
        };
    }
    async updateUserVipLevel(userId) {
        const result = await this.vipConfigService.updateUserVipLevel(userId);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async recalculateAllUserVipLevels() {
        const result = await this.vipConfigService.recalculateAllUserVipLevels();
        return {
            code: 200,
            message: '重新计算完成',
            result,
        };
    }
    async claimDailyGoldReward(userId) {
        const result = await this.dailyRewardService.claimDailyGoldReward(userId);
        return {
            code: 200,
            message: result.message,
            result,
        };
    }
    async getDailyRewardStatus(userId) {
        const result = await this.dailyRewardService.getDailyRewardStatus(userId);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async getDailyRewardHistory(userId, page = 1, pageSize = 10) {
        const result = await this.dailyRewardService.getDailyRewardHistory(userId, page, pageSize);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
};
exports.VipConfigController = VipConfigController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'VIP等级已存在' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_vip_config_dto_1.CreateVipConfigDto, Object]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP配置列表' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [vip_config_query_dto_1.VipConfigQueryDto]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('active-levels'),
    (0, swagger_1.ApiOperation)({ summary: '获取所有启用的VIP等级' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getActiveLevels", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取VIP配置详情' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'VIP等级已存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_vip_config_dto_1.UpdateVipConfigDto, Object]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除VIP配置' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'VIP配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '该VIP等级已被用户使用，无法删除' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('calculate-points/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '计算指定用户的VIP积分' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '计算成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "calculateUserPoints", null);
__decorate([
    (0, common_1.Post)('update-level/:userId'),
    (0, swagger_1.ApiOperation)({ summary: '更新指定用户的VIP等级' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "updateUserVipLevel", null);
__decorate([
    (0, common_1.Post)('recalculate-all'),
    (0, swagger_1.ApiOperation)({ summary: '重新计算所有用户的VIP等级' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '重新计算完成' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "recalculateAllUserVipLevels", null);
__decorate([
    (0, common_1.Post)('daily-reward/:userId/claim'),
    (0, swagger_1.ApiOperation)({ summary: '领取每日VIP金币奖励' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '领取成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '今日已领取或无奖励' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "claimDailyGoldReward", null);
__decorate([
    (0, common_1.Get)('daily-reward/:userId/status'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户每日奖励状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getDailyRewardStatus", null);
__decorate([
    (0, common_1.Get)('daily-reward/:userId/history'),
    (0, swagger_1.ApiOperation)({ summary: '获取用户每日奖励领取历史' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('pageSize', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], VipConfigController.prototype, "getDailyRewardHistory", null);
exports.VipConfigController = VipConfigController = __decorate([
    (0, swagger_1.ApiTags)('VIP积分配置管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/vip'),
    __metadata("design:paramtypes", [vip_config_service_1.VipConfigService,
        daily_reward_service_1.DailyRewardService])
], VipConfigController);
//# sourceMappingURL=vip-config.controller.js.map