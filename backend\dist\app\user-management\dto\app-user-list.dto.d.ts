export declare class AppUserListItemDto {
    id: number;
    uid: number;
    username: string;
    email: string;
    phone: string;
    nickname: string;
    avatar: string;
    gender: number;
    rechargeBalance: number;
    goldBalance: number;
    withdrawableBalance: number;
    vipLevel: number;
    status: number;
    accountType: number;
    isVerified: number;
    riskScore: number;
    kycStatus: number;
    inviterId?: number;
    channelName?: string;
    adName?: string;
    adIdentifier?: string;
    acquisitionTag?: string;
    tags?: string[];
    lastLoginTime: Date;
    daysNotLoggedIn: number;
    registerIp: string;
    createTime: Date;
}
export declare class AppUserListResponseDto {
    list: AppUserListItemDto[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
}
