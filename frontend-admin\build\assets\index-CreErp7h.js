import{a_ as x,a$ as a,aZ as h,bk as g,bl as T,av as F,P as C}from"./antd-CXPM1OiB.js";import{a as j}from"./react-BUTTOX-3.js";import{P as c}from"./BaseForm-DrXILIwp.js";import{j as n}from"./index-CHjq8S-S.js";var y=["fieldProps","proFieldProps"],S=["fieldProps","proFieldProps"],d="text",_=function(r){var e=r.fieldProps,l=r.proFieldProps,t=x(r,y);return n.jsx(c,a({valueType:d,fieldProps:e,filedConfig:{valueType:d},proFieldProps:l},t))},R=function(r){var e=T(r.open||!1,{value:r.open,onChange:r.onOpenChange}),l=h(e,2),t=l[0],p=l[1];return n.jsx(<PERSON><PERSON>,{shouldUpdate:!0,noStyle:!0,children:function(v){var s,P=v.getFieldValue(r.name||[]);return n.jsx(C,a(a({getPopupContainer:function(o){return o&&o.parentNode?o.parentNode:o},onOpenChange:function(o){return p(o)},content:n.jsxs("div",{style:{padding:"4px 0"},children:[(s=r.statusRender)===null||s===void 0?void 0:s.call(r,P),r.strengthText?n.jsx("div",{style:{marginTop:10},children:n.jsx("span",{children:r.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},r.popoverProps),{},{open:t,children:r.children}))}})},w=function(r){var e=r.fieldProps,l=r.proFieldProps,t=x(r,S),p=j.useState(!1),u=h(p,2),v=u[0],s=u[1];return e!=null&&e.statusRender&&t.name?n.jsx(R,{name:t.name,statusRender:e==null?void 0:e.statusRender,popoverProps:e==null?void 0:e.popoverProps,strengthText:e==null?void 0:e.strengthText,open:v,onOpenChange:s,children:n.jsx("div",{children:n.jsx(c,a({valueType:"password",fieldProps:a(a({},g(e,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(i){var o;e==null||(o=e.onBlur)===null||o===void 0||o.call(e,i),s(!1)},onClick:function(i){var o;e==null||(o=e.onClick)===null||o===void 0||o.call(e,i),s(!0)}}),proFieldProps:l,filedConfig:{valueType:d}},t))})}):n.jsx(c,a({valueType:"password",fieldProps:e,proFieldProps:l,filedConfig:{valueType:d}},t))},m=_;m.Password=w;m.displayName="ProFormComponent";export{m as W};
