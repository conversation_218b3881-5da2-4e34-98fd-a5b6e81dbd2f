import{j as r}from"./index-CHjq8S-S.js";import{a as y}from"./react-BUTTOX-3.js";import{av as s,al as i,M as D,p as n,I as p,q as x,aF as b,s as m}from"./antd-CXPM1OiB.js";import{C as T,A as v,f as R,u as w}from"./index-ip1XApgs.js";const{TextArea:A}=p,{Option:o}=n,{RangePicker:F}=b;function _({visible:c,mode:l,initialValues:a,channels:g,onCancel:f,onSuccess:u}){const[d]=s.useForm();y.useEffect(()=>{if(c)if(l==="edit"&&a){const e=[];a.startDate&&e.push(i(a.startDate)),a.endDate&&e.push(i(a.endDate)),d.setFieldsValue({channelId:a.channelId,name:a.name,identifier:a.identifier,status:a.status,description:a.description,budget:a.budget,adType:a.adType||1,referrerId:a.referrerId,dateRange:e.length===2?e:void 0})}else d.resetFields(),d.setFieldsValue({status:1,adType:1})},[c,l,a,d]);const j=async()=>{try{const e=await d.validateFields(),t={...e};if(e.dateRange&&e.dateRange.length===2&&(t.startDate=e.dateRange[0].format("YYYY-MM-DD"),t.endDate=e.dateRange[1].format("YYYY-MM-DD")),delete t.dateRange,l==="create")(await R(t)).code===200&&(m.success("创建成功"),u());else{const h=t;(await w(a.id,h)).code===200&&(m.success("更新成功"),u())}}catch(e){if(e.errorFields)return;m.error(e.message||`${l==="create"?"创建":"更新"}失败`)}};return r.jsx(D,{title:l==="create"?"新增广告":"编辑广告",open:c,onCancel:f,onOk:j,width:700,destroyOnClose:!0,children:r.jsxs(s,{form:d,layout:"vertical",preserve:!1,children:[r.jsx(s.Item,{name:"channelId",label:"所属渠道",rules:[{required:!0,message:"请选择所属渠道"}],children:r.jsx(n,{placeholder:"请选择所属渠道",children:g.map(e=>r.jsx(o,{value:e.id,children:e.name},e.id))})}),r.jsx(s.Item,{name:"name",label:"广告名称",rules:[{required:!0,message:"请输入广告名称"},{max:100,message:"广告名称不能超过100个字符"}],children:r.jsx(p,{placeholder:"请输入广告名称，如：Q3男性用户投放"})}),r.jsx(s.Item,{name:"identifier",label:"广告标识",rules:[{required:!0,message:"请输入广告标识"},{max:50,message:"广告标识不能超过50个字符"},{pattern:/^[a-zA-Z0-9_-]+$/,message:"广告标识只能包含字母、数字、下划线和连字符"}],children:r.jsx(p,{placeholder:"请输入广告标识，如：q3_male_ads"})}),r.jsx(s.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:r.jsx(n,{placeholder:"请选择状态",children:T.map(e=>r.jsx(o,{value:e.value,children:e.label},e.value))})}),r.jsx(s.Item,{name:"adType",label:"广告类型",rules:[{required:!0,message:"请选择广告类型"}],children:r.jsx(n,{placeholder:"请选择广告类型",children:v.map(e=>r.jsx(o,{value:e.value,children:e.label},e.value))})}),r.jsx(s.Item,{noStyle:!0,shouldUpdate:(e,t)=>e.adType!==t.adType,children:({getFieldValue:e})=>e("adType")===2?r.jsx(s.Item,{name:"referrerId",label:"推荐人ID",rules:[{required:!0,message:"推荐系统引流类型必须填写推荐人ID"},{type:"number",min:1,message:"推荐人ID必须是正整数"}],children:r.jsx(x,{style:{width:"100%"},placeholder:"请输入推荐人ID",precision:0})}):null}),r.jsx(s.Item,{name:"budget",label:"预算金额",rules:[{type:"number",min:0,message:"预算金额不能小于0"}],children:r.jsx(x,{style:{width:"100%"},placeholder:"请输入预算金额",formatter:e=>`¥ ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,""),precision:2})}),r.jsx(s.Item,{name:"dateRange",label:"投放时间",children:r.jsx(F,{style:{width:"100%"},placeholder:["开始日期","结束日期"]})}),r.jsx(s.Item,{name:"description",label:"描述",rules:[{max:500,message:"描述不能超过500个字符"}],children:r.jsx(A,{rows:4,placeholder:"请输入广告描述（可选）",showCount:!0,maxLength:500})})]})})}export{_ as AdForm};
