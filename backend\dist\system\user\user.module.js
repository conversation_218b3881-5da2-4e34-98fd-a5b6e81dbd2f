"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemUserModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_service_1 = require("./user.service");
const user_controller_1 = require("./user.controller");
const sys_user_entity_1 = require("../entities/sys-user.entity");
const sys_role_entity_1 = require("../entities/sys-role.entity");
const super_admin_guard_1 = require("../auth/guards/super-admin.guard");
let SystemUserModule = class SystemUserModule {
};
exports.SystemUserModule = SystemUserModule;
exports.SystemUserModule = SystemUserModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sys_user_entity_1.SysUser, sys_role_entity_1.SysRole])],
        controllers: [user_controller_1.SystemUserController],
        providers: [user_service_1.SystemUserService, super_admin_guard_1.SuperAdminGuard],
        exports: [user_service_1.SystemUserService],
    })
], SystemUserModule);
//# sourceMappingURL=user.module.js.map