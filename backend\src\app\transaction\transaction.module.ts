import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction } from '../entities/cash-transaction.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';
import { RechargeTransaction } from '../entities/recharge-transaction.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AppUser,
      CashTransaction,
      GoldTransaction,
      RechargeTransaction,
    ]),
  ],
  controllers: [TransactionController],
  providers: [TransactionService],
  exports: [TransactionService],
})
export class TransactionModule {}
