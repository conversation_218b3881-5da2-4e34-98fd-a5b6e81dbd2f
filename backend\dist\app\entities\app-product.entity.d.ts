import { AppCategory } from './app-category.entity';
import { AppOrderItem } from './app-order-item.entity';
export declare class AppProduct {
    id: number;
    name: string;
    description: string;
    categoryId: number;
    price: number;
    originalPrice: number;
    stock: number;
    sales: number;
    images: any;
    specifications: any;
    status: number;
    isFeatured: number;
    weight: number;
    category: AppCategory;
    orderItems: AppOrderItem[];
    createTime: Date;
    updateTime: Date;
}
