import { Repository, DataSource } from 'typeorm';
import { AppUser } from '../entities/app-user.entity';
import { CashTransaction } from '../entities/cash-transaction.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';
import { RechargeTransaction } from '../entities/recharge-transaction.entity';
export interface CreateTransactionDto {
    userId: number;
    amount: number;
    status: number;
    transactionType: number;
    orderId?: string;
    description?: string;
    remark?: string;
    operatorId?: number;
}
export interface TransactionStatistics {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    transactionCount: number;
}
export interface TransactionListQuery {
    userId: number;
    status?: number;
    transactionType?: number;
    startDate?: string | Date;
    endDate?: string | Date;
    page?: number;
    pageSize?: number;
}
export interface TransactionListResponse {
    list: any[];
    total: number;
    page: number;
    pageSize: number;
}
export declare class TransactionService {
    private readonly userRepository;
    private readonly cashTransactionRepository;
    private readonly goldTransactionRepository;
    private readonly rechargeTransactionRepository;
    private readonly dataSource;
    constructor(userRepository: Repository<AppUser>, cashTransactionRepository: Repository<CashTransaction>, goldTransactionRepository: Repository<GoldTransaction>, rechargeTransactionRepository: Repository<RechargeTransaction>, dataSource: DataSource);
    createCashTransaction(dto: CreateTransactionDto): Promise<CashTransaction>;
    createGoldTransaction(dto: CreateTransactionDto): Promise<GoldTransaction>;
    createRechargeTransaction(dto: CreateTransactionDto): Promise<RechargeTransaction>;
    getCashStatistics(userId: number): Promise<TransactionStatistics>;
    getGoldStatistics(userId: number): Promise<TransactionStatistics>;
    getRechargeStatistics(userId: number): Promise<TransactionStatistics>;
    getCashTransactionList(query: TransactionListQuery): Promise<TransactionListResponse>;
    getGoldTransactionList(query: TransactionListQuery): Promise<TransactionListResponse>;
    getRechargeTransactionList(query: TransactionListQuery): Promise<TransactionListResponse>;
    getUserAssetStatistics(userId: number): Promise<{
        cash: TransactionStatistics;
        gold: TransactionStatistics;
        recharge: TransactionStatistics;
    }>;
}
