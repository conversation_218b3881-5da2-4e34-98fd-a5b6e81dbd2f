"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestVipConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const vip_config_service_1 = require("./vip-config.service");
let TestVipConfigController = class TestVipConfigController {
    vipConfigService;
    constructor(vipConfigService) {
        this.vipConfigService = vipConfigService;
    }
    async findAll() {
        try {
            const result = await this.vipConfigService.findAll({});
            return {
                code: 200,
                message: '获取成功',
                result,
                debug: {
                    timestamp: new Date().toISOString(),
                    note: '这是测试接口，绕过了认证验证'
                }
            };
        }
        catch (error) {
            console.error('测试VIP配置查询失败:', error);
            return {
                code: 500,
                message: '查询失败',
                error: error.message,
                debug: {
                    timestamp: new Date().toISOString(),
                    stack: error.stack
                }
            };
        }
    }
    async findAllRaw() {
        try {
            const { vipConfigRepository } = this.vipConfigService;
            const configs = await vipConfigRepository.find({
                order: { vipLevel: 'ASC' }
            });
            return {
                code: 200,
                message: '获取原始数据成功',
                result: configs,
                debug: {
                    timestamp: new Date().toISOString(),
                    count: configs.length,
                    note: '这是原始数据库查询结果'
                }
            };
        }
        catch (error) {
            console.error('测试原始VIP配置查询失败:', error);
            return {
                code: 500,
                message: '查询失败',
                error: error.message,
                debug: {
                    timestamp: new Date().toISOString(),
                    stack: error.stack
                }
            };
        }
    }
};
exports.TestVipConfigController = TestVipConfigController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '测试获取VIP配置列表（无认证）' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestVipConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('raw'),
    (0, swagger_1.ApiOperation)({ summary: '测试获取原始VIP配置数据' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestVipConfigController.prototype, "findAllRaw", null);
exports.TestVipConfigController = TestVipConfigController = __decorate([
    (0, swagger_1.ApiTags)('测试VIP配置'),
    (0, common_1.Controller)('test/config/vip'),
    __metadata("design:paramtypes", [vip_config_service_1.VipConfigService])
], TestVipConfigController);
//# sourceMappingURL=test-vip-config.controller.js.map