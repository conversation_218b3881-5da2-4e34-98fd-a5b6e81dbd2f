import { CreateVipConfigDto } from './create-vip-config.dto';
declare const UpdateVipConfigDto_base: import("@nestjs/common").Type<Partial<CreateVipConfigDto>>;
export declare class UpdateVipConfigDto extends UpdateVipConfigDto_base {
    vipLevel?: number;
    levelName?: string;
    requiredPoints?: number;
    balanceRatio?: number;
    cashRatio?: number;
    goldRatio?: number;
    dailyGoldReward?: number;
    status?: number;
    remark?: string;
}
export {};
