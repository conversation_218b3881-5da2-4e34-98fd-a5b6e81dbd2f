"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMenuModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const menu_service_1 = require("./menu.service");
const menu_controller_1 = require("./menu.controller");
const sys_menu_entity_1 = require("../entities/sys-menu.entity");
let SystemMenuModule = class SystemMenuModule {
};
exports.SystemMenuModule = SystemMenuModule;
exports.SystemMenuModule = SystemMenuModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([sys_menu_entity_1.SysMenu])],
        controllers: [menu_controller_1.SystemMenuController],
        providers: [menu_service_1.SystemMenuService],
        exports: [menu_service_1.SystemMenuService],
    })
], SystemMenuModule);
//# sourceMappingURL=menu.module.js.map