"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysWorkbook = exports.SysMenu = exports.SysPermission = exports.SysRole = exports.SysUser = void 0;
var sys_user_entity_1 = require("./sys-user.entity");
Object.defineProperty(exports, "SysUser", { enumerable: true, get: function () { return sys_user_entity_1.SysUser; } });
var sys_role_entity_1 = require("./sys-role.entity");
Object.defineProperty(exports, "SysRole", { enumerable: true, get: function () { return sys_role_entity_1.SysRole; } });
var sys_permission_entity_1 = require("./sys-permission.entity");
Object.defineProperty(exports, "SysPermission", { enumerable: true, get: function () { return sys_permission_entity_1.SysPermission; } });
var sys_menu_entity_1 = require("./sys-menu.entity");
Object.defineProperty(exports, "SysMenu", { enumerable: true, get: function () { return sys_menu_entity_1.SysMenu; } });
var sys_workbook_entity_1 = require("./sys-workbook.entity");
Object.defineProperty(exports, "SysWorkbook", { enumerable: true, get: function () { return sys_workbook_entity_1.SysWorkbook; } });
//# sourceMappingURL=index.js.map