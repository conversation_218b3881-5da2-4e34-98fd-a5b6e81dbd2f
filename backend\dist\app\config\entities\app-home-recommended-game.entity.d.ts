import { SysUser } from '../../../system/entities/sys-user.entity';
import { Application } from '../../entities/application.entity';
import { AppHomeConfig } from './app-home-config.entity';
export declare class AppHomeRecommendedGame {
    id: number;
    homeConfigId: number;
    applicationId: number;
    sortOrder: number;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    homeConfig: AppHomeConfig;
    application: Application;
    creator: SysUser;
    updater: SysUser;
    isEnabled(): boolean;
    getGameInfo(): {
        id: number;
        name: string;
        iconUrl: string;
    } | null;
}
