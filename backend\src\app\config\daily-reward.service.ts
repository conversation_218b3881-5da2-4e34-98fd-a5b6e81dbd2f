import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VipConfig } from './entities/vip-config.entity';
import { AppUser } from '../entities/app-user.entity';
import { GoldTransaction } from '../entities/gold-transaction.entity';

@Injectable()
export class DailyRewardService {
  constructor(
    @InjectRepository(VipConfig)
    private vipConfigRepository: Repository<VipConfig>,
    @InjectRepository(AppUser)
    private appUserRepository: Repository<AppUser>,
    @InjectRepository(GoldTransaction)
    private goldTransactionRepository: Repository<GoldTransaction>,
  ) {}

  // 检查用户今日是否已领取奖励
  async checkTodayRewardClaimed(userId: number): Promise<boolean> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);

    const rewardTransaction = await this.goldTransactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.userId = :userId', { userId })
      .andWhere('transaction.transactionType = :transactionType', { transactionType: 6 })
      .andWhere('transaction.description = :description', { description: 'VIP每日金币奖励' })
      .andWhere('transaction.createTime >= :startOfDay', { startOfDay })
      .andWhere('transaction.createTime <= :endOfDay', { endOfDay })
      .getOne();

    return !!rewardTransaction;
  }

  // 领取每日VIP金币奖励
  async claimDailyGoldReward(userId: number): Promise<{ success: boolean; goldAmount: number; message: string }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    // 检查今日是否已领取
    const alreadyClaimed = await this.checkTodayRewardClaimed(userId);
    if (alreadyClaimed) {
      throw new BadRequestException('今日VIP金币奖励已领取');
    }

    // 获取用户VIP等级配置
    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });

    if (!vipConfig) {
      throw new NotFoundException('VIP等级配置不存在');
    }

    if (vipConfig.dailyGoldReward <= 0) {
      throw new BadRequestException('当前VIP等级无每日金币奖励');
    }

    // 更新用户金币余额
    const newGoldBalance = user.goldBalance + vipConfig.dailyGoldReward;
    await this.appUserRepository.update(userId, {
      goldBalance: newGoldBalance,
    });

    // 记录金币交易流水
    const goldTransaction = this.goldTransactionRepository.create({
      userId,
      transactionId: `VIP_DAILY_${Date.now()}_${userId}`,
      amount: vipConfig.dailyGoldReward,
      balanceBefore: user.goldBalance,
      balanceAfter: newGoldBalance,
      status: 1, // 1表示收入
      transactionType: 6, // 6表示活动领取
      description: `VIP${user.vipLevel}等级每日奖励`,
    });

    await this.goldTransactionRepository.save(goldTransaction);

    return {
      success: true,
      goldAmount: vipConfig.dailyGoldReward,
      message: `成功领取VIP${user.vipLevel}等级每日金币奖励 ${vipConfig.dailyGoldReward} 枚`,
    };
  }

  // 获取用户每日奖励状态
  async getDailyRewardStatus(userId: number): Promise<{
    canClaim: boolean;
    goldAmount: number;
    vipLevel: number;
    levelName: string;
    alreadyClaimed: boolean;
  }> {
    const user = await this.appUserRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const vipConfig = await this.vipConfigRepository.findOne({
      where: { vipLevel: user.vipLevel, status: 1 },
    });

    if (!vipConfig) {
      throw new NotFoundException('VIP等级配置不存在');
    }

    const alreadyClaimed = await this.checkTodayRewardClaimed(userId);
    const canClaim = !alreadyClaimed && vipConfig.dailyGoldReward > 0;

    return {
      canClaim,
      goldAmount: vipConfig.dailyGoldReward,
      vipLevel: user.vipLevel,
      levelName: vipConfig.levelName,
      alreadyClaimed,
    };
  }

  // 获取用户历史领取记录
  async getDailyRewardHistory(userId: number, page: number = 1, pageSize: number = 10) {
    const skip = (page - 1) * pageSize;

    const [transactions, total] = await this.goldTransactionRepository.findAndCount({
      where: {
        userId,
        transactionType: 6,
        description: 'VIP每日金币奖励',
      },
      order: { createdAt: 'DESC' },
      skip,
      take: pageSize,
    });

    return {
      list: transactions.map(transaction => ({
        id: transaction.id,
        amount: transaction.amount,
        vipLevel: transaction.description?.match(/VIP(\d+)/)?.[1] || '未知',
        claimTime: transaction.createdAt,
        remark: transaction.description,
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }
}
