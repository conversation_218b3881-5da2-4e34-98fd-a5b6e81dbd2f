"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeRecommendedGame = void 0;
const typeorm_1 = require("typeorm");
const sys_user_entity_1 = require("../../../system/entities/sys-user.entity");
const application_entity_1 = require("../../entities/application.entity");
const app_home_config_entity_1 = require("./app-home-config.entity");
let AppHomeRecommendedGame = class AppHomeRecommendedGame {
    id;
    homeConfigId;
    applicationId;
    sortOrder;
    status;
    createdBy;
    updatedBy;
    createTime;
    updateTime;
    homeConfig;
    application;
    creator;
    updater;
    isEnabled() {
        return this.status === 1;
    }
    getGameInfo() {
        if (!this.application) {
            return null;
        }
        return {
            id: this.application.id,
            name: this.application.name,
            iconUrl: this.application.iconUrl,
        };
    }
};
exports.AppHomeRecommendedGame = AppHomeRecommendedGame;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'home_config_id', comment: '关联app_home_configs表' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "homeConfigId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'application_id', comment: '关联applications表' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "applicationId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'sort_order', default: 0, comment: '排序，数字越小越靠前' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "sortOrder", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', nullable: true, comment: '创建人ID' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true, comment: '最后更新人ID' }),
    __metadata("design:type", Number)
], AppHomeRecommendedGame.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppHomeRecommendedGame.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppHomeRecommendedGame.prototype, "updateTime", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_home_config_entity_1.AppHomeConfig, (homeConfig) => homeConfig.recommendedGames),
    (0, typeorm_1.JoinColumn)({ name: 'home_config_id' }),
    __metadata("design:type", app_home_config_entity_1.AppHomeConfig)
], AppHomeRecommendedGame.prototype, "homeConfig", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => application_entity_1.Application, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", application_entity_1.Application)
], AppHomeRecommendedGame.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeRecommendedGame.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => sys_user_entity_1.SysUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", sys_user_entity_1.SysUser)
], AppHomeRecommendedGame.prototype, "updater", void 0);
exports.AppHomeRecommendedGame = AppHomeRecommendedGame = __decorate([
    (0, typeorm_1.Entity)('app_home_recommended_games')
], AppHomeRecommendedGame);
//# sourceMappingURL=app-home-recommended-game.entity.js.map