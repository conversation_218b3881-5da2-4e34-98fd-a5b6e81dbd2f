// 用户状态枚举
export enum UserStatus {
  NORMAL = 0,
  BANNED = 1,
  DEACTIVATED = 2,
}

// 账户类型枚举
export enum AccountType {
  NORMAL_USER = 0,
  INTERNAL_EMPLOYEE = 1,
}

// KYC状态枚举
export enum KycStatus {
  NOT_VERIFIED = 0,
  UNDER_REVIEW = 1,
  APPROVED = 2,
  REJECTED = 3,
}

// 查询用户参数
export interface QueryAppUserParams {
  page?: number;
  pageSize?: number;
  id?: number;
  username?: string;
  email?: string;
  phone?: string;
  nickname?: string;
  status?: UserStatus;
  accountType?: AccountType;
  kycStatus?: KycStatus;
  channelId?: number;
  adId?: number;
  inviterId?: number;
  minRiskScore?: number;
  maxRiskScore?: number;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

// 用户列表项
export interface AppUserListItem {
  id: number;
  uid: number;
  username: string;
  email: string;
  phone: string;
  nickname: string;
  avatar: string;
  gender: number;
  rechargeBalance: number;
  goldBalance: number;
  withdrawableBalance: number;
  vipLevel: number;
  status: number;
  accountType: number;
  isVerified: number;
  riskScore: number;
  kycStatus: number;
  inviterId?: number;
  channelName?: string;
  adName?: string;
  adIdentifier?: string;
  acquisitionTag?: string;
  tags?: string[];
  lastLoginTime: string;
  daysNotLoggedIn: number;
  registerIp: string;
  createTime: string;
}

// 用户列表响应
export interface AppUserListResponse {
  list: AppUserListItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 用户详情
export interface AppUserDetail {
  id: number;
  uid: number;
  username: string;
  email: string;
  phone: string;
  googleId?: string;
  nickname: string;
  avatar: string;
  gender: number;
  birthday: string;
  rechargeBalance: number;
  goldBalance: number;
  withdrawableBalance: number;
  vipLevel: number;
  vipExp: number;
  status: number;
  accountType: number;
  isVerified: number;
  riskScore: number;
  kycStatus: number;
  kycRejectReason?: string;
  inviterId?: number;
  invitationPath?: string;
  channelId?: number;
  adId?: number;
  promotionalPageId?: number;
  acquisitionTag?: string;
  tags?: any;
  lastLoginTime: string;
  daysNotLoggedIn: number;
  lastLoginIp: string;
  registerIp: string;
  createTime: string;
  updateTime: string;
  inviter?: {
    id: number;
    username: string;
    nickname: string;
  };
  channel?: {
    id: number;
    name: string;
    identifier: string;
  };
  ad?: {
    id: number;
    name: string;
    identifier: string;
  };
  adIdentifier?: string;
  promotionalPage?: {
    id: number;
    name: string;
    identifier: string;
  };
}

// 更新用户状态参数
export interface UpdateUserStatusParams {
  status: UserStatus;
  reason?: string;
}

// 更新用户标签参数
export interface UpdateUserTagsParams {
  tags?: string[];
  reason?: string;
}

// 邀请用户信息
export interface InvitationUser {
  id: number;
  uid: number;
  username: string;
  nickname: string;
  avatar: string;
  status: number;
  createTime: string;
  lastLoginTime: string;
}

// 邀请关系
export interface InvitationRelationship {
  inviter?: InvitationUser;
  invitees: InvitationUser[];
  invitationPath: string;
  invitationLevel: number;
  totalInvitees: number;
  directInvitees: number;
  indirectInvitees: number;
}

// 风险事件
export interface RiskEvent {
  id: number;
  userId: number;
  eventType: string;
  riskIncrement: number;
  details: any;
  status: number;
  operatorId?: number;
  createdAt: string;
  updatedAt: string;
}

// 风险事件列表响应
export interface RiskEventListResponse {
  list: RiskEvent[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 营销渠道
export interface MarketingChannel {
  id: number;
  name: string;
  identifier: string;
  status: number;
  createdAt: string;
}

// 营销广告
export interface MarketingAd {
  id: number;
  channelId: number;
  name: string;
  identifier: string;
  status: number;
  createdAt: string;
  channel?: MarketingChannel;
}
