import { AdType, JumpType } from '../entities/ad-config.entity';
export declare class AdConfigQueryDto {
    page?: number;
    pageSize?: number;
    adType?: AdType;
    jumpType?: JumpType;
    status?: number;
    title?: string;
    adIdentifier?: string;
}
export declare class AdConfigListDto {
    id: number;
    adIdentifier: string;
    adType: AdType;
    adTypeName: string;
    title: string;
    images: string[];
    jumpType: JumpType;
    jumpTypeName: string;
    jumpTarget: string;
    sortOrder: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator?: {
        id: number;
        username: string;
    };
    updater?: {
        id: number;
        username: string;
    };
}
