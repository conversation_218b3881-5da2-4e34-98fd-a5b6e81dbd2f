import {
  require_jsx_runtime
} from "./chunk-5OHQ4AYE.js";
import {
  require_react_dom
} from "./chunk-3VTW7PKX.js";
import {
  require_react
} from "./chunk-THYVJR3I.js";
import {
  __toESM
} from "./chunk-4B2QHNJT.js";

// node_modules/.pnpm/keepalive-for-react@4.0.2_r_5c6f15039a6f4305f3f6aafcbf1c9ec2/node_modules/keepalive-for-react/dist/esm/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_react_dom = __toESM(require_react_dom(), 1);
function isNil(value) {
  return value === null || value === void 0;
}
function isRegExp(value) {
  return Object.prototype.toString.call(value) === "[object RegExp]";
}
function isArr(value) {
  return Array.isArray(value);
}
function isFn(value) {
  return typeof value === "function";
}
function domAttrSet(dom) {
  return {
    set: (key, value) => {
      dom.setAttribute(key, value);
      return domAttrSet(dom);
    }
  };
}
function delayAsync(milliseconds = 100) {
  let _timeID;
  return new Promise((resolve, _reject) => {
    _timeID = setTimeout(() => {
      resolve();
      if (!isNil(_timeID)) {
        clearTimeout(_timeID);
      }
    }, milliseconds);
  });
}
function isInclude(include, val) {
  const includes = isArr(include) ? include : isNil(include) ? [] : [include];
  return includes.some((include2) => {
    if (isRegExp(include2)) {
      return include2.test(val);
    } else {
      return val === include2;
    }
  });
}
function macroTask(fn) {
  setTimeout(fn, 0);
}
var CacheComponentContext = (0, import_react.createContext)({
  active: false,
  refresh: () => {
  },
  destroy: () => Promise.resolve(),
  destroyAll: () => Promise.resolve(),
  destroyOther: () => Promise.resolve(),
  getCacheNodes: () => []
});
var CacheComponentProvider = (0, import_react.memo)(function(props) {
  const { children, active, refresh, destroy, destroyAll, destroyOther, getCacheNodes } = props;
  const value = (0, import_react.useMemo)(() => {
    return { active, refresh, destroy, destroyAll, destroyOther, getCacheNodes };
  }, [active, refresh, destroy, destroyAll, destroyOther, getCacheNodes]);
  return (0, import_jsx_runtime.jsx)(CacheComponentContext.Provider, { value, children });
});
function __awaiter(thisArg, _arguments, P, generator) {
  function adopt(value) {
    return value instanceof P ? value : new P(function(resolve) {
      resolve(value);
    });
  }
  return new (P || (P = Promise))(function(resolve, reject) {
    function fulfilled(value) {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    }
    function rejected(value) {
      try {
        step(generator["throw"](value));
      } catch (e) {
        reject(e);
      }
    }
    function step(result) {
      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
    }
    step((generator = generator.apply(thisArg, _arguments || [])).next());
  });
}
var cacheDivMarkedClassName = "keepalive-cache-div";
function getChildNodes(dom) {
  return dom ? Array.from(dom.children) : [];
}
function removeDivNodes(nodes) {
  nodes.forEach((node) => {
    if (node.classList.contains(cacheDivMarkedClassName)) {
      node.remove();
    }
  });
}
function renderCacheDiv(containerDiv, cacheDiv) {
  const removeNodes = getChildNodes(containerDiv);
  removeDivNodes(removeNodes);
  containerDiv.appendChild(cacheDiv);
  cacheDiv.classList.remove("inactive");
  cacheDiv.classList.add("active");
}
function switchActiveNodesToInactive(containerDiv, cacheKey) {
  const nodes = getChildNodes(containerDiv);
  const activeNodes = nodes.filter((node) => node.classList.contains("active") && node.getAttribute("data-cache-key") !== cacheKey);
  activeNodes.forEach((node) => {
    node.classList.remove("active");
    node.classList.add("inactive");
  });
  return activeNodes;
}
function isCached(cacheKey, exclude, include) {
  if (include) {
    return isInclude(include, cacheKey);
  } else {
    if (exclude) {
      return !isInclude(exclude, cacheKey);
    }
    return true;
  }
}
var CacheComponent = (0, import_react.memo)(function(props) {
  const { errorElement: ErrorBoundary = import_react.Fragment, cacheNodeClassName, children, cacheKey, exclude, include } = props;
  const { active, renderCount, destroy, transition, viewTransition, duration, containerDivRef } = props;
  const activatedRef = (0, import_react.useRef)(false);
  activatedRef.current = activatedRef.current || active;
  const cacheDiv = (0, import_react.useMemo)(() => {
    const cacheDiv2 = document.createElement("div");
    domAttrSet(cacheDiv2).set("data-cache-key", cacheKey).set("style", "height: 100%").set("data-render-count", renderCount.toString());
    cacheDiv2.className = cacheDivMarkedClassName + (cacheNodeClassName ? ` ${cacheNodeClassName}` : "");
    return cacheDiv2;
  }, [renderCount, cacheNodeClassName]);
  (0, import_react.useEffect)(() => {
    const cached = isCached(cacheKey, exclude, include);
    const containerDiv = containerDivRef.current;
    if (!containerDiv) {
      console.warn(`keepalive: cache container not found`);
      return;
    }
    if (transition) {
      (() => __awaiter(this, void 0, void 0, function* () {
        if (active) {
          const inactiveNodes = switchActiveNodesToInactive(containerDiv, cacheKey);
          yield delayAsync(duration - 40);
          removeDivNodes(inactiveNodes);
          if (containerDiv.contains(cacheDiv)) {
            return;
          }
          renderCacheDiv(containerDiv, cacheDiv);
        } else {
          if (!cached) {
            yield delayAsync(duration);
            destroy(cacheKey);
          }
        }
      }))();
    } else {
      if (active) {
        const makeChange = () => {
          const inactiveNodes = switchActiveNodesToInactive(containerDiv, cacheKey);
          removeDivNodes(inactiveNodes);
          if (containerDiv.contains(cacheDiv)) {
            return;
          }
          renderCacheDiv(containerDiv, cacheDiv);
        };
        if (viewTransition && document.startViewTransition) {
          document.startViewTransition(makeChange);
        } else {
          makeChange();
        }
      } else {
        if (!cached) {
          destroy(cacheKey);
        }
      }
    }
  }, [active, containerDivRef, cacheKey, exclude, include]);
  return activatedRef.current ? (0, import_react_dom.createPortal)((0, import_jsx_runtime.jsx)(ErrorBoundary, { children }), cacheDiv, cacheKey) : null;
}, (prevProps, nextProps) => {
  return prevProps.active === nextProps.active && prevProps.renderCount === nextProps.renderCount && prevProps.children === nextProps.children && prevProps.exclude === nextProps.exclude && prevProps.include === nextProps.include;
});
var safeStartTransition = (cb) => {
  if (typeof import_react.startTransition !== "undefined" && isFn(import_react.startTransition)) {
    (0, import_react.startTransition)(cb);
  } else {
    cb();
  }
};
function useKeepAliveRef() {
  return (0, import_react.useRef)();
}
function KeepAlive(props) {
  const { activeCacheKey, max = 10, exclude, include, onBeforeActive, customContainerRef, cacheNodeClassName = `cache-component`, containerClassName = "keep-alive-render", errorElement, transition = false, viewTransition = false, duration = 200, children, aliveRef, maxAliveTime = 0 } = props;
  const containerDivRef = customContainerRef || (0, import_react.useRef)(null);
  const [cacheNodes, setCacheNodes] = (0, import_react.useState)([]);
  (0, import_react.useLayoutEffect)(() => {
    if (isNil(activeCacheKey))
      return;
    safeStartTransition(() => {
      setCacheNodes((prevCacheNodes) => {
        const lastActiveTime = Date.now();
        const cacheNode = prevCacheNodes.find((item) => item.cacheKey === activeCacheKey);
        if (cacheNode) {
          return prevCacheNodes.map((item) => {
            if (item.cacheKey === activeCacheKey) {
              let needUpdate = false;
              if (isFn(onBeforeActive))
                onBeforeActive(activeCacheKey);
              if (maxAliveTime) {
                const prev = item.lastActiveTime;
                if (isArr(maxAliveTime)) {
                  const config = maxAliveTime.find((item2) => {
                    return isRegExp(item2.match) ? item2.match.test(activeCacheKey) : item2.match === activeCacheKey;
                  });
                  if (config) {
                    needUpdate = config && prev + config.expire * 1e3 < lastActiveTime;
                  }
                } else {
                  needUpdate = prev + maxAliveTime * 1e3 < lastActiveTime;
                }
              }
              return Object.assign(Object.assign({}, item), { ele: children, lastActiveTime, renderCount: needUpdate ? item.renderCount + 1 : item.renderCount });
            }
            return item;
          });
        } else {
          if (isFn(onBeforeActive))
            onBeforeActive(activeCacheKey);
          if (prevCacheNodes.length > max) {
            const node = prevCacheNodes.reduce((prev, cur) => {
              return prev.lastActiveTime < cur.lastActiveTime ? prev : cur;
            });
            prevCacheNodes.splice(prevCacheNodes.indexOf(node), 1);
          }
          return [...prevCacheNodes, { cacheKey: activeCacheKey, lastActiveTime, ele: children, renderCount: 0 }];
        }
      });
    });
  }, [activeCacheKey, children]);
  const refresh = (0, import_react.useCallback)((cacheKey) => {
    setCacheNodes((cacheNodes2) => {
      const targetCacheKey = cacheKey || activeCacheKey;
      return cacheNodes2.map((item) => {
        if (item.cacheKey === targetCacheKey) {
          return Object.assign(Object.assign({}, item), { renderCount: item.renderCount + 1 });
        }
        return item;
      });
    });
  }, [setCacheNodes, activeCacheKey]);
  const destroy = (0, import_react.useCallback)((cacheKey) => {
    const targetCacheKey = cacheKey || activeCacheKey;
    const cacheKeys = isArr(targetCacheKey) ? targetCacheKey : [targetCacheKey];
    return new Promise((resolve) => {
      macroTask(() => {
        setCacheNodes((cacheNodes2) => {
          return [...cacheNodes2.filter((item) => !cacheKeys.includes(item.cacheKey))];
        });
        resolve();
      });
    });
  }, [setCacheNodes, activeCacheKey]);
  const destroyAll = (0, import_react.useCallback)(() => {
    return new Promise((resolve) => {
      macroTask(() => {
        setCacheNodes([]);
        resolve();
      });
    });
  }, [setCacheNodes]);
  const destroyOther = (0, import_react.useCallback)((cacheKey) => {
    const targetCacheKey = cacheKey || activeCacheKey;
    return new Promise((resolve) => {
      macroTask(() => {
        setCacheNodes((cacheNodes2) => {
          return [...cacheNodes2.filter((item) => item.cacheKey === targetCacheKey)];
        });
        resolve();
      });
    });
  }, [activeCacheKey, setCacheNodes]);
  const getCacheNodes = (0, import_react.useCallback)(() => {
    return cacheNodes;
  }, [cacheNodes]);
  (0, import_react.useImperativeHandle)(aliveRef, () => ({
    refresh,
    destroy,
    destroyAll,
    destroyOther,
    getCacheNodes
  }));
  return (0, import_jsx_runtime.jsxs)(import_react.Fragment, { children: [(0, import_jsx_runtime.jsx)("div", { ref: containerDivRef, className: containerClassName, style: { height: "100%" } }), cacheNodes.map((item) => {
    const { cacheKey, ele, renderCount } = item;
    return (0, import_jsx_runtime.jsx)(CacheComponentProvider, { active: activeCacheKey === cacheKey, refresh, destroy, destroyAll, destroyOther, getCacheNodes, children: (0, import_jsx_runtime.jsx)(CacheComponent, { destroy, include, exclude, transition, viewTransition, duration, renderCount, containerDivRef, errorElement, active: activeCacheKey === cacheKey, cacheNodeClassName, cacheKey, children: ele }) }, `${cacheKey}-${renderCount}`);
  })] });
}
var useKeepAliveContext = () => {
  return (0, import_react.useContext)(CacheComponentContext);
};
function useOnActive(cb, deps, skipMount = false, effect) {
  const { active } = useKeepAliveContext();
  const isMount = (0, import_react.useRef)(false);
  effect(() => {
    if (!active)
      return;
    if (skipMount && !isMount.current) {
      isMount.current = true;
      return;
    }
    const destroyCb = cb();
    return () => {
      if (isFn(destroyCb)) {
        destroyCb();
      }
    };
  }, [active, ...deps]);
}
var useEffectOnActive = (cb, deps, skipMount = false) => {
  useOnActive(cb, deps, skipMount, import_react.useEffect);
};
var useLayoutEffectOnActive = (cb, deps, skipMount = false) => {
  useOnActive(cb, deps, skipMount, import_react.useLayoutEffect);
};
var useKeepaliveRef = useKeepAliveRef;
export {
  KeepAlive,
  KeepAlive as default,
  useEffectOnActive,
  useKeepAliveContext,
  useKeepAliveRef,
  useKeepaliveRef,
  useLayoutEffectOnActive
};
//# sourceMappingURL=keepalive-for-react.js.map
