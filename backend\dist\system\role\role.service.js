"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoleService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const sys_role_entity_1 = require("../entities/sys-role.entity");
const sys_permission_entity_1 = require("../entities/sys-permission.entity");
let SystemRoleService = class SystemRoleService {
    roleRepository;
    permissionRepository;
    constructor(roleRepository, permissionRepository) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }
    async create(createRoleDto) {
        const { code, permissionIds, ...rest } = createRoleDto;
        const existingRole = await this.roleRepository.findOne({
            where: { code },
        });
        if (existingRole) {
            throw new common_1.ConflictException('角色代码已存在');
        }
        const role = this.roleRepository.create({
            code,
            ...rest,
        });
        if (permissionIds && permissionIds.length > 0) {
            const permissions = await this.permissionRepository.findBy({
                id: (0, typeorm_2.In)(permissionIds),
            });
            role.permissions = permissions;
        }
        return await this.roleRepository.save(role);
    }
    async findSimpleRoles() {
        return await this.roleRepository
            .createQueryBuilder('role')
            .select(['role.id', 'role.name', 'role.code', 'role.status'])
            .where('role.status = :status', { status: 1 })
            .orderBy('role.name', 'ASC')
            .getMany();
    }
    async findAll(queryRoleDto) {
        const { name, code, status, current = 1, pageSize = 10 } = queryRoleDto;
        const queryBuilder = this.roleRepository
            .createQueryBuilder('role')
            .leftJoinAndSelect('role.permissions', 'permission');
        if (name) {
            queryBuilder.andWhere('role.name LIKE :name', {
                name: `%${name}%`,
            });
        }
        if (code) {
            queryBuilder.andWhere('role.code = :code', { code });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('role.status = :status', { status });
        }
        const total = await queryBuilder.getCount();
        const list = await queryBuilder
            .skip((current - 1) * pageSize)
            .take(pageSize)
            .getMany();
        return {
            list,
            total,
            current,
            pageSize,
        };
    }
    async findOne(id) {
        const role = await this.roleRepository.findOne({
            where: { id },
            relations: ['permissions'],
        });
        if (!role) {
            throw new common_1.NotFoundException('角色不存在');
        }
        return role;
    }
    async update(id, updateRoleDto) {
        const role = await this.findOne(id);
        const { permissionIds, ...rest } = updateRoleDto;
        Object.assign(role, rest);
        if (permissionIds !== undefined) {
            if (permissionIds.length > 0) {
                const permissions = await this.permissionRepository.findBy({
                    id: (0, typeorm_2.In)(permissionIds),
                });
                role.permissions = permissions;
            }
            else {
                role.permissions = [];
            }
        }
        return await this.roleRepository.save(role);
    }
    async remove(id) {
        const role = await this.findOne(id);
        const roleWithUsers = await this.roleRepository.findOne({
            where: { id },
            relations: ['users'],
        });
        if (roleWithUsers && roleWithUsers.users && roleWithUsers.users.length > 0) {
            throw new common_1.ConflictException(`该角色已绑定 ${roleWithUsers.users.length} 个用户，无法删除。请先解除用户绑定关系。`);
        }
        await this.roleRepository.remove(role);
        return { message: '删除成功' };
    }
    async findAllSimple() {
        return await this.roleRepository.find({
            where: { status: 1 },
            select: ['id', 'name', 'code'],
            order: { createTime: 'ASC' },
        });
    }
};
exports.SystemRoleService = SystemRoleService;
exports.SystemRoleService = SystemRoleService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_role_entity_1.SysRole)),
    __param(1, (0, typeorm_1.InjectRepository)(sys_permission_entity_1.SysPermission)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SystemRoleService);
//# sourceMappingURL=role.service.js.map