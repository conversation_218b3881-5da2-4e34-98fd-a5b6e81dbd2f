import { createClient } from '@supabase/supabase-js';
import AWS from 'aws-sdk';

// Supabase配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://ytrftwscazjboxbwnrxp.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0cmZ0d3NjYXpqYm94YnducnhwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NTU5NzQsImV4cCI6MjA1MDUzMTk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

// 创建Supabase客户端
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 配置AWS S3客户端用于Supabase存储
const s3Client = new AWS.S3({
  accessKeyId: '6d0ebd129ee95173fe4719b9bbb6ac49',
  secretAccessKey: '****************************************************************',
  endpoint: 'https://ytrftwscazjboxbwnrxp.supabase.co/storage/v1/s3',
  region: 'ap-southeast-1',
  s3ForcePathStyle: true, // 强制使用路径样式
  signatureVersion: 'v4'
});

/**
 * 上传图片到Supabase存储 (使用S3 API)
 * @param file 要上传的文件
 * @param folder 存储文件夹名称（如：'ads', 'avatars', 'products'）
 * @returns 返回图片的公共URL
 */
export async function uploadImageToSupabase(file: File, folder: string = 'ads'): Promise<string> {
  try {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      throw new Error('只能上传图片文件');
    }

    // 验证文件大小（限制为5MB）
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      throw new Error('图片大小不能超过5MB');
    }

    // 生成唯一的文件名
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `${folder}/${fileName}`;

    // 将File转换为Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // 使用S3 API上传到Supabase存储
    const uploadParams = {
      Bucket: 'inda', // 使用inda存储桶
      Key: filePath,
      Body: buffer,
      ContentType: file.type,
      CacheControl: 'max-age=3600',
      ACL: 'public-read' // 设置为公共读取
    };

    const uploadResult = await s3Client.upload(uploadParams).promise();

    if (!uploadResult.Location) {
      throw new Error('上传失败，未获取到文件位置');
    }

    // 构建公共URL
    const publicUrl = `${supabaseUrl}/storage/v1/object/public/inda/${filePath}`;

    return publicUrl;
  } catch (error) {
    console.error('图片上传失败:', error);
    throw error;
  }
}

/**
 * 删除Supabase存储中的图片 (使用S3 API)
 * @param imageUrl 图片的完整URL
 * @param folder 存储文件夹名称
 * @returns 返回删除结果
 */
export async function deleteImageFromSupabase(imageUrl: string, folder: string = 'ads'): Promise<boolean> {
  try {
    // 从URL中提取文件路径
    const url = new URL(imageUrl);
    const pathParts = url.pathname.split('/');

    // 提取文件路径，去掉前缀部分
    let filePath = '';
    const publicIndex = pathParts.indexOf('public');
    if (publicIndex !== -1 && pathParts[publicIndex + 1] === 'inda') {
      // URL格式: .../storage/v1/object/public/inda/ads/filename.jpg
      filePath = pathParts.slice(publicIndex + 2).join('/');
    } else {
      // 备用方案：假设是文件名
      const fileName = pathParts[pathParts.length - 1];
      filePath = `${folder}/${fileName}`;
    }

    // 使用S3 API删除文件
    const deleteParams = {
      Bucket: 'inda',
      Key: filePath
    };

    await s3Client.deleteObject(deleteParams).promise();
    return true;
  } catch (error) {
    console.error('图片删除失败:', error);
    return false;
  }
}

/**
 * 批量上传图片到Supabase存储
 * @param files 要上传的文件数组
 * @param folder 存储文件夹名称
 * @param onProgress 上传进度回调
 * @returns 返回上传结果数组
 */
export async function uploadMultipleImagesToSupabase(
  files: File[],
  folder: string = 'ads',
  onProgress?: (progress: number, current: number, total: number) => void
): Promise<Array<{ success: boolean; url?: string; error?: string; fileName: string }>> {
  const results: Array<{ success: boolean; url?: string; error?: string; fileName: string }> = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    try {
      const url = await uploadImageToSupabase(file, folder);
      results.push({ success: true, url, fileName: file.name });
    } catch (error) {
      results.push({ 
        success: false, 
        error: error instanceof Error ? error.message : '上传失败', 
        fileName: file.name 
      });
    }
    
    // 调用进度回调
    if (onProgress) {
      const progress = ((i + 1) / files.length) * 100;
      onProgress(progress, i + 1, files.length);
    }
  }
  
  return results;
}

/**
 * 压缩图片
 * @param file 原始图片文件
 * @param maxWidth 最大宽度
 * @param maxHeight 最大高度
 * @param quality 压缩质量 (0-1)
 * @returns 返回压缩后的文件
 */
export function compressImage(
  file: File, 
  maxWidth: number = 1920, 
  maxHeight: number = 1080, 
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新的尺寸
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // 设置canvas尺寸
      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      // 转换为Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('图片压缩失败'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 上传压缩后的图片
 * @param file 原始图片文件
 * @param folder 存储文件夹名称
 * @param options 压缩选项
 * @returns 返回图片URL
 */
export async function uploadCompressedImageToSupabase(
  file: File,
  folder: string = 'ads',
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<string> {
  const { maxWidth = 1920, maxHeight = 1080, quality = 0.8 } = options;
  
  try {
    // 压缩图片
    const compressedFile = await compressImage(file, maxWidth, maxHeight, quality);
    
    // 上传压缩后的图片
    return await uploadImageToSupabase(compressedFile, folder);
  } catch (error) {
    console.error('压缩上传失败:', error);
    throw error;
  }
}
