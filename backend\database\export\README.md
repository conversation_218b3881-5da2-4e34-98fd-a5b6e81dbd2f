# InApp2 Database Export

## 文件说明

- `database-structure.sql` - 数据库结构文件，包含所有表的创建语句
- `database-data-export.sql` - 数据库数据文件，包含所有表的数据
- `README.md` - 本说明文件

## 使用方法

1. 首先执行结构文件创建表：
   ```sql
   \i database-structure.sql
   ```

2. 然后导入数据：
   ```sql
   \i database-data-export.sql
   ```

## 表结构概览

### 系统管理表
- `sys_users` - 后台管理员用户
- `sys_roles` - 角色管理
- `sys_permissions` - 权限管理
- `sys_menus` - 菜单管理
- `sys_workbook` - 数据字典

### 应用用户表
- `app_users` - APP端用户
- `cash_transactions` - 现金交易记录
- `gold_transactions` - 金币交易记录
- `diamond_transactions` - 钻石交易记录

### 应用管理表
- `application_providers` - 应用供应商
- `applications` - 应用列表
- `provider_environments` - 供应商环境配置

### 营销管理表
- `marketing_channels` - 营销渠道
- `marketing_ads` - 广告管理
- `promotional_pages` - 推广页面

### 配置管理表
- `vip_configs` - VIP配置
- `membership_card_configs` - 会员卡配置
- `ad_configs` - 广告配置
- `app_home_configs` - APP首页配置

## 生成时间

2025-06-26T11:13:31.057Z
