"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUser = void 0;
const typeorm_1 = require("typeorm");
const app_order_entity_1 = require("./app-order.entity");
const app_user_address_entity_1 = require("./app-user-address.entity");
const marketing_channel_entity_1 = require("./marketing-channel.entity");
const marketing_ad_entity_1 = require("./marketing-ad.entity");
const promotional_page_entity_1 = require("./promotional-page.entity");
let AppUser = class AppUser {
    id;
    uid;
    username;
    email;
    phone;
    googleId;
    password;
    fundPasswordHash;
    nickname;
    avatar;
    gender;
    birthday;
    rechargeBalance;
    goldBalance;
    withdrawableBalance;
    vipLevel;
    vipExp;
    status;
    accountType;
    isVerified;
    riskScore;
    kycStatus;
    kycRejectReason;
    inviterId;
    invitationPath;
    channelId;
    adId;
    promotionalPageId;
    acquisitionTag;
    tags;
    lastLoginTime;
    lastLoginIp;
    registerIp;
    inviter;
    channel;
    ad;
    promotionalPage;
    orders;
    addresses;
    createTime;
    updateTime;
};
exports.AppUser = AppUser;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], AppUser.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true }),
    __metadata("design:type", Number)
], AppUser.prototype, "uid", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true, length: 50 }),
    __metadata("design:type", String)
], AppUser.prototype, "username", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 100 }),
    __metadata("design:type", String)
], AppUser.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ unique: true, nullable: true, length: 20 }),
    __metadata("design:type", String)
], AppUser.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'google_id', unique: true, nullable: true, length: 255 }),
    __metadata("design:type", String)
], AppUser.prototype, "googleId", void 0);
__decorate([
    (0, typeorm_1.Column)({ select: false }),
    __metadata("design:type", String)
], AppUser.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'fund_password_hash', nullable: true, length: 255 }),
    __metadata("design:type", String)
], AppUser.prototype, "fundPasswordHash", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 50 }),
    __metadata("design:type", String)
], AppUser.prototype, "nickname", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500 }),
    __metadata("design:type", String)
], AppUser.prototype, "avatar", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '性别：0-未知，1-男，2-女' }),
    __metadata("design:type", Number)
], AppUser.prototype, "gender", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], AppUser.prototype, "birthday", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'recharge_balance', type: 'decimal', precision: 18, scale: 4, default: 0.0000, comment: '充值余额：用户直接现金充值的钱' }),
    __metadata("design:type", Number)
], AppUser.prototype, "rechargeBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'gold_balance', type: 'bigint', default: 0, comment: '金币余额：用户充值购买的金币，VIP卡领取,和各种活动赠送的游戏体验币' }),
    __metadata("design:type", Number)
], AppUser.prototype, "goldBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'withdrawable_balance', type: 'decimal', precision: 18, scale: 4, default: 0.0000, comment: '可提现余额：用户各种推广奖励，或者游戏中的现金' }),
    __metadata("design:type", Number)
], AppUser.prototype, "withdrawableBalance", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vip_level', default: 0 }),
    __metadata("design:type", Number)
], AppUser.prototype, "vipLevel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'vip_exp', type: 'bigint', default: 0 }),
    __metadata("design:type", Number)
], AppUser.prototype, "vipExp", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：0-正常，1-已封禁，2-已注销' }),
    __metadata("design:type", Number)
], AppUser.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'account_type', default: 0, comment: '账户类型：0-普通用户，1-内部员工' }),
    __metadata("design:type", Number)
], AppUser.prototype, "accountType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'is_verified', default: 0, comment: '是否认证：1-已认证，0-未认证' }),
    __metadata("design:type", Number)
], AppUser.prototype, "isVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'risk_score', default: 0 }),
    __metadata("design:type", Number)
], AppUser.prototype, "riskScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kyc_status', default: 0, comment: 'KYC状态：0-未认证，1-审核中，2-已通过，3-已拒绝' }),
    __metadata("design:type", Number)
], AppUser.prototype, "kycStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'kyc_reject_reason', nullable: true, length: 255 }),
    __metadata("design:type", String)
], AppUser.prototype, "kycRejectReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'inviter_id', nullable: true }),
    __metadata("design:type", Number)
], AppUser.prototype, "inviterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'invitation_path', nullable: true, length: 255 }),
    __metadata("design:type", String)
], AppUser.prototype, "invitationPath", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'channel_id', nullable: true }),
    __metadata("design:type", Number)
], AppUser.prototype, "channelId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ad_id', nullable: true }),
    __metadata("design:type", Number)
], AppUser.prototype, "adId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'promotional_page_id', nullable: true }),
    __metadata("design:type", Number)
], AppUser.prototype, "promotionalPageId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'acquisition_tag', nullable: true, length: 255 }),
    __metadata("design:type", String)
], AppUser.prototype, "acquisitionTag", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', nullable: true }),
    __metadata("design:type", Object)
], AppUser.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_login_time', nullable: true }),
    __metadata("design:type", Date)
], AppUser.prototype, "lastLoginTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_login_ip', nullable: true, length: 45 }),
    __metadata("design:type", String)
], AppUser.prototype, "lastLoginIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'register_ip', nullable: true, length: 50 }),
    __metadata("design:type", String)
], AppUser.prototype, "registerIp", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => AppUser, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'inviter_id' }),
    __metadata("design:type", AppUser)
], AppUser.prototype, "inviter", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => marketing_channel_entity_1.MarketingChannel, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'channel_id' }),
    __metadata("design:type", marketing_channel_entity_1.MarketingChannel)
], AppUser.prototype, "channel", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => marketing_ad_entity_1.MarketingAd, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'ad_id' }),
    __metadata("design:type", marketing_ad_entity_1.MarketingAd)
], AppUser.prototype, "ad", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => promotional_page_entity_1.PromotionalPage, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'promotional_page_id' }),
    __metadata("design:type", promotional_page_entity_1.PromotionalPage)
], AppUser.prototype, "promotionalPage", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_order_entity_1.AppOrder, (order) => order.user),
    __metadata("design:type", Array)
], AppUser.prototype, "orders", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => app_user_address_entity_1.AppUserAddress, (address) => address.user),
    __metadata("design:type", Array)
], AppUser.prototype, "addresses", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], AppUser.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], AppUser.prototype, "updateTime", void 0);
exports.AppUser = AppUser = __decorate([
    (0, typeorm_1.Entity)('app_users')
], AppUser);
//# sourceMappingURL=app-user.entity.js.map