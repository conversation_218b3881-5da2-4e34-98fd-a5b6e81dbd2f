import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { SysUser } from '../../../system/entities/sys-user.entity';

// 广告类型枚举
export enum AdType {
  CAROUSEL = 1,    // 轮播
  POPUP = 2,       // 弹窗广告
  FLOAT = 3,       // 浮点弹窗
  EMBED = 4,       // 嵌入广告
  BANNER = 5,      // banner
  INTERSTITIAL = 6, // 插屏广告
  HOME_GRID_4 = 7  // 首页4宫格
}

// 跳转类型枚举
export enum JumpType {
  INTERNAL_ROUTE = 1, // 内部路由
  IFRAME_PAGE = 2     // iframe页面
}

// 图片跳转项接口
export interface ImageItem {
  imageUrl: string;      // 图片URL
  jumpType: JumpType;    // 跳转类型
  jumpTarget: string;    // 跳转目标
  title?: string;        // 图片标题
  description?: string;  // 图片描述
}

@Entity('ad_configs')
export class AdConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ 
    name: 'ad_identifier', 
    length: 50, 
    unique: true, 
    comment: '唯一字符标识，前端根据此标识渲染广告' 
  })
  adIdentifier: string;

  @Column({ 
    name: 'ad_type', 
    type: 'int', 
    comment: '广告类型：1-轮播，2-弹窗，3-浮点弹窗，4-嵌入，5-banner，6-插屏' 
  })
  adType: AdType;

  @Column({ length: 100, comment: '广告标题' })
  title: string;

  @Column({
    name: 'image_items',
    type: 'jsonb',
    comment: '图片跳转项数组，每个元素包含imageUrl、jumpType、jumpTarget、title、description'
  })
  imageItems: ImageItem[];

  @Column({
    name: 'sort_order',
    default: 0,
    comment: '排序，数字越小越靠前'
  })
  sortOrder: number;

  @Column({ default: 1, comment: '状态：1-启用，0-禁用' })
  status: number;

  @Column({ nullable: true, length: 500, comment: '备注说明' })
  remark: string;

  @Column({ name: 'created_by', nullable: true, comment: '创建人ID' })
  createdBy: number;

  @Column({ name: 'updated_by', nullable: true, comment: '最后更新人ID' })
  updatedBy: number;

  @CreateDateColumn({ name: 'create_time' })
  createTime: Date;

  @UpdateDateColumn({ name: 'update_time' })
  updateTime: Date;

  // 关联创建人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'created_by' })
  creator: SysUser;

  // 关联更新人
  @ManyToOne(() => SysUser, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: SysUser;

  // 获取广告类型名称
  getAdTypeName(): string {
    const typeNames = {
      [AdType.CAROUSEL]: '轮播',
      [AdType.POPUP]: '弹窗广告',
      [AdType.FLOAT]: '浮点弹窗',
      [AdType.EMBED]: '嵌入广告',
      [AdType.BANNER]: 'Banner',
      [AdType.INTERSTITIAL]: '插屏广告',
      [AdType.HOME_GRID_4]: '首页4宫格'
    };
    return typeNames[this.adType] || '未知类型';
  }

  // 获取跳转类型名称（静态方法）
  static getJumpTypeName(jumpType: JumpType): string {
    const jumpTypeNames = {
      [JumpType.INTERNAL_ROUTE]: '内部路由',
      [JumpType.IFRAME_PAGE]: 'iframe页面'
    };
    return jumpTypeNames[jumpType] || '未知类型';
  }

  // 检查是否启用
  isEnabled(): boolean {
    return this.status === 1;
  }

  // 检查是否为轮播类型
  isCarouselType(): boolean {
    return this.adType === AdType.CAROUSEL;
  }

  // 检查是否为4宫格类型
  isHomeGrid4Type(): boolean {
    return this.adType === AdType.HOME_GRID_4;
  }

  // 获取图片数量
  getImageCount(): number {
    return Array.isArray(this.imageItems) ? this.imageItems.length : 0;
  }

  // 获取第一张图片（用于列表显示）
  getFirstImage(): string | null {
    return Array.isArray(this.imageItems) && this.imageItems.length > 0 ? this.imageItems[0].imageUrl : null;
  }

  // 获取所有图片URL
  getAllImageUrls(): string[] {
    return Array.isArray(this.imageItems) ? this.imageItems.map(item => item.imageUrl) : [];
  }

  // 验证图片数量是否符合广告类型要求
  validateImageCount(): { valid: boolean; message?: string } {
    const imageCount = this.getImageCount();

    switch (this.adType) {
      case AdType.CAROUSEL:
        if (imageCount < 2) {
          return { valid: false, message: `轮播广告至少需要2张图片，当前只有${imageCount}张` };
        }
        break;
      case AdType.HOME_GRID_4:
        if (imageCount !== 4) {
          return { valid: false, message: `首页4宫格广告必须上传4张图片，当前有${imageCount}张` };
        }
        break;
      default:
        if (imageCount === 0) {
          return { valid: false, message: '至少需要上传一张图片' };
        }
        break;
    }

    return { valid: true };
  }
}
