import { Repository } from 'typeorm';
import { MembershipCardConfig } from './entities/membership-card-config.entity';
export interface CreateMembershipCardConfigDto {
    cardType: string;
    cardName: string;
    price: number;
    description?: string;
    dailyGoldBase: number;
    dailyGoldActivityRatio: number;
    cashDiscountBase: number;
    cashDiscountActivity: number;
    activityStartTime?: string;
    activityEndTime?: string;
    status?: number;
}
export interface UpdateMembershipCardConfigDto {
    cardName?: string;
    price?: number;
    description?: string;
    dailyGoldBase?: number;
    dailyGoldActivityRatio?: number;
    cashDiscountBase?: number;
    cashDiscountActivity?: number;
    activityStartTime?: string;
    activityEndTime?: string;
    status?: number;
}
export declare class MembershipCardConfigService {
    private membershipCardConfigRepository;
    constructor(membershipCardConfigRepository: Repository<MembershipCardConfig>);
    create(createDto: CreateMembershipCardConfigDto, userId: number): Promise<MembershipCardConfig>;
    findAll(): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        cardType: string;
        cardName: string;
        price: number;
        description: string;
        dailyGoldBase: number;
        dailyGoldActivityRatio: number;
        cashDiscountBase: number;
        cashDiscountActivity: number;
        activityStartTime: Date;
        activityEndTime: Date;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }[]>;
    findOne(id: number): Promise<{
        creator: {
            id: number;
            username: string;
        } | null;
        updater: {
            id: number;
            username: string;
        } | null;
        id: number;
        cardType: string;
        cardName: string;
        price: number;
        description: string;
        dailyGoldBase: number;
        dailyGoldActivityRatio: number;
        cashDiscountBase: number;
        cashDiscountActivity: number;
        activityStartTime: Date;
        activityEndTime: Date;
        status: number;
        createdBy: number;
        updatedBy: number;
        createTime: Date;
        updateTime: Date;
    }>;
    update(id: number, updateDto: UpdateMembershipCardConfigDto, userId: number): Promise<MembershipCardConfig>;
    remove(id: number): Promise<{
        message: string;
    }>;
    findAllEffective(): Promise<{
        id: number;
        cardType: string;
        cardName: string;
        price: number;
        description: string;
        dailyGoldBase: number;
        effectiveDailyGold: number;
        cashDiscountBase: number;
        effectiveCashDiscount: number;
        isActivityActive: boolean;
        activityStatusDescription: string;
    }[]>;
    getActiveActivityConfigs(): Promise<MembershipCardConfig[]>;
    updateActivityTime(startTime: Date, endTime: Date, userId: number): Promise<{
        message: string;
    }>;
}
