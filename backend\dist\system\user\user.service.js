"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemUserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const bcrypt = require("bcryptjs");
const sys_user_entity_1 = require("../entities/sys-user.entity");
const sys_role_entity_1 = require("../entities/sys-role.entity");
let SystemUserService = class SystemUserService {
    userRepository;
    roleRepository;
    constructor(userRepository, roleRepository) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
    }
    async create(createUserDto, currentUserId) {
        const { username, email, password, roleIds, ...rest } = createUserDto;
        if (currentUserId) {
            await this.checkSuperAdminPermission(currentUserId);
        }
        const existingUser = await this.userRepository.findOne({
            where: [{ username }, { email }],
        });
        if (existingUser) {
            throw new common_1.ConflictException('用户名或邮箱已存在');
        }
        const finalPassword = password || this.generateRandomPassword();
        const hashedPassword = await bcrypt.hash(finalPassword, 10);
        const user = this.userRepository.create({
            username,
            email,
            password: hashedPassword,
            ...rest,
        });
        if (roleIds && roleIds.length > 0) {
            const roles = await this.roleRepository.findBy({
                id: (0, typeorm_2.In)(roleIds),
            });
            user.roles = roles;
        }
        const savedUser = await this.userRepository.save(user);
        return {
            user: savedUser,
            generatedPassword: password ? undefined : finalPassword,
        };
    }
    async findAll(queryUserDto) {
        const { username, email, status, current = 1, pageSize = 10 } = queryUserDto;
        const queryBuilder = this.userRepository
            .createQueryBuilder('user')
            .leftJoinAndSelect('user.roles', 'role')
            .select([
            'user.id',
            'user.username',
            'user.email',
            'user.phoneNumber',
            'user.avatar',
            'user.description',
            'user.status',
            'user.isSuperAdmin',
            'user.lastLoginTime',
            'user.createTime',
            'user.updateTime',
            'role.id',
            'role.name',
            'role.code',
        ]);
        if (username) {
            queryBuilder.andWhere('user.username LIKE :username', {
                username: `%${username}%`,
            });
        }
        if (email) {
            queryBuilder.andWhere('user.email LIKE :email', {
                email: `%${email}%`,
            });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('user.status = :status', { status });
        }
        const total = await queryBuilder.getCount();
        const list = await queryBuilder
            .skip((current - 1) * pageSize)
            .take(pageSize)
            .getMany();
        return {
            list,
            total,
            current,
            pageSize,
        };
    }
    async findOne(id) {
        const user = await this.userRepository.findOne({
            where: { id },
            relations: ['roles'],
            select: [
                'id',
                'username',
                'email',
                'phoneNumber',
                'avatar',
                'description',
                'status',
                'isSuperAdmin',
                'lastLoginTime',
                'createTime',
                'updateTime',
            ],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return user;
    }
    async update(id, updateUserDto, currentUserId) {
        if (currentUserId) {
            await this.checkSuperAdminPermission(currentUserId);
        }
        const user = await this.findOne(id);
        const { roleIds, username, email, ...rest } = updateUserDto;
        if ('isSuperAdmin' in rest) {
            delete rest.isSuperAdmin;
        }
        if (username && username !== user.username) {
            const existingUserByUsername = await this.userRepository.findOne({
                where: { username },
            });
            if (existingUserByUsername) {
                throw new common_1.ConflictException('用户名已存在');
            }
        }
        if (email && email !== user.email) {
            const existingUserByEmail = await this.userRepository.findOne({
                where: { email },
            });
            if (existingUserByEmail) {
                throw new common_1.ConflictException('邮箱已存在');
            }
        }
        Object.assign(user, { username, email, ...rest });
        if (roleIds !== undefined) {
            if (roleIds.length > 0) {
                const roles = await this.roleRepository.findBy({
                    id: (0, typeorm_2.In)(roleIds),
                });
                user.roles = roles;
            }
            else {
                user.roles = [];
            }
        }
        return await this.userRepository.save(user);
    }
    async remove(id, currentUserId) {
        if (currentUserId) {
            await this.checkSuperAdminPermission(currentUserId);
        }
        const user = await this.userRepository.findOne({
            where: { id },
            select: ['id', 'username', 'isSuperAdmin'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        if (user.isSuperAdmin) {
            throw new common_1.ForbiddenException('不能删除超级管理员');
        }
        if (currentUserId && user.id === currentUserId) {
            throw new common_1.ForbiddenException('不能删除自己');
        }
        await this.userRepository.remove(user);
        return { message: '删除成功' };
    }
    async getUserInfo(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['roles'],
            select: [
                'id',
                'username',
                'email',
                'phoneNumber',
                'avatar',
                'description',
                'isSuperAdmin',
            ],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return {
            ...user,
            roles: user.roles?.map((role) => role.code) || [],
        };
    }
    async checkSuperAdminPermission(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            select: ['id', 'isSuperAdmin'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        if (!user.isSuperAdmin) {
            throw new common_1.ForbiddenException('只有超级管理员才能执行此操作');
        }
    }
    async isSuperAdmin(userId) {
        const user = await this.userRepository.findOne({
            where: { id: userId },
            select: ['id', 'isSuperAdmin'],
        });
        return user?.isSuperAdmin || false;
    }
    async resetPassword(userId, currentUserId) {
        await this.checkSuperAdminPermission(currentUserId);
        const user = await this.userRepository.findOne({
            where: { id: userId },
            select: ['id', 'username', 'isSuperAdmin'],
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        if (user.isSuperAdmin && user.id !== currentUserId) {
            throw new common_1.ForbiddenException('不能重置其他超级管理员的密码');
        }
        const newPassword = this.generateRandomPassword();
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.userRepository.update(userId, { password: hashedPassword });
        return { newPassword };
    }
    generateRandomPassword() {
        const length = 12;
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        const special = '!@#$%^&*';
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
        password += numbers[Math.floor(Math.random() * numbers.length)];
        password += special[Math.floor(Math.random() * special.length)];
        for (let i = 4; i < length; i++) {
            password += charset[Math.floor(Math.random() * charset.length)];
        }
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }
    async getSuperAdmin() {
        return await this.userRepository.findOne({
            where: { isSuperAdmin: true },
            relations: ['roles'],
        });
    }
    async setSuperAdmin(userId) {
        const existingSuperAdmin = await this.getSuperAdmin();
        if (existingSuperAdmin) {
            throw new common_1.ConflictException('系统已存在超级管理员');
        }
        const user = await this.userRepository.findOne({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        user.isSuperAdmin = true;
        await this.userRepository.save(user);
    }
};
exports.SystemUserService = SystemUserService;
exports.SystemUserService = SystemUserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(sys_user_entity_1.SysUser)),
    __param(1, (0, typeorm_1.InjectRepository)(sys_role_entity_1.SysRole)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], SystemUserService);
//# sourceMappingURL=user.service.js.map