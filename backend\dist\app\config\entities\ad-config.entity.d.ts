import { SysUser } from '../../../system/entities/sys-user.entity';
export declare enum AdType {
    CAROUSEL = 1,
    POPUP = 2,
    FLOAT = 3,
    EMBED = 4,
    BANNER = 5,
    INTERSTITIAL = 6,
    HOME_GRID_4 = 7
}
export declare enum JumpType {
    INTERNAL_ROUTE = 1,
    IFRAME_PAGE = 2
}
export declare class AdConfig {
    id: number;
    adIdentifier: string;
    adType: AdType;
    title: string;
    images: string[];
    jumpType: JumpType;
    jumpTarget: string;
    sortOrder: number;
    status: number;
    remark: string;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    getAdTypeName(): string;
    getJumpTypeName(): string;
    isEnabled(): boolean;
    isCarouselType(): boolean;
    isHomeGrid4Type(): boolean;
    getImageCount(): number;
    getFirstImage(): string | null;
    validateImageCount(): {
        valid: boolean;
        message?: string;
    };
}
