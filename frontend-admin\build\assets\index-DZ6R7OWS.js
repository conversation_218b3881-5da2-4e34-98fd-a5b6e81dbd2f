import{j as s}from"./index-CHjq8S-S.js";import{a}from"./react-BUTTOX-3.js";import{B as j}from"./index-DDI4OfxQ.js";import{c as P,d as w}from"./index-DMJlwb4Y.js";import d from"./PermissionForm-C2D_VLHD.js";import{aq as C,d as u,u as T,an as c,aC as v,aD as R,aE as S,s as p}from"./antd-CXPM1OiB.js";import{P as b}from"./Table-DTyH0rpt.js";import"./index-CZVy594C.js";import"./index-BKSqgtRx.js";import"./BaseForm-DrXILIwp.js";import"./index-CnuJ2TqD.js";import"./index-DVrd-Evt.js";const $=()=>{const[f,r]=a.useState(!1),[m,i]=a.useState(!1),[x,l]=a.useState(),n=a.useRef(),h=e=>{switch(e){case"menu":return"blue";case"button":return"green";case"api":return"orange";default:return"default"}},y=e=>{switch(e){case"menu":return"菜单";case"button":return"按钮";case"api":return"API";default:return"未知"}},g=async e=>{var t;try{await w(e),p.success("删除成功"),(t=n.current)==null||t.reload()}catch{p.error("删除失败")}},I=[{title:"ID",dataIndex:"id",width:80,search:!1},{title:"权限名称",dataIndex:"name",ellipsis:!0,formItemProps:{rules:[{required:!0,message:"请输入权限名称"}]}},{title:"权限代码",dataIndex:"code",ellipsis:!0,formItemProps:{rules:[{required:!0,message:"请输入权限代码"}]}},{title:"权限类型",dataIndex:"type",width:100,valueType:"select",valueEnum:{menu:{text:"菜单",status:"Default"},button:{text:"按钮",status:"Processing"},api:{text:"API",status:"Warning"}},render:(e,t)=>s.jsx(c,{color:h(t.type),children:y(t.type)})},{title:"权限描述",dataIndex:"description",ellipsis:!0,search:!1},{title:"状态",dataIndex:"status",width:80,valueType:"select",valueEnum:{0:{text:"禁用",status:"Default"},1:{text:"启用",status:"Success"}},render:(e,t)=>s.jsx(c,{color:t.status===1?"green":"red",children:t.status===1?"启用":"禁用"})},{title:"创建时间",dataIndex:"createTime",width:180,valueType:"dateTime",search:!1},{title:"操作",dataIndex:"option",valueType:"option",width:150,render:(e,t)=>[s.jsx(u,{type:"link",size:"small",icon:s.jsx(v,{}),onClick:()=>{l(t),i(!0)},children:"编辑"},"edit"),s.jsx(R,{title:"确定要删除这个权限吗？",onConfirm:()=>g(t.id),okText:"确定",cancelText:"取消",children:s.jsx(u,{type:"link",size:"small",danger:!0,icon:s.jsx(S,{}),children:"删除"})},"delete")]}];return s.jsxs(j,{children:[s.jsx(C,{children:s.jsx(b,{headerTitle:"权限管理",actionRef:n,rowKey:"id",search:{labelWidth:120},toolBarRender:()=>[s.jsx(u,{type:"primary",icon:s.jsx(T,{}),onClick:()=>r(!0),children:"新建权限"},"create")],request:async e=>{const t={current:e.current||1,pageSize:e.pageSize||10};e.name&&e.name!=="undefined"&&(t.name=e.name),e.code&&e.code!=="undefined"&&(t.code=e.code),e.type&&e.type!=="undefined"&&(t.type=e.type),e.status!==void 0&&e.status!=="undefined"&&(t.status=e.status);const o=await P(t);return{data:o.result.list,success:!0,total:o.result.total}},columns:I,pagination:{defaultPageSize:10,showSizeChanger:!0}})}),s.jsx(d,{open:f,onOpenChange:r,onFinish:()=>{var e;r(!1),(e=n.current)==null||e.reload()}}),s.jsx(d,{open:m,onOpenChange:i,initialValues:x,onFinish:()=>{var e;i(!1),l(void 0),(e=n.current)==null||e.reload()}})]})};export{$ as default};
