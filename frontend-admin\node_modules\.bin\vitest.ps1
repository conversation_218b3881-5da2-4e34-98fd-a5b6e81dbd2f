#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules\vitest\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f\node_modules;E:\inwork\inapp\frontend-admin\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules:/mnt/e/inwork/inapp/frontend-admin/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" $args
  } else {
    & "node$exe"  "$basedir/../.pnpm/vitest@3.0.9_@types+debug@4_ad6ee2898c0dc8edd917bdef03589f7f/node_modules/vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
