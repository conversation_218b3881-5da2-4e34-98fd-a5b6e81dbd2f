"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfigController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const app_home_config_service_1 = require("./app-home-config.service");
const jwt_auth_guard_1 = require("../../system/auth/guards/jwt-auth.guard");
const dto_1 = require("./dto");
let AppHomeConfigController = class AppHomeConfigController {
    appHomeConfigService;
    constructor(appHomeConfigService) {
        this.appHomeConfigService = appHomeConfigService;
    }
    async create(createDto, req) {
        const result = await this.appHomeConfigService.create(createDto, req.user.id);
        return {
            code: 200,
            message: '创建成功',
            result,
        };
    }
    async findAll(query) {
        const result = await this.appHomeConfigService.findAll(query);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async findOne(id) {
        const result = await this.appHomeConfigService.findOne(id);
        return {
            code: 200,
            message: '获取成功',
            result,
        };
    }
    async update(id, updateDto, req) {
        const result = await this.appHomeConfigService.update(id, updateDto, req.user.id);
        return {
            code: 200,
            message: '更新成功',
            result,
        };
    }
    async remove(id) {
        const result = await this.appHomeConfigService.remove(id);
        return {
            code: 200,
            message: result.message,
        };
    }
    async toggleStatus(id, req) {
        const result = await this.appHomeConfigService.toggleStatus(id, req.user.id);
        return {
            code: 200,
            message: result.message,
            result: { status: result.status },
        };
    }
};
exports.AppHomeConfigController = AppHomeConfigController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: '创建APP首页配置' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: '创建成功' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '配置名称已存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数验证失败' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAppHomeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: '获取APP首页配置列表' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: dto_1.AppHomeConfigPageResponseDto
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.QueryAppHomeConfigDto]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '获取APP首页配置详情' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配置ID', type: 'number' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: '获取成功',
        type: dto_1.AppHomeConfigDetailResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '更新APP首页配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配置ID', type: 'number' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: '配置名称已存在' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '参数验证失败' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateAppHomeConfigDto, Object]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: '删除APP首页配置' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配置ID', type: 'number' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '删除成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/toggle-status'),
    (0, swagger_1.ApiOperation)({ summary: '切换APP首页配置状态' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: '配置ID', type: 'number' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '状态切换成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '配置不存在' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AppHomeConfigController.prototype, "toggleStatus", null);
exports.AppHomeConfigController = AppHomeConfigController = __decorate([
    (0, swagger_1.ApiTags)('APP首页配置管理'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.SystemJwtAuthGuard),
    (0, common_1.Controller)('config/app-home'),
    __metadata("design:paramtypes", [app_home_config_service_1.AppHomeConfigService])
], AppHomeConfigController);
//# sourceMappingURL=app-home-config.controller.js.map