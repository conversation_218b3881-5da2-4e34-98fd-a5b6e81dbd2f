const { Client } = require('pg');

// 数据库配置
const dbConfig = {
  host: '**************',
  port: 5435,
  database: 'inapp2',
  user: 'user_jJSpPW',
  password: 'password_DmrhYX',
};

async function checkTableStructure() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔗 连接到数据库...');
    await client.connect();
    
    console.log('📋 检查配置管理表结构...');
    
    const tables = [
      'vip_configs',
      'membership_card_configs', 
      'gold_recharge_configs',
      'balance_recharge_configs',
      'balance_recharge_limits',
      'ad_configs',
      'app_home_configs',
      'app_home_recommended_games',
      'app_home_game_categories',
      'app_home_category_games'
    ];
    
    for (const table of tables) {
      console.log(`\n📊 表: ${table}`);
      
      // 检查表是否存在
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table]);
      
      if (!tableExists.rows[0].exists) {
        console.log(`  ❌ 表不存在`);
        continue;
      }
      
      // 获取表结构
      const columns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = $1
        ORDER BY ordinal_position
      `, [table]);
      
      console.log(`  ✅ 表存在，列数: ${columns.rows.length}`);
      columns.rows.forEach(col => {
        console.log(`    - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
      });
      
      // 检查数据数量
      const count = await client.query(`SELECT COUNT(*) as count FROM ${table}`);
      console.log(`  📊 数据行数: ${count.rows[0].count}`);
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    await client.end();
  }
}

// 运行检查
if (require.main === module) {
  checkTableStructure().catch(console.error);
}

module.exports = { checkTableStructure };
