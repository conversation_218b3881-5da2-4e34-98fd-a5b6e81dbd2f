{"version": 3, "file": "role.service.js", "sourceRoot": "", "sources": ["../../../src/system/role/role.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AACxB,6CAAmD;AACnD,qCAAyC;AACzC,iEAAsD;AACtD,6EAAkE;AAM3D,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAGlB;IAEA;IAJV,YAEU,cAAmC,EAEnC,oBAA+C;QAF/C,mBAAc,GAAd,cAAc,CAAqB;QAEnC,yBAAoB,GAApB,oBAAoB,CAA2B;IACtD,CAAC;IAEJ,KAAK,CAAC,MAAM,CAAC,aAA4B;QACvC,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,GAAG,aAAa,CAAC;QAGvD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACrD,KAAK,EAAE,EAAE,IAAI,EAAE;SAChB,CAAC,CAAC;QACH,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,SAAS,CAAC,CAAC;QACzC,CAAC;QAGD,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YACtC,IAAI;YACJ,GAAG,IAAI;SACR,CAAC,CAAC;QAGH,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACzD,EAAE,EAAE,IAAA,YAAE,EAAC,aAAa,CAAC;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,OAAO,MAAM,IAAI,CAAC,cAAc;aAC7B,kBAAkB,CAAC,MAAM,CAAC;aAC1B,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;aAC5D,KAAK,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;aAC7C,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC;aAC3B,OAAO,EAAE,CAAC;IACf,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,YAA0B;QACtC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,YAAY,CAAC;QAExE,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc;aACrC,kBAAkB,CAAC,MAAM,CAAC;aAC1B,iBAAiB,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;QAEvD,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,sBAAsB,EAAE;gBAC5C,IAAI,EAAE,IAAI,IAAI,GAAG;aAClB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,EAAE,CAAC;YACT,YAAY,CAAC,QAAQ,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,YAAY,CAAC,QAAQ,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,MAAM,YAAY;aAC5B,IAAI,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC;aAC9B,IAAI,CAAC,QAAQ,CAAC;aACd,OAAO,EAAE,CAAC;QAEb,OAAO;YACL,IAAI;YACJ,KAAK;YACL,OAAO;YACP,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YAC7C,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,aAAa,CAAC;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,0BAAiB,CAAC,OAAO,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,aAA4B;QACnD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACpC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,EAAE,GAAG,aAAa,CAAC;QAGjD,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAG1B,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBACzD,EAAE,EAAE,IAAA,YAAE,EAAC,aAAa,CAAC;iBACtB,CAAC,CAAC;gBACH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACrB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAGpC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;YACtD,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,OAAO,CAAC;SACrB,CAAC,CAAC;QAEH,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,0BAAiB,CAAC,UAAU,aAAa,CAAC,KAAK,CAAC,MAAM,uBAAuB,CAAC,CAAC;QAC3F,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACvC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACpC,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;YAC9B,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE;SAC7B,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AA9IY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,yBAAO,CAAC,CAAA;IAEzB,WAAA,IAAA,0BAAgB,EAAC,qCAAa,CAAC,CAAA;qCADR,oBAAU;QAEJ,oBAAU;GAL/B,iBAAiB,CA8I7B"}