import { UserManagementService } from './user-management.service';
import { UserOperationsService } from './user-operations.service';
import { QueryAppUserDto, UpdateUserStatusDto, UpdateUserTagsDto, AppUserDetailDto, AppUserListResponseDto, InvitationRelationshipDto } from './dto';
export declare class UserManagementController {
    private readonly userManagementService;
    private readonly userOperationsService;
    constructor(userManagementService: UserManagementService, userOperationsService: UserOperationsService);
    findUsers(queryDto: QueryAppUserDto): Promise<{
        code: number;
        message: string;
        result: AppUserListResponseDto;
    }>;
    findUserById(id: number): Promise<{
        code: number;
        message: string;
        result: AppUserDetailDto;
    }>;
    updateUserStatus(id: number, updateDto: UpdateUserStatusDto, req: any): Promise<{
        code: number;
        message: string;
        result: {};
    }>;
    updateUserTags(id: number, updateDto: UpdateUserTagsDto, req: any): Promise<{
        code: number;
        message: string;
        result: {};
    }>;
    getUserInvitationRelationship(id: number): Promise<{
        code: number;
        message: string;
        result: InvitationRelationshipDto;
    }>;
    getUserRiskEvents(id: number, page?: number, pageSize?: number): Promise<{
        code: number;
        message: string;
        result: {
            list: import("../entities").RiskEvent[];
            total: number;
            page: number;
            pageSize: number;
            totalPages: number;
        };
    }>;
    getMarketingChannels(): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingChannel[];
    }>;
    getMarketingAds(channelId?: number): Promise<{
        code: number;
        message: string;
        result: import("../entities").MarketingAd[];
    }>;
}
