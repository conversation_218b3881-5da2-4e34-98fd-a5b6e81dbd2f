import { SysUser } from '../../../system/entities/sys-user.entity';
export declare class MembershipCardConfig {
    id: number;
    cardType: string;
    cardName: string;
    price: number;
    description: string;
    dailyGoldBase: number;
    dailyGoldActivityRatio: number;
    cashDiscountBase: number;
    cashDiscountActivity: number;
    activityStartTime: Date;
    activityEndTime: Date;
    status: number;
    createdBy: number;
    updatedBy: number;
    createTime: Date;
    updateTime: Date;
    creator: SysUser;
    updater: SysUser;
    isActivityActive(): boolean;
    getEffectiveDailyGold(): number;
    getEffectiveCashDiscount(): number;
    getActivityStatus(): {
        isActive: boolean;
        description: string;
    };
    getActivityBonusGold(): number;
    getActivityBonusDiscount(): number;
}
