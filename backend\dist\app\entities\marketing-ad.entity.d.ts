import { MarketingChannel } from './marketing-channel.entity';
import { PromotionalPage } from './promotional-page.entity';
import { AppUser } from './app-user.entity';
export declare class MarketingAd {
    id: number;
    channelId: number;
    name: string;
    identifier: string;
    status: number;
    description: string;
    budget: number;
    startDate: Date;
    endDate: Date;
    targetAudience: any;
    adType: number;
    referrerId: number;
    channel: MarketingChannel;
    promotionalPages: PromotionalPage[];
    users: AppUser[];
    createdAt: Date;
    updatedAt: Date;
}
