import{j as o,u as E}from"./index-CHjq8S-S.js";import{f as R,a as q}from"./index-aswgGRDw.js";import{R as v,a as P}from"./react-BUTTOX-3.js";import{f}from"./index-DMJlwb4Y.js";import{aw as S,a$ as m,a_ as C,an as I,p as L,av as k}from"./antd-CXPM1OiB.js";import{getMenuTypeOptions as A}from"./constants-DshMr_Vi.js";import{M as G}from"./index-DVrd-Evt.js";import{c as N,t as F,F as M,z as g,P as W}from"./index-BKSqgtRx.js";import{P as w}from"./BaseForm-DrXILIwp.js";import{W as b}from"./index-CreErp7h.js";import{F as O}from"./index-BvyzE_eE.js";var B=["fieldProps","options","radioType","layout","proFieldProps","valueEnum"],V=v.forwardRef(function(l,a){var n=l.fieldProps,u=l.options,d=l.radioType,i=l.layout,e=l.proFieldProps,p=l.valueEnum,c=C(l,B);return o.jsx(w,m(m({valueType:d==="button"?"radioButton":"radio",ref:a,valueEnum:F(p,void 0)},c),{},{fieldProps:m({options:u,layout:i},n),proFieldProps:e,filedConfig:{customLightMode:!0}}))}),$=v.forwardRef(function(l,a){var n=l.fieldProps,u=l.children;return o.jsx(S,m(m({},n),{},{ref:a,children:u}))}),z=N($,{valuePropName:"checked",ignoreWidth:!0}),x=z;x.Group=V;x.Button=S.Button;x.displayName="ProFormComponent";var D=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],U=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],H=function(a,n){var u=a.fieldProps,d=a.children,i=a.params,e=a.proFieldProps,p=a.mode,c=a.valueEnum,s=a.request,t=a.showSearch,r=a.options,h=C(a,D),y=P.useContext(M);return o.jsx(w,m(m({valueEnum:F(c),request:s,params:i,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:m({options:r,mode:p,showSearch:t,getPopupContainer:y.getPopupContainer},u),ref:n,proFieldProps:e},h),{},{children:d}))},J=v.forwardRef(function(l,a){var n=l.fieldProps,u=l.children,d=l.params,i=l.proFieldProps,e=l.mode,p=l.valueEnum,c=l.request,s=l.options,t=C(l,U),r=m({options:s,mode:e||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},n),h=P.useContext(M);return o.jsx(w,m(m({valueEnum:F(p),request:c,params:d,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:m({getPopupContainer:h.getPopupContainer},r),ref:a,proFieldProps:i},t),{},{children:u}))}),K=v.forwardRef(H),Q=J,j=K;j.SearchSelect=Q;j.displayName="ProFormComponent";const X=l=>{switch(l){case"menu":return"blue";case"button":return"green";case"api":return"orange";case"custom":return"purple";default:return"default"}},Y=l=>{switch(l){case"menu":return"菜单";case"button":return"按钮";case"api":return"API";case"custom":return"自定义";default:return"未知"}};function Z({type:l,showTypeTag:a=!1,predefinedCodes:n=[],...u}){const[d,i]=P.useState([]),[e,p]=P.useState(!1);P.useEffect(()=>{(async()=>{try{p(!0);let r=[];if(l)r=(await f(l)).result||[];else{const[h,y,T]=await Promise.all([f("button").catch(()=>({result:[]})),f("menu").catch(()=>({result:[]})),f("api").catch(()=>({result:[]}))]);r=[...h.result||[],...y.result||[],...T.result||[]]}i(r)}catch(r){console.error("获取权限数据失败:",r),i([])}finally{p(!1)}})()},[l]);const c=P.useMemo(()=>d.map(t=>({label:t.name||t.code,value:t.code,type:t.type,description:t.description||t.name})),[d]),s=P.useMemo(()=>{const t=[...c];return n.forEach(r=>{t.find(h=>h.value===r)||t.push({label:r,value:r,type:"custom",description:"自定义权限"})}),t.map(r=>({label:a?o.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[o.jsx("span",{children:r.label}),o.jsx(I,{color:X(r.type),children:Y(r.type)})]}):r.label,value:r.value,code:r}))},[c,n,a]);return o.jsx(L,{...u,options:s,placeholder:"请选择或输入权限码",showSearch:!0,allowClear:!0,loading:e,mode:u.mode||"tags",filterOption:(t,r)=>((r==null?void 0:r.value)??"").toLowerCase().includes(t.toLowerCase()),optionFilterProp:"value"})}function me({title:l,open:a,flatParentMenus:n,onCloseChange:u,detailData:d,refreshTable:i}){const{t:e}=E(),[p]=k.useForm(),c=async s=>{var t,r;return d.id?(await R(s),(t=window.$message)==null||t.success(e("common.updateSuccess"))):(await q(s),(r=window.$message)==null||r.success(e("common.addSuccess"))),i==null||i(),!0};return P.useEffect(()=>{a&&p.setFieldsValue(d)},[a]),o.jsxs(G,{title:l,open:a,onOpenChange:s=>{s===!1&&u()},labelCol:{md:5,xl:3},layout:"horizontal",form:p,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0},grid:!0,width:{xl:800,md:500},onFinish:c,initialValues:{menuType:0,status:1},children:[o.jsx(x.Group,{fieldProps:{buttonStyle:"solid"},name:"menuType",label:e("system.menu.menuType"),radioType:"button",required:!0,options:A()}),o.jsx(g,{name:["menuType"],children:({menuType:s})=>{if(Number(s)===1){const t=n.filter(r=>r.menuType===0);return o.jsx(j,{name:"parentId",label:e("system.menu.parentMenu"),placeholder:"请选择上级菜单",rules:[{required:!0,message:"请选择上级菜单"}],fieldProps:{showSearch:!0,optionFilterProp:"label"},options:t.map(r=>({label:r.name,value:r.id}))})}return null}}),o.jsx(g,{name:["menuType"],children:({menuType:s})=>{const t=Number(s)===0,r=Number(s)===1;return o.jsxs(o.Fragment,{children:[o.jsx(b,{allowClear:!0,rules:[{required:!0}],labelCol:{md:5,xl:6},name:"name",label:e("system.menu.name"),tooltip:e("form.length",{length:24})}),o.jsx(O,{allowClear:!0,rules:[{required:!0}],labelCol:{md:5,xl:6},name:"order",label:e("system.menu.menuOrder")}),o.jsx(x.Group,{name:"status",label:e("common.status"),radioType:"button",labelCol:{md:5,xl:6},options:[{label:e("common.enabled"),value:1},{label:e("common.deactivated"),value:0}]}),t&&o.jsxs(o.Fragment,{children:[o.jsx(b,{allowClear:!0,rules:[{required:!0}],labelCol:{md:5,xl:6},name:"icon",label:e("system.menu.menuIcon")}),o.jsx(x.Group,{name:"hideInMenu",label:e("system.menu.hideInMenu"),radioType:"button",labelCol:{md:5,xl:6},options:[{label:e("common.enabled"),value:1},{label:e("common.deactivated"),value:0}]})]}),r&&o.jsxs(o.Fragment,{children:[o.jsx(b,{allowClear:!0,rules:[{required:!0}],labelCol:{md:5,xl:6},name:"path",label:e("system.menu.routePath")}),o.jsx(b,{allowClear:!0,labelCol:{md:5,xl:6},name:"icon",label:e("system.menu.menuIcon")}),o.jsx(b,{allowClear:!0,rules:[{required:!0}],labelCol:{md:5,xl:6},name:"component",label:e("system.menu.componentUrl")}),o.jsx(x.Group,{name:"keepAlive",label:e("system.menu.keepAlive"),radioType:"button",labelCol:{md:5,xl:6},options:[{label:e("common.enabled"),value:1},{label:e("common.deactivated"),value:0}]}),o.jsx(x.Group,{name:"hideInMenu",label:e("system.menu.hideInMenu"),radioType:"button",labelCol:{md:5,xl:6},options:[{label:e("common.enabled"),value:1},{label:e("common.deactivated"),value:0}]}),o.jsx(b,{allowClear:!0,labelCol:{md:5,xl:6},name:"currentActiveMenu",label:e("system.menu.currentActiveMenu")}),o.jsx(b,{allowClear:!0,labelCol:{md:5,xl:6},name:"iframeLink",label:e("system.menu.iframeLink")}),o.jsx(b,{allowClear:!0,labelCol:{md:5,xl:6},name:"externalLink",label:e("system.menu.externalLink")}),o.jsx(b,{allowClear:!0,labelCol:{md:5,xl:6},name:"permissionCode",label:e("system.menu.permissionCode"),tooltip:"菜单权限代码，用于控制菜单访问权限"}),o.jsx(W,{name:"buttonPermissions",label:e("system.menu.buttonPermissions"),labelCol:{md:5,xl:6},tooltip:"该菜单下的按钮权限代码列表",children:o.jsx(Z,{type:"button",showTypeTag:!0,placeholder:"请选择按钮权限"})})]})]})}})]})}export{me as Detail};
