"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdConfigService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const ad_config_entity_1 = require("./entities/ad-config.entity");
let AdConfigService = class AdConfigService {
    adConfigRepository;
    constructor(adConfigRepository) {
        this.adConfigRepository = adConfigRepository;
    }
    async create(createAdConfigDto, userId) {
        const existingConfig = await this.adConfigRepository.findOne({
            where: { adIdentifier: createAdConfigDto.adIdentifier },
        });
        if (existingConfig) {
            throw new common_1.ConflictException(`广告标识 ${createAdConfigDto.adIdentifier} 已存在`);
        }
        this.validateImageItemsByType(createAdConfigDto.adType, createAdConfigDto.imageItems);
        const adConfig = this.adConfigRepository.create({
            ...createAdConfigDto,
            createdBy: userId,
            updatedBy: userId,
        });
        return await this.adConfigRepository.save(adConfig);
    }
    async findAll(query) {
        const { page = 1, pageSize = 10, adType, jumpType, status, title, adIdentifier } = query;
        const queryBuilder = this.adConfigRepository
            .createQueryBuilder('config')
            .leftJoinAndSelect('config.creator', 'creator')
            .leftJoinAndSelect('config.updater', 'updater');
        if (adType !== undefined) {
            queryBuilder.andWhere('config.adType = :adType', { adType });
        }
        if (status !== undefined) {
            queryBuilder.andWhere('config.status = :status', { status });
        }
        if (title) {
            queryBuilder.andWhere('config.title LIKE :title', { title: `%${title}%` });
        }
        if (adIdentifier) {
            queryBuilder.andWhere('config.adIdentifier LIKE :adIdentifier', { adIdentifier: `%${adIdentifier}%` });
        }
        queryBuilder.orderBy('config.status', 'DESC')
            .addOrderBy('config.sortOrder', 'ASC')
            .addOrderBy('config.createTime', 'DESC');
        const skip = (page - 1) * pageSize;
        queryBuilder.skip(skip).take(pageSize);
        const [list, total] = await queryBuilder.getManyAndCount();
        return {
            list: list.map(config => ({
                ...config,
                adTypeName: config.getAdTypeName(),
                creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
                updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
            })),
            total,
            page,
            pageSize,
            totalPages: Math.ceil(total / pageSize),
        };
    }
    async findOne(id) {
        const adConfig = await this.adConfigRepository.findOne({
            where: { id },
            relations: ['creator', 'updater'],
        });
        if (!adConfig) {
            throw new common_1.NotFoundException('广告配置不存在');
        }
        return {
            ...adConfig,
            adTypeName: adConfig.getAdTypeName(),
            creator: adConfig.creator ? { id: adConfig.creator.id, username: adConfig.creator.username } : null,
            updater: adConfig.updater ? { id: adConfig.updater.id, username: adConfig.updater.username } : null,
        };
    }
    async update(id, updateAdConfigDto, userId) {
        const adConfig = await this.adConfigRepository.findOne({ where: { id } });
        if (!adConfig) {
            throw new common_1.NotFoundException('广告配置不存在');
        }
        if (updateAdConfigDto.adIdentifier && updateAdConfigDto.adIdentifier !== adConfig.adIdentifier) {
            const existingConfig = await this.adConfigRepository.findOne({
                where: { adIdentifier: updateAdConfigDto.adIdentifier },
            });
            if (existingConfig) {
                throw new common_1.ConflictException(`广告标识 ${updateAdConfigDto.adIdentifier} 已存在`);
            }
        }
        const newAdType = updateAdConfigDto.adType ?? adConfig.adType;
        const newImageItems = updateAdConfigDto.imageItems ?? adConfig.imageItems;
        this.validateImageItemsByType(newAdType, newImageItems);
        Object.assign(adConfig, updateAdConfigDto, { updatedBy: userId });
        const savedConfig = await this.adConfigRepository.save(adConfig);
        return savedConfig;
    }
    async remove(id) {
        const adConfig = await this.adConfigRepository.findOne({ where: { id } });
        if (!adConfig) {
            throw new common_1.NotFoundException('广告配置不存在');
        }
        await this.adConfigRepository.remove(adConfig);
        return { message: '删除成功' };
    }
    async toggleStatus(id, userId) {
        const adConfig = await this.adConfigRepository.findOne({ where: { id } });
        if (!adConfig) {
            throw new common_1.NotFoundException('广告配置不存在');
        }
        adConfig.status = adConfig.status === 1 ? 0 : 1;
        adConfig.updatedBy = userId;
        const savedConfig = await this.adConfigRepository.save(adConfig);
        return {
            id: savedConfig.id,
            status: savedConfig.status,
            message: savedConfig.status === 1 ? '已启用' : '已禁用'
        };
    }
    async updateSortOrder(updates, userId) {
        const queryRunner = this.adConfigRepository.manager.connection.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            for (const update of updates) {
                await queryRunner.manager.update(ad_config_entity_1.AdConfig, update.id, {
                    sortOrder: update.sortOrder,
                    updatedBy: userId,
                });
            }
            await queryRunner.commitTransaction();
            return { message: '排序更新成功' };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async findByIdentifier(adIdentifier) {
        const adConfig = await this.adConfigRepository.findOne({
            where: {
                adIdentifier,
                status: 1
            },
        });
        if (!adConfig) {
            throw new common_1.NotFoundException('广告配置不存在或已禁用');
        }
        return {
            id: adConfig.id,
            adIdentifier: adConfig.adIdentifier,
            adType: adConfig.adType,
            title: adConfig.title,
            imageItems: adConfig.imageItems,
            sortOrder: adConfig.sortOrder,
        };
    }
    async findByType(adType) {
        const adConfigs = await this.adConfigRepository.find({
            where: {
                adType,
                status: 1
            },
            order: { sortOrder: 'ASC', createTime: 'DESC' },
        });
        return adConfigs.map(config => ({
            id: config.id,
            adIdentifier: config.adIdentifier,
            adType: config.adType,
            title: config.title,
            imageItems: config.imageItems,
            sortOrder: config.sortOrder,
        }));
    }
    validateImageItemsByType(adType, imageItems) {
        const itemCount = imageItems ? imageItems.length : 0;
        switch (adType) {
            case ad_config_entity_1.AdType.CAROUSEL:
                if (itemCount < 2) {
                    throw new common_1.BadRequestException(`轮播广告至少需要2个图片跳转项，当前只有${itemCount}个`);
                }
                break;
            case ad_config_entity_1.AdType.HOME_GRID_4:
                if (itemCount !== 4) {
                    throw new common_1.BadRequestException(`首页4宫格广告必须配置4个图片跳转项，当前有${itemCount}个`);
                }
                break;
            default:
                if (itemCount === 0) {
                    throw new common_1.BadRequestException('至少需要配置一个图片跳转项');
                }
                break;
        }
        imageItems.forEach((item, index) => {
            if (!item.imageUrl || !item.jumpTarget) {
                throw new common_1.BadRequestException(`第${index + 1}个图片跳转项的图片URL和跳转目标不能为空`);
            }
            if (!item.jumpType || ![1, 2].includes(item.jumpType)) {
                throw new common_1.BadRequestException(`第${index + 1}个图片跳转项的跳转类型必须是1或2`);
            }
        });
    }
    async getAdTypeStats() {
        const stats = await this.adConfigRepository
            .createQueryBuilder('config')
            .select('config.adType', 'adType')
            .addSelect('COUNT(*)', 'total')
            .addSelect('SUM(CASE WHEN config.status = 1 THEN 1 ELSE 0 END)', 'enabled')
            .groupBy('config.adType')
            .getRawMany();
        return stats.map(stat => ({
            adType: parseInt(stat.adType),
            adTypeName: this.getAdTypeNameByValue(parseInt(stat.adType)),
            total: parseInt(stat.total),
            enabled: parseInt(stat.enabled),
            disabled: parseInt(stat.total) - parseInt(stat.enabled),
        }));
    }
    getAdTypeNameByValue(adType) {
        const typeNames = {
            [ad_config_entity_1.AdType.CAROUSEL]: '轮播',
            [ad_config_entity_1.AdType.POPUP]: '弹窗广告',
            [ad_config_entity_1.AdType.FLOAT]: '浮点弹窗',
            [ad_config_entity_1.AdType.EMBED]: '嵌入广告',
            [ad_config_entity_1.AdType.BANNER]: 'Banner',
            [ad_config_entity_1.AdType.INTERSTITIAL]: '插屏广告',
            [ad_config_entity_1.AdType.HOME_GRID_4]: '首页4宫格'
        };
        return typeNames[adType] || '未知类型';
    }
};
exports.AdConfigService = AdConfigService;
exports.AdConfigService = AdConfigService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(ad_config_entity_1.AdConfig)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AdConfigService);
//# sourceMappingURL=ad-config.service.js.map