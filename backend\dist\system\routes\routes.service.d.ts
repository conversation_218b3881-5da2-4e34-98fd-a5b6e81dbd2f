import { Repository } from 'typeorm';
import { SystemMenuService } from '../menu/menu.service';
import { SysPermission } from '../entities/sys-permission.entity';
import { SysRole } from '../entities/sys-role.entity';
export declare class SystemRoutesService {
    private readonly menuService;
    private permissionRepository;
    private roleRepository;
    constructor(menuService: SystemMenuService, permissionRepository: Repository<SysPermission>, roleRepository: Repository<SysRole>);
    getRoutes(userRoles: string[], isSuperAdmin?: boolean, userPermissions?: string[]): Promise<any[]>;
    getDefaultRoutes(): Promise<({
        path: string;
        handle: {
            icon: string;
            title: string;
            order: number;
        };
        children?: undefined;
    } | {
        path: string;
        handle: {
            icon: string;
            title: string;
            order: number;
        };
        children: {
            path: string;
            handle: {
                icon: string;
                title: string;
                order: number;
            };
        }[];
    })[]>;
    getUserPermissions(userRoles: string[], isSuperAdmin?: boolean): Promise<string[]>;
}
