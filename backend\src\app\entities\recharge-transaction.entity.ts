import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON>in<PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AppUser } from './app-user.entity';

// 充值交易状态枚举
export enum RechargeTransactionStatus {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
}

// 充值交易类型枚举
export enum RechargeTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  DEPOSIT = 3, // 充值
  BALANCE_EXCHANGE = 4, // 余额兑换
}

@Entity('recharge_transactions')
export class RechargeTransaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'transaction_id' })
  transactionId: string;

  @Column({ type: 'decimal', precision: 18, scale: 4 })
  amount: number;

  @Column({ name: 'balance_before', type: 'decimal', precision: 18, scale: 4 })
  balanceBefore: number;

  @Column({ name: 'balance_after', type: 'decimal', precision: 18, scale: 4 })
  balanceAfter: number;

  @Column({ type: 'int', comment: '状态：1-收入，2-支出' })
  status: RechargeTransactionStatus;

  @Column({ name: 'transaction_type', type: 'int', comment: '交易类型：1-下注，2-赢金，3-充值，4-余额兑换' })
  transactionType: RechargeTransactionType;

  @Column({ nullable: true, length: 255 })
  description: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => AppUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: AppUser;
}

// 交易类型标签映射
export const RechargeTransactionTypeLabels = {
  [RechargeTransactionType.BET]: '下注',
  [RechargeTransactionType.WIN]: '赢金',
  [RechargeTransactionType.DEPOSIT]: '充值',
  [RechargeTransactionType.BALANCE_EXCHANGE]: '余额兑换',
};

// 交易状态标签映射
export const RechargeTransactionStatusLabels = {
  [RechargeTransactionStatus.INCOME]: '收入',
  [RechargeTransactionStatus.EXPENSE]: '支出',
};
