import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdConfig, AdType, ImageItem } from './entities/ad-config.entity';
import { CreateAdConfigDto } from './dto/create-ad-config.dto';
import { UpdateAdConfigDto } from './dto/update-ad-config.dto';
import { AdConfigQueryDto } from './dto/ad-config-query.dto';

@Injectable()
export class AdConfigService {
  constructor(
    @InjectRepository(AdConfig)
    private adConfigRepository: Repository<AdConfig>,
  ) {}

  async create(createAdConfigDto: CreateAdConfigDto, userId: number) {
    // 检查广告标识是否已存在
    const existingConfig = await this.adConfigRepository.findOne({
      where: { adIdentifier: createAdConfigDto.adIdentifier },
    });
    if (existingConfig) {
      throw new ConflictException(`广告标识 ${createAdConfigDto.adIdentifier} 已存在`);
    }

    // 验证图片数量
    this.validateImageItemsByType(createAdConfigDto.adType, createAdConfigDto.imageItems);

    const adConfig = this.adConfigRepository.create({
      ...createAdConfigDto,
      createdBy: userId,
      updatedBy: userId,
    });

    return await this.adConfigRepository.save(adConfig);
  }

  async findAll(query: AdConfigQueryDto) {
    const { page = 1, pageSize = 10, adType, jumpType, status, title, adIdentifier } = query;
    const queryBuilder = this.adConfigRepository
      .createQueryBuilder('config')
      .leftJoinAndSelect('config.creator', 'creator')
      .leftJoinAndSelect('config.updater', 'updater');

    // 添加筛选条件
    if (adType !== undefined) {
      queryBuilder.andWhere('config.adType = :adType', { adType });
    }
    // 注意：由于jumpType现在在imageItems数组中，这里的筛选需要调整
    // 暂时注释掉，后续可以通过JSON查询实现
    // if (jumpType !== undefined) {
    //   queryBuilder.andWhere('config.jumpType = :jumpType', { jumpType });
    // }
    if (status !== undefined) {
      queryBuilder.andWhere('config.status = :status', { status });
    }
    if (title) {
      queryBuilder.andWhere('config.title LIKE :title', { title: `%${title}%` });
    }
    if (adIdentifier) {
      queryBuilder.andWhere('config.adIdentifier LIKE :adIdentifier', { adIdentifier: `%${adIdentifier}%` });
    }

    // 排序：先按状态（启用的在前），再按排序字段，最后按创建时间
    queryBuilder.orderBy('config.status', 'DESC')
                .addOrderBy('config.sortOrder', 'ASC')
                .addOrderBy('config.createTime', 'DESC');

    // 分页
    const skip = (page - 1) * pageSize;
    queryBuilder.skip(skip).take(pageSize);

    const [list, total] = await queryBuilder.getManyAndCount();

    return {
      list: list.map(config => ({
        ...config,
        adTypeName: config.getAdTypeName(),
        creator: config.creator ? { id: config.creator.id, username: config.creator.username } : null,
        updater: config.updater ? { id: config.updater.id, username: config.updater.username } : null,
      })),
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize),
    };
  }

  async findOne(id: number) {
    const adConfig = await this.adConfigRepository.findOne({
      where: { id },
      relations: ['creator', 'updater'],
    });

    if (!adConfig) {
      throw new NotFoundException('广告配置不存在');
    }

    return {
      ...adConfig,
      adTypeName: adConfig.getAdTypeName(),
      creator: adConfig.creator ? { id: adConfig.creator.id, username: adConfig.creator.username } : null,
      updater: adConfig.updater ? { id: adConfig.updater.id, username: adConfig.updater.username } : null,
    };
  }

  async update(id: number, updateAdConfigDto: UpdateAdConfigDto, userId: number) {
    const adConfig = await this.adConfigRepository.findOne({ where: { id } });
    if (!adConfig) {
      throw new NotFoundException('广告配置不存在');
    }

    // 如果更新广告标识，检查是否冲突
    if (updateAdConfigDto.adIdentifier && updateAdConfigDto.adIdentifier !== adConfig.adIdentifier) {
      const existingConfig = await this.adConfigRepository.findOne({
        where: { adIdentifier: updateAdConfigDto.adIdentifier },
      });
      if (existingConfig) {
        throw new ConflictException(`广告标识 ${updateAdConfigDto.adIdentifier} 已存在`);
      }
    }

    // 验证图片数量
    const newAdType = updateAdConfigDto.adType ?? adConfig.adType;
    const newImageItems = updateAdConfigDto.imageItems ?? adConfig.imageItems;
    this.validateImageItemsByType(newAdType, newImageItems);

    Object.assign(adConfig, updateAdConfigDto, { updatedBy: userId });
    const savedConfig = await this.adConfigRepository.save(adConfig);
    return savedConfig;
  }

  async remove(id: number) {
    const adConfig = await this.adConfigRepository.findOne({ where: { id } });
    if (!adConfig) {
      throw new NotFoundException('广告配置不存在');
    }

    await this.adConfigRepository.remove(adConfig);
    return { message: '删除成功' };
  }

  // 切换状态
  async toggleStatus(id: number, userId: number) {
    const adConfig = await this.adConfigRepository.findOne({ where: { id } });
    if (!adConfig) {
      throw new NotFoundException('广告配置不存在');
    }

    adConfig.status = adConfig.status === 1 ? 0 : 1;
    adConfig.updatedBy = userId;
    
    const savedConfig = await this.adConfigRepository.save(adConfig);
    return {
      id: savedConfig.id,
      status: savedConfig.status,
      message: savedConfig.status === 1 ? '已启用' : '已禁用'
    };
  }

  // 批量更新排序
  async updateSortOrder(updates: Array<{ id: number; sortOrder: number }>, userId: number) {
    const queryRunner = this.adConfigRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const update of updates) {
        await queryRunner.manager.update(AdConfig, update.id, {
          sortOrder: update.sortOrder,
          updatedBy: userId,
        });
      }
      await queryRunner.commitTransaction();
      return { message: '排序更新成功' };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // 根据广告标识获取广告配置（供前端APP使用）
  async findByIdentifier(adIdentifier: string) {
    const adConfig = await this.adConfigRepository.findOne({
      where: {
        adIdentifier,
        status: 1 // 只返回启用的广告
      },
    });

    if (!adConfig) {
      throw new NotFoundException('广告配置不存在或已禁用');
    }

    return {
      id: adConfig.id,
      adIdentifier: adConfig.adIdentifier,
      adType: adConfig.adType,
      title: adConfig.title,
      imageItems: adConfig.imageItems,
      sortOrder: adConfig.sortOrder,
    };
  }

  // 根据广告类型获取广告列表（供前端APP使用）
  async findByType(adType: AdType) {
    const adConfigs = await this.adConfigRepository.find({
      where: { 
        adType,
        status: 1 // 只返回启用的广告
      },
      order: { sortOrder: 'ASC', createTime: 'DESC' },
    });

    return adConfigs.map(config => ({
      id: config.id,
      adIdentifier: config.adIdentifier,
      adType: config.adType,
      title: config.title,
      imageItems: config.imageItems,
      sortOrder: config.sortOrder,
    }));
  }

  // 根据广告类型验证图片跳转项数量
  private validateImageItemsByType(adType: AdType, imageItems: ImageItem[]) {
    const itemCount = imageItems ? imageItems.length : 0;

    switch (adType) {
      case AdType.CAROUSEL:
        if (itemCount < 2) {
          throw new BadRequestException(`轮播广告至少需要2个图片跳转项，当前只有${itemCount}个`);
        }
        break;
      case AdType.HOME_GRID_4:
        if (itemCount !== 4) {
          throw new BadRequestException(`首页4宫格广告必须配置4个图片跳转项，当前有${itemCount}个`);
        }
        break;
      default:
        if (itemCount === 0) {
          throw new BadRequestException('至少需要配置一个图片跳转项');
        }
        break;
    }

    // 验证每个图片跳转项的必填字段
    imageItems.forEach((item, index) => {
      if (!item.imageUrl || !item.jumpTarget) {
        throw new BadRequestException(`第${index + 1}个图片跳转项的图片URL和跳转目标不能为空`);
      }
      if (!item.jumpType || ![1, 2].includes(item.jumpType)) {
        throw new BadRequestException(`第${index + 1}个图片跳转项的跳转类型必须是1或2`);
      }
    });
  }

  // 获取广告类型统计
  async getAdTypeStats() {
    const stats = await this.adConfigRepository
      .createQueryBuilder('config')
      .select('config.adType', 'adType')
      .addSelect('COUNT(*)', 'total')
      .addSelect('SUM(CASE WHEN config.status = 1 THEN 1 ELSE 0 END)', 'enabled')
      .groupBy('config.adType')
      .getRawMany();

    return stats.map(stat => ({
      adType: parseInt(stat.adType),
      adTypeName: this.getAdTypeNameByValue(parseInt(stat.adType)),
      total: parseInt(stat.total),
      enabled: parseInt(stat.enabled),
      disabled: parseInt(stat.total) - parseInt(stat.enabled),
    }));
  }

  private getAdTypeNameByValue(adType: number): string {
    const typeNames = {
      [AdType.CAROUSEL]: '轮播',
      [AdType.POPUP]: '弹窗广告',
      [AdType.FLOAT]: '浮点弹窗',
      [AdType.EMBED]: '嵌入广告',
      [AdType.BANNER]: 'Banner',
      [AdType.INTERSTITIAL]: '插屏广告',
      [AdType.HOME_GRID_4]: '首页4宫格'
    };
    return typeNames[adType] || '未知类型';
  }
}
