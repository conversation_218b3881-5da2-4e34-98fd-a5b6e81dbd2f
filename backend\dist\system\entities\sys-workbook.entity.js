"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SysWorkbook = void 0;
const typeorm_1 = require("typeorm");
let SysWorkbook = class SysWorkbook {
    id;
    type;
    text;
    value;
    valueType;
    remark;
    status;
    order;
    createTime;
    updateTime;
};
exports.SysWorkbook = SysWorkbook;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], SysWorkbook.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, comment: '字典类型，如 PAYTYPE' }),
    __metadata("design:type", String)
], SysWorkbook.prototype, "type", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 200, comment: '显示文本，如 "支付成功"' }),
    __metadata("design:type", String)
], SysWorkbook.prototype, "text", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 500, comment: '字典值，如 "1"' }),
    __metadata("design:type", String)
], SysWorkbook.prototype, "value", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'value_type',
        length: 20,
        default: 'text',
        comment: '值类型：text-文本型, number-数值型, json-JSON型'
    }),
    __metadata("design:type", String)
], SysWorkbook.prototype, "valueType", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true, length: 500, comment: '备注说明' }),
    __metadata("design:type", String)
], SysWorkbook.prototype, "remark", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 1, comment: '状态：1-启用，0-禁用' }),
    __metadata("design:type", Number)
], SysWorkbook.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0, comment: '排序字段' }),
    __metadata("design:type", Number)
], SysWorkbook.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'create_time' }),
    __metadata("design:type", Date)
], SysWorkbook.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'update_time' }),
    __metadata("design:type", Date)
], SysWorkbook.prototype, "updateTime", void 0);
exports.SysWorkbook = SysWorkbook = __decorate([
    (0, typeorm_1.Entity)('sys_workbook')
], SysWorkbook);
//# sourceMappingURL=sys-workbook.entity.js.map