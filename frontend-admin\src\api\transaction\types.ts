// 交易状态枚举
export enum TransactionStatus {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
}

// 现金交易类型枚举
export enum CashTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  DEPOSIT = 3, // 充值
  WITHDRAW = 4, // 提取
  GOLD_EXCHANGE = 5, // 金币兑换
  RECHARGE_EXCHANGE = 6, // 充值余额兑换
}

// 金币交易类型枚举
export enum GoldTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  VIP_CARD_CLAIM = 3, // 会员卡领取
  GIFT = 4, // 赠送
  TRADE_ACQUIRE = 5, // 交易获取
  ACTIVITY_CLAIM = 6, // 活动领取
}

// 充值交易类型枚举
export enum RechargeTransactionType {
  BET = 1, // 下注
  WIN = 2, // 赢金
  DEPOSIT = 3, // 充值
  BALANCE_EXCHANGE = 4, // 余额兑换
}

// 交易统计数据
export interface TransactionStatistics {
  totalIncome: number;
  totalExpense: number;
  balance: number;
  transactionCount: number;
}

// 用户资产统计
export interface UserAssetStatistics {
  withdrawable: TransactionStatistics;
  gold: TransactionStatistics;
  recharge: TransactionStatistics;
}

// 交易记录项
export interface TransactionItem {
  id: number;
  userId: number;
  uid: number;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  status: TransactionStatus;
  transactionType: number;
  orderId?: string;
  description?: string;
  remark?: string;
  operatorId?: number;
  createTime: string;
  updateTime: string;
}

// 交易列表查询参数
export interface TransactionListQuery {
  userId: number;
  status?: TransactionStatus;
  transactionType?: number;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

// 交易列表响应
export interface TransactionListResponse {
  list: TransactionItem[];
  total: number;
  page: number;
  pageSize: number;
}

// 创建交易记录参数
export interface CreateTransactionParams {
  userId: number;
  amount: number;
  status: TransactionStatus;
  transactionType: number;
  orderId?: string;
  description?: string;
  remark?: string;
  operatorId?: number;
}

// 交易类型标签映射
export const CashTransactionTypeLabels = {
  [CashTransactionType.BET]: '下注',
  [CashTransactionType.WIN]: '赢金',
  [CashTransactionType.DEPOSIT]: '充值',
  [CashTransactionType.WITHDRAW]: '提取',
  [CashTransactionType.GOLD_EXCHANGE]: '金币兑换',
  [CashTransactionType.RECHARGE_EXCHANGE]: '充值余额兑换',
};

export const GoldTransactionTypeLabels = {
  [GoldTransactionType.BET]: '下注',
  [GoldTransactionType.WIN]: '赢金',
  [GoldTransactionType.VIP_CARD_CLAIM]: '会员卡领取',
  [GoldTransactionType.GIFT]: '赠送',
  [GoldTransactionType.TRADE_ACQUIRE]: '交易获取',
  [GoldTransactionType.ACTIVITY_CLAIM]: '活动领取',
};

export const RechargeTransactionTypeLabels = {
  [RechargeTransactionType.BET]: '下注',
  [RechargeTransactionType.WIN]: '赢金',
  [RechargeTransactionType.DEPOSIT]: '充值',
  [RechargeTransactionType.BALANCE_EXCHANGE]: '余额兑换',
};

// 交易状态标签映射
export const TransactionStatusLabels = {
  [TransactionStatus.INCOME]: '收入',
  [TransactionStatus.EXPENSE]: '支出',
};
