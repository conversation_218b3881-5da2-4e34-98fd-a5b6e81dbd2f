"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppHomeConfigModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const app_home_config_service_1 = require("./app-home-config.service");
const app_home_config_controller_1 = require("./app-home-config.controller");
const entities_1 = require("./entities");
const application_entity_1 = require("../entities/application.entity");
let AppHomeConfigModule = class AppHomeConfigModule {
};
exports.AppHomeConfigModule = AppHomeConfigModule;
exports.AppHomeConfigModule = AppHomeConfigModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                entities_1.AppHomeConfig,
                entities_1.AppHomeRecommendedGame,
                entities_1.AppHomeGameCategory,
                entities_1.AppHomeCategoryGame,
                entities_1.AdConfig,
                application_entity_1.Application,
            ]),
        ],
        controllers: [app_home_config_controller_1.AppHomeConfigController],
        providers: [app_home_config_service_1.AppHomeConfigService],
        exports: [app_home_config_service_1.AppHomeConfigService],
    })
], AppHomeConfigModule);
//# sourceMappingURL=app-home-config.module.js.map