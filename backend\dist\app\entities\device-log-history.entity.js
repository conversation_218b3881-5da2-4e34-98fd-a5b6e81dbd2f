"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeviceLogHistory = void 0;
const typeorm_1 = require("typeorm");
const app_user_entity_1 = require("./app-user.entity");
let DeviceLogHistory = class DeviceLogHistory {
    id;
    userId;
    deviceId;
    ipAddress;
    userAgent;
    logType;
    user;
    createdAt;
};
exports.DeviceLogHistory = DeviceLogHistory;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], DeviceLogHistory.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_id' }),
    __metadata("design:type", Number)
], DeviceLogHistory.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'device_id', length: 255 }),
    __metadata("design:type", String)
], DeviceLogHistory.prototype, "deviceId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ip_address', length: 45 }),
    __metadata("design:type", String)
], DeviceLogHistory.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'user_agent', type: 'text', nullable: true }),
    __metadata("design:type", String)
], DeviceLogHistory.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'log_type', comment: '日志类型：1-注册，2-登录' }),
    __metadata("design:type", Number)
], DeviceLogHistory.prototype, "logType", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => app_user_entity_1.AppUser),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", app_user_entity_1.AppUser)
], DeviceLogHistory.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], DeviceLogHistory.prototype, "createdAt", void 0);
exports.DeviceLogHistory = DeviceLogHistory = __decorate([
    (0, typeorm_1.Entity)('device_logs_history')
], DeviceLogHistory);
//# sourceMappingURL=device-log-history.entity.js.map