"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemRoutesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const menu_service_1 = require("../menu/menu.service");
const sys_permission_entity_1 = require("../entities/sys-permission.entity");
const sys_role_entity_1 = require("../entities/sys-role.entity");
let SystemRoutesService = class SystemRoutesService {
    menuService;
    permissionRepository;
    roleRepository;
    constructor(menuService, permissionRepository, roleRepository) {
        this.menuService = menuService;
        this.permissionRepository = permissionRepository;
        this.roleRepository = roleRepository;
    }
    async getRoutes(userRoles, isSuperAdmin = false, userPermissions = []) {
        console.log(`[ROUTES_SERVICE] getRoutes调用:`, {
            userRoles,
            isSuperAdmin,
            userPermissionsCount: userPermissions.length
        });
        if (userPermissions.length === 0) {
            userPermissions = await this.getUserPermissions(userRoles, isSuperAdmin);
        }
        const userMenus = await this.menuService.getUserMenus(userRoles, isSuperAdmin, userPermissions);
        console.log(`[ROUTES_SERVICE] ${isSuperAdmin ? '超级管理员' : '普通用户'}，从数据库获取菜单数量: ${userMenus.length}`);
        if (userMenus.length === 0 && isSuperAdmin) {
            console.log(`[ROUTES_SERVICE] 数据库无菜单，超级管理员使用默认路由`);
            const defaultRoutes = await this.getDefaultRoutes();
            console.log(`[ROUTES_SERVICE] 默认路由数量: ${defaultRoutes.length}`);
            return defaultRoutes;
        }
        return userMenus;
    }
    async getDefaultRoutes() {
        return [
            {
                path: '/home',
                handle: {
                    icon: 'DashboardOutlined',
                    title: 'common.menu.home',
                    order: 1,
                },
            },
            {
                path: '/system',
                handle: {
                    icon: 'SettingOutlined',
                    title: 'common.menu.system',
                    order: 2,
                },
                children: [
                    {
                        path: '/system/user',
                        handle: {
                            icon: 'UserOutlined',
                            title: 'common.menu.user',
                            order: 1,
                        },
                    },
                    {
                        path: '/system/role',
                        handle: {
                            icon: 'TeamOutlined',
                            title: 'common.menu.role',
                            order: 2,
                        },
                    },
                    {
                        path: '/system/permission',
                        handle: {
                            icon: 'SafetyOutlined',
                            title: 'common.menu.permission',
                            order: 3,
                        },
                    },
                    {
                        path: '/system/menu',
                        handle: {
                            icon: 'MenuOutlined',
                            title: 'common.menu.menu',
                            order: 4,
                        },
                    },
                    {
                        path: '/system/workbook',
                        handle: {
                            icon: 'BookOutlined',
                            title: 'common.menu.workbook',
                            order: 5,
                        },
                    },
                ],
            },
            {
                path: '/app',
                handle: {
                    icon: 'ShopOutlined',
                    title: 'common.menu.applicationManagement',
                    order: 3,
                },
                children: [
                    {
                        path: '/app/supplier',
                        handle: {
                            icon: 'ShopOutlined',
                            title: 'common.menu.supplierManagement',
                            order: 1,
                        },
                    },
                    {
                        path: '/app/application',
                        handle: {
                            icon: 'ShopOutlined',
                            title: 'common.menu.applicationCenter',
                            order: 2,
                        },
                    },
                ],
            },
            {
                path: '/user-management',
                handle: {
                    icon: 'UsergroupAddOutlined',
                    title: 'common.menu.user',
                    order: 4,
                },
                children: [
                    {
                        path: '/user-management/list',
                        handle: {
                            icon: 'UserOutlined',
                            title: 'common.menu.appUserList',
                            order: 1,
                        },
                    },
                ],
            },
            {
                path: '/personal-center',
                handle: {
                    icon: 'UserOutlined',
                    title: 'common.menu.personalCenter',
                    order: 5,
                },
                children: [
                    {
                        path: '/personal-center/profile',
                        handle: {
                            icon: 'ProfileOutlined',
                            title: 'common.menu.profile',
                            order: 1,
                        },
                    },
                    {
                        path: '/personal-center/settings',
                        handle: {
                            icon: 'SettingOutlined',
                            title: 'common.menu.settings',
                            order: 2,
                        },
                    },
                ],
            },
        ];
    }
    async getUserPermissions(userRoles, isSuperAdmin = false) {
        if (isSuperAdmin) {
            console.log(`[ROUTES_SERVICE] 超级管理员，返回所有权限`);
            const allPermissions = await this.permissionRepository.find({
                where: { status: 1 },
                select: ['code'],
            });
            console.log(`[ROUTES_SERVICE] 超级管理员权限数量: ${allPermissions.length}`);
            return allPermissions.map(p => p.code);
        }
        if (userRoles.length === 0) {
            console.log(`[ROUTES_SERVICE] 用户无角色，返回空权限`);
            return [];
        }
        console.log(`[ROUTES_SERVICE] 普通用户，根据角色获取权限: ${userRoles.join(', ')}`);
        const permissions = await this.permissionRepository
            .createQueryBuilder('permission')
            .innerJoin('sys_role_permissions', 'rp', 'rp.permission_id = permission.id')
            .innerJoin('sys_roles', 'role', 'role.id = rp.role_id')
            .where('role.code IN (:...roleCodes)', { roleCodes: userRoles })
            .andWhere('permission.status = :status', { status: 1 })
            .select('permission.code')
            .getMany();
        console.log(`[ROUTES_SERVICE] 普通用户权限数量: ${permissions.length}`);
        return permissions.map(p => p.code);
    }
};
exports.SystemRoutesService = SystemRoutesService;
exports.SystemRoutesService = SystemRoutesService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(sys_permission_entity_1.SysPermission)),
    __param(2, (0, typeorm_1.InjectRepository)(sys_role_entity_1.SysRole)),
    __metadata("design:paramtypes", [menu_service_1.SystemMenuService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SystemRoutesService);
//# sourceMappingURL=routes.service.js.map